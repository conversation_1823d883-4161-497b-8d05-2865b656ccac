'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');

const popperArrowProps = runtime.buildProps({
  arrowOffset: {
    type: Number,
    default: 5
  }
});
const usePopperArrowProps = popperArrowProps;

exports.popperArrowProps = popperArrowProps;
exports.usePopperArrowProps = usePopperArrowProps;
//# sourceMappingURL=arrow.js.map
