{"version": 3, "file": "use-computed-data.js", "sources": ["../../../../../../../packages/components/transfer/src/composables/use-computed-data.ts"], "sourcesContent": ["import { computed } from 'vue'\nimport { usePropsAlias } from './use-props-alias'\n\nimport type { TransferDataItem, TransferKey, TransferProps } from '../transfer'\n\nexport const useComputedData = (props: TransferProps) => {\n  const propsAlias = usePropsAlias(props)\n\n  const dataObj = computed(() =>\n    props.data.reduce((o, cur) => (o[cur[propsAlias.value.key]] = cur) && o, {})\n  )\n\n  const sourceData = computed(() =>\n    props.data.filter(\n      (item) => !props.modelValue.includes(item[propsAlias.value.key])\n    )\n  )\n\n  const targetData = computed(() => {\n    if (props.targetOrder === 'original') {\n      return props.data.filter((item) =>\n        props.modelValue.includes(item[propsAlias.value.key])\n      )\n    } else {\n      return props.modelValue.reduce(\n        (arr: TransferDataItem[], cur: TransferKey) => {\n          const val = dataObj.value[cur]\n          if (val) {\n            arr.push(val)\n          }\n          return arr\n        },\n        []\n      )\n    }\n  })\n\n  return {\n    sourceData,\n    targetData,\n  }\n}\n"], "names": ["usePropsAlias", "computed"], "mappings": ";;;;;;;AAEY,MAAC,eAAe,GAAG,CAAC,KAAK,KAAK;AAC1C,EAAE,MAAM,UAAU,GAAGA,2BAAa,CAAC,KAAK,CAAC,CAAC;AAC1C,EAAE,MAAM,OAAO,GAAGC,YAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC/G,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACzH,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM;AACpC,IAAI,IAAI,KAAK,CAAC,WAAW,KAAK,UAAU,EAAE;AAC1C,MAAM,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAChG,KAAK,MAAM;AACX,MAAM,OAAO,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK;AACnD,QAAQ,MAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACvC,QAAQ,IAAI,GAAG,EAAE;AACjB,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACxB,SAAS;AACT,QAAQ,OAAO,GAAG,CAAC;AACnB,OAAO,EAAE,EAAE,CAAC,CAAC;AACb,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}