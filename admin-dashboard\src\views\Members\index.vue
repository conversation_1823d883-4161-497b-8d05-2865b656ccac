<template>
  <div class="members-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">会员管理</h1>
      <p class="page-subtitle">管理会员用户和套餐配置</p>
    </div>

    <!-- 会员统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#1890ff"><Postcard /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ memberStats.totalMembers }}</div>
            <div class="stat-label">会员总数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#52c41a"><Crown /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ memberStats.permanentMembers }}</div>
            <div class="stat-label">永久会员</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#faad14"><Calendar /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ memberStats.monthlyMembers }}</div>
            <div class="stat-label">月会员</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#f5222d"><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ memberStats.conversionRate }}%</div>
            <div class="stat-label">转化率</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 会员套餐管理 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">会员套餐配置</h3>
        <el-button type="primary" @click="showPackageDialog">
          <el-icon><Plus /></el-icon>
          添加套餐
        </el-button>
      </div>

      <el-row :gutter="20">
        <el-col :xs="24" :sm="12" :lg="8" v-for="pkg in memberPackages" :key="pkg.id">
          <div class="package-card" :class="{ 'package-popular': pkg.isPopular }">
            <div class="package-header">
              <div class="package-name">{{ pkg.name }}</div>
              <div class="package-price">
                <span class="price-symbol">¥</span>
                <span class="price-value">{{ pkg.price }}</span>
                <span class="price-unit">{{ pkg.unit }}</span>
              </div>
            </div>

            <div class="package-features">
              <div class="feature-item" v-for="feature in pkg.features" :key="feature">
                <el-icon color="#52c41a"><Check /></el-icon>
                <span>{{ feature }}</span>
              </div>
            </div>

            <div class="package-stats">
              <div class="stat-item">
                <span class="stat-label">已售出</span>
                <span class="stat-value">{{ pkg.soldCount }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">活跃用户</span>
                <span class="stat-value">{{ pkg.activeUsers }}</span>
              </div>
            </div>

            <div class="package-actions">
              <el-button size="small" @click="editPackage(pkg)">编辑</el-button>
              <el-button size="small" type="warning" @click="togglePackageStatus(pkg)">
                {{ pkg.isActive ? '禁用' : '启用' }}
              </el-button>
              <el-popconfirm
                title="确定要删除这个套餐吗？"
                @confirm="deletePackage(pkg)"
              >
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 会员用户列表 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">会员用户列表</h3>
        <div class="member-filter">
          <el-select v-model="memberFilter" @change="handleFilterChange" style="width: 150px">
            <el-option label="全部会员" value="all" />
            <el-option label="永久会员" value="permanent" />
            <el-option label="月会员" value="monthly" />
            <el-option label="即将到期" value="expiring" />
          </el-select>
        </div>
      </div>

      <el-table :data="memberUsers" style="width: 100%">
        <el-table-column prop="username" label="用户名" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewUser(row.id)">
              {{ row.username }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column label="会员类型" width="120">
          <template #default="{ row }">
            <el-tag :type="getMemberTagType(row.membershipType)" size="small">
              {{ getMemberTypeText(row.membershipType) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="开通时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.memberStartTime) }}
          </template>
        </el-table-column>

        <el-table-column label="到期时间" width="160">
          <template #default="{ row }">
            <span v-if="row.membershipType === 'permanent'" class="permanent-member">
              永久有效
            </span>
            <span v-else-if="row.memberExpireTime">
              {{ formatDate(row.memberExpireTime) }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="剩余天数" width="100">
          <template #default="{ row }">
            <span v-if="row.membershipType === 'permanent'" class="permanent-member">
              ∞
            </span>
            <span v-else-if="row.memberExpireTime">
              <el-tag
                :type="getRemainingDaysTagType(getRemainingDays(row.memberExpireTime))"
                size="small"
              >
                {{ getRemainingDays(row.memberExpireTime) }}天
              </el-tag>
            </span>
          </template>
        </el-table-column>

        <el-table-column label="使用的会员码" width="150">
          <template #default="{ row }">
            <span v-if="row.memberCode">
              <el-button type="text" size="small" @click="viewMemberCode(row.memberCode)">
                {{ row.memberCode }}
              </el-button>
            </span>
            <span v-else class="no-code">-</span>
          </template>
        </el-table-column>

        <el-table-column label="数据同步" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.isDataSyncEnabled"
              @change="handleSyncToggle(row)"
            />
          </template>
        </el-table-column>

        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="viewUser(row.id)"
            >
              详情
            </el-button>

            <el-button
              type="warning"
              size="small"
              @click="extendMembership(row)"
            >
              续期
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 套餐编辑对话框 -->
    <el-dialog
      v-model="packageDialogVisible"
      :title="isEditingPackage ? '编辑套餐' : '添加套餐'"
      width="600px"
    >
      <el-form
        ref="packageFormRef"
        :model="packageForm"
        :rules="packageRules"
        label-width="100px"
      >
        <el-form-item label="套餐名称" prop="name">
          <el-input v-model="packageForm.name" />
        </el-form-item>

        <el-form-item label="套餐描述" prop="description">
          <el-input v-model="packageForm.description" type="textarea" :rows="2" />
        </el-form-item>

        <el-form-item label="套餐价格" prop="price">
          <el-input-number v-model="packageForm.price" :min="0" :precision="2" style="width: 100%" />
        </el-form-item>

        <el-form-item label="价格单位" prop="unit">
          <el-select v-model="packageForm.unit" style="width: 100%">
            <el-option label="一次性" value="一次性" />
            <el-option label="/月" value="/月" />
            <el-option label="/年" value="/年" />
          </el-select>
        </el-form-item>

        <el-form-item label="有效期(天)" prop="durationDays">
          <el-input-number v-model="packageForm.durationDays" :min="-1" style="width: 100%" />
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            -1表示永久有效，其他数字表示天数
          </div>
        </el-form-item>

        <el-form-item label="套餐特性">
          <el-input
            v-model="featureInput"
            placeholder="输入特性后按回车添加"
            @keyup.enter="addFeature"
          />
          <div style="margin-top: 8px;">
            <el-tag
              v-for="(feature, index) in packageForm.features"
              :key="index"
              closable
              @close="removeFeature(index)"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ feature }}
            </el-tag>
          </div>
        </el-form-item>

        <!-- 权限设置 -->
        <el-divider content-position="left">权限设置</el-divider>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="每日生成小说数">
              <el-input-number
                v-model="packageForm.permissions.maxNovelsPerDay"
                :min="-1"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999;">-1表示无限制</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每本小说章节数">
              <el-input-number
                v-model="packageForm.permissions.maxChaptersPerNovel"
                :min="-1"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999;">-1表示无限制</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="知识库文档数">
              <el-input-number
                v-model="packageForm.permissions.maxKnowledgeDocuments"
                :min="-1"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999;">-1表示无限制</div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每次生成字数">
              <el-input-number
                v-model="packageForm.permissions.maxWordsPerGeneration"
                :min="-1"
                style="width: 100%"
              />
              <div style="font-size: 12px; color: #999;">-1表示无限制</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="扩展功能">
              <el-switch v-model="packageForm.permissions.canUseExtendedFeatures" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="知识库访问">
              <el-switch v-model="packageForm.permissions.canAccessKnowledgeBase" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="小说扩展">
              <el-switch v-model="packageForm.permissions.canUseNovelExtension" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="高级AI">
              <el-switch v-model="packageForm.permissions.canUseAdvancedAI" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="多格式导出">
              <el-switch v-model="packageForm.permissions.canExportToMultipleFormats" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="自定义角色数">
              <el-input-number
                v-model="packageForm.permissions.maxCustomCharacterTypes"
                :min="-1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="是否热门">
          <el-switch v-model="packageForm.isPopular" />
        </el-form-item>

        <el-form-item label="是否启用">
          <el-switch v-model="packageForm.isActive" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="packageDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSavePackage" :loading="saveLoading">
          保存
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const saveLoading = ref(false)
const packageDialogVisible = ref(false)
const isEditingPackage = ref(false)
const packageFormRef = ref<FormInstance>()
const memberFilter = ref('all')
const featureInput = ref('')

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 会员统计
const memberStats = ref({
  totalMembers: 0,
  permanentMembers: 0,
  monthlyMembers: 0,
  conversionRate: 0
})

// 会员套餐数据（从API动态加载）
const memberPackages = ref([])

// 会员用户数据
const memberUsers = ref([])

// 套餐表单
const packageForm = reactive({
  id: '',
  name: '',
  description: '',
  price: 0,
  unit: '一次性',
  durationDays: 30,
  features: [],
  permissions: {
    maxNovelsPerDay: -1,
    maxChaptersPerNovel: -1,
    maxKnowledgeDocuments: -1,
    canUseExtendedFeatures: true,
    maxWordsPerGeneration: -1,
    canExportToMultipleFormats: true,
    canUseAdvancedAI: true,
    maxCustomCharacterTypes: -1,
    canAccessKnowledgeBase: true,
    canUseNovelExtension: true
  },
  isPopular: false,
  isActive: true
})

// 表单验证规则
const packageRules: FormRules = {
  name: [
    { required: true, message: '请输入套餐名称', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入套餐价格', trigger: 'blur' }
  ]
}

// 获取会员类型标签类型
const getMemberTagType = (type: string) => {
  switch (type) {
    case 'permanent': return 'success'
    case 'monthly': return 'warning'
    default: return 'info'
  }
}

// 获取会员类型文本
const getMemberTypeText = (type: string) => {
  switch (type) {
    case 'permanent': return '永久会员'
    case 'monthly': return '月会员'
    default: return '普通用户'
  }
}

// 获取剩余天数
const getRemainingDays = (expireTime: string) => {
  const expire = dayjs(expireTime)
  const now = dayjs()
  return Math.max(0, expire.diff(now, 'day'))
}

// 获取剩余天数标签类型
const getRemainingDaysTagType = (days: number) => {
  if (days <= 7) return 'danger'
  if (days <= 30) return 'warning'
  return 'success'
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm')
}

// 查看用户
const viewUser = (userId: string) => {
  router.push(`/users/${userId}`)
}

// 查看会员码
const viewMemberCode = (code: string) => {
  router.push('/member-codes')
}

// 处理筛选变化
const handleFilterChange = (value: string) => {
  loadMemberUsers()
}

// 处理同步开关切换
const handleSyncToggle = async (user: any) => {
  try {
    ElMessage.success('设置已更新')
  } catch (error) {
    ElMessage.error('设置失败')
    user.isDataSyncEnabled = !user.isDataSyncEnabled
  }
}

// 续期会员
const extendMembership = async (user: any) => {
  try {
    await ElMessageBox.prompt('请输入续期天数', '会员续期', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^\d+$/,
      inputErrorMessage: '请输入有效的天数'
    }).then(async ({ value }) => {
      const days = parseInt(value)

      const response = await fetch(`/api/users/${user.id}/extend-membership`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify({ days })
      })

      const result = await response.json()

      if (response.ok && result.success) {
        ElMessage.success(result.message)
        loadMemberUsers()
      } else {
        ElMessage.error(result.message || '续期失败')
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      console.error('续期失败:', error)
      ElMessage.error('续期失败')
    }
  }
}

// 显示套餐对话框
const showPackageDialog = () => {
  isEditingPackage.value = false
  resetPackageForm()
  packageDialogVisible.value = true
}

// 编辑套餐
const editPackage = (pkg: any) => {
  isEditingPackage.value = true

  // 确保权限对象存在
  const permissions = pkg.permissions || {
    maxNovelsPerDay: -1,
    maxChaptersPerNovel: -1,
    maxKnowledgeDocuments: -1,
    canUseExtendedFeatures: true,
    maxWordsPerGeneration: -1,
    canExportToMultipleFormats: true,
    canUseAdvancedAI: true,
    maxCustomCharacterTypes: -1,
    canAccessKnowledgeBase: true,
    canUseNovelExtension: true
  }

  Object.assign(packageForm, {
    ...pkg,
    permissions: { ...permissions }
  })

  packageDialogVisible.value = true
}

// 切换套餐状态
const togglePackageStatus = async (pkg: any) => {
  const newStatus = !pkg.isActive

  try {
    const response = await fetch(`/api/packages/${pkg.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      },
      body: JSON.stringify({
        isActive: newStatus
      })
    })

    const result = await response.json()

    if (response.ok && result.success) {
      pkg.isActive = newStatus
      ElMessage.success(`套餐已${newStatus ? '启用' : '禁用'}`)
    } else {
      ElMessage.error(result.message || '操作失败')
    }
  } catch (error) {
    console.error('切换套餐状态失败:', error)
    ElMessage.error('操作失败')
  }
}

// 删除套餐
const deletePackage = async (pkg: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除套餐"${pkg.name}"吗？删除后无法恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await fetch(`/api/packages/${pkg.id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message || '套餐删除成功')
      await loadMemberPackages()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除套餐失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 添加特性
const addFeature = () => {
  if (featureInput.value.trim()) {
    packageForm.features.push(featureInput.value.trim())
    featureInput.value = ''
  }
}

// 移除特性
const removeFeature = (index: number) => {
  packageForm.features.splice(index, 1)
}

// 重置套餐表单
const resetPackageForm = () => {
  Object.assign(packageForm, {
    id: '',
    name: '',
    description: '',
    price: 0,
    unit: '一次性',
    durationDays: 30,
    features: [],
    permissions: {
      maxNovelsPerDay: -1,
      maxChaptersPerNovel: -1,
      maxKnowledgeDocuments: -1,
      canUseExtendedFeatures: true,
      maxWordsPerGeneration: -1,
      canExportToMultipleFormats: true,
      canUseAdvancedAI: true,
      maxCustomCharacterTypes: -1,
      canAccessKnowledgeBase: true,
      canUseNovelExtension: true
    },
    isPopular: false,
    isActive: true
  })
}

// 保存套餐
const handleSavePackage = async () => {
  if (!packageFormRef.value) return

  try {
    const valid = await packageFormRef.value.validate()
    if (!valid) return

    saveLoading.value = true

    const packageData = {
      name: packageForm.name,
      price: packageForm.price,
      unit: packageForm.unit,
      features: packageForm.features,
      isPopular: packageForm.isPopular,
      isActive: packageForm.isActive
    }

    let response
    if (isEditingPackage.value && packageForm.id) {
      // 更新套餐
      response = await fetch(`/api/packages/${packageForm.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify(packageData)
      })
    } else {
      // 添加套餐
      response = await fetch('/api/packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        },
        body: JSON.stringify(packageData)
      })
    }

    const result = await response.json()

    if (response.ok && result.success) {
      ElMessage.success(result.message || '套餐保存成功')
      packageDialogVisible.value = false
      await loadMemberPackages()
    } else {
      ElMessage.error(result.message || '保存失败')
    }
  } catch (error) {
    console.error('保存套餐失败:', error)
    ElMessage.error('保存失败')
  } finally {
    saveLoading.value = false
  }
}

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadMemberUsers()
}

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page
  loadMemberUsers()
}

// 加载会员统计
const loadMemberStats = async () => {
  try {
    const response = await fetch('/api/members/stats', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        memberStats.value = result.data

        // 更新套餐统计（安全检查）
        if (memberPackages.value.length >= 2) {
          memberPackages.value[0].soldCount = result.data.permanentMembers || 0
          memberPackages.value[0].activeUsers = result.data.permanentMembers || 0
          memberPackages.value[1].soldCount = result.data.monthlyMembers || 0
          memberPackages.value[1].activeUsers = result.data.monthlyMembers || 0
        } else {
          console.warn('套餐数据未加载完成，跳过统计更新')
        }
      }
    }
  } catch (error) {
    console.error('加载会员统计失败:', error)
  }
}

// 加载会员套餐
const loadMemberPackages = async () => {
  try {
    const response = await fetch('/api/packages', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        memberPackages.value = result.data || []
      } else {
        ElMessage.error(result.message || '加载套餐失败')
      }
    } else {
      ElMessage.error('加载套餐失败')
    }
  } catch (error) {
    console.error('加载会员套餐失败:', error)
    ElMessage.error('加载会员套餐失败')
  }
}

// 加载会员用户
const loadMemberUsers = async () => {
  try {
    const params = new URLSearchParams({
      page: pagination.page.toString(),
      size: pagination.size.toString(),
      memberType: memberFilter.value === 'all' ? '' : memberFilter.value
    })

    const response = await fetch(`/api/users?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        // 只显示会员用户
        const allUsers = result.data.users || []
        memberUsers.value = allUsers.filter(user => user.isMember)
        pagination.total = memberUsers.value.length
      }
    }
  } catch (error) {
    console.error('加载会员用户失败:', error)
    ElMessage.error('加载会员用户失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadMemberStats()
  loadMemberPackages()
  loadMemberUsers()
})
</script>

<style scoped>
.members-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.package-card {
  background: #f8f9fa;
  border: 2px solid #e8e8e8;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 20px;
  transition: all 0.3s;
  position: relative;
}

.package-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.package-card.package-popular {
  border-color: #1890ff;
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
}

.package-card.package-popular::before {
  content: '热门';
  position: absolute;
  top: -1px;
  right: 20px;
  background: #1890ff;
  color: white;
  padding: 4px 12px;
  border-radius: 0 0 8px 8px;
  font-size: 12px;
  font-weight: 600;
}

.package-header {
  text-align: center;
  margin-bottom: 20px;
}

.package-name {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 12px;
}

.package-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.price-symbol {
  font-size: 16px;
  color: #8c8c8c;
}

.price-value {
  font-size: 32px;
  font-weight: bold;
  color: #1890ff;
}

.price-unit {
  font-size: 14px;
  color: #8c8c8c;
}

.package-features {
  margin-bottom: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #595959;
}

.package-stats {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 8px;
}

.stat-item {
  text-align: center;
}

.stat-item .stat-label {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.stat-item .stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.package-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.member-filter {
  display: flex;
  align-items: center;
  gap: 12px;
}

.permanent-member {
  color: #52c41a;
  font-weight: 600;
}

.no-code {
  color: #d9d9d9;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式 */
@media (max-width: 768px) {
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .package-stats {
    flex-direction: column;
    gap: 12px;
  }

  .package-actions {
    flex-direction: column;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<style scoped>
.members-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
}

.coming-soon h2 {
  font-size: 24px;
  color: #262626;
  margin: 20px 0 12px 0;
}

.coming-soon p {
  color: #8c8c8c;
}
</style>
