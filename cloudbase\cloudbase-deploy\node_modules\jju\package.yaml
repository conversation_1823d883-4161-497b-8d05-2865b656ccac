# "jju" stands for "json/json5 utils"
name: jju

version: 1.4.0
description: a set of utilities to work with JSON / JSON5 documents

author:
  name: <PERSON>
  email: <EMAIL>

repository:
  type: git
  url: git://github.com/rlidwka/jju

bugs:
  url: https://github.com/rlidwka/jju/issues

homepage: http://rlidwka.github.io/jju/

devDependencies:
  mocha: '^5.2.0'
  js-yaml: '^3.12.0'
  eslint: '~0.4.2' # TODO: update this

scripts:
  test: make test
  lint: make lint

keywords:
  - json
  - json5
  - parser
  - serializer
  - data

license: MIT
