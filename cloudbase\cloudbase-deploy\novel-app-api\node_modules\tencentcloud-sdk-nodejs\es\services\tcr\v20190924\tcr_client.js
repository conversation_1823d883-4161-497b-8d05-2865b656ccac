import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tcr.tencentcloudapi.com", "2019-09-24", clientConfig);
    }
    async DescribeImageLifecycleGlobalPersonal(req, cb) {
        return this.request("DescribeImageLifecycleGlobalPersonal", req, cb);
    }
    async DescribeReplicationInstances(req, cb) {
        return this.request("DescribeReplicationInstances", req, cb);
    }
    async DeleteSecurityPolicy(req, cb) {
        return this.request("DeleteSecurityPolicy", req, cb);
    }
    async DescribeImagePersonal(req, cb) {
        return this.request("DescribeImagePersonal", req, cb);
    }
    async DeleteReplicationInstance(req, cb) {
        return this.request("DeleteReplicationInstance", req, cb);
    }
    async CreateNamespace(req, cb) {
        return this.request("CreateNamespace", req, cb);
    }
    async DescribeRepositories(req, cb) {
        return this.request("DescribeRepositories", req, cb);
    }
    async ManageReplication(req, cb) {
        return this.request("ManageReplication", req, cb);
    }
    async DescribeWebhookTriggerLog(req, cb) {
        return this.request("DescribeWebhookTriggerLog", req, cb);
    }
    async DescribeImageManifests(req, cb) {
        return this.request("DescribeImageManifests", req, cb);
    }
    async ManageImageLifecycleGlobalPersonal(req, cb) {
        return this.request("ManageImageLifecycleGlobalPersonal", req, cb);
    }
    async CreateInstanceCustomizedDomain(req, cb) {
        return this.request("CreateInstanceCustomizedDomain", req, cb);
    }
    async DeleteImmutableTagRules(req, cb) {
        return this.request("DeleteImmutableTagRules", req, cb);
    }
    async DeleteImagePersonal(req, cb) {
        return this.request("DeleteImagePersonal", req, cb);
    }
    async CreateImageAccelerationService(req, cb) {
        return this.request("CreateImageAccelerationService", req, cb);
    }
    async ModifyWebhookTrigger(req, cb) {
        return this.request("ModifyWebhookTrigger", req, cb);
    }
    async BatchDeleteRepositoryPersonal(req, cb) {
        return this.request("BatchDeleteRepositoryPersonal", req, cb);
    }
    async DeleteWebhookTrigger(req, cb) {
        return this.request("DeleteWebhookTrigger", req, cb);
    }
    async DescribeReplicationInstanceCreateTasks(req, cb) {
        return this.request("DescribeReplicationInstanceCreateTasks", req, cb);
    }
    async DeleteImageAccelerateService(req, cb) {
        return this.request("DeleteImageAccelerateService", req, cb);
    }
    async DescribeFavorRepositoryPersonal(req, cb) {
        return this.request("DescribeFavorRepositoryPersonal", req, cb);
    }
    async CreateMultipleSecurityPolicy(req, cb) {
        return this.request("CreateMultipleSecurityPolicy", req, cb);
    }
    async DeleteServiceAccount(req, cb) {
        return this.request("DeleteServiceAccount", req, cb);
    }
    async DescribeTagRetentionRules(req, cb) {
        return this.request("DescribeTagRetentionRules", req, cb);
    }
    async DuplicateImagePersonal(req, cb) {
        return this.request("DuplicateImagePersonal", req, cb);
    }
    async CreateTagRetentionRule(req, cb) {
        return this.request("CreateTagRetentionRule", req, cb);
    }
    async DeleteImageLifecycleGlobalPersonal(req, cb) {
        return this.request("DeleteImageLifecycleGlobalPersonal", req, cb);
    }
    async DescribeReplicationInstanceSyncStatus(req, cb) {
        return this.request("DescribeReplicationInstanceSyncStatus", req, cb);
    }
    async DeleteNamespacePersonal(req, cb) {
        return this.request("DeleteNamespacePersonal", req, cb);
    }
    async ModifyRepositoryAccessPersonal(req, cb) {
        return this.request("ModifyRepositoryAccessPersonal", req, cb);
    }
    async ModifyInstance(req, cb) {
        return this.request("ModifyInstance", req, cb);
    }
    async DescribeApplicationTriggerPersonal(req, cb) {
        return this.request("DescribeApplicationTriggerPersonal", req, cb);
    }
    async ModifyServiceAccountPassword(req, cb) {
        return this.request("ModifyServiceAccountPassword", req, cb);
    }
    async DescribeTagRetentionExecution(req, cb) {
        return this.request("DescribeTagRetentionExecution", req, cb);
    }
    async DeleteInstanceCustomizedDomain(req, cb) {
        return this.request("DeleteInstanceCustomizedDomain", req, cb);
    }
    async DescribeGCJobs(req, cb) {
        return this.request("DescribeGCJobs", req, cb);
    }
    async DescribeImageAccelerateService(req, cb) {
        return this.request("DescribeImageAccelerateService", req, cb);
    }
    async ModifyRepositoryInfoPersonal(req, cb) {
        return this.request("ModifyRepositoryInfoPersonal", req, cb);
    }
    async CreateTagRetentionExecution(req, cb) {
        return this.request("CreateTagRetentionExecution", req, cb);
    }
    async CheckInstanceName(req, cb) {
        return this.request("CheckInstanceName", req, cb);
    }
    async CheckInstance(req, cb) {
        return this.request("CheckInstance", req, cb);
    }
    async DescribeNamespacePersonal(req, cb) {
        return this.request("DescribeNamespacePersonal", req, cb);
    }
    async DescribeInstanceAllNamespaces(req, cb) {
        return this.request("DescribeInstanceAllNamespaces", req, cb);
    }
    async DescribeRepositoryPersonal(req, cb) {
        return this.request("DescribeRepositoryPersonal", req, cb);
    }
    async RenewInstance(req, cb) {
        return this.request("RenewInstance", req, cb);
    }
    async CreateInstance(req, cb) {
        return this.request("CreateInstance", req, cb);
    }
    async DescribeRegions(req, cb) {
        return this.request("DescribeRegions", req, cb);
    }
    async DescribeSecurityPolicies(req, cb) {
        return this.request("DescribeSecurityPolicies", req, cb);
    }
    async BatchDeleteImagePersonal(req, cb) {
        return this.request("BatchDeleteImagePersonal", req, cb);
    }
    async CreateReplicationInstance(req, cb) {
        return this.request("CreateReplicationInstance", req, cb);
    }
    async CreateRepository(req, cb) {
        return this.request("CreateRepository", req, cb);
    }
    async ManageExternalEndpoint(req, cb) {
        return this.request("ManageExternalEndpoint", req, cb);
    }
    async ModifySecurityPolicy(req, cb) {
        return this.request("ModifySecurityPolicy", req, cb);
    }
    async DescribeTagRetentionExecutionTask(req, cb) {
        return this.request("DescribeTagRetentionExecutionTask", req, cb);
    }
    async DeleteMultipleSecurityPolicy(req, cb) {
        return this.request("DeleteMultipleSecurityPolicy", req, cb);
    }
    async DescribeImageLifecyclePersonal(req, cb) {
        return this.request("DescribeImageLifecyclePersonal", req, cb);
    }
    async DescribeRepositoryFilterPersonal(req, cb) {
        return this.request("DescribeRepositoryFilterPersonal", req, cb);
    }
    async CreateUserPersonal(req, cb) {
        return this.request("CreateUserPersonal", req, cb);
    }
    async DescribeInstanceCustomizedDomain(req, cb) {
        return this.request("DescribeInstanceCustomizedDomain", req, cb);
    }
    async ModifyNamespace(req, cb) {
        return this.request("ModifyNamespace", req, cb);
    }
    async DescribeImmutableTagRules(req, cb) {
        return this.request("DescribeImmutableTagRules", req, cb);
    }
    async CreateSecurityPolicy(req, cb) {
        return this.request("CreateSecurityPolicy", req, cb);
    }
    async CreateSignaturePolicy(req, cb) {
        return this.request("CreateSignaturePolicy", req, cb);
    }
    async DeleteInstance(req, cb) {
        return this.request("DeleteInstance", req, cb);
    }
    async ModifyRepository(req, cb) {
        return this.request("ModifyRepository", req, cb);
    }
    async DescribeChartDownloadInfo(req, cb) {
        return this.request("DescribeChartDownloadInfo", req, cb);
    }
    async ModifyServiceAccount(req, cb) {
        return this.request("ModifyServiceAccount", req, cb);
    }
    async DescribeUserQuotaPersonal(req, cb) {
        return this.request("DescribeUserQuotaPersonal", req, cb);
    }
    async CreateImmutableTagRules(req, cb) {
        return this.request("CreateImmutableTagRules", req, cb);
    }
    async DeleteRepositoryTags(req, cb) {
        return this.request("DeleteRepositoryTags", req, cb);
    }
    async DescribeInstanceToken(req, cb) {
        return this.request("DescribeInstanceToken", req, cb);
    }
    async DownloadHelmChart(req, cb) {
        return this.request("DownloadHelmChart", req, cb);
    }
    async ModifyImmutableTagRules(req, cb) {
        return this.request("ModifyImmutableTagRules", req, cb);
    }
    async DeleteApplicationTriggerPersonal(req, cb) {
        return this.request("DeleteApplicationTriggerPersonal", req, cb);
    }
    async CreateInternalEndpointDns(req, cb) {
        return this.request("CreateInternalEndpointDns", req, cb);
    }
    async ModifyInstanceToken(req, cb) {
        return this.request("ModifyInstanceToken", req, cb);
    }
    async CreateWebhookTrigger(req, cb) {
        return this.request("CreateWebhookTrigger", req, cb);
    }
    async CreateApplicationTriggerPersonal(req, cb) {
        return this.request("CreateApplicationTriggerPersonal", req, cb);
    }
    async ValidateRepositoryExistPersonal(req, cb) {
        return this.request("ValidateRepositoryExistPersonal", req, cb);
    }
    async ModifyApplicationTriggerPersonal(req, cb) {
        return this.request("ModifyApplicationTriggerPersonal", req, cb);
    }
    async DescribeNamespaces(req, cb) {
        return this.request("DescribeNamespaces", req, cb);
    }
    async DescribeServiceAccounts(req, cb) {
        return this.request("DescribeServiceAccounts", req, cb);
    }
    async CreateSignature(req, cb) {
        return this.request("CreateSignature", req, cb);
    }
    async DeleteInternalEndpointDns(req, cb) {
        return this.request("DeleteInternalEndpointDns", req, cb);
    }
    async ModifyTagRetentionRule(req, cb) {
        return this.request("ModifyTagRetentionRule", req, cb);
    }
    async DescribeImageFilterPersonal(req, cb) {
        return this.request("DescribeImageFilterPersonal", req, cb);
    }
    async DeleteRepository(req, cb) {
        return this.request("DeleteRepository", req, cb);
    }
    async DescribeRepositoryOwnerPersonal(req, cb) {
        return this.request("DescribeRepositoryOwnerPersonal", req, cb);
    }
    async DescribeImages(req, cb) {
        return this.request("DescribeImages", req, cb);
    }
    async DeleteTagRetentionRule(req, cb) {
        return this.request("DeleteTagRetentionRule", req, cb);
    }
    async DeleteNamespace(req, cb) {
        return this.request("DeleteNamespace", req, cb);
    }
    async DeleteSignaturePolicy(req, cb) {
        return this.request("DeleteSignaturePolicy", req, cb);
    }
    async CreateServiceAccount(req, cb) {
        return this.request("CreateServiceAccount", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async DeleteRepositoryPersonal(req, cb) {
        return this.request("DeleteRepositoryPersonal", req, cb);
    }
    async DescribeApplicationTriggerLogPersonal(req, cb) {
        return this.request("DescribeApplicationTriggerLogPersonal", req, cb);
    }
    async DeleteInstanceToken(req, cb) {
        return this.request("DeleteInstanceToken", req, cb);
    }
    async ModifyUserPasswordPersonal(req, cb) {
        return this.request("ModifyUserPasswordPersonal", req, cb);
    }
    async DescribeWebhookTrigger(req, cb) {
        return this.request("DescribeWebhookTrigger", req, cb);
    }
    async ManageInternalEndpoint(req, cb) {
        return this.request("ManageInternalEndpoint", req, cb);
    }
    async DuplicateImage(req, cb) {
        return this.request("DuplicateImage", req, cb);
    }
    async DescribeInstanceStatus(req, cb) {
        return this.request("DescribeInstanceStatus", req, cb);
    }
    async CreateRepositoryPersonal(req, cb) {
        return this.request("CreateRepositoryPersonal", req, cb);
    }
    async DeleteImage(req, cb) {
        return this.request("DeleteImage", req, cb);
    }
    async ValidateNamespaceExistPersonal(req, cb) {
        return this.request("ValidateNamespaceExistPersonal", req, cb);
    }
    async CreateNamespacePersonal(req, cb) {
        return this.request("CreateNamespacePersonal", req, cb);
    }
    async DescribeExternalEndpointStatus(req, cb) {
        return this.request("DescribeExternalEndpointStatus", req, cb);
    }
    async DescribeInternalEndpoints(req, cb) {
        return this.request("DescribeInternalEndpoints", req, cb);
    }
    async DescribeInternalEndpointDnsStatus(req, cb) {
        return this.request("DescribeInternalEndpointDnsStatus", req, cb);
    }
    async CreateInstanceToken(req, cb) {
        return this.request("CreateInstanceToken", req, cb);
    }
}
