{"version": 3, "file": "alpha-slider.js", "sources": ["../../../../../../../packages/components/color-picker/src/props/alpha-slider.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Color from '../utils/color'\n\nexport const alphaSliderProps = buildProps({\n  color: {\n    type: definePropType<Color>(Object),\n    required: true,\n  },\n  vertical: <PERSON>olean,\n} as const)\n\nexport type AlphaSliderProps = ExtractPropTypes<typeof alphaSliderProps>\nexport type AlphaSliderPropsPublic = __ExtractPublicPropTypes<\n  typeof alphaSliderProps\n>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,gBAAgB,GAAGA,kBAAU,CAAC;AAC3C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC;;;;"}