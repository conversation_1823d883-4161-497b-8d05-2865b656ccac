import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/screens/generator/novel_settings_screen.dart';
import 'package:novel_app/widgets/background_service_widget.dart';
import 'package:novel_app/models/novel.dart';

class GeneratorScreen extends StatelessWidget {
  const GeneratorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<NovelController>();
    final titleController =
        TextEditingController(text: controller.currentNovelTitle);
    final outlineEditController = TextEditingController();

    ever(controller.generationStage, (GenerationStage stage) {
      if (stage == GenerationStage.outlineReady) {
        final outline = controller.currentOutline.value;
        if (outline != null && outline.chapters.isNotEmpty) {
          final buffer = StringBuffer();
          buffer.writeln('小说标题：${outline.novelTitle}');
          for (final chOutline in outline.chapters) {
            buffer.writeln(
                '\n第${chOutline.chapterNumber}章：${chOutline.chapterTitle}');
            buffer.writeln(chOutline.contentOutline);
          }
          outlineEditController.text = buffer.toString();
        } else {
          outlineEditController.text = "// 无法显示或解析大纲，请在此手动编辑或重新生成...";
        }
      } else if (stage == GenerationStage.idle) {
        outlineEditController.clear();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('创作新故事'),
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Get.to(() => const NovelSettingsScreen()),
            tooltip: '高级设置',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '基本设置',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: titleController,
                      decoration: const InputDecoration(
                        labelText: '故事标题',
                        hintText: '请输入故事标题',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) => controller.setNovelTitle(value),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            icon: const Icon(Icons.settings_applications),
                            label: const Text('高级设置'),
                            onPressed: () =>
                                Get.to(() => const NovelSettingsScreen()),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.isGenerating.value &&
                  (controller.generationStage.value ==
                          GenerationStage.generatingOutline ||
                      controller.generationStage.value ==
                          GenerationStage.generatingDetailedOutline ||
                      controller.generationStage.value ==
                          GenerationStage.generatingChapters ||
                      controller.generationStage.value ==
                          GenerationStage.generatingShortNovelWorldBuilding ||
                      controller.generationStage.value ==
                          GenerationStage.generatingShortNovelOutline ||
                      controller.generationStage.value ==
                          GenerationStage.generatingShortNovelContent)) {
                return _buildGenerationProgressWidget(controller, context);
              } else if (controller.generationStage.value ==
                  GenerationStage.idle) {
                return _buildGenerationControlWidget(
                    controller, titleController);
              } else {
                return Container();
              }
            }),
            Obx(() => controller.generationStage.value ==
                    GenerationStage.outlineReady
                ? _buildOutlineEditorWidget(controller, outlineEditController)
                : Container()),
            Obx(() => controller.generationStage.value ==
                    GenerationStage.detailedOutlineReady
                ? _buildDetailedOutlineViewerWidget(controller, context)
                : Container()),
            // 短篇小说世界观预览
            Obx(() => controller.generationStage.value ==
                    GenerationStage.shortNovelWorldBuildingReady
                ? _buildShortNovelWorldBuildingViewerWidget(controller, context)
                : Container()),
            // 短篇小说大纲预览
            Obx(() => controller.generationStage.value ==
                    GenerationStage.shortNovelOutlineReady
                ? _buildShortNovelOutlineViewerWidget(controller, context)
                : Container()),
            const SizedBox(height: 16),
            _buildGenerationOutputWidget(controller),
            const SizedBox(height: 16),
            const BackgroundServiceWidget(),
            const SizedBox(height: 16),
            _buildTipsWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildOutlineEditorWidget(
      NovelController controller, TextEditingController outlineController) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '生成的大纲',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              '请检查并修改下方生成的大纲，确认无误后点击"生成细纲"。',
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: outlineController,
              maxLines: 15,
              decoration: InputDecoration(
                hintText: '编辑大纲内容...',
                border: const OutlineInputBorder(),
                filled: true,
                fillColor: Colors.grey[50],
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.checklist_rtl),
                    label: const Text('生成细纲'),
                    onPressed: () {
                      final editedOutlineText = outlineController.text;
                      controller
                          .generateDetailedOutlineFromEdited(editedOutlineText);
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: controller.generateOutlineWrapper,
                  child: const Text('重新生成大纲'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerationProgressWidget(
      NovelController controller, BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            LinearProgressIndicator(
              value: controller.generationProgress.value,
              backgroundColor: Colors.grey[200],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              controller.generationStatus.value,
              style: const TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 8),
            // 显示短篇小说生成阶段
            Obx(() {
              final stage = controller.generationStage.value;
              if (controller.novelType.value == NovelType.shortNovel) {
                return _buildShortNovelStageIndicator(stage);
              }
              return Container();
            }),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Obx(() => controller.isPaused.value
                    ? ElevatedButton.icon(
                        icon: const Icon(Icons.play_arrow),
                        label: const Text('继续生成'),
                        onPressed: controller.checkAndContinueGeneration,
                      )
                    : ElevatedButton.icon(
                        icon: const Icon(Icons.pause),
                        label: const Text('暂停生成'),
                        onPressed: controller.stopGeneration,
                      )),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  icon: const Icon(Icons.stop),
                  label: const Text('停止生成'),
                  onPressed: controller.stopGeneration,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerationControlWidget(
      NovelController controller, TextEditingController titleController) {
    return Obx(() {
      if (controller.generationStage.value == GenerationStage.idle) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                const Text(
                  '开始创作',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Obx(() {
                  if (controller.novelType.value == NovelType.shortNovel) {
                    return Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            icon: const Icon(Icons.short_text),
                            label: Text('生成短篇小说 (${controller.shortNovelWordCount.value.displayName})'),
                            onPressed: controller.generateShortNovel,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  } else {
                    return Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            icon: const Icon(Icons.auto_stories),
                            label: const Text('生成大纲'),
                            onPressed: controller.generateOutlineWrapper,
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                }),
              ],
            ),
          ),
        );
      } else {
        return Container();
      }
    });
  }

  Widget _buildGenerationOutputWidget(NovelController controller) {
    final apiConfigController = Get.find<ApiConfigController>();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '生成输出',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                // 嵌入模型状态显示
                Obx(() {
                  final isEnabled =
                      apiConfigController.embeddingModel.value.enabled;
                  return Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: isEnabled
                          ? Colors.green.withAlpha(50)
                          : Colors.red.withAlpha(50),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: isEnabled ? Colors.green : Colors.red,
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isEnabled ? Icons.check_circle : Icons.cancel,
                          size: 16,
                          color: isEnabled ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          isEnabled ? '嵌入模型已启用' : '嵌入模型未启用',
                          style: TextStyle(
                            fontSize: 12,
                            color: isEnabled ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              height: 300,
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              padding: const EdgeInsets.all(16),
              child: Obx(() => SingleChildScrollView(
                    child: Text(
                      controller.realtimeOutput.value,
                      style: const TextStyle(
                        fontFamily: 'monospace',
                        height: 1.5,
                      ),
                    ),
                  )),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTipsWidget() {
    return const Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '创作提示',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '1. 在高级设置中可以设置更多参数，如角色、背景、风格等\n'
              '2. 生成过程中可以随时暂停和继续\n'
              '3. 生成完成后可以在小说库中查看和编辑\n'
              '4. 可以使用角色卡片和角色类型来丰富故事角色',
              style: TextStyle(
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedOutlineViewerWidget(
      NovelController controller, BuildContext context) {
    final detailedOutline = controller.currentOutline.value;

    if (detailedOutline == null || detailedOutline.chapters.isEmpty) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Text(
            '错误：无法显示细纲。请尝试重新生成。',
            style: TextStyle(color: Colors.red[700]),
          ),
        ),
      );
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '生成的细纲',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              '请检查下方生成的章节细纲（情节梗概），确认无误后点击"生成小说章节"。',
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            Container(
              constraints: const BoxConstraints(maxHeight: 400),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: detailedOutline.chapters.length,
                itemBuilder: (context, index) {
                  final chapter = detailedOutline.chapters[index];
                  return ExpansionTile(
                    title: Text(
                        '第${chapter.chapterNumber}章: ${chapter.chapterTitle}',
                        style: const TextStyle(fontWeight: FontWeight.w600)),
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16.0, vertical: 8.0),
                        child: Text(chapter.contentOutline,
                            textAlign: TextAlign.left),
                      ),
                    ],
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.auto_fix_high),
                    label: const Text('生成小说章节'),
                    onPressed: () {
                      controller.generationStage.value =
                          GenerationStage.generatingChapters;
                      controller.startGeneration();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                TextButton(
                  onPressed: () {
                    controller.generationStage.value =
                        GenerationStage.outlineReady;
                  },
                  child: const Text('返回修改大纲'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 短篇小说生成阶段指示器
  Widget _buildShortNovelStageIndicator(GenerationStage stage) {
    final stages = [
      {'stage': GenerationStage.generatingShortNovelWorldBuilding, 'title': '世界观构建', 'icon': Icons.public},
      {'stage': GenerationStage.generatingShortNovelOutline, 'title': '大纲生成', 'icon': Icons.list_alt},
      {'stage': GenerationStage.generatingShortNovelContent, 'title': '内容生成', 'icon': Icons.edit},
    ];

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: stages.map((stageInfo) {
          final isActive = stage == stageInfo['stage'];
          final isCompleted = _isStageCompleted(stage, stageInfo['stage'] as GenerationStage);

          return Column(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isCompleted
                      ? Colors.green
                      : isActive
                          ? Colors.blue
                          : Colors.grey[300],
                ),
                child: Icon(
                  isCompleted
                      ? Icons.check
                      : stageInfo['icon'] as IconData,
                  color: isCompleted || isActive ? Colors.white : Colors.grey[600],
                  size: 20,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                stageInfo['title'] as String,
                style: TextStyle(
                  fontSize: 12,
                  color: isCompleted || isActive ? Colors.black87 : Colors.grey[600],
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  // 判断阶段是否已完成
  bool _isStageCompleted(GenerationStage currentStage, GenerationStage targetStage) {
    final stageOrder = [
      GenerationStage.generatingShortNovelWorldBuilding,
      GenerationStage.generatingShortNovelOutline,
      GenerationStage.generatingShortNovelContent,
    ];

    final currentIndex = stageOrder.indexOf(currentStage);
    final targetIndex = stageOrder.indexOf(targetStage);

    return currentIndex > targetIndex;
  }

  // 构建短篇小说世界观预览界面
  Widget _buildShortNovelWorldBuildingViewerWidget(
      NovelController controller, BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.public, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  '世界观构建完成',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Text(
                '世界观和角色信息已生成完成，请查看并确认后继续生成详细大纲。',
                style: TextStyle(color: Colors.blue),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.visibility),
                    label: const Text('查看世界观'),
                    onPressed: () {
                      controller.showShortNovelWorldBuildingPreview();
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.arrow_forward),
                    label: const Text('生成大纲'),
                    onPressed: () {
                      controller.continueShortNovelOutlineGeneration();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建短篇小说大纲预览界面
  Widget _buildShortNovelOutlineViewerWidget(
      NovelController controller, BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.list_alt, color: Colors.orange),
                const SizedBox(width: 8),
                const Text(
                  '详细大纲生成完成',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Text(
                '详细大纲已生成完成，请查看并确认后开始生成小说内容。',
                style: TextStyle(color: Colors.orange),
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    icon: const Icon(Icons.visibility),
                    label: const Text('查看大纲'),
                    onPressed: () {
                      controller.showShortNovelOutlinePreview();
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton.icon(
                    icon: const Icon(Icons.create),
                    label: const Text('生成小说'),
                    onPressed: () {
                      controller.continueShortNovelContentGeneration();
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      backgroundColor: Colors.purple,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
