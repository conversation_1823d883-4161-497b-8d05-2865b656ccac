<template>
  <div class="novel-detail-container">
    <!-- 返回按钮 -->
    <div class="back-header">
      <el-button @click="$router.back()" type="text">
        <el-icon><ArrowLeft /></el-icon>
        返回小说列表
      </el-button>
    </div>

    <!-- 小说基本信息 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">小说基本信息</h3>
        <div class="header-actions">
          <el-button type="warning" @click="handleAnalyze">
            <el-icon><TrendCharts /></el-icon>
            质量分析
          </el-button>
          <el-button type="primary" @click="handleEdit">
            <el-icon><Edit /></el-icon>
            编辑小说
          </el-button>
        </div>
      </div>

      <div class="novel-info">
        <div class="info-main">
          <h1 class="novel-title">{{ novelInfo.title }}</h1>
          <div class="novel-meta">
            <el-tag :type="getGenreTagType(novelInfo.genre)" size="large">
              {{ novelInfo.genre }}
            </el-tag>
            <el-tag :type="getStatusTagType(novelInfo.status)" size="large">
              {{ getStatusText(novelInfo.status) }}
            </el-tag>
            <span class="author-info">
              作者：
              <el-button type="text" @click="viewAuthor">{{ novelInfo.author }}</el-button>
            </span>
          </div>
          <div class="novel-description">
            {{ novelInfo.description || '暂无简介' }}
          </div>
        </div>

        <div class="info-stats">
          <div class="stat-item">
            <div class="stat-value">{{ formatWordCount(novelInfo.wordCount) }}</div>
            <div class="stat-label">总字数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">{{ novelInfo.chapterCount || 0 }}</div>
            <div class="stat-label">章节数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">
              <el-rate v-model="novelInfo.qualityScore" disabled show-score />
            </div>
            <div class="stat-label">质量评分</div>
          </div>
        </div>
      </div>

      <el-row :gutter="20" class="detail-grid">
        <el-col :xs="24" :sm="12" :md="8">
          <div class="detail-item">
            <div class="detail-label">创建时间</div>
            <div class="detail-value">{{ formatDate(novelInfo.createdAt) }}</div>
          </div>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <div class="detail-item">
            <div class="detail-label">最后更新</div>
            <div class="detail-value">{{ formatDate(novelInfo.updatedAt) }}</div>
          </div>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <div class="detail-item">
            <div class="detail-label">平均章节字数</div>
            <div class="detail-value">{{ Math.round(novelInfo.wordCount / (novelInfo.chapterCount || 1)) }}</div>
          </div>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <div class="detail-item">
            <div class="detail-label">更新频率</div>
            <div class="detail-value">{{ getUpdateFrequency() }}</div>
          </div>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <div class="detail-item">
            <div class="detail-label">创作天数</div>
            <div class="detail-value">{{ getCreationDays() }}天</div>
          </div>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8">
          <div class="detail-item">
            <div class="detail-label">小说ID</div>
            <div class="detail-value">{{ novelInfo.id }}</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 章节列表 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">章节列表</h3>
        <div class="chapter-stats">
          共 {{ chapterList.length }} 章节
        </div>
      </div>

      <el-table :data="chapterList" style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="title" label="章节标题" show-overflow-tooltip />
        <el-table-column label="字数" width="100">
          <template #default="{ row }">
            {{ row.wordCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="viewChapter(row)">
              查看
            </el-button>
            <el-button type="text" size="small" @click="editChapter(row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 角色列表 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">相关角色</h3>
        <div class="character-stats">
          共 {{ characterList.length }} 个角色
        </div>
      </div>

      <el-row :gutter="16">
        <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="character in characterList" :key="character.id">
          <div class="character-card">
            <div class="character-avatar">
              <el-avatar :size="60" :src="character.avatar">
                <el-icon size="30"><User /></el-icon>
              </el-avatar>
            </div>
            <div class="character-info">
              <div class="character-name">{{ character.name }}</div>
              <div class="character-role">{{ character.role || '角色' }}</div>
              <div class="character-desc">{{ character.description || '暂无描述' }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 创作统计图表 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">创作统计</h3>
      </div>

      <el-row :gutter="20">
        <el-col :xs="24" :lg="12">
          <div class="chart-container">
            <h4>字数增长趋势</h4>
            <div class="chart-placeholder">
              <el-icon size="48" color="#d9d9d9"><TrendCharts /></el-icon>
              <p>图表功能开发中...</p>
            </div>
          </div>
        </el-col>

        <el-col :xs="24" :lg="12">
          <div class="chart-container">
            <h4>章节更新频率</h4>
            <div class="chart-placeholder">
              <el-icon size="48" color="#d9d9d9"><BarChart /></el-icon>
              <p>图表功能开发中...</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()

const novelId = route.params.id as string

// 小说信息
const novelInfo = ref({
  id: '',
  title: '',
  author: '',
  userId: '',
  genre: '',
  status: 'ongoing',
  description: '',
  wordCount: 0,
  chapterCount: 0,
  qualityScore: 0,
  createdAt: '',
  updatedAt: ''
})

// 章节列表
const chapterList = ref([])

// 角色列表
const characterList = ref([])

// 获取类型标签类型
const getGenreTagType = (genre: string) => {
  const typeMap = {
    '科幻': 'primary',
    '历史': 'success',
    '都市': 'warning',
    '玄幻': 'danger',
    '其他': 'info'
  }
  return typeMap[genre] || 'info'
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap = {
    'ongoing': 'success',
    'completed': 'primary',
    'paused': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    'ongoing': '连载中',
    'completed': '已完结',
    'paused': '暂停'
  }
  return textMap[status] || '未知'
}

// 格式化字数
const formatWordCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 获取更新频率
const getUpdateFrequency = () => {
  const days = getCreationDays()
  const chapters = novelInfo.value.chapterCount || 1
  const frequency = chapters / days
  if (frequency >= 1) {
    return `${frequency.toFixed(1)}章/天`
  } else {
    return `${(1/frequency).toFixed(1)}天/章`
  }
}

// 获取创作天数
const getCreationDays = () => {
  const created = dayjs(novelInfo.value.createdAt)
  const updated = dayjs(novelInfo.value.updatedAt)
  return updated.diff(created, 'day') + 1
}

// 查看作者
const viewAuthor = () => {
  router.push(`/users/${novelInfo.value.userId}`)
}

// 处理分析
const handleAnalyze = () => {
  ElMessage.info('质量分析功能开发中...')
}

// 处理编辑
const handleEdit = () => {
  ElMessage.info('编辑功能开发中...')
}

// 查看章节
const viewChapter = (chapter: any) => {
  ElMessage.info(`查看章节: ${chapter.title}`)
}

// 编辑章节
const editChapter = (chapter: any) => {
  ElMessage.info(`编辑章节: ${chapter.title}`)
}

// 加载小说详情
const loadNovelDetail = async () => {
  try {
    const response = await fetch(`/api/novels/${novelId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        novelInfo.value = result.data

        // 生成模拟章节数据
        const chapterCount = result.data.chapterCount || 0
        const avgWordsPerChapter = chapterCount > 0 ? Math.floor(result.data.wordCount / chapterCount) : 2000

        const chapters = []
        for (let i = 1; i <= Math.min(chapterCount, 10); i++) {
          chapters.push({
            id: i.toString(),
            title: `第${i}章：${generateChapterTitle()}`,
            wordCount: avgWordsPerChapter + Math.floor(Math.random() * 500) - 250,
            createdAt: new Date(Date.now() - (chapterCount - i) * 24 * 60 * 60 * 1000).toISOString()
          })
        }
        chapterList.value = chapters

        // 生成模拟角色数据
        const characterNames = ['主角', '女主角', '反派', '导师', '朋友', '敌人']
        const characters = []
        const characterCount = Math.min(Math.floor(Math.random() * 6) + 2, characterNames.length)

        for (let i = 0; i < characterCount; i++) {
          characters.push({
            id: (i + 1).toString(),
            name: `角色${i + 1}`,
            role: characterNames[i],
            description: `${characterNames[i]}的详细描述`,
            avatar: ''
          })
        }
        characterList.value = characters

      } else {
        ElMessage.error(result.message || '小说不存在')
      }
    } else if (response.status === 404) {
      ElMessage.error('小说不存在')
    } else {
      ElMessage.error('网络请求失败')
    }
  } catch (error) {
    console.error('加载小说详情失败:', error)
    ElMessage.error('加载小说详情失败')
  }
}

// 生成章节标题
const generateChapterTitle = () => {
  const titles = [
    '初遇', '觉醒', '挑战', '成长', '危机', '转折', '突破', '对决', '真相', '终章',
    '序幕', '开端', '冲突', '高潮', '结局', '新生', '探索', '发现', '抉择', '命运'
  ]
  return titles[Math.floor(Math.random() * titles.length)]
}

onMounted(() => {
  loadNovelDetail()
})
</script>

<style scoped>
.novel-detail-container {
  padding: 0;
}

.back-header {
  margin-bottom: 20px;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.novel-info {
  display: flex;
  gap: 32px;
  margin-bottom: 24px;
}

.info-main {
  flex: 1;
}

.novel-title {
  font-size: 28px;
  font-weight: bold;
  color: #262626;
  margin: 0 0 16px 0;
}

.novel-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.author-info {
  color: #8c8c8c;
  font-size: 14px;
}

.novel-description {
  color: #595959;
  line-height: 1.6;
  font-size: 14px;
}

.info-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-width: 200px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.detail-grid {
  margin-top: 20px;
}

.detail-item {
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.detail-value {
  font-size: 16px;
  color: #262626;
  font-weight: 500;
}

.chapter-stats,
.character-stats {
  color: #8c8c8c;
  font-size: 14px;
}

.character-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  margin-bottom: 16px;
}

.character-avatar {
  margin-bottom: 12px;
}

.character-name {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.character-role {
  font-size: 12px;
  color: #1890ff;
  margin-bottom: 8px;
}

.character-desc {
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

.chart-container {
  text-align: center;
}

.chart-container h4 {
  font-size: 16px;
  color: #262626;
  margin-bottom: 16px;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 8px;
  color: #8c8c8c;
}

.chart-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

/* 响应式 */
@media (max-width: 768px) {
  .novel-info {
    flex-direction: column;
    gap: 20px;
  }

  .info-stats {
    flex-direction: row;
    min-width: auto;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }
}
</style>
