{"name": "@vueuse/core", "version": "9.13.0", "description": "Collection of essential Vue Composition Utilities", "author": "<PERSON> <https://github.com/antfu>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse#readme", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/core"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "keywords": ["vue", "vue-use", "utils"], "sideEffects": false, "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}, "./*": "./*", "./metadata": {"types": "./metadata.d.ts", "require": "./metadata.cjs", "import": "./metadata.mjs"}}, "main": "./index.cjs", "module": "./index.mjs", "unpkg": "./index.iife.min.js", "jsdelivr": "./index.iife.min.js", "types": "./index.d.ts", "dependencies": {"@types/web-bluetooth": "^0.0.16", "@vueuse/metadata": "9.13.0", "@vueuse/shared": "9.13.0", "vue-demi": "*"}}