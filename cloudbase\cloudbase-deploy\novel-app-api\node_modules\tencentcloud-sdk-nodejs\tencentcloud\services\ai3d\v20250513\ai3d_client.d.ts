import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { QueryHunyuanTo3DJobRequest, SubmitHunyuanTo3DJobResponse, QueryHunyuanTo3DJobResponse, SubmitHunyuanTo3DJobRequest } from "./ai3d_models";
/**
 * ai3d client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 混元生3D接口，基于混元大模型，根据输入的文本描述/图片智能生成3D。
默认提供1个并发，代表最多能同时处理1个已提交的任务，上一个任务处理完毕后，才能开始处理下一个任务。
     */
    SubmitHunyuanTo3DJob(req: SubmitHunyuanTo3DJobRequest, cb?: (error: string, rep: SubmitHunyuanTo3DJobResponse) => void): Promise<SubmitHunyuanTo3DJobResponse>;
    /**
     * 混元生3D接口，基于混元大模型，根据输入的文本描述/图片智能生成3D。
默认提供1个并发，代表最多能同时处理1个已提交的任务，上一个任务处理完毕后，才能开始处理下一个任务。
     */
    QueryHunyuanTo3DJob(req: QueryHunyuanTo3DJobRequest, cb?: (error: string, rep: QueryHunyuanTo3DJobResponse) => void): Promise<QueryHunyuanTo3DJobResponse>;
}
