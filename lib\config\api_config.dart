class ApiConfig {
  // 环境配置 - 使用生产环境
  static const bool isProduction =
      bool.fromEnvironment('PRODUCTION', defaultValue: true);

  // 生产环境配置 - 使用自定义域名（已解决路径映射问题）
  // CloudBase默认域名备用：https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/novel-app-api
  static const String productionBaseUrl = 'https://api.dznovel.top/api';
  static const String productionWsUrl = 'wss://api.dznovel.top/ws';

  // 开发环境配置
  static const String developmentBaseUrl = 'http://*************:3001/api';
  static const String developmentWsUrl = 'ws://*************:3001/ws';

  // 动态选择API地址
  static String get baseUrl =>
      isProduction ? productionBaseUrl : developmentBaseUrl;
  static String get wsUrl => isProduction ? productionWsUrl : developmentWsUrl;

  // 原有的小说生成服务器URL
  static const String novelApiUrl = 'https://www.dznovel.top'; // 主域名（更可靠）
  static const String backupUrl = 'http://*************:8000'; // 备用IP地址
  // static const String novelApiUrl = 'http://localhost:8000'; // 开发环境

  // 获取服务器连接超时时间（秒）
  static const int connectionTimeout = 120; // 增加到2分钟
  static const int receiveTimeout = 120; // 增加到2分钟
  static const int sendTimeout = 120; // 增加到2分钟

  // API端点配置 - 使用查询参数解决CloudBase路径映射问题
  static const Map<String, String> endpoints = {
    // 健康检查
    'health': '/health?_api_path=health',

    // 认证相关
    'sendCode': '/auth/send-code?_api_path=auth/send-code',
    'verifyCode': '/auth/verify-code?_api_path=auth/verify-code',
    'register': '/auth/register?_api_path=auth/register',
    'login': '/auth/login?_api_path=auth/login',
    'logout': '/auth/logout?_api_path=auth/logout',
    'refreshToken': '/auth/refresh?_api_path=auth/refresh',

    // 用户相关
    'userProfile': '/user/profile?_api_path=user/profile',
    'userPassword': '/user/password?_api_path=user/password',
    'userAvatar': '/user/avatar?_api_path=user/avatar',
    'userSettings': '/user/settings?_api_path=user/settings',
    'deleteAccount': '/user/account?_api_path=user/account',

    // 数据同步 - 统一使用CloudBase数据库API
    'syncUpload': '/sync/upload?_api_path=sync/upload',
    'syncDownload': '/sync/download?_api_path=sync/download',
    'syncStats': '/sync/stats?_api_path=sync/stats',
    'syncRecords': '/sync/records?_api_path=sync/records',

    // 支付相关
    'packages': '/packages?_api_path=packages',
    'createOrder': '/orders/create?_api_path=orders/create',
    'myOrders': '/orders/my?_api_path=orders/my',
    'cancelOrder': '/orders/{id}/cancel?_api_path=orders/{id}/cancel',
    'wechatPay': '/payment/wechat?_api_path=payment/wechat',
    'memberCodePay': '/payment/member-code?_api_path=payment/member-code',
    'paymentStatus': '/payment/status/{id}?_api_path=payment/status/{id}',

    // 会员码相关
    'validateMemberCode':
        '/member-code/validate?_api_path=member-code/validate',
  };

  /// 获取API端点URL
  static String getEndpoint(String key, [Map<String, String>? params]) {
    String endpoint = endpoints[key] ?? '';
    if (params != null) {
      params.forEach((key, value) {
        endpoint = endpoint.replaceAll('{$key}', value);
      });
    }
    return '$baseUrl$endpoint';
  }
}
