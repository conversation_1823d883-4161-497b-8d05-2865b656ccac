{"name": "@cloudbase/signature-nodejs", "version": "2.0.0", "description": "cloudbase api signature for node.js", "main": "lib/index.js", "scripts": {"test": "npx jest --coverage --verbose", "build": "tsc"}, "repository": {"type": "git", "url": "*******************:QBase/cloudbase-signature-nodejs.git"}, "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lint-staged": {"*.ts": ["eslint --fix src/**/*", "git add"]}, "dependencies": {"@types/clone": "^2.1.0", "clone": "^2.1.2", "is-stream": "^2.0.0", "url": "^0.11.0"}, "devDependencies": {"@tencent/eslint-config-tencent": "^0.13.3", "@types/is-stream": "^2.0.0", "@types/jest": "^24.0.23", "@types/node": "^15.12.1", "@typescript-eslint/eslint-plugin": "^4.26.0", "@typescript-eslint/parser": "^4.26.0", "eslint": "^7.28.0", "eslint-plugin-typescript": "^0.14.0", "husky": "^3.1.0", "jest": "^24.9.0", "lint-staged": "^9.5.0", "ts-jest": "^24.2.0", "typescript": "^4.3.2", "typescript-eslint-parser": "^22.0.0"}}