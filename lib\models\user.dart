import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user.g.dart';

/// 用户模型
@HiveType(typeId: 20)
@JsonSerializable()
class User extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  String username;

  @HiveField(2)
  String phoneNumber;

  @HiveField(3)
  String? email;

  @HiveField(4)
  String? avatar;

  @HiveField(5)
  bool isMember;

  @HiveField(6)
  DateTime? memberExpireTime;

  @HiveField(7)
  MembershipType membershipType;

  @HiveField(8)
  final DateTime createdAt;

  @HiveField(9)
  DateTime updatedAt;

  @HiveField(10)
  bool isDataSyncEnabled;

  @HiveField(11)
  String? memberCode; // 使用的会员码

  @HiveField(12)
  bool isPermanentMember; // 是否为永久会员

  @HiveField(13)
  UserSettings settings;

  User({
    required this.id,
    required this.username,
    required this.phoneNumber,
    this.email,
    this.avatar,
    this.isMember = false,
    this.memberExpireTime,
    this.membershipType = MembershipType.none,
    required this.createdAt,
    required this.updatedAt,
    this.isDataSyncEnabled = true,
    this.memberCode,
    this.isPermanentMember = false,
    required this.settings,
  });

  /// 检查会员是否有效
  bool get isValidMember {
    if (isPermanentMember) return true;
    if (!isMember) return false;
    if (memberExpireTime == null) return false;
    return memberExpireTime!.isAfter(DateTime.now());
  }

  /// 获取会员剩余天数
  int get memberRemainingDays {
    if (isPermanentMember) return -1; // -1表示永久
    if (!isMember || memberExpireTime == null) return 0;
    final remaining = memberExpireTime!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);
}

/// 会员类型枚举
@HiveType(typeId: 21)
enum MembershipType {
  @HiveField(0)
  none, // 非会员
  @HiveField(1)
  monthly, // 月会员
  @HiveField(2)
  permanent, // 永久会员
}

/// 用户设置
@HiveType(typeId: 22)
@JsonSerializable()
class UserSettings extends HiveObject {
  @HiveField(0)
  bool enableBiometric; // 生物识别登录

  @HiveField(1)
  bool autoSync; // 自动同步

  @HiveField(2)
  bool enableNotification; // 推送通知

  @HiveField(3)
  String theme; // 主题设置

  @HiveField(4)
  String language; // 语言设置

  UserSettings({
    this.enableBiometric = false,
    this.autoSync = true,
    this.enableNotification = true,
    this.theme = 'system',
    this.language = 'zh-CN',
  });

  factory UserSettings.fromJson(Map<String, dynamic> json) => _$UserSettingsFromJson(json);
  Map<String, dynamic> toJson() => _$UserSettingsToJson(this);
}

/// 登录响应模型
@JsonSerializable()
class LoginResponse {
  final String token;
  final String refreshToken;
  final User user;
  final int expiresIn; // token过期时间（秒）

  LoginResponse({
    required this.token,
    required this.refreshToken,
    required this.user,
    required this.expiresIn,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) => _$LoginResponseFromJson(json);
  Map<String, dynamic> toJson() => _$LoginResponseToJson(this);
}

/// 注册请求模型
@JsonSerializable()
class RegisterRequest {
  final String username;
  final String password;
  final String phoneNumber;
  final String verificationCode;
  final String? memberCode; // 可选的会员码

  RegisterRequest({
    required this.username,
    required this.password,
    required this.phoneNumber,
    required this.verificationCode,
    this.memberCode,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) => _$RegisterRequestFromJson(json);
  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}

/// 登录请求模型
@JsonSerializable()
class LoginRequest {
  final String username;
  final String password;

  LoginRequest({
    required this.username,
    required this.password,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) => _$LoginRequestFromJson(json);
  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}
