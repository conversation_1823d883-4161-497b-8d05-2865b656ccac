import 'dart:convert';
import 'dart:async';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../screens/announcement_screen.dart';

/// 公告服务 - 简化版，无缓存机制，直接实时获取
class AnnouncementService extends GetxService {
  static const String _lastAnnouncementIdKey = 'last_announcement_id';

  final announcement = Rxn<Announcement>();
  final RxBool isLoading = false.obs;

  // 静态公告文件地址
  final String _announcementUrl = '${ApiConfig.baseUrl}/announcement.json';
  final String _backupAnnouncementUrl = '${ApiConfig.backupUrl}/announcement.json';

  // 检查间隔（秒）- 实时推送，每30秒检查一次
  static const int _checkIntervalSeconds = 30;
  
  Timer? _checkTimer;

  @override
  void onInit() {
    super.onInit();
    initAnnouncement();
    // 启动实时检查定时器
    _startRealTimeCheck();
  }

  @override
  void onClose() {
    _checkTimer?.cancel();
    super.onClose();
  }

  /// 初始化公告服务 - 简化版，无缓存
  Future<void> initAnnouncement() async {
    try {
      // 立即检查一次公告
      await _checkForNewAnnouncementRealTime();
    } catch (e) {
      // 静默处理错误，避免终端刷屏
    }
  }

  /// 从静态文件获取最新公告
  Future<Announcement?> _fetchLatestAnnouncement() async {
    try {
      final headers = {
        'Accept': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'User-Agent': 'DaizongNovelApp/1.0',
      };

      final client = http.Client();
      try {
        final timeout = Duration(seconds: 10);

        // 先尝试主域名
        try {
          final response = await client
              .get(Uri.parse(_announcementUrl), headers: headers)
              .timeout(timeout);

          if (response.statusCode == 200 && response.body.isNotEmpty) {
            try {
              final data = json.decode(response.body);

              // 检查公告是否激活
              if (data['is_active'] == true) {
                final announcement = Announcement.fromJson(data);
                return announcement;
              } else {
                return null;
              }
            } catch (jsonError) {
              return null;
            }
          }
        } catch (e) {

          // 如果主域名失败，尝试备用地址
          try {
            final response = await client
                .get(Uri.parse(_backupAnnouncementUrl), headers: headers)
                .timeout(timeout);

            if (response.statusCode == 200 && response.body.isNotEmpty) {
              try {
                final data = json.decode(response.body);

                // 检查公告是否激活
                if (data['is_active'] == true) {
                  final announcement = Announcement.fromJson(data);
                  return announcement;
                } else {
                  return null;
                }
              } catch (jsonError) {
                return null;
              }
            }
          } catch (backupError) {
            // 静默处理备用域名错误
          }
        }

        return null;
      } finally {
        client.close();
      }
    } catch (e) {
      return null;
    }
  }

  /// 手动刷新公告 - 简化版
  Future<void> refreshAnnouncement() async {
    try {
      await _checkForNewAnnouncementRealTime();
    } catch (e) {
      Get.snackbar('错误', '刷新公告失败');
    }
  }

  /// 标记公告为已读
  Future<void> markAnnouncementAsRead() async {
    try {
      if (announcement.value != null) {
        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_lastAnnouncementIdKey, announcement.value!.id);
      }
    } catch (e) {
      // 静默处理错误
    }
  }

  /// 显示公告对话框
  void _showAnnouncementDialog(Announcement announcement) {
    try {
      // 确保在UI线程中执行
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (Get.context != null) {
          Get.dialog(
            AnnouncementScreen(announcement: announcement),
            barrierDismissible: false,
          );
        }
      });
    } catch (e) {
      // 静默处理错误
    }
  }

  /// 启动实时检查定时器
  void _startRealTimeCheck() {
    // 使用Timer.periodic创建定时器
    _checkTimer = Timer.periodic(Duration(seconds: _checkIntervalSeconds), (timer) async {
      try {
        await _checkForNewAnnouncementRealTime();
      } catch (e) {
        // 静默处理错误
      }
    });
  }

  /// 实时检查新公告 - 简化版，无缓存
  Future<void> _checkForNewAnnouncementRealTime() async {
    try {
      // 直接获取最新公告
      final latestAnnouncement = await _fetchLatestAnnouncement();

      if (latestAnnouncement != null) {
        // 获取上次显示的公告ID
        final prefs = await SharedPreferences.getInstance();
        final lastAnnouncementId = prefs.getString(_lastAnnouncementIdKey);

        // 如果是新公告，立即显示
        if (lastAnnouncementId != latestAnnouncement.id) {
          // 更新公告状态
          announcement.value = latestAnnouncement;

          // 立即显示公告对话框
          _showAnnouncementDialog(latestAnnouncement);
        }
      }
    } catch (e) {
      // 静默处理错误
    }
  }

  /// 清除公告记录（用于调试）
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_lastAnnouncementIdKey);
      announcement.value = null;
    } catch (e) {
      // 静默处理错误
    }
  }
}

/// 公告数据模型
class Announcement {
  final String id;
  final String title;
  final String content;
  final DateTime date;
  final bool isImportant;
  final bool isActive;

  Announcement({
    required this.id,
    required this.title,
    required this.content,
    required this.date,
    this.isImportant = false,
    this.isActive = true,
  });

  /// 从JSON创建Announcement对象
  factory Announcement.fromJson(Map<String, dynamic> json) {
    return Announcement(
      id: json['id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      date: _parseDateTime(json['created_at'] as String),
      isImportant: json['is_important'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// 解析日期时间，处理不标准的ISO格式
  static DateTime _parseDateTime(String dateTimeString) {
    try {
      // 首先尝试直接解析
      return DateTime.parse(dateTimeString);
    } catch (e) {
      // 如果解析失败，尝试修复格式
      // 处理类似 "2025-06-30T9:00:00Z" 的格式，将单位数小时补零
      String fixedDateTime = dateTimeString;

      // 使用正则表达式匹配并修复单位数小时
      final regex = RegExp(r'T(\d):');
      if (regex.hasMatch(fixedDateTime)) {
        fixedDateTime = fixedDateTime.replaceAllMapped(regex, (match) {
          return 'T0${match.group(1)}:';
        });
      }

      try {
        return DateTime.parse(fixedDateTime);
      } catch (e2) {
        // 如果还是失败，返回当前时间
        return DateTime.now();
      }
    }
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'created_at': date.toIso8601String(),
      'is_important': isImportant,
      'is_active': isActive,
    };
  }
}
