@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(empty) {
  @include set-component-css-var('empty', $empty);

  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  text-align: center;
  box-sizing: border-box;
  padding: getCssVar('empty-padding');

  @include e(image) {
    width: getCssVar('empty-image-width');

    img {
      user-select: none;
      width: 100%;
      height: 100%;
      vertical-align: top;
      object-fit: contain;
    }

    svg {
      color: getCssVar('svg-monochrome-grey');
      fill: currentColor;
      width: 100%;
      height: 100%;
      vertical-align: top;
    }
  }

  @include e(description) {
    margin-top: getCssVar('empty-description-margin-top');

    p {
      margin: 0;
      font-size: getCssVar('font-size', 'base');
      color: getCssVar('text-color', 'secondary');
    }
  }

  @include e(bottom) {
    margin-top: getCssVar('empty-bottom-margin-top');
  }
}
