import 'package:get/get.dart';
import 'package:novel_app/models/character_card.dart';
import 'package:novel_app/models/character_type.dart';
import 'package:novel_app/services/ai_service.dart';
import 'package:novel_app/services/character_card_service.dart';
import 'package:novel_app/services/character_type_service.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/langchain/prompts/character_generation_prompts.dart';
import 'package:uuid/uuid.dart';
import 'dart:convert';
import 'dart:async';

// 定义生成阶段枚举
enum GenerationStage {
  preparing, // 准备阶段
  generating, // 生成内容阶段
  processing, // 处理内容阶段
  completed, // 完成阶段
  error, // 错误阶段
}

// 定义生成进度类
class GenerationProgress {
  final GenerationStage stage;
  final String message;
  final double progress;
  final String? currentContent;
  final List<CharacterCard>? characters;

  GenerationProgress({
    required this.stage,
    required this.message,
    required this.progress,
    this.currentContent,
    this.characters,
  });
}

class CharacterGeneratorService extends GetxService {
  final AIService _aiService;
  final CharacterCardService _characterCardService;
  final CharacterTypeService _characterTypeService;

  CharacterGeneratorService(
    this._aiService,
    this._characterCardService,
    this._characterTypeService,
  );

  Future<List<CharacterCard>> generateCharacters({
    required String title,
    required String genre,
    required String background,
    required int characterCount,
    List<CharacterType>? preferredTypes,
  }) async {
    // 使用内置的角色生成提示词
    const promptContent = '''
请为每个角色提供以下信息：
1. 基本信息：姓名、性别、年龄、种族
2. 外貌特征：体型描述、面部特征、服装风格、特殊配饰
3. 性格特点：性格描述、行为习惯、说话方式
4. 背景故事：出生地、家庭背景、重要经历、动机和目标
5. 能力与技能：特殊能力、战斗技能、专业特长
6. 关系网络：与其他角色的关系、盟友、敌人
7. 角色弧光：角色在故事中的成长和变化
8. 独特之处：使角色与众不同的特点或怪癖
''';

    // 使用新的提示词模板
    String prompt = CharacterGenerationPrompts.buildSimpleCharacterPrompt(
      title: title,
      genre: genre,
      background: background,
      characterCount: characterCount,
      preferredTypes: preferredTypes?.map((t) => '${t.name}：${t.description}').toList(),
    );

    int retryCount = 0;
    const maxRetries = 2;

    while (retryCount <= maxRetries) {
      try {
        print('开始生成角色，小说标题: $title，类型: $genre，角色数量: $characterCount，尝试次数: ${retryCount + 1}');

        // 获取当前使用的模型信息
        final apiConfig = Get.find<ApiConfigController>();
        final currentModel = apiConfig.getCurrentModel();
        print('使用模型: ${currentModel.name}，格式: ${currentModel.apiFormat}');

        // 调用AI服务生成角色
        final response = await _aiService.generateContent(prompt);
        print('AI响应接收完成，长度: ${response.length}字符');

        // 检查响应是否为空
        if (response.trim().isEmpty) {
          throw Exception('AI返回了空响应');
        }

        // 使用新的JSON提取方法
        final jsonStr = CharacterGenerationPrompts.extractJsonFromResponse(response);

        if (jsonStr != null) {
          print('JSON提取成功，长度: ${jsonStr.length}字符');

          // 验证JSON格式
          try {
            final testDecode = jsonDecode(jsonStr);
            if (testDecode is List && testDecode.isNotEmpty) {
              print('JSON格式验证成功，包含${testDecode.length}个角色');

              // 转换为角色对象
              final characters = await generateCharactersFromJson(jsonStr);

              // 保存到数据库
              for (final character in characters) {
                await _characterCardService.addCard(character);
              }
              print('所有角色已保存到数据库');

              return characters;
            } else {
              throw Exception('JSON数组为空或格式不正确');
            }
          } catch (e) {
            throw Exception('JSON格式验证失败: $e');
          }
        } else {
          throw Exception('无法从AI响应中提取有效的JSON数据');
        }
      } catch (e) {
        print('生成角色失败 (尝试 ${retryCount + 1}/$maxRetries): $e');

        retryCount++;
        if (retryCount <= maxRetries) {
          print('等待2秒后重试...');
          await Future.delayed(const Duration(seconds: 2));

          // 如果是第二次重试，使用简化的提示词
          if (retryCount == maxRetries) {
            prompt = CharacterGenerationPrompts.buildErrorRecoveryPrompt(
              title: title,
              genre: genre,
              characterCount: characterCount,
            );
            print('使用简化的错误恢复提示词');
          }
        } else {
          // 所有重试都失败了，创建错误角色
          print('所有重试都失败，创建错误角色');
          final errorCharacter = CharacterCard(
            id: const Uuid().v4(),
            name: "生成失败的角色",
            characterTypeId: const Uuid().v4(),
            gender: "未知",
            age: "未知",
            background: "角色生成过程中发生错误，请重试。错误信息: $e",
            personalityTraits: "请检查网络连接和API配置，然后重试",
            bodyDescription: "",
          );

          // 保存错误角色，以便用户可以看到错误信息
          await _characterCardService.addCard(errorCharacter);
          return [errorCharacter];
        }
      }
  }

    // 这里不应该到达，但作为安全措施
    final errorCharacter = CharacterCard(
      id: const Uuid().v4(),
      name: "未知错误",
      characterTypeId: const Uuid().v4(),
      gender: "未知",
      age: "未知",
      background: "发生了未预期的错误",
      personalityTraits: "请重试",
      bodyDescription: "",
    );

    await _characterCardService.addCard(errorCharacter);
    return [errorCharacter];
  }

  String _extractJsonFromResponse(String response) {
    print('开始从响应中提取JSON数据，响应长度: ${response.length}字符');

    try {
      // 1. 首先尝试直接解析整个响应
      if (response.trim().startsWith('[') && response.trim().endsWith(']')) {
        print('响应本身是JSON数组格式，尝试直接解析');
        try {
          jsonDecode(response.trim()); // 验证JSON格式
          print('响应本身是有效的JSON数组，直接使用');
          return response.trim();
        } catch (e) {
          print('响应虽然是数组格式但JSON无效: $e');
        }
      }

      // 2. 尝试提取```json```格式的代码块
      final jsonRegex = RegExp(r'```(?:json)?\s*([\s\S]*?)\s*```');
      final match = jsonRegex.firstMatch(response);

      if (match != null && match.groupCount >= 1) {
        final extractedJson = match.group(1)!.trim();
        print('从代码块中提取到JSON数据，长度: ${extractedJson.length}字符');
        try {
          jsonDecode(extractedJson); // 验证JSON格式
          print('代码块中的JSON格式有效');
          return extractedJson;
        } catch (e) {
          print('代码块中的JSON格式无效: $e');
        }
      }
    } catch (e) {
      print('初步JSON验证失败: $e');
    }

    // 3. 尝试从文本中提取JSON数组 - 使用更宽松的正则表达式
    final arrayRegex = RegExp(r'\[\s*\{[\s\S]*?\}\s*\]');
    final arrayMatch = arrayRegex.firstMatch(response);
    if (arrayMatch != null) {
      final extractedArray = arrayMatch.group(0)!.trim();
      print('从文本中提取到JSON数组，长度: ${extractedArray.length}字符');
      return extractedArray;
    }

    // 4. 尝试处理流式响应格式
    if (response.contains('data:')) {
      print('检测到流式响应格式，尝试解析');
      final buffer = StringBuffer();
      final lines = response.split('\n');
      print('流式响应行数: ${lines.length}');

      for (final line in lines) {
        if (line.startsWith('data:')) {
          final data = line.substring(5).trim();
          if (data == '[DONE]') continue;

          try {
            // 尝试解析JSON
            final json = jsonDecode(data);

            // 提取内容
            String content = '';

            // 尝试OpenAI格式
            if (json['choices'] != null && json['choices'].isNotEmpty) {
              if (json['choices'][0]['message'] != null) {
                content = json['choices'][0]['message']['content'];
              } else if (json['choices'][0]['delta'] != null &&
                  json['choices'][0]['delta']['content'] != null) {
                content = json['choices'][0]['delta']['content'];
              }
            }
            // 尝试Google/Gemini格式
            else if (json['candidates'] != null &&
                json['candidates'].isNotEmpty) {
              if (json['candidates'][0]['content'] != null &&
                  json['candidates'][0]['content']['parts'] != null &&
                  json['candidates'][0]['content']['parts'].isNotEmpty) {
                content = json['candidates'][0]['content']['parts'][0]['text'];
              }
            }

            if (content.isNotEmpty) {
              buffer.write(content);
            }
          } catch (e) {
            // 忽略解析错误，继续处理下一行
            print('解析流式数据行失败: $e');
          }
        }
      }

      final fullContent = buffer.toString();
      print('流式响应合并后内容长度: ${fullContent.length}字符');

      // 5. 从合并后的内容中尝试提取JSON数组
      // 首先尝试提取代码块
      final jsonRegex = RegExp(r'```(?:json)?\s*([\s\S]*?)\s*```');
      final fullContentCodeMatch = jsonRegex.firstMatch(fullContent);
      if (fullContentCodeMatch != null &&
          fullContentCodeMatch.groupCount >= 1) {
        final extractedJson = fullContentCodeMatch.group(1)!.trim();
        print('从流式响应代码块中提取到JSON数据，长度: ${extractedJson.length}字符');
        return extractedJson;
      }

      // 然后尝试提取数组
      final fullContentArrayMatch = arrayRegex.firstMatch(fullContent);
      if (fullContentArrayMatch != null) {
        final extractedArray = fullContentArrayMatch.group(0)!.trim();
        print('从流式响应中提取到JSON数组，长度: ${extractedArray.length}字符');
        return extractedArray;
      }

      // 6. 如果内容不为空但没有找到JSON数组，尝试更激进的方法
      if (fullContent.isNotEmpty) {
        // 尝试查找可能的JSON开始和结束位置
        int startBracket = fullContent.indexOf('[');
        int endBracket = fullContent.lastIndexOf(']');

        if (startBracket >= 0 && endBracket > startBracket) {
          final possibleJson =
              fullContent.substring(startBracket, endBracket + 1);
          print('尝试提取可能的JSON片段，长度: ${possibleJson.length}字符');

          try {
            // 验证是否为有效JSON
            jsonDecode(possibleJson);
            print('成功提取有效JSON片段');
            return possibleJson;
          } catch (e) {
            print('提取的JSON片段无效: $e');
          }
        }

        // 如果所有方法都失败，构建一个简单的角色数组
        print('无法提取JSON，构建简单角色数组');
        // 清理文本，移除可能导致JSON解析错误的字符
        String cleanedContent = fullContent
            .replaceAll('"', '\\"') // 转义双引号
            .replaceAll('\n', ' ') // 移除换行符
            .replaceAll('\r', ' '); // 移除回车符

        return '[{"name": "自动生成角色", "gender": "未知", "age": "未知", "background": "$cleanedContent"}]';
      }
    }

    // 7. 最后尝试从原始响应中提取任何可能的JSON
    print('尝试从原始响应中提取任何可能的JSON');
    try {
      // 尝试查找可能的JSON开始和结束位置
      int startBracket = response.indexOf('[');
      int endBracket = response.lastIndexOf(']');

      if (startBracket >= 0 && endBracket > startBracket) {
        final possibleJson = response.substring(startBracket, endBracket + 1);
        print('尝试提取可能的JSON片段，长度: ${possibleJson.length}字符');

        try {
          // 验证是否为有效JSON
          jsonDecode(possibleJson);
          print('成功提取有效JSON片段');
          return possibleJson;
        } catch (e) {
          print('提取的JSON片段无效: $e');
        }
      }

      // 如果无法提取有效JSON，构建一个简单的角色数组
      print('无法从原始响应中提取JSON，构建简单角色数组');
      // 获取响应的前200个字符作为背景信息
      String previewContent =
          response.length > 200 ? "${response.substring(0, 200)}..." : response;

      // 清理文本，移除可能导致JSON解析错误的字符
      previewContent = previewContent
          .replaceAll('"', '\\"') // 转义双引号
          .replaceAll('\n', ' ') // 移除换行符
          .replaceAll('\r', ' '); // 移除回车符

      return '[{"name": "解析失败的角色", "gender": "未知", "age": "未知", "background": "无法从AI响应中提取有效的JSON数据。", "personalityTraits": "请重试或检查API配置"}]';
    } catch (e) {
      print('最终提取尝试失败: $e');
      return '[{"name": "解析失败的角色", "gender": "未知", "age": "未知", "background": "提取JSON时发生错误: $e", "personalityTraits": "请重试或检查API配置"}]';
    }
  }

  Future<List<CharacterCard>> generateCharactersFromJson(
      String jsonData) async {
    try {
      final List<dynamic> charactersJson = jsonDecode(jsonData);

      // 转换为CharacterCard对象，并指定一个默认的characterTypeId
      return charactersJson
          .map((json) => CharacterCard(
                id: const Uuid().v4(),
                name: json['name'] ?? '未命名角色',
                characterTypeId:
                    const Uuid().v4(), // 使用新的UUID作为默认characterTypeId
                gender: json['gender']?.toString() ?? '',
                age: json['age']?.toString() ?? '',
                race: json['race'],
                bodyDescription: json['bodyDescription'] ?? '',
                faceFeatures: json['faceFeatures'],
                clothingStyle: json['clothingStyle'],
                accessories: json['accessories'],
                personalityTraits: json['personalityTraits'] ?? '',
                personalityComplexity: json['personalityComplexity'],
                personalityFormation: json['personalityFormation'],
                background: json['background'] ?? '',
                lifeExperiences: json['lifeExperiences'],
                pastEvents: json['pastEvents'],
                shortTermGoals: json['shortTermGoals'],
                longTermGoals: json['longTermGoals'],
                motivation: json['motivation'],
                specialAbilities: json['specialAbilities'],
                normalSkills: json['normalSkills'],
                familyRelations: json['familyRelations'],
                friendships: json['friendships'],
                enemies: json['enemies'],
                loveInterests: json['loveInterests'],
              ))
          .toList();
    } catch (e) {
      print('解析JSON数据失败: $e');
      return [];
    }
  }

  // 流式生成角色的方法
  Stream<GenerationProgress> generateCharactersStream({
    required String title,
    required String genre,
    required String background,
    required int characterCount,
    List<CharacterType>? preferredTypes,
  }) async* {
    // 使用新的提示词模板（流式版本）
    final prompt = CharacterGenerationPrompts.buildSimpleCharacterPrompt(
      title: title,
      genre: genre,
      background: background,
      characterCount: characterCount,
      preferredTypes: preferredTypes?.map((t) => '${t.name}：${t.description}').toList(),
    );

    try {
      // 发送初始状态
      yield GenerationProgress(
        stage: GenerationStage.preparing,
        message: '正在准备生成角色...',
        progress: 0.1,
      );

      // 创建一个缓冲区来收集流式响应
      final buffer = StringBuffer();

      // 使用流式API生成内容
      yield GenerationProgress(
        stage: GenerationStage.generating,
        message: '正在生成角色...',
        progress: 0.2,
      );

      // 使用AIService的流式API
      await for (final chunk in _aiService.generateTextStream(
        systemPrompt: CharacterGenerationPrompts.systemPrompt,
        userPrompt: prompt,
        temperature: 0.7,
        maxTokens: 3000,
      )) {
        buffer.write(chunk);

        // 更新生成进度
        yield GenerationProgress(
          stage: GenerationStage.generating,
          message: '正在生成角色...',
          progress: 0.5,
          currentContent: buffer.toString(),
        );
      }

      // 解析响应
      yield GenerationProgress(
        stage: GenerationStage.processing,
        message: '正在处理生成的内容...',
        progress: 0.8,
        currentContent: buffer.toString(),
      );

      final jsonStr = CharacterGenerationPrompts.extractJsonFromResponse(buffer.toString()) ??
          CharacterGenerationPrompts.createErrorCharacterJson(
            '流式生成过程中无法提取有效的JSON数据',
            count: characterCount,
          );

      // 尝试解析JSON数组
      List<dynamic> charactersJson;
      try {
        charactersJson = jsonDecode(jsonStr);
      } catch (e) {
        // 如果解析失败，创建一个包含错误信息的角色
        charactersJson = [
          {
            "name": "生成失败的角色",
            "gender": "未知",
            "age": "未知",
            "background": "角色生成过程中发生错误，请重试。错误信息: $e",
            "personalityTraits": "未知"
          }
        ];
      }

      // 转换为CharacterCard对象
      final characters = charactersJson.map((json) {
        // 为每个字段提供默认值，避免空值异常
        return CharacterCard(
          id: const Uuid().v4(),
          name: json['name'] ?? '未命名角色',
          characterTypeId: preferredTypes != null && preferredTypes.isNotEmpty
              ? preferredTypes[0].id
              : const Uuid().v4(),
          gender: json['gender']?.toString() ?? '',
          age: json['age']?.toString() ?? '',
          race: json['race'] ?? '',
          bodyDescription: json['bodyDescription'] ?? '',
          faceFeatures: json['faceFeatures'] ?? '',
          clothingStyle: json['clothingStyle'] ?? '',
          accessories: json['accessories'] ?? '',
          personalityTraits: json['personalityTraits'] ?? '',
          personalityComplexity: json['personalityComplexity'] ?? '',
          personalityFormation: json['personalityFormation'] ?? '',
          background: json['background'] ?? '',
          lifeExperiences: json['lifeExperiences'] ?? '',
          pastEvents: json['pastEvents'] ?? '',
          shortTermGoals: json['shortTermGoals'] ?? '',
          longTermGoals: json['longTermGoals'] ?? '',
          motivation: json['motivation'] ?? '',
          specialAbilities: json['specialAbilities'] ?? '',
          normalSkills: json['normalSkills'] ?? '',
          familyRelations: json['familyRelations'] ?? '',
          friendships: json['friendships'] ?? '',
          enemies: json['enemies'] ?? '',
          loveInterests: json['loveInterests'] ?? '',
        );
      }).toList();

      // 保存生成的角色
      for (var character in characters) {
        await _characterCardService.addCard(character);
      }

      // 发送完成状态
      yield GenerationProgress(
        stage: GenerationStage.completed,
        message: '角色生成完成！',
        progress: 1.0,
        characters: characters,
      );
    } catch (e) {
      // 发送错误状态
      yield GenerationProgress(
        stage: GenerationStage.error,
        message: '生成角色失败: $e',
        progress: 0,
      );

      // 创建一个错误角色并返回
      final errorCharacter = CharacterCard(
        id: const Uuid().v4(),
        name: "生成失败的角色",
        characterTypeId: const Uuid().v4(),
        gender: "未知",
        age: "未知",
        background: "角色生成过程中发生错误，请重试。错误信息: $e",
        personalityTraits: "请检查网络连接和API配置，然后重试",
        bodyDescription: "",
      );

      // 保存错误角色，以便用户可以看到错误信息
      await _characterCardService.addCard(errorCharacter);

      // 发送完成状态，但包含错误角色
      yield GenerationProgress(
        stage: GenerationStage.completed,
        message: '角色生成完成，但有错误发生',
        progress: 1.0,
        characters: [errorCharacter],
      );
    }
  }
}
