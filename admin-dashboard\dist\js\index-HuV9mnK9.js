import{_ as Ve}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                      *//* empty css                     *//* empty css                 *//* empty css                        *//* empty css                        *//* empty css                  *//* empty css                   *//* empty css                  *//* empty css                   *//* empty css               */import{d as Ee,r as g,a as L,o as ze,c as v,b as s,e as t,w as l,W as Te,q as Pe,a2 as Me,Z as Se,a5 as Ue,a6 as $e,v as u,i as c,X as Ae,E as De,B as x,t as n,h as d,F as I,G as B,a7 as Ie,_ as Be,$ as Fe,I as K,a8 as Re,g as je,l as Ne,m as Oe,ad as Ye,f as qe,a0 as F,u as He,U as Le,H as G,M as Ke,a9 as Ge}from"./index-CAzH2L69.js";const Je={class:"members-container"},We={class:"stat-card"},Xe={class:"stat-icon"},Ze={class:"stat-content"},Qe={class:"stat-value"},et={class:"stat-card"},tt={class:"stat-icon"},at={class:"stat-content"},st={class:"stat-value"},lt={class:"stat-card"},ot={class:"stat-icon"},nt={class:"stat-content"},rt={class:"stat-value"},it={class:"stat-card"},dt={class:"stat-icon"},ct={class:"stat-content"},ut={class:"stat-value"},mt={class:"dashboard-card"},pt={class:"card-header"},_t={class:"package-header"},vt={class:"package-name"},ft={class:"package-price"},gt={class:"price-value"},bt={class:"price-unit"},yt={class:"package-features"},ht={class:"package-stats"},Ct={class:"stat-item"},kt={class:"stat-value"},xt={class:"stat-item"},wt={class:"stat-value"},Vt={class:"package-actions"},Et={class:"dashboard-card"},zt={class:"card-header"},Tt={class:"member-filter"},Pt={key:0,class:"permanent-member"},Mt={key:1},St={key:0,class:"permanent-member"},Ut={key:1},$t={key:0},At={key:1,class:"no-code"},Dt={class:"pagination-container"},It={style:{"margin-top":"8px"}},Bt=Ee({__name:"index",setup(Ft){const R=He(),P=g(!1),C=g(!1),M=g(!1),S=g(),T=g("all"),w=g(""),m=L({page:1,size:20,total:0}),V=g({totalMembers:0,permanentMembers:0,monthlyMembers:0,conversionRate:0}),E=g([{id:"pkg_permanent",name:"永久会员",price:99,unit:"一次性",features:["无限制创作","数据云同步","高级AI功能","专属客服"],soldCount:0,activeUsers:0,isPopular:!0,isActive:!0},{id:"pkg_monthly",name:"月会员",price:19.9,unit:"/月",features:["无限制创作","数据云同步","基础AI功能"],soldCount:0,activeUsers:0,isPopular:!1,isActive:!0}]),U=g([]),r=L({id:"",name:"",price:0,unit:"一次性",features:[],isPopular:!1,isActive:!0}),J={name:[{required:!0,message:"请输入套餐名称",trigger:"blur"}],price:[{required:!0,message:"请输入套餐价格",trigger:"blur"}]},W=o=>{switch(o){case"permanent":return"success";case"monthly":return"warning";default:return"info"}},X=o=>{switch(o){case"permanent":return"永久会员";case"monthly":return"月会员";default:return"普通用户"}},j=o=>{const e=F(o),b=F();return Math.max(0,e.diff(b,"day"))},Z=o=>o<=7?"danger":o<=30?"warning":"success",N=o=>F(o).format("YYYY-MM-DD HH:mm"),O=o=>{R.push(`/users/${o}`)},Q=o=>{R.push("/member-codes")},ee=o=>{z()},te=async o=>{try{u.success("设置已更新")}catch{u.error("设置失败"),o.isDataSyncEnabled=!o.isDataSyncEnabled}},ae=async o=>{try{await Le.prompt("请输入续期天数","会员续期",{confirmButtonText:"确定",cancelButtonText:"取消",inputPattern:/^\d+$/,inputErrorMessage:"请输入有效的天数"}).then(async({value:e})=>{const b=parseInt(e),p=await fetch(`/api/users/${o.id}/extend-membership`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({days:b})}),_=await p.json();p.ok&&_.success?(u.success(_.message),z()):u.error(_.message||"续期失败")})}catch(e){e!=="cancel"&&(console.error("续期失败:",e),u.error("续期失败"))}},se=()=>{M.value=!1,de(),C.value=!0},le=o=>{M.value=!0,Object.assign(r,o),C.value=!0},oe=async o=>{try{o.isActive=!o.isActive,u.success(`套餐已${o.isActive?"启用":"禁用"}`)}catch{u.error("操作失败"),o.isActive=!o.isActive}},ne=async o=>{try{u.success("套餐删除成功"),$()}catch{u.error("删除失败")}},re=()=>{w.value.trim()&&(r.features.push(w.value.trim()),w.value="")},ie=o=>{r.features.splice(o,1)},de=()=>{Object.assign(r,{id:"",name:"",price:0,unit:"一次性",features:[],isPopular:!1,isActive:!0})},ce=async()=>{if(S.value)try{if(!await S.value.validate())return;P.value=!0,await new Promise(e=>setTimeout(e,1e3)),u.success("套餐保存成功"),C.value=!1,$()}catch{u.error("保存失败")}finally{P.value=!1}},ue=o=>{m.size=o,m.page=1,z()},me=o=>{m.page=o,z()},pe=async()=>{try{const o=await fetch("/api/members/stats",{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(o.ok){const e=await o.json();e.success&&(V.value=e.data,E.value[0].soldCount=e.data.permanentMembers,E.value[0].activeUsers=e.data.permanentMembers,E.value[1].soldCount=e.data.monthlyMembers,E.value[1].activeUsers=e.data.monthlyMembers)}}catch(o){console.error("加载会员统计失败:",o)}},$=async()=>{},z=async()=>{try{const o=new URLSearchParams({page:m.page.toString(),size:m.size.toString(),memberType:T.value==="all"?"":T.value}),e=await fetch(`/api/users?${o}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(e.ok){const b=await e.json();if(b.success){const p=b.data.users||[];U.value=p.filter(_=>_.isMember),m.total=U.value.length}}}catch(o){console.error("加载会员用户失败:",o),u.error("加载会员用户失败")}};return ze(()=>{pe(),$(),z()}),(o,e)=>{const b=x("Postcard"),p=De,_=Ae,_e=x("Crown"),ve=x("Calendar"),fe=x("TrendCharts"),Y=Te,ge=x("Plus"),f=Pe,be=x("Check"),ye=Ge,h=Ie,q=Me,y=Be,A=Fe,D=Re,he=Se,Ce=Ue,H=Oe,k=Ne,ke=Ye,xe=je,we=$e;return c(),v("div",Je,[e[27]||(e[27]=s("div",{class:"page-header"},[s("h1",{class:"page-title"},"会员管理"),s("p",{class:"page-subtitle"},"管理会员用户和套餐配置")],-1)),t(Y,{gutter:20,class:"stats-row"},{default:l(()=>[t(_,{xs:12,sm:6},{default:l(()=>[s("div",We,[s("div",Xe,[t(p,{size:"32",color:"#1890ff"},{default:l(()=>[t(b)]),_:1})]),s("div",Ze,[s("div",Qe,n(V.value.totalMembers),1),e[11]||(e[11]=s("div",{class:"stat-label"},"会员总数",-1))])])]),_:1}),t(_,{xs:12,sm:6},{default:l(()=>[s("div",et,[s("div",tt,[t(p,{size:"32",color:"#52c41a"},{default:l(()=>[t(_e)]),_:1})]),s("div",at,[s("div",st,n(V.value.permanentMembers),1),e[12]||(e[12]=s("div",{class:"stat-label"},"永久会员",-1))])])]),_:1}),t(_,{xs:12,sm:6},{default:l(()=>[s("div",lt,[s("div",ot,[t(p,{size:"32",color:"#faad14"},{default:l(()=>[t(ve)]),_:1})]),s("div",nt,[s("div",rt,n(V.value.monthlyMembers),1),e[13]||(e[13]=s("div",{class:"stat-label"},"月会员",-1))])])]),_:1}),t(_,{xs:12,sm:6},{default:l(()=>[s("div",it,[s("div",dt,[t(p,{size:"32",color:"#f5222d"},{default:l(()=>[t(fe)]),_:1})]),s("div",ct,[s("div",ut,n(V.value.conversionRate)+"%",1),e[14]||(e[14]=s("div",{class:"stat-label"},"转化率",-1))])])]),_:1})]),_:1}),s("div",mt,[s("div",pt,[e[16]||(e[16]=s("h3",{class:"card-title"},"会员套餐配置",-1)),t(f,{type:"primary",onClick:se},{default:l(()=>[t(p,null,{default:l(()=>[t(ge)]),_:1}),e[15]||(e[15]=d(" 添加套餐 "))]),_:1,__:[15]})]),t(Y,{gutter:20},{default:l(()=>[(c(!0),v(I,null,B(E.value,a=>(c(),G(_,{xs:24,sm:12,lg:8,key:a.id},{default:l(()=>[s("div",{class:Ke(["package-card",{"package-popular":a.isPopular}])},[s("div",_t,[s("div",vt,n(a.name),1),s("div",ft,[e[17]||(e[17]=s("span",{class:"price-symbol"},"¥",-1)),s("span",gt,n(a.price),1),s("span",bt,n(a.unit),1)])]),s("div",yt,[(c(!0),v(I,null,B(a.features,i=>(c(),v("div",{class:"feature-item",key:i},[t(p,{color:"#52c41a"},{default:l(()=>[t(be)]),_:1}),s("span",null,n(i),1)]))),128))]),s("div",ht,[s("div",Ct,[e[18]||(e[18]=s("span",{class:"stat-label"},"已售出",-1)),s("span",kt,n(a.soldCount),1)]),s("div",xt,[e[19]||(e[19]=s("span",{class:"stat-label"},"活跃用户",-1)),s("span",wt,n(a.activeUsers),1)])]),s("div",Vt,[t(f,{size:"small",onClick:i=>le(a)},{default:l(()=>e[20]||(e[20]=[d("编辑")])),_:2,__:[20]},1032,["onClick"]),t(f,{size:"small",type:"warning",onClick:i=>oe(a)},{default:l(()=>[d(n(a.isActive?"禁用":"启用"),1)]),_:2},1032,["onClick"]),t(ye,{title:"确定要删除这个套餐吗？",onConfirm:i=>ne(a)},{reference:l(()=>[t(f,{size:"small",type:"danger"},{default:l(()=>e[21]||(e[21]=[d("删除")])),_:1,__:[21]})]),_:2},1032,["onConfirm"])])],2)]),_:2},1024))),128))]),_:1})]),s("div",Et,[s("div",zt,[e[22]||(e[22]=s("h3",{class:"card-title"},"会员用户列表",-1)),s("div",Tt,[t(q,{modelValue:T.value,"onUpdate:modelValue":e[0]||(e[0]=a=>T.value=a),onChange:ee,style:{width:"150px"}},{default:l(()=>[t(h,{label:"全部会员",value:"all"}),t(h,{label:"永久会员",value:"permanent"}),t(h,{label:"月会员",value:"monthly"}),t(h,{label:"即将到期",value:"expiring"})]),_:1},8,["modelValue"])])]),t(he,{data:U.value,style:{width:"100%"}},{default:l(()=>[t(y,{prop:"username",label:"用户名",width:"120"},{default:l(({row:a})=>[t(f,{type:"text",onClick:i=>O(a.id)},{default:l(()=>[d(n(a.username),1)]),_:2},1032,["onClick"])]),_:1}),t(y,{label:"会员类型",width:"120"},{default:l(({row:a})=>[t(A,{type:W(a.membershipType),size:"small"},{default:l(()=>[d(n(X(a.membershipType)),1)]),_:2},1032,["type"])]),_:1}),t(y,{label:"开通时间",width:"160"},{default:l(({row:a})=>[d(n(N(a.memberStartTime)),1)]),_:1}),t(y,{label:"到期时间",width:"160"},{default:l(({row:a})=>[a.membershipType==="permanent"?(c(),v("span",Pt," 永久有效 ")):a.memberExpireTime?(c(),v("span",Mt,n(N(a.memberExpireTime)),1)):K("",!0)]),_:1}),t(y,{label:"剩余天数",width:"100"},{default:l(({row:a})=>[a.membershipType==="permanent"?(c(),v("span",St," ∞ ")):a.memberExpireTime?(c(),v("span",Ut,[t(A,{type:Z(j(a.memberExpireTime)),size:"small"},{default:l(()=>[d(n(j(a.memberExpireTime))+"天 ",1)]),_:2},1032,["type"])])):K("",!0)]),_:1}),t(y,{label:"使用的会员码",width:"150"},{default:l(({row:a})=>[a.memberCode?(c(),v("span",$t,[t(f,{type:"text",size:"small",onClick:i=>Q(a.memberCode)},{default:l(()=>[d(n(a.memberCode),1)]),_:2},1032,["onClick"])])):(c(),v("span",At,"-"))]),_:1}),t(y,{label:"数据同步",width:"80"},{default:l(({row:a})=>[t(D,{modelValue:a.isDataSyncEnabled,"onUpdate:modelValue":i=>a.isDataSyncEnabled=i,onChange:i=>te(a)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),_:1}),t(y,{label:"操作",width:"150",fixed:"right"},{default:l(({row:a})=>[t(f,{type:"primary",size:"small",onClick:i=>O(a.id)},{default:l(()=>e[23]||(e[23]=[d(" 详情 ")])),_:2,__:[23]},1032,["onClick"]),t(f,{type:"warning",size:"small",onClick:i=>ae(a)},{default:l(()=>e[24]||(e[24]=[d(" 续期 ")])),_:2,__:[24]},1032,["onClick"])]),_:1})]),_:1},8,["data"]),s("div",Dt,[t(Ce,{"current-page":m.page,"onUpdate:currentPage":e[1]||(e[1]=a=>m.page=a),"page-size":m.size,"onUpdate:pageSize":e[2]||(e[2]=a=>m.size=a),"page-sizes":[10,20,50,100],total:m.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ue,onCurrentChange:me},null,8,["current-page","page-size","total"])])]),t(we,{modelValue:C.value,"onUpdate:modelValue":e[10]||(e[10]=a=>C.value=a),title:M.value?"编辑套餐":"添加套餐",width:"600px"},{footer:l(()=>[t(f,{onClick:e[9]||(e[9]=a=>C.value=!1)},{default:l(()=>e[25]||(e[25]=[d("取消")])),_:1,__:[25]}),t(f,{type:"primary",onClick:ce,loading:P.value},{default:l(()=>e[26]||(e[26]=[d(" 保存 ")])),_:1,__:[26]},8,["loading"])]),default:l(()=>[t(xe,{ref_key:"packageFormRef",ref:S,model:r,rules:J,"label-width":"100px"},{default:l(()=>[t(k,{label:"套餐名称",prop:"name"},{default:l(()=>[t(H,{modelValue:r.name,"onUpdate:modelValue":e[3]||(e[3]=a=>r.name=a)},null,8,["modelValue"])]),_:1}),t(k,{label:"套餐价格",prop:"price"},{default:l(()=>[t(ke,{modelValue:r.price,"onUpdate:modelValue":e[4]||(e[4]=a=>r.price=a),min:0,precision:2,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(k,{label:"价格单位",prop:"unit"},{default:l(()=>[t(q,{modelValue:r.unit,"onUpdate:modelValue":e[5]||(e[5]=a=>r.unit=a),style:{width:"100%"}},{default:l(()=>[t(h,{label:"一次性",value:"一次性"}),t(h,{label:"/月",value:"/月"}),t(h,{label:"/年",value:"/年"})]),_:1},8,["modelValue"])]),_:1}),t(k,{label:"套餐特性"},{default:l(()=>[t(H,{modelValue:w.value,"onUpdate:modelValue":e[6]||(e[6]=a=>w.value=a),placeholder:"输入特性后按回车添加",onKeyup:qe(re,["enter"])},null,8,["modelValue"]),s("div",It,[(c(!0),v(I,null,B(r.features,(a,i)=>(c(),G(A,{key:i,closable:"",onClose:Rt=>ie(i),style:{"margin-right":"8px","margin-bottom":"8px"}},{default:l(()=>[d(n(a),1)]),_:2},1032,["onClose"]))),128))])]),_:1}),t(k,{label:"是否热门"},{default:l(()=>[t(D,{modelValue:r.isPopular,"onUpdate:modelValue":e[7]||(e[7]=a=>r.isPopular=a)},null,8,["modelValue"])]),_:1}),t(k,{label:"是否启用"},{default:l(()=>[t(D,{modelValue:r.isActive,"onUpdate:modelValue":e[8]||(e[8]=a=>r.isActive=a)},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue","title"])])}}}),Zt=Ve(Bt,[["__scopeId","data-v-b374e472"]]);export{Zt as default};
