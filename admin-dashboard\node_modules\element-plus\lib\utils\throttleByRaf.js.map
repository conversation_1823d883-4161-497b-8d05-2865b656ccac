{"version": 3, "file": "throttleByRaf.js", "sources": ["../../../../packages/utils/throttleByRaf.ts"], "sourcesContent": ["import { cAF, rAF } from './raf'\n\nexport function throttleByRaf(cb: (...args: any[]) => void) {\n  let timer = 0\n\n  const throttle = (...args: any[]): void => {\n    if (timer) {\n      cAF(timer)\n    }\n    timer = rAF(() => {\n      cb(...args)\n      timer = 0\n    })\n  }\n\n  throttle.cancel = () => {\n    cAF(timer)\n    timer = 0\n  }\n\n  return throttle\n}\n"], "names": ["cAF", "rAF"], "mappings": ";;;;;;AACO,SAAS,aAAa,CAAC,EAAE,EAAE;AAClC,EAAE,IAAI,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,MAAM,QAAQ,GAAG,CAAC,GAAG,IAAI,KAAK;AAChC,IAAI,IAAI,KAAK,EAAE;AACf,MAAMA,OAAG,CAAC,KAAK,CAAC,CAAC;AACjB,KAAK;AACL,IAAI,KAAK,GAAGC,OAAG,CAAC,MAAM;AACtB,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC;AAClB,MAAM,KAAK,GAAG,CAAC,CAAC;AAChB,KAAK,CAAC,CAAC;AACP,GAAG,CAAC;AACJ,EAAE,QAAQ,CAAC,MAAM,GAAG,MAAM;AAC1B,IAAID,OAAG,CAAC,KAAK,CAAC,CAAC;AACf,IAAI,KAAK,GAAG,CAAC,CAAC;AACd,GAAG,CAAC;AACJ,EAAE,OAAO,QAAQ,CAAC;AAClB;;;;"}