# 岱宗文脉 - 架构更新说明

## 📋 更新概述

本次更新将项目完全迁移到CloudBase云数据库架构，统一了后端数据存储和API服务。

## 🏗️ 新架构说明

### ✅ 当前架构（推荐）

```
Flutter应用 ←→ CloudBase云函数API ←→ CloudBase云数据库
     ↓              ↓                    ↓
后台管理系统 ←→ CloudBase云函数API ←→ CloudBase云数据库
```

**核心组件：**
- **CloudBase云函数**: `cloudbase/cloudbase-deploy/novel-app-api/index.js`
- **CloudBase数据库**: 环境ID `novel-app-2gywkgnn15cbd6a8`
- **API地址**: `https://api.dznovel.top/api`

### ❌ 旧架构（已弃用）

```
Flutter应用 ←→ 本地JSON服务器 ←→ db.json文件
     ↓              ↓              ↓
后台管理系统 ←→ 本地JSON服务器 ←→ db.json文件
```

**已弃用组件：**
- ~~`cloudbase-server.js`~~ - 本地JSON服务器（已注释）
- ~~`db.json`~~ - 本地JSON数据文件

## 📊 数据存储

### CloudBase数据集合

| 集合名称 | 用途 | 说明 |
|---------|------|------|
| `users` | 用户数据 | 包含用户信息、会员状态、同步数据 |
| `memberData` | 会员码 | 会员码生成、使用状态管理 |
| `packages` | 套餐信息 | 会员套餐配置 |

### 数据同步流程

1. **Flutter应用** → 收集本地数据 → CloudBase云函数 → CloudBase数据库
2. **后台管理系统** → 查询/管理数据 → CloudBase云函数 → CloudBase数据库

## 🔧 API接口

### 管理员接口
- `POST /admin/login` - 管理员登录
- `GET /admin/verify` - 验证管理员token

### 仪表板接口
- `GET /dashboard` - 获取仪表板数据
- `GET /system/status` - 获取系统状态

### 用户管理接口
- `GET /users` - 获取用户列表
- `PUT /users/{id}` - 更新用户信息
- `DELETE /users/{id}` - 删除用户

### 会员码管理接口
- `GET /member-codes` - 获取会员码列表
- `POST /member-codes/generate` - 生成会员码
- `DELETE /member-codes/{id}` - 删除会员码

### 数据同步接口
- `POST /sync/upload` - 上传同步数据
- `GET /sync/download` - 下载同步数据
- `GET /sync/stats` - 同步统计
- `GET /sync/records` - 同步记录

## 🚀 部署配置

### 生产环境
- **API地址**: `https://api.dznovel.top/api`
- **CloudBase环境**: `novel-app-2gywkgnn15cbd6a8`
- **数据存储**: CloudBase云数据库

### 开发环境
- **API地址**: `https://api.dznovel.top/api` (与生产环境相同)
- **代理配置**: Vite开发服务器代理到CloudBase
- **数据存储**: CloudBase云数据库

## ⚠️ 重要提醒

1. **不要使用本地JSON服务器**: `cloudbase-server.js` 已被弃用
2. **统一使用CloudBase**: 所有数据操作都通过CloudBase云函数
3. **环境一致性**: 开发和生产环境都使用相同的CloudBase服务
4. **数据安全**: 所有数据存储在腾讯云CloudBase，安全可靠

## 🔍 故障排查

### 常见问题

1. **API请求失败**
   - 检查网络连接
   - 确认API地址配置正确
   - 查看CloudBase云函数日志

2. **数据同步失败**
   - 检查用户是否为有效会员
   - 确认token是否有效
   - 查看数据大小是否超限

3. **后台管理系统无法访问**
   - 确认管理员账号密码
   - 检查API配置是否指向CloudBase
   - 查看浏览器网络请求

### 调试方法

1. **查看CloudBase日志**
   ```bash
   # 在CloudBase控制台查看云函数执行日志
   ```

2. **本地调试**
   ```bash
   # 如需本地调试，可临时启用cloudbase-server.js
   # 但请确保生产环境使用CloudBase
   ```

## 📝 更新日志

### v4.3.12 (2025-01-24)
- ✅ 完全迁移到CloudBase云数据库
- ✅ 统一后台管理系统API配置
- ✅ 弃用本地JSON服务器
- ✅ 添加管理员token验证接口
- ✅ 添加系统状态监控接口
- ✅ 优化数据同步流程

---

**维护者**: 岱宗文脉开发团队  
**更新时间**: 2025-01-24
