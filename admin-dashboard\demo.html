<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小说应用后台管理系统 - 演示版</title>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/@element-plus/icons-vue/dist/index.iife.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .demo-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .demo-card {
            background: white;
            border-radius: 16px;
            padding: 40px;
            max-width: 800px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        .demo-title {
            font-size: 32px;
            font-weight: bold;
            color: #262626;
            margin-bottom: 16px;
        }
        .demo-subtitle {
            font-size: 18px;
            color: #8c8c8c;
            margin-bottom: 32px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 32px 0;
        }
        .feature-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            text-align: left;
        }
        .feature-icon {
            font-size: 32px;
            color: #1890ff;
            margin-bottom: 12px;
        }
        .feature-title {
            font-size: 18px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
        }
        .feature-desc {
            font-size: 14px;
            color: #8c8c8c;
            line-height: 1.5;
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 32px 0;
        }
        .status-item {
            background: #f0f9ff;
            border: 1px solid #e1f5fe;
            border-radius: 8px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .status-icon {
            color: #52c41a;
            font-size: 20px;
        }
        .status-text {
            font-size: 14px;
            color: #262626;
        }
        .demo-actions {
            margin-top: 32px;
            display: flex;
            gap: 16px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .demo-note {
            background: #fff7e6;
            border: 1px solid #ffd591;
            border-radius: 8px;
            padding: 16px;
            margin-top: 24px;
            text-align: left;
        }
        .demo-note-title {
            font-weight: 600;
            color: #d46b08;
            margin-bottom: 8px;
        }
        .demo-note-content {
            font-size: 14px;
            color: #ad6800;
            line-height: 1.5;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="demo-container">
            <div class="demo-card">
                <div class="demo-title">🎉 小说应用后台管理系统</div>
                <div class="demo-subtitle">基于 Vue 3 + Element Plus 构建的现代化管理平台</div>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <div class="feature-title">仪表板</div>
                        <div class="feature-desc">系统概览、用户统计、数据可视化图表</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">👥</div>
                        <div class="feature-title">用户管理</div>
                        <div class="feature-desc">用户列表、详情查看、会员状态管理</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🎫</div>
                        <div class="feature-title">会员码管理</div>
                        <div class="feature-desc">生成会员码、使用状态跟踪、批量操作</div>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📚</div>
                        <div class="feature-title">内容管理</div>
                        <div class="feature-desc">小说管理、角色管理、知识库管理</div>
                    </div>
                </div>

                <div class="status-grid">
                    <div class="status-item">
                        <div class="status-icon">✅</div>
                        <div class="status-text">CloudBase API 服务正常</div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon">✅</div>
                        <div class="status-text">管理员认证已配置</div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon">✅</div>
                        <div class="status-text">数据库连接正常</div>
                    </div>
                    <div class="status-item">
                        <div class="status-icon">✅</div>
                        <div class="status-text">前端框架已就绪</div>
                    </div>
                </div>

                <div class="demo-actions">
                    <el-button type="primary" size="large" @click="showLoginDemo">
                        <el-icon><User /></el-icon>
                        演示登录
                    </el-button>
                    <el-button size="large" @click="showApiDemo">
                        <el-icon><Connection /></el-icon>
                        测试API
                    </el-button>
                    <el-button size="large" @click="showDocsDemo">
                        <el-icon><Document /></el-icon>
                        查看文档
                    </el-button>
                </div>

                <div class="demo-note">
                    <div class="demo-note-title">🚀 系统状态</div>
                    <div class="demo-note-content">
                        • CloudBase服务器运行在: http://localhost:3001<br>
                        • 管理员账号: admin / admin123<br>
                        • 前端开发服务器: http://localhost:8080 (npm install 完成后可启动)<br>
                        • 支持功能: 用户管理、会员码管理、数据同步、仪表板等
                    </div>
                </div>
            </div>
        </div>

        <!-- 登录演示对话框 -->
        <el-dialog v-model="loginVisible" title="管理员登录演示" width="400px">
            <el-form :model="loginForm" label-width="80px">
                <el-form-item label="用户名">
                    <el-input v-model="loginForm.username" placeholder="admin" />
                </el-form-item>
                <el-form-item label="密码">
                    <el-input v-model="loginForm.password" type="password" placeholder="admin123" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="loginVisible = false">取消</el-button>
                <el-button type="primary" @click="handleLogin" :loading="loginLoading">
                    登录
                </el-button>
            </template>
        </el-dialog>

        <!-- API测试对话框 -->
        <el-dialog v-model="apiVisible" title="API测试" width="600px">
            <div style="margin-bottom: 16px;">
                <el-button @click="testHealthApi" :loading="apiLoading">测试健康检查</el-button>
                <el-button @click="testDashboardApi" :loading="apiLoading">测试仪表板API</el-button>
                <el-button @click="testUsersApi" :loading="apiLoading">测试用户API</el-button>
            </div>
            <el-input
                v-model="apiResult"
                type="textarea"
                :rows="10"
                placeholder="API测试结果将显示在这里..."
                readonly
            />
        </el-dialog>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElButton, ElDialog, ElForm, ElFormItem, ElInput, ElIcon, ElMessage } = ElementPlus;

        createApp({
            components: {
                ElButton,
                ElDialog,
                ElForm,
                ElFormItem,
                ElInput,
                ElIcon
            },
            data() {
                return {
                    loginVisible: false,
                    apiVisible: false,
                    loginLoading: false,
                    apiLoading: false,
                    loginForm: {
                        username: 'admin',
                        password: 'admin123'
                    },
                    apiResult: ''
                };
            },
            methods: {
                showLoginDemo() {
                    this.loginVisible = true;
                },
                showApiDemo() {
                    this.apiVisible = true;
                    this.apiResult = '';
                },
                showDocsDemo() {
                    ElMessage.info('文档功能开发中...');
                },
                async handleLogin() {
                    this.loginLoading = true;
                    try {
                        const response = await fetch('/api/admin/login', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(this.loginForm)
                        });
                        
                        const result = await response.json();
                        
                        if (result.success) {
                            ElMessage.success('登录成功！');
                            this.loginVisible = false;
                        } else {
                            ElMessage.error(result.message || '登录失败');
                        }
                    } catch (error) {
                        ElMessage.error('网络错误: ' + error.message);
                    } finally {
                        this.loginLoading = false;
                    }
                },
                async testHealthApi() {
                    this.apiLoading = true;
                    try {
                        const response = await fetch('/api/health');
                        const result = await response.json();
                        this.apiResult = '健康检查API测试结果:\n' + JSON.stringify(result, null, 2);
                    } catch (error) {
                        this.apiResult = '健康检查API测试失败:\n' + error.message;
                    } finally {
                        this.apiLoading = false;
                    }
                },
                async testDashboardApi() {
                    this.apiLoading = true;
                    try {
                        // 先登录获取token
                        const loginResponse = await fetch('/api/admin/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ username: 'admin', password: 'admin123' })
                        });
                        const loginResult = await loginResponse.json();
                        
                        if (loginResult.success) {
                            const response = await fetch('/api/dashboard', {
                                headers: {
                                    'Authorization': 'Bearer ' + loginResult.data.token
                                }
                            });
                            const result = await response.json();
                            this.apiResult = '仪表板API测试结果:\n' + JSON.stringify(result, null, 2);
                        } else {
                            this.apiResult = '登录失败: ' + loginResult.message;
                        }
                    } catch (error) {
                        this.apiResult = '仪表板API测试失败:\n' + error.message;
                    } finally {
                        this.apiLoading = false;
                    }
                },
                async testUsersApi() {
                    this.apiLoading = true;
                    try {
                        // 先登录获取token
                        const loginResponse = await fetch('/api/admin/login', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ username: 'admin', password: 'admin123' })
                        });
                        const loginResult = await loginResponse.json();
                        
                        if (loginResult.success) {
                            const response = await fetch('/api/users', {
                                headers: {
                                    'Authorization': 'Bearer ' + loginResult.data.token
                                }
                            });
                            const result = await response.json();
                            this.apiResult = '用户API测试结果:\n' + JSON.stringify(result, null, 2);
                        } else {
                            this.apiResult = '登录失败: ' + loginResult.message;
                        }
                    } catch (error) {
                        this.apiResult = '用户API测试失败:\n' + error.message;
                    } finally {
                        this.apiLoading = false;
                    }
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
