{"version": 3, "file": "item.js", "sources": ["../../../../../../packages/components/steps/src/item.vue"], "sourcesContent": ["<template>\n  <div :style=\"style\" :class=\"containerKls\">\n    <!-- icon & line -->\n    <div :class=\"[ns.e('head'), ns.is(currentStatus)]\">\n      <div v-if=\"!isSimple\" :class=\"ns.e('line')\">\n        <i :class=\"ns.e('line-inner')\" :style=\"lineStyle\" />\n      </div>\n\n      <div\n        :class=\"[ns.e('icon'), ns.is(icon || $slots.icon ? 'icon' : 'text')]\"\n      >\n        <slot name=\"icon\">\n          <el-icon v-if=\"icon\" :class=\"ns.e('icon-inner')\">\n            <component :is=\"icon\" />\n          </el-icon>\n          <el-icon\n            v-else-if=\"currentStatus === 'success'\"\n            :class=\"[ns.e('icon-inner'), ns.is('status')]\"\n          >\n            <Check />\n          </el-icon>\n          <el-icon\n            v-else-if=\"currentStatus === 'error'\"\n            :class=\"[ns.e('icon-inner'), ns.is('status')]\"\n          >\n            <Close />\n          </el-icon>\n          <div v-else-if=\"!isSimple\" :class=\"ns.e('icon-inner')\">\n            {{ index + 1 }}\n          </div>\n        </slot>\n      </div>\n    </div>\n    <!-- title & description -->\n    <div :class=\"ns.e('main')\">\n      <div :class=\"[ns.e('title'), ns.is(currentStatus)]\">\n        <slot name=\"title\">{{ title }}</slot>\n      </div>\n      <div v-if=\"isSimple\" :class=\"ns.e('arrow')\" />\n      <div v-else :class=\"[ns.e('description'), ns.is(currentStatus)]\">\n        <slot name=\"description\">{{ description }}</slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onBeforeUnmount,\n  onMounted,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { Check, Close } from '@element-plus/icons-vue'\nimport { isNumber } from '@element-plus/utils'\nimport { stepProps } from './item'\nimport { STEPS_INJECTION_KEY } from './tokens'\n\nimport type { CSSProperties, Ref, VNode } from 'vue'\nimport type { StepsProps } from './steps'\n\nexport interface StepItemState {\n  uid: number\n  getVnode: () => VNode\n  currentStatus: string\n  setIndex: (val: number) => void\n  calcProgress: (status: string) => void\n}\n\nexport interface IStepsInject {\n  props: StepsProps\n  steps: Ref<StepItemState[]>\n  addStep: (item: StepItemState) => void\n  removeStep: (item: StepItemState) => void\n}\n\ndefineOptions({\n  name: 'ElStep',\n})\n\nconst props = defineProps(stepProps)\nconst ns = useNamespace('step')\nconst index = ref(-1)\nconst lineStyle = ref({})\nconst internalStatus = ref('')\nconst parent = inject(STEPS_INJECTION_KEY) as IStepsInject\nconst currentInstance = getCurrentInstance()!\n\nonMounted(() => {\n  watch(\n    [\n      () => parent.props.active,\n      () => parent.props.processStatus,\n      () => parent.props.finishStatus,\n    ],\n    ([active]) => {\n      updateStatus(active)\n    },\n    { immediate: true }\n  )\n})\n\nconst currentStatus = computed(() => {\n  return props.status || internalStatus.value\n})\n\nconst prevStatus = computed(() => {\n  const prevStep = parent.steps.value[index.value - 1]\n  return prevStep ? prevStep.currentStatus : 'wait'\n})\n\nconst isCenter = computed(() => {\n  return parent.props.alignCenter\n})\n\nconst isVertical = computed(() => {\n  return parent.props.direction === 'vertical'\n})\n\nconst isSimple = computed(() => {\n  return parent.props.simple\n})\n\nconst stepsCount = computed(() => {\n  return parent.steps.value.length\n})\n\nconst isLast = computed(() => {\n  return parent.steps.value[stepsCount.value - 1]?.uid === currentInstance.uid\n})\n\nconst space = computed(() => {\n  return isSimple.value ? '' : parent.props.space\n})\n\nconst containerKls = computed(() => {\n  return [\n    ns.b(),\n    ns.is(isSimple.value ? 'simple' : parent.props.direction),\n    ns.is('flex', isLast.value && !space.value && !isCenter.value),\n    ns.is('center', isCenter.value && !isVertical.value && !isSimple.value),\n  ]\n})\n\nconst style = computed(() => {\n  const style: CSSProperties = {\n    flexBasis: isNumber(space.value)\n      ? `${space.value}px`\n      : space.value\n      ? space.value\n      : `${100 / (stepsCount.value - (isCenter.value ? 0 : 1))}%`,\n  }\n  if (isVertical.value) return style\n  if (isLast.value) {\n    style.maxWidth = `${100 / stepsCount.value}%`\n  }\n  return style\n})\n\nconst setIndex = (val: number) => {\n  index.value = val\n}\n\nconst calcProgress = (status: string) => {\n  const isWait = status === 'wait'\n  const style: CSSProperties = {\n    transitionDelay: `${isWait ? '-' : ''}${150 * index.value}ms`,\n  }\n  const step = status === parent.props.processStatus || isWait ? 0 : 100\n\n  style.borderWidth = step && !isSimple.value ? '1px' : 0\n  style[parent.props.direction === 'vertical' ? 'height' : 'width'] = `${step}%`\n  lineStyle.value = style\n}\n\nconst updateStatus = (activeIndex: number) => {\n  if (activeIndex > index.value) {\n    internalStatus.value = parent.props.finishStatus\n  } else if (activeIndex === index.value && prevStatus.value !== 'error') {\n    internalStatus.value = parent.props.processStatus\n  } else {\n    internalStatus.value = 'wait'\n  }\n  const prevChild = parent.steps.value[index.value - 1]\n  if (prevChild) prevChild.calcProgress(internalStatus.value)\n}\n\nconst stepItemState = reactive({\n  uid: currentInstance.uid,\n  getVnode: () => currentInstance.vnode,\n  currentStatus,\n  setIndex,\n  calcProgress,\n})\n\nparent.addStep(stepItemState)\n\nonBeforeUnmount(() => {\n  parent.removeStep(stepItemState)\n})\n</script>\n"], "names": ["useNamespace", "index", "ref", "inject", "STEPS_INJECTION_KEY", "getCurrentInstance", "onMounted", "watch", "computed", "isNumber", "style", "reactive", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;uCAkFc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAKA,mBAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAAC,OAAA,GAAQC,QAAI,CAAE,CAAA,CAAA,CAAA;AACpB,IAAM,MAAA,SAAA,GAAYA,OAAI,CAAA,EAAE,CAAA,CAAA;AACxB,IAAM,MAAA,cAAA,GAAiBA,QAAI,EAAE,CAAA,CAAA;AAC7B,IAAM,MAAA,MAAA,GAASC,WAAOC,0BAAmB,CAAA,CAAA;AACzC,IAAA,MAAM,kBAAkBC,sBAAmB,EAAA,CAAA;AAE3C,IAAAC,aAAA,CAAU,MAAM;AACd,MAAAC,SAAA,CAAA;AAAA,QACE,MAAA,MAAA,CAAA,KAAA,CAAA,MAAA;AAAA,QACE,MAAA,YAAmB,CAAA,aAAA;AAAA,QACnB,MAAA,YAAmB,CAAA,YAAA;AAAA,OACnB,EAAA,CAAA,CAAA,YAAa;AAAM,QACrB,YAAA,CAAA,MAAA,CAAA,CAAA;AAAA,OACA,EAAC,EAAC,SAAY,EAAA,IAAA,EAAA,CAAA,CAAA;AACZ,KAAA,CAAA,CAAA;AAAmB,IACrB,MAAA,aAAA,GAAAC,YAAA,CAAA,MAAA;AAAA,MACA,mBAAkB,IAAA,cAAA,CAAA,KAAA,CAAA;AAAA,KACpB,CAAA,CAAA;AAAA,IACF,MAAC,UAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAM,MAAA,QAAA,GAAA,YAAyB,CAAM,KAAA,CAAAP,OAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AACnC,MAAO,OAAA,mBAA+B,CAAA,aAAA,GAAA,MAAA,CAAA;AAAA,KACvC,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAAO,mBAA4B;AAChC,MAAA,mBAAwB,CAAA,WAAY,CAAA;AACpC,KAAO,CAAA,CAAA;AAAoC,IAC7C,MAAC,UAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAM,OAAA,MAAA,CAAA,eAA0B,KAAA,UAAA,CAAA;AAC9B,KAAA,CAAA,CAAA;AAAoB,IACtB,MAAC,QAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAM,OAAA,MAAA,CAAA,YAAsB,CAAM;AAChC,KAAO,CAAA,CAAA;AAA2B,IACpC,MAAC,UAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAM,OAAA,MAAA,CAAA,WAA0B,CAAA,MAAA,CAAA;AAC9B,KAAA,CAAA,CAAA;AAAoB,IACtB,MAAC,MAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAM,IAAA,EAAA,CAAA;AACJ,MAAO,OAAA,CAAA,CAAA,EAAA,GAAO,MAAM,CAAM,KAAA,CAAA,KAAA,CAAA,UAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAA,eAAA,CAAA,GAAA,CAAA;AAAA,KAC3B,CAAA,CAAA;AAED,IAAM,MAAA,KAAA,GAAAA,mBAAwB;AAC5B,MAAO,OAAA,cAAmB,GAAA,EAAA,GAAA,MAAA,CAAA,WAAoB,CAAA;AAA2B,KAC1E,CAAA,CAAA;AAED,IAAM,MAAA,2BAAuB,CAAA,MAAA;AAC3B,MAAA,OAAO;AAAmC,QAC3C,EAAA,CAAA,CAAA,EAAA;AAED,QAAM,EAAA,CAAA,EAAA,CAAA,QAAA,CAAA,gBAA8B,GAAA,MAAA,CAAA,KAAA,CAAA,SAAA,CAAA;AAClC,QAAO,EAAA,CAAA,EAAA,CAAA,MAAA,EAAA,MAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA;AAAA,QACL,GAAG,EAAE,CAAA,QAAA,EAAA,QAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,CAAA;AAAA,OAAA,CACL;AAAwD,KACxD,CAAA,CAAA;AAA6D,IAC7D,MAAA,KAAM,GAAAA,YAAmB,CAAA,MAAA;AAA6C,MACxE,MAAA,MAAA,GAAA;AAAA,QACD,SAAA,EAAAC,cAAA,CAAA,KAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA,GAAA,KAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,GAAA,CAAA,EAAA,GAAA,IAAA,UAAA,CAAA,KAAA,IAAA,QAAA,CAAA,KAAA,GAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAED,OAAM,CAAA;AACJ,MAAA,IAAA,UAA6B,CAAA,KAAA;AAAA,QAC3B,OAAA;AAI0D,MAC5D,IAAA,MAAA,CAAA,KAAA,EAAA;AACA,QAAI,MAAA,CAAA,YAAkB,EAAOC,GAAAA,GAAAA,UAAAA,CAAAA,KAAAA,CAAAA,CAAAA,CAAAA,CAAAA;AAC7B,OAAA;AACE,MAAA,OAAM,MAAA,CAAA;AAAoC,KAC5C,CAAA,CAAA;AACA,IAAOA,MAAAA,QAAAA,GAAAA,CAAAA,GAAAA,KAAAA;AAAA,MACRT,OAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAED,KAAM,CAAA;AACJ,IAAA,MAAA,YAAc,GAAA,CAAA,MAAA,KAAA;AAAA,MAChB,MAAA,MAAA,GAAA,MAAA,KAAA,MAAA,CAAA;AAEA,MAAM,MAAA,MAAA,GAAA;AACJ,QAAA,eAA0B,EAAA,CAAA,EAAA,MAAA,GAAA,GAAA,GAAA,EAAA,CAAA,EAAA,GAAA,GAAAA,OAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AAC1B,OAAA,CAAA;AAA6B,MAC3B,MAAA,IAAA,GAAA,MAAiB,KAAY,MAAA,CAAA,KAAA,CAAM,iBAAiB,MAAK,GAAA,CAAA,GAAA,GAAA,CAAA;AAAA,MAC3D,MAAA,CAAA,WAAA,GAAA,IAAA,IAAA,CAAA,QAAA,CAAA,KAAA,GAAA,KAAA,GAAA,CAAA,CAAA;AACA,MAAA,MAAM,OAAO,CAAW,KAAA,CAAA,SAAA,KAAa,UAAA,GAAA,kBAA8B,CAAA,GAAA,CAAA,EAAA,IAAA,CAAA,CAAA,CAAA,CAAA;AAEnE,MAAAS,SAAoB,CAAA,KAAA,GAAA,MAAA,CAAA;AACpB,KAAAA,CAAAA;AACA,IAAA,MAAA,YAAkBA,GAAAA,CAAAA,WAAAA,KAAAA;AAAA,MACpB,IAAA,WAAA,GAAAT,OAAA,CAAA,KAAA,EAAA;AAEA,QAAM,cAAA,CAAA,KAAwC,GAAA,MAAA,CAAA,KAAA,CAAA,YAAA,CAAA;AAC5C,OAAI,MAAA,IAAA,gBAA2BA,OAAA,CAAA,KAAA,IAAA,UAAA,CAAA,KAAA,KAAA,OAAA,EAAA;AAC7B,QAAe,cAAA,CAAA,KAAA,GAAQ,OAAO,KAAM,CAAA,aAAA,CAAA;AAAA;AAEpC,QAAe,cAAA,CAAA,KAAA,GAAQ,OAAO;AAAM,OAC/B;AACL,MAAA,MAAA,SAAA,GAAuB,MAAA,CAAA,KAAA,CAAA,KAAA,CAAAA,OAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA;AAAA,MACzB,IAAA,SAAA;AACA,QAAA,sBAAyB,CAAA,cAAY,CAAA;AACrC,KAAA,CAAA;AAA0D,IAC5D,MAAA,aAAA,GAAAU,YAAA,CAAA;AAEA,MAAA,GAAA,iBAAsB,CAAS,GAAA;AAAA,MAC7B,QAAqB,EAAA,MAAA,eAAA,CAAA,KAAA;AAAA,MACrB;AAAgC,MAChC,QAAA;AAAA,MACA,YAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACF,MAAC,CAAA,OAAA,CAAA,aAAA,CAAA,CAAA;AAED,IAAAC,mBAAe,CAAa,MAAA;AAE5B,MAAA,MAAA,CAAA,UAAsB,CAAA,aAAA,CAAA,CAAA;AACpB,KAAA,CAAA,CAAA;AAA+B,IACjC,OAAC,CAAA,IAAA,EAAA,MAAA,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}