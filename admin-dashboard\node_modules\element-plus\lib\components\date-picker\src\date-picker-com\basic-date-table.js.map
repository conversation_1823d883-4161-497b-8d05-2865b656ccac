{"version": 3, "file": "basic-date-table.js", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/basic-date-table.vue"], "sourcesContent": ["<template>\n  <table\n    :aria-label=\"tableLabel\"\n    :class=\"tableKls\"\n    cellspacing=\"0\"\n    cellpadding=\"0\"\n    role=\"grid\"\n    @click=\"handlePickDate\"\n    @mousemove=\"handleMouseMove\"\n    @mousedown.prevent=\"handleMouseDown\"\n    @mouseup=\"handleMouseUp\"\n  >\n    <tbody ref=\"tbodyRef\">\n      <tr>\n        <th v-if=\"showWeekNumber\" scope=\"col\" :class=\"weekHeaderClass\" />\n        <th\n          v-for=\"(week, key) in WEEKS\"\n          :key=\"key\"\n          :aria-label=\"t('el.datepicker.weeksFull.' + week)\"\n          scope=\"col\"\n        >\n          {{ t('el.datepicker.weeks.' + week) }}\n        </th>\n      </tr>\n      <tr\n        v-for=\"(row, rowKey) in rows\"\n        :key=\"rowKey\"\n        :class=\"getRowKls(row[1])\"\n      >\n        <td\n          v-for=\"(cell, columnKey) in row\"\n          :key=\"`${rowKey}.${columnKey}`\"\n          :ref=\"(el) => !isUnmounting && isSelectedCell(cell) && (currentCellRef = el as HTMLElement)\"\n          :class=\"getCellClasses(cell)\"\n          :aria-current=\"cell.isCurrent ? 'date' : undefined\"\n          :aria-selected=\"cell.isCurrent\"\n          :tabindex=\"isSelectedCell(cell) ? 0 : -1\"\n          @focus=\"handleFocus\"\n        >\n          <el-date-picker-cell :cell=\"cell\" />\n        </td>\n      </tr>\n    </tbody>\n  </table>\n</template>\n\n<script lang=\"ts\" setup>\nimport { onBeforeUnmount } from 'vue'\nimport {\n  basicDateTableEmits,\n  basicDateTableProps,\n} from '../props/basic-date-table'\nimport {\n  useBasicDateTable,\n  useBasicDateTableDOM,\n} from '../composables/use-basic-date-table'\nimport ElDatePickerCell from './basic-cell-render'\n\nconst props = defineProps(basicDateTableProps)\nconst emit = defineEmits(basicDateTableEmits)\n\nconst {\n  WEEKS,\n  rows,\n  tbodyRef,\n  currentCellRef,\n\n  focus,\n  isCurrent,\n  isWeekActive,\n  isSelectedCell,\n\n  handlePickDate,\n  handleMouseUp,\n  handleMouseDown,\n  handleMouseMove,\n  handleFocus,\n} = useBasicDateTable(props, emit)\nconst { tableLabel, tableKls, getCellClasses, getRowKls, weekHeaderClass, t } =\n  useBasicDateTableDOM(props, {\n    isCurrent,\n    isWeekActive,\n  })\nlet isUnmounting = false\n\nonBeforeUnmount(() => {\n  isUnmounting = true\n})\n\ndefineExpose({\n  /**\n   * @description focus on current cell\n   */\n  focus,\n})\n</script>\n"], "names": ["useBasicDateTable", "useBasicDateTableDOM", "onBeforeUnmount", "_openBlock", "_createElementBlock", "_unref"], "mappings": ";;;;;;;;;;;;;;;;AA6DA,IAAM,MAAA;AAAA,MACJ,KAAA;AAAA,MACA,IAAA;AAAA,MACA,QAAA;AAAA,MACA,cAAA;AAAA,MAEA,KAAA;AAAA,MACA,SAAA;AAAA,MACA,YAAA;AAAA,MACA,cAAA;AAAA,MAEA,cAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,WAAA;AAAA,KACF,GAAIA,mCAAkB,CAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AACjC,IAAM,MAAA,EAAE,YAAY,QAAU,EAAA,cAAA,EAAgB,WAAW,eAAiB,EAAA,CAAA,EACxE,GAAAC,sCAAA,CAAqB,KAAO,EAAA;AAAA,MAC1B,SAAA;AAAA,MACA,YAAA;AAAA,KACD,CAAA,CAAA;AACH,IAAA,IAAI,YAAe,GAAA,KAAA,CAAA;AAEnB,IAAAC,mBAAA,CAAgB,MAAM;AACpB,MAAe,YAAA,GAAA,IAAA,CAAA;AAAA,KAChB,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MAAA,KAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAIX,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,OAAA,EAAA;AAAA,QACD,YAAA,EAAAC,SAAA,CAAA,UAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}