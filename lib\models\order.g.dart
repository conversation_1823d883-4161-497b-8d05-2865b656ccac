// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class OrderAdapter extends TypeAdapter<Order> {
  @override
  final int typeId = 25;

  @override
  Order read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Order(
      id: fields[0] as String,
      userId: fields[1] as String,
      packageId: fields[2] as String,
      packageName: fields[3] as String,
      amount: fields[4] as double,
      status: fields[5] as OrderStatus,
      paymentMethod: fields[6] as PaymentMethod?,
      createdAt: fields[7] as DateTime,
      updatedAt: fields[8] as DateTime,
      paidAt: fields[9] as DateTime?,
      transactionId: fields[10] as String?,
      memberCode: fields[11] as String?,
      expireAt: fields[12] as DateTime?,
      metadata: (fields[13] as Map?)?.cast<String, dynamic>(),
    );
  }

  @override
  void write(BinaryWriter writer, Order obj) {
    writer
      ..writeByte(14)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.userId)
      ..writeByte(2)
      ..write(obj.packageId)
      ..writeByte(3)
      ..write(obj.packageName)
      ..writeByte(4)
      ..write(obj.amount)
      ..writeByte(5)
      ..write(obj.status)
      ..writeByte(6)
      ..write(obj.paymentMethod)
      ..writeByte(7)
      ..write(obj.createdAt)
      ..writeByte(8)
      ..write(obj.updatedAt)
      ..writeByte(9)
      ..write(obj.paidAt)
      ..writeByte(10)
      ..write(obj.transactionId)
      ..writeByte(11)
      ..write(obj.memberCode)
      ..writeByte(12)
      ..write(obj.expireAt)
      ..writeByte(13)
      ..write(obj.metadata);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class OrderStatusAdapter extends TypeAdapter<OrderStatus> {
  @override
  final int typeId = 23;

  @override
  OrderStatus read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return OrderStatus.pending;
      case 1:
        return OrderStatus.paid;
      case 2:
        return OrderStatus.cancelled;
      case 3:
        return OrderStatus.refunded;
      case 4:
        return OrderStatus.expired;
      default:
        return OrderStatus.pending;
    }
  }

  @override
  void write(BinaryWriter writer, OrderStatus obj) {
    switch (obj) {
      case OrderStatus.pending:
        writer.writeByte(0);
        break;
      case OrderStatus.paid:
        writer.writeByte(1);
        break;
      case OrderStatus.cancelled:
        writer.writeByte(2);
        break;
      case OrderStatus.refunded:
        writer.writeByte(3);
        break;
      case OrderStatus.expired:
        writer.writeByte(4);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderStatusAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class PaymentMethodAdapter extends TypeAdapter<PaymentMethod> {
  @override
  final int typeId = 24;

  @override
  PaymentMethod read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return PaymentMethod.wechat;
      case 1:
        return PaymentMethod.alipay;
      case 2:
        return PaymentMethod.memberCode;
      default:
        return PaymentMethod.wechat;
    }
  }

  @override
  void write(BinaryWriter writer, PaymentMethod obj) {
    switch (obj) {
      case PaymentMethod.wechat:
        writer.writeByte(0);
        break;
      case PaymentMethod.alipay:
        writer.writeByte(1);
        break;
      case PaymentMethod.memberCode:
        writer.writeByte(2);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PaymentMethodAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Order _$OrderFromJson(Map<String, dynamic> json) => Order(
      id: json['id'] as String,
      userId: json['userId'] as String,
      packageId: json['packageId'] as String,
      packageName: json['packageName'] as String,
      amount: (json['amount'] as num).toDouble(),
      status: $enumDecodeNullable(_$OrderStatusEnumMap, json['status']) ??
          OrderStatus.pending,
      paymentMethod:
          $enumDecodeNullable(_$PaymentMethodEnumMap, json['paymentMethod']),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      paidAt: json['paidAt'] == null
          ? null
          : DateTime.parse(json['paidAt'] as String),
      transactionId: json['transactionId'] as String?,
      memberCode: json['memberCode'] as String?,
      expireAt: json['expireAt'] == null
          ? null
          : DateTime.parse(json['expireAt'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$OrderToJson(Order instance) => <String, dynamic>{
      'id': instance.id,
      'userId': instance.userId,
      'packageId': instance.packageId,
      'packageName': instance.packageName,
      'amount': instance.amount,
      'status': _$OrderStatusEnumMap[instance.status]!,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod],
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'paidAt': instance.paidAt?.toIso8601String(),
      'transactionId': instance.transactionId,
      'memberCode': instance.memberCode,
      'expireAt': instance.expireAt?.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$OrderStatusEnumMap = {
  OrderStatus.pending: 'pending',
  OrderStatus.paid: 'paid',
  OrderStatus.cancelled: 'cancelled',
  OrderStatus.refunded: 'refunded',
  OrderStatus.expired: 'expired',
};

const _$PaymentMethodEnumMap = {
  PaymentMethod.wechat: 'wechat',
  PaymentMethod.alipay: 'alipay',
  PaymentMethod.memberCode: 'memberCode',
};

PaymentRequest _$PaymentRequestFromJson(Map<String, dynamic> json) =>
    PaymentRequest(
      orderId: json['orderId'] as String,
      paymentMethod: $enumDecode(_$PaymentMethodEnumMap, json['paymentMethod']),
      memberCode: json['memberCode'] as String?,
    );

Map<String, dynamic> _$PaymentRequestToJson(PaymentRequest instance) =>
    <String, dynamic>{
      'orderId': instance.orderId,
      'paymentMethod': _$PaymentMethodEnumMap[instance.paymentMethod]!,
      'memberCode': instance.memberCode,
    };

PaymentResponse _$PaymentResponseFromJson(Map<String, dynamic> json) =>
    PaymentResponse(
      success: json['success'] as bool,
      paymentUrl: json['paymentUrl'] as String?,
      qrCode: json['qrCode'] as String?,
      paymentData: json['paymentData'] as Map<String, dynamic>?,
      message: json['message'] as String?,
    );

Map<String, dynamic> _$PaymentResponseToJson(PaymentResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'paymentUrl': instance.paymentUrl,
      'qrCode': instance.qrCode,
      'paymentData': instance.paymentData,
      'message': instance.message,
    };
