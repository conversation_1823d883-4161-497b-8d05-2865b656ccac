#!/usr/bin/env node

const tcb = require('@cloudbase/node-sdk');

// 初始化CloudBase SDK
const app = tcb.init({
  env: process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

// 默认套餐数据
const defaultPackages = [
  {
    id: 'pkg_permanent',
    name: '永久会员',
    price: 99.00,
    unit: '一次性',
    features: ['无限制创作', '数据云同步', '高级AI功能', '专属客服'],
    isPopular: true,
    isActive: true,
    soldCount: 0,
    activeUsers: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'pkg_monthly',
    name: '月会员',
    price: 19.90,
    unit: '/月',
    features: ['无限制创作', '数据云同步', '基础AI功能'],
    isPopular: false,
    isActive: true,
    soldCount: 0,
    activeUsers: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    id: 'pkg_yearly',
    name: '年会员',
    price: 199.00,
    unit: '/年',
    features: ['无限制创作', '数据云同步', '高级AI功能', '优先客服'],
    isPopular: false,
    isActive: true,
    soldCount: 0,
    activeUsers: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// 初始化套餐数据
async function initializePackages() {
  try {
    console.log('🚀 开始初始化CloudBase套餐数据...');

    // 检查是否已有套餐数据
    const existingPackagesResult = await db.collection('packages').get();
    const existingPackages = existingPackagesResult.data || [];

    console.log(`📊 现有套餐数量: ${existingPackages.length}`);

    if (existingPackages.length > 0) {
      console.log('⚠️  套餐数据已存在，跳过初始化');
      console.log('现有套餐:');
      existingPackages.forEach(pkg => {
        console.log(`  - ${pkg.name} (${pkg.price}元${pkg.unit})`);
      });
      return;
    }

    // 添加默认套餐
    console.log('📦 添加默认套餐数据...');
    
    for (const packageData of defaultPackages) {
      try {
        const result = await db.collection('packages').add(packageData);
        console.log(`✅ 套餐添加成功: ${packageData.name} (ID: ${result.id})`);
      } catch (error) {
        console.error(`❌ 套餐添加失败: ${packageData.name}`, error);
      }
    }

    console.log('🎉 套餐数据初始化完成！');

    // 验证初始化结果
    const finalPackagesResult = await db.collection('packages').get();
    const finalPackages = finalPackagesResult.data || [];
    console.log(`📊 最终套餐数量: ${finalPackages.length}`);

  } catch (error) {
    console.error('❌ 初始化套餐数据失败:', error);
  }
}

// 清空套餐数据（危险操作，仅用于开发测试）
async function clearPackages() {
  try {
    console.log('⚠️  开始清空套餐数据...');
    
    const packagesResult = await db.collection('packages').get();
    const packages = packagesResult.data || [];
    
    console.log(`📊 找到 ${packages.length} 个套餐需要删除`);
    
    for (const pkg of packages) {
      await db.collection('packages').doc(pkg._id).remove();
      console.log(`🗑️  已删除套餐: ${pkg.name}`);
    }
    
    console.log('✅ 套餐数据清空完成');
  } catch (error) {
    console.error('❌ 清空套餐数据失败:', error);
  }
}

// 命令行参数处理
const args = process.argv.slice(2);
const command = args[0];

async function main() {
  switch (command) {
    case 'init':
      await initializePackages();
      break;
    case 'clear':
      await clearPackages();
      break;
    case 'reset':
      await clearPackages();
      await initializePackages();
      break;
    default:
      console.log('📖 使用说明:');
      console.log('  node init-packages.js init   - 初始化默认套餐数据');
      console.log('  node init-packages.js clear  - 清空所有套餐数据');
      console.log('  node init-packages.js reset  - 重置套餐数据（清空后重新初始化）');
      break;
  }
}

main().then(() => {
  console.log('🏁 操作完成');
  process.exit(0);
}).catch(error => {
  console.error('💥 操作失败:', error);
  process.exit(1);
});
