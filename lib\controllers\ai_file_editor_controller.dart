import 'dart:convert';
import 'package:get/get.dart';
import '../services/file_edit_service.dart';
import '../services/smart_composer_service.dart';
import '../models/smart_composer_models.dart';

/// AI文件编辑控制器
/// 管理AI驱动的文件编辑功能
class AIFileEditorController extends GetxController {
  final FileEditService _fileEditService = FileEditService();
  final SmartComposerService _smartComposerService = SmartComposerService();

  // 响应式状态
  final RxString currentFilePath = ''.obs;
  final RxString currentFileContent = ''.obs;
  final RxBool isEditing = false.obs;
  final RxList<FileEditResult> editHistory = <FileEditResult>[].obs;
  final RxList<PendingFileEdit> pendingEdits = <PendingFileEdit>[].obs;

  /// 打开文件进行编辑
  Future<bool> openFile(String filePath) async {
    try {
      final content = await _fileEditService.readFileContent(filePath);
      if (content == null) {
        Get.snackbar('错误', '无法读取文件: $filePath');
        return false;
      }

      currentFilePath.value = filePath;
      currentFileContent.value = content;
      return true;
    } catch (e) {
      Get.snackbar('错误', '打开文件失败: $e');
      return false;
    }
  }

  /// 请求AI编辑文件
  Future<void> requestAIEdit({
    required String instruction,
    required ChatModel model,
    required LLMProvider provider,
    String? selectedText,
    int? startLine,
    int? endLine,
  }) async {
    if (currentFilePath.value.isEmpty) {
      Get.snackbar('错误', '请先打开一个文件');
      return;
    }

    isEditing.value = true;

    try {
      // 构建编辑提示
      final editPrompt = _buildEditPrompt(
        instruction: instruction,
        fileContent: currentFileContent.value,
        filePath: currentFilePath.value,
        selectedText: selectedText,
        startLine: startLine,
        endLine: endLine,
      );

      // 发送到AI服务
      final response = await _smartComposerService.sendChatMessage(
        model: model,
        provider: provider,
        messages: [
          ChatMessage(
            id: DateTime.now().millisecondsSinceEpoch.toString(),
            role: 'user',
            content: editPrompt,
            timestamp: DateTime.now(),
          ),
        ],
        systemPrompt: _getFileEditSystemPrompt(),
        temperature: 0.3, // 较低的温度以确保准确性
      );

      // 解析AI响应
      final editResult = _parseAIEditResponse(response);
      if (editResult != null) {
        // 添加到待处理编辑列表
        pendingEdits.add(PendingFileEdit(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          filePath: currentFilePath.value,
          originalContent: currentFileContent.value,
          modifiedContent: editResult.modifiedContent,
          instruction: instruction,
          description: editResult.description,
          selectedText: selectedText,
          startLine: startLine,
          endLine: endLine,
          timestamp: DateTime.now(),
        ));
      }
    } catch (e) {
      Get.snackbar('错误', 'AI编辑请求失败: $e');
    } finally {
      isEditing.value = false;
    }
  }

  /// 应用待处理的编辑
  Future<void> applyPendingEdit(String editId) async {
    final edit = pendingEdits.firstWhereOrNull((e) => e.id == editId);
    if (edit == null) return;

    try {
      final result = await _fileEditService.applyFileEdit(
        filePath: edit.filePath,
        originalContent: edit.originalContent,
        modifiedContent: edit.modifiedContent,
        description: edit.description,
      );

      if (result.isSuccess) {
        // 更新当前文件内容
        currentFileContent.value = edit.modifiedContent;
        
        // 添加到编辑历史
        editHistory.add(result);
        
        // 从待处理列表中移除
        pendingEdits.removeWhere((e) => e.id == editId);
        
        Get.snackbar('成功', '文件修改已应用');
      } else {
        Get.snackbar('错误', result.description);
      }
    } catch (e) {
      Get.snackbar('错误', '应用编辑失败: $e');
    }
  }

  /// 拒绝待处理的编辑
  void rejectPendingEdit(String editId) {
    pendingEdits.removeWhere((e) => e.id == editId);
    Get.snackbar('已拒绝', '编辑建议已被拒绝');
  }

  /// 撤销最近的编辑
  Future<void> undoLastEdit() async {
    if (editHistory.isEmpty) {
      Get.snackbar('提示', '没有可撤销的编辑');
      return;
    }

    final lastEdit = editHistory.last;
    final success = await _fileEditService.undoFileEdit(lastEdit);
    
    if (success) {
      currentFileContent.value = lastEdit.originalContent;
      editHistory.removeLast();
      Get.snackbar('成功', '编辑已撤销');
    } else {
      Get.snackbar('错误', '撤销编辑失败');
    }
  }

  /// 构建编辑提示
  String _buildEditPrompt({
    required String instruction,
    required String fileContent,
    required String filePath,
    String? selectedText,
    int? startLine,
    int? endLine,
  }) {
    final buffer = StringBuffer();
    
    buffer.writeln('请根据以下指令编辑文件内容：');
    buffer.writeln('');
    buffer.writeln('文件路径: $filePath');
    buffer.writeln('编辑指令: $instruction');
    buffer.writeln('');
    
    if (selectedText != null) {
      buffer.writeln('选中的文本:');
      buffer.writeln('```');
      buffer.writeln(selectedText);
      buffer.writeln('```');
      buffer.writeln('');
    }
    
    if (startLine != null && endLine != null) {
      buffer.writeln('编辑范围: 第${startLine}行到第${endLine}行');
      buffer.writeln('');
    }
    
    buffer.writeln('当前文件内容:');
    buffer.writeln('```');
    buffer.writeln(fileContent);
    buffer.writeln('```');
    
    return buffer.toString();
  }

  /// 获取文件编辑系统提示
  String _getFileEditSystemPrompt() {
    return '''你是一个专业的代码编辑助手，类似于Cursor IDE的AI功能。

你的任务是根据用户的指令精确地编辑文件内容。请遵循以下规则：

1. 仔细理解用户的编辑指令
2. 只修改需要修改的部分，保持其他内容不变
3. 确保修改后的代码语法正确且逻辑合理
4. 保持原有的代码风格和格式
5. 如果是文本文件，保持原有的格式和结构

请以以下JSON格式返回你的编辑结果：
{
  "modified_content": "修改后的完整文件内容",
  "description": "简短描述你做了什么修改",
  "changes_summary": "修改摘要"
}

重要：只返回JSON格式的响应，不要包含其他文本。''';
  }

  /// 解析AI编辑响应
  AIEditResult? _parseAIEditResponse(String response) {
    try {
      // 尝试从响应中提取JSON
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}');

      if (jsonStart == -1 || jsonEnd == -1) {
        // 如果没有找到JSON格式，尝试直接使用响应内容
        return AIEditResult(
          modifiedContent: response.trim(),
          description: '文件已根据指令进行修改',
        );
      }

      final jsonStr = response.substring(jsonStart, jsonEnd + 1);

      try {
        // 清理Grok模型的<think>标签
        String cleanedJsonStr = _cleanThinkTags(jsonStr);

        // 使用dart:convert进行JSON解析
        final Map<String, dynamic> jsonData = json.decode(cleanedJsonStr);

        final modifiedContent = jsonData['modified_content'] as String?;
        final description = jsonData['description'] as String?;

        if (modifiedContent != null && description != null) {
          return AIEditResult(
            modifiedContent: modifiedContent,
            description: description,
          );
        }
      } catch (jsonError) {
        // JSON解析失败，尝试简单的字符串提取
        final modifiedContent = _extractJsonValue(jsonStr, 'modified_content');
        final description = _extractJsonValue(jsonStr, 'description');

        if (modifiedContent != null && description != null) {
          return AIEditResult(
            modifiedContent: modifiedContent,
            description: description,
          );
        }
      }

      // 如果所有解析都失败，返回原始响应
      return AIEditResult(
        modifiedContent: response.trim(),
        description: '文件已根据指令进行修改',
      );
    } catch (e) {
      print('解析AI响应失败: $e');
      // 返回原始响应作为备选
      return AIEditResult(
        modifiedContent: response.trim(),
        description: '文件已根据指令进行修改（解析失败）',
      );
    }
  }

  /// 提取JSON值（简单实现）
  String? _extractJsonValue(String json, String key) {
    final pattern = RegExp('"$key"\\s*:\\s*"([^"]*)"');
    final match = pattern.firstMatch(json);
    return match?.group(1);
  }

  /// 清空编辑历史
  void clearEditHistory() {
    editHistory.clear();
    Get.snackbar('已清空', '编辑历史已清空');
  }

  /// 清空待处理编辑
  void clearPendingEdits() {
    pendingEdits.clear();
    Get.snackbar('已清空', '待处理编辑已清空');
  }

  /// 清理Grok模型输出的<think>标签
  String _cleanThinkTags(String content) {
    if (content.isEmpty) return content;

    String cleaned = content;

    // 移除<think>...</think>标签及其内容
    cleaned = cleaned.replaceAll(RegExp(r'<think>.*?</think>', dotAll: true), '');

    // 处理未闭合的<think>标签：查找<think>标签的位置
    final thinkStartIndex = cleaned.indexOf('<think>');
    if (thinkStartIndex != -1) {
      // 如果找到<think>标签，尝试保留标签前的内容
      final beforeThink = cleaned.substring(0, thinkStartIndex).trim();
      if (beforeThink.isNotEmpty) {
        cleaned = beforeThink;
      } else {
        // 如果<think>标签前没有内容，尝试查找标签后是否有有效的JSON
        final afterThink = cleaned.substring(thinkStartIndex + 7); // 跳过<think>
        final jsonMatch = RegExp(r'(\[.*\]|\{.*\})', dotAll: true).firstMatch(afterThink);
        if (jsonMatch != null) {
          cleaned = jsonMatch.group(1)!;
        } else {
          // 如果都没有，移除整个<think>部分
          cleaned = cleaned.replaceAll(RegExp(r'<think>.*$', dotAll: true), '');
        }
      }
    }

    // 清理多余的空白字符
    cleaned = cleaned.trim();

    // 如果清理后内容为空，返回原内容（避免过度清理）
    if (cleaned.isEmpty) {
      print("[AIFileEditorController] 警告：清理<think>标签后内容为空，返回原内容");
      return content;
    }

    if (cleaned != content) {
      print("[AIFileEditorController] 已清理<think>标签，原长度: ${content.length}, 清理后长度: ${cleaned.length}");
    }
    return cleaned;
  }
}

/// 待处理的文件编辑
class PendingFileEdit {
  final String id;
  final String filePath;
  final String originalContent;
  final String modifiedContent;
  final String instruction;
  final String description;
  final String? selectedText;
  final int? startLine;
  final int? endLine;
  final DateTime timestamp;

  PendingFileEdit({
    required this.id,
    required this.filePath,
    required this.originalContent,
    required this.modifiedContent,
    required this.instruction,
    required this.description,
    this.selectedText,
    this.startLine,
    this.endLine,
    required this.timestamp,
  });
}

/// AI编辑结果
class AIEditResult {
  final String modifiedContent;
  final String description;

  AIEditResult({
    required this.modifiedContent,
    required this.description,
  });
}
