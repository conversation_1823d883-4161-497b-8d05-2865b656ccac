{"version": 3, "file": "slider.mjs", "sources": ["../../../../../../packages/components/slider/src/slider.vue"], "sourcesContent": ["<template>\n  <div\n    :id=\"range ? inputId : undefined\"\n    ref=\"sliderWrapper\"\n    :class=\"sliderKls\"\n    :role=\"range ? 'group' : undefined\"\n    :aria-label=\"range && !isLabeledByFormItem ? groupLabel : undefined\"\n    :aria-labelledby=\"\n      range && isLabeledByFormItem ? elFormItem?.labelId : undefined\n    \"\n  >\n    <div\n      ref=\"slider\"\n      :class=\"[\n        ns.e('runway'),\n        { 'show-input': showInput && !range },\n        ns.is('disabled', sliderDisabled),\n      ]\"\n      :style=\"runwayStyle\"\n      @mousedown=\"onSliderDown\"\n      @touchstart.passive=\"onSliderDown\"\n    >\n      <div :class=\"ns.e('bar')\" :style=\"barStyle\" />\n      <slider-button\n        :id=\"!range ? inputId : undefined\"\n        ref=\"firstButton\"\n        :model-value=\"firstValue\"\n        :vertical=\"vertical\"\n        :tooltip-class=\"tooltipClass\"\n        :placement=\"placement\"\n        role=\"slider\"\n        :aria-label=\"\n          range || !isLabeledByFormItem ? firstButtonLabel : undefined\n        \"\n        :aria-labelledby=\"\n          !range && isLabeledByFormItem ? elFormItem?.labelId : undefined\n        \"\n        :aria-valuemin=\"min\"\n        :aria-valuemax=\"range ? secondValue : max\"\n        :aria-valuenow=\"firstValue\"\n        :aria-valuetext=\"firstValueText\"\n        :aria-orientation=\"vertical ? 'vertical' : 'horizontal'\"\n        :aria-disabled=\"sliderDisabled\"\n        @update:model-value=\"setFirstValue\"\n      />\n      <slider-button\n        v-if=\"range\"\n        ref=\"secondButton\"\n        :model-value=\"secondValue\"\n        :vertical=\"vertical\"\n        :tooltip-class=\"tooltipClass\"\n        :placement=\"placement\"\n        role=\"slider\"\n        :aria-label=\"secondButtonLabel\"\n        :aria-valuemin=\"firstValue\"\n        :aria-valuemax=\"max\"\n        :aria-valuenow=\"secondValue\"\n        :aria-valuetext=\"secondValueText\"\n        :aria-orientation=\"vertical ? 'vertical' : 'horizontal'\"\n        :aria-disabled=\"sliderDisabled\"\n        @update:model-value=\"setSecondValue\"\n      />\n      <div v-if=\"showStops\">\n        <div\n          v-for=\"(item, key) in stops\"\n          :key=\"key\"\n          :class=\"ns.e('stop')\"\n          :style=\"getStopStyle(item)\"\n        />\n      </div>\n      <template v-if=\"markList.length > 0\">\n        <div>\n          <div\n            v-for=\"(item, key) in markList\"\n            :key=\"key\"\n            :style=\"getStopStyle(item.position)\"\n            :class=\"[ns.e('stop'), ns.e('marks-stop')]\"\n          />\n        </div>\n        <div :class=\"ns.e('marks')\">\n          <slider-marker\n            v-for=\"(item, key) in markList\"\n            :key=\"key\"\n            :mark=\"item.mark\"\n            :style=\"getStopStyle(item.position)\"\n            @mousedown.stop=\"onSliderMarkerDown(item.position)\"\n          />\n        </div>\n      </template>\n    </div>\n    <el-input-number\n      v-if=\"showInput && !range\"\n      ref=\"input\"\n      :model-value=\"firstValue\"\n      :class=\"ns.e('input')\"\n      :step=\"step\"\n      :disabled=\"sliderDisabled\"\n      :controls=\"showInputControls\"\n      :min=\"min\"\n      :max=\"max\"\n      :precision=\"precision\"\n      :debounce=\"debounce\"\n      :size=\"sliderInputSize\"\n      @update:model-value=\"setFirstValue\"\n      @change=\"emitChange\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, reactive, toRefs } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport ElInputNumber from '@element-plus/components/input-number'\nimport { useFormItemInputId, useFormSize } from '@element-plus/components/form'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { sliderContextKey } from './constants'\nimport { sliderEmits, sliderProps } from './slider'\nimport SliderButton from './button.vue'\nimport SliderMarker from './marker'\nimport {\n  useLifecycle,\n  useMarks,\n  useSlide,\n  useStops,\n  useWatch,\n} from './composables'\n\nimport type { SliderInitData } from './slider'\n\ndefineOptions({\n  name: 'ElSlider',\n})\n\nconst props = defineProps(sliderProps)\nconst emit = defineEmits(sliderEmits)\n\nconst ns = useNamespace('slider')\nconst { t } = useLocale()\n\nconst initData = reactive<SliderInitData>({\n  firstValue: 0,\n  secondValue: 0,\n  oldValue: 0,\n  dragging: false,\n  sliderSize: 1,\n})\n\nconst {\n  elFormItem,\n  slider,\n  firstButton,\n  secondButton,\n  sliderDisabled,\n  minValue,\n  maxValue,\n  runwayStyle,\n  barStyle,\n  resetSize,\n  emitChange,\n  onSliderWrapperPrevent,\n  onSliderClick,\n  onSliderDown,\n  onSliderMarkerDown,\n  setFirstValue,\n  setSecondValue,\n} = useSlide(props, initData, emit)\n\nconst { stops, getStopStyle } = useStops(props, initData, minValue, maxValue)\n\nconst { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: elFormItem,\n})\n\nconst sliderWrapperSize = useFormSize()\nconst sliderInputSize = computed(\n  () => props.inputSize || sliderWrapperSize.value\n)\n\nconst groupLabel = computed<string>(() => {\n  return (\n    props.ariaLabel ||\n    t('el.slider.defaultLabel', {\n      min: props.min,\n      max: props.max,\n    })\n  )\n})\n\nconst firstButtonLabel = computed<string>(() => {\n  if (props.range) {\n    return props.rangeStartLabel || t('el.slider.defaultRangeStartLabel')\n  } else {\n    return groupLabel.value\n  }\n})\n\nconst firstValueText = computed<string>(() => {\n  return props.formatValueText\n    ? props.formatValueText(firstValue.value)\n    : `${firstValue.value}`\n})\n\nconst secondButtonLabel = computed<string>(() => {\n  return props.rangeEndLabel || t('el.slider.defaultRangeEndLabel')\n})\n\nconst secondValueText = computed<string>(() => {\n  return props.formatValueText\n    ? props.formatValueText(secondValue.value)\n    : `${secondValue.value}`\n})\n\nconst sliderKls = computed(() => [\n  ns.b(),\n  ns.m(sliderWrapperSize.value),\n  ns.is('vertical', props.vertical),\n  { [ns.m('with-input')]: props.showInput },\n])\n\nconst markList = useMarks(props)\n\nuseWatch(props, initData, minValue, maxValue, emit, elFormItem!)\n\nconst precision = computed(() => {\n  const precisions = [props.min, props.max, props.step].map((item) => {\n    const decimal = `${item}`.split('.')[1]\n    return decimal ? decimal.length : 0\n  })\n  return Math.max.apply(null, precisions)\n})\n\nconst { sliderWrapper } = useLifecycle(props, initData, resetSize)\n\nconst { firstValue, secondValue, sliderSize } = toRefs(initData)\n\nconst updateDragging = (val: boolean) => {\n  initData.dragging = val\n}\n\nuseEventListener(sliderWrapper, 'touchstart', onSliderWrapperPrevent, {\n  passive: false,\n})\nuseEventListener(sliderWrapper, 'touchmove', onSliderWrapperPrevent, {\n  passive: false,\n})\n\nprovide(sliderContextKey, {\n  ...toRefs(props),\n  sliderSize,\n  disabled: sliderDisabled,\n  precision,\n  emitChange,\n  resetSize,\n  updateDragging,\n})\n\ndefineExpose({\n  onSliderClick,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;mCAiIc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AAExB,IAAA,MAAM,WAAW,QAAyB,CAAA;AAAA,MACxC,UAAY,EAAA,CAAA;AAAA,MACZ,WAAa,EAAA,CAAA;AAAA,MACb,QAAU,EAAA,CAAA;AAAA,MACV,QAAU,EAAA,KAAA;AAAA,MACV,UAAY,EAAA,CAAA;AAAA,KACb,CAAA,CAAA;AAED,IAAM,MAAA;AAAA,MACJ,UAAA;AAAA,MACA,MAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,cAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,sBAAA;AAAA,MACA,aAAA;AAAA,MACA,YAAA;AAAA,MACA,kBAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,KACE,GAAA,QAAA,CAAS,KAAO,EAAA,QAAA,EAAU,IAAI,CAAA,CAAA;AAElC,IAAM,MAAA,EAAE,OAAO,YAAa,EAAA,GAAI,SAAS,KAAO,EAAA,QAAA,EAAU,UAAU,QAAQ,CAAA,CAAA;AAE5E,IAAA,MAAM,EAAE,OAAA,EAAS,mBAAoB,EAAA,GAAI,mBAAmB,KAAO,EAAA;AAAA,MACjE,eAAiB,EAAA,UAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAA,MAAM,oBAAoB,WAAY,EAAA,CAAA;AACtC,IAAA,MAAM,eAAkB,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,SAAA,IAAA,iBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,IACtB,MAAA,UAAY,GAAA,QAAa,CAAkB,MAAA;AAAA,MAC7C,OAAA,KAAA,CAAA,SAAA,IAAA,CAAA,CAAA,wBAAA,EAAA;AAEA,QAAM,GAAA,EAAA,KAAA,CAAA,GAAA;AACJ,QACE,GAAA,EAAA,KAAA,CAAM,GACN;AAA4B,OAAA,CAC1B;AAAW,KAAA,CAAA,CAAA;AACA,IAAA,MACZ,gBAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MAEJ,IAAA,KAAA,CAAA,KAAA,EAAA;AAED,QAAM,OAAA,KAAA,CAAA,mBAA0C,CAAA,CAAA,kCAAA,CAAA,CAAA;AAC9C,OAAA;AACE,QAAO,OAAA,UAAyB,CAAA,KAAA,CAAA;AAAoC,OAC/D;AACL,KAAA,CAAA,CAAA;AAAkB,IACpB,MAAA,cAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,OAAA,KAAA,CAAA,eAAA,GAAA,KAAA,CAAA,eAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,4BACG,CAAA,MAAA;AACa,MACxB,OAAA,KAAA,CAAA,aAAA,IAAA,CAAA,CAAA,gCAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,eAAuB,GAAA,QAAA,CAAA,MAAkC;AAAA,MACjE,OAAA,KAAA,CAAA,eAAA,GAAA,KAAA,CAAA,eAAA,CAAA,WAAA,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA,WAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,SAAM,kBACT;AACoB,MACzB,EAAA,CAAA,CAAA,EAAA;AAED,MAAM,EAAA,CAAA,CAAA,CAAA,uBAA2B,CAAA;AAAA,MAC/B,GAAG,EAAE,CAAA,UAAA,EAAA,KAAA,CAAA,QAAA,CAAA;AAAA,MACL,EAAA,CAAG,EAAE,CAAA,CAAA,CAAA,YAAA,CAAA,GAAuB,KAAA,CAAA,SAAA,EAAA;AAAA,KAAA,CAC5B,CAAG;AAA6B,IAChC,MAAM,mBAAe,CAAG,MAAM,CAAU;AAAA,IAC1C,QAAC,CAAA,KAAA,EAAA,QAAA,EAAA,QAAA,EAAA,QAAA,EAAA,IAAA,EAAA,UAAA,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,WAAoB,CAAK,MAAA;AAE/B,MAAA,MAAA,UAAgB,GAAA,CAAA,KAAoB,CAAA,GAAA,EAAA,KAAA,CAAA,GAAA,EAAA,UAA2B,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,KAAA;AAE/D,QAAM,MAAA,OAAA,UAAqB,CAAM,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAC/B,QAAM,OAAA,OAAA,GAAa,OAAO,CAAK,MAAA,GAAA,CAAM,CAAK;AACxC,OAAA,CAAA,CAAA;AACA,MAAO,OAAA,IAAA,CAAA,GAAA,CAAA,YAA2B,UAAA,CAAA,CAAA;AAAA,KAAA,CACpC,CAAC;AACD,IAAA,MAAA,EAAA,aAAsB,EAAA,GAAA,YAAgB,CAAA,KAAA,EAAA,QAAA,EAAA,SAAA,CAAA,CAAA;AAAA,IACxC,MAAC,EAAA,UAAA,EAAA,WAAA,EAAA,UAAA,EAAA,GAAA,MAAA,CAAA,QAAA,CAAA,CAAA;AAED,IAAA,MAAM,cAAgB,GAAA,CAAA,GAAiB,KAAA;AAEvC,MAAA,QAAoB,CAAA,QAAA,GAAA,GAAA,CAAA;AAEpB,KAAM,CAAA;AACJ,IAAA,gBAAoB,CAAA,aAAA,EAAA,YAAA,EAAA,sBAAA,EAAA;AAAA,MACtB,OAAA,EAAA,KAAA;AAEA,KAAiB,CAAA,CAAA;AAAqD,IAAA,gBAC3D,CAAA,aAAA,EAAA,WAAA,EAAA,sBAAA,EAAA;AAAA,MACV,OAAA,EAAA,KAAA;AACD,KAAiB,CAAA,CAAA;AAAoD,IAAA,OAC1D,CAAA,gBAAA,EAAA;AAAA,MACV,GAAA,MAAA,CAAA,KAAA,CAAA;AAED,MAAA,UAA0B;AAAA,MACxB,UAAU,cAAK;AAAA,MACf,SAAA;AAAA,MACA,UAAU;AAAA,MACV,SAAA;AAAA,MACA,cAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,CAAA;AAAA,MACD,aAAA;AAED,KAAa,CAAA,CAAA;AAAA,IACX,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACD,IAAA,EAAA,EAAA,EAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}