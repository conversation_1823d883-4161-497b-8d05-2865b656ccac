{"version": 3, "file": "index.mjs", "sources": ["../../../../../../packages/components/infinite-scroll/src/index.ts"], "sourcesContent": ["// @ts-nocheck\nimport { nextTick } from 'vue'\nimport { throttle } from 'lodash-unified'\nimport {\n  getOffsetTopDistance,\n  getScrollContainer,\n  isFunction,\n  throwError,\n} from '@element-plus/utils'\n\nimport type { ComponentPublicInstance, ObjectDirective } from 'vue'\n\nexport const SCOPE = 'ElInfiniteScroll'\nexport const CHECK_INTERVAL = 50\nexport const DEFAULT_DELAY = 200\nexport const DEFAULT_DISTANCE = 0\n\nconst attributes = {\n  delay: {\n    type: Number,\n    default: DEFAULT_DELAY,\n  },\n  distance: {\n    type: Number,\n    default: DEFAULT_DISTANCE,\n  },\n  disabled: {\n    type: Boolean,\n    default: false,\n  },\n  immediate: {\n    type: Boolean,\n    default: true,\n  },\n}\n\ntype Attrs = typeof attributes\ntype ScrollOptions = { [K in keyof Attrs]: Attrs[K]['default'] }\ntype InfiniteScrollCallback = () => void\ntype InfiniteScrollEl = HTMLElement & {\n  [SCOPE]: {\n    container: HTMLElement | Window\n    containerEl: HTMLElement\n    instance: ComponentPublicInstance\n    delay: number // export for test\n    lastScrollTop: number\n    cb: InfiniteScrollCallback\n    onScroll: () => void\n    observer?: MutationObserver\n  }\n}\n\nconst getScrollOptions = (\n  el: HTMLElement,\n  instance: ComponentPublicInstance\n): ScrollOptions => {\n  return Object.entries(attributes).reduce((acm, [name, option]) => {\n    const { type, default: defaultValue } = option\n    const attrVal = el.getAttribute(`infinite-scroll-${name}`)\n    let value = instance[attrVal] ?? attrVal ?? defaultValue\n    value = value === 'false' ? false : value\n    value = type(value)\n    acm[name] = Number.isNaN(value) ? defaultValue : value\n    return acm\n  }, {} as ScrollOptions)\n}\n\nconst destroyObserver = (el: InfiniteScrollEl) => {\n  const { observer } = el[SCOPE]\n\n  if (observer) {\n    observer.disconnect()\n    delete el[SCOPE].observer\n  }\n}\n\nconst handleScroll = (el: InfiniteScrollEl, cb: InfiniteScrollCallback) => {\n  const { container, containerEl, instance, observer, lastScrollTop } =\n    el[SCOPE]\n  const { disabled, distance } = getScrollOptions(el, instance)\n  const { clientHeight, scrollHeight, scrollTop } = containerEl\n  const delta = scrollTop - lastScrollTop\n\n  el[SCOPE].lastScrollTop = scrollTop\n\n  // trigger only if full check has done and not disabled and scroll down\n  if (observer || disabled || delta < 0) return\n\n  let shouldTrigger = false\n\n  if (container === el) {\n    shouldTrigger = scrollHeight - (clientHeight + scrollTop) <= distance\n  } else {\n    // get the scrollHeight since el might be visible overflow\n    const { clientTop, scrollHeight: height } = el\n    const offsetTop = getOffsetTopDistance(el, containerEl)\n    shouldTrigger =\n      scrollTop + clientHeight >= offsetTop + clientTop + height - distance\n  }\n\n  if (shouldTrigger) {\n    cb.call(instance)\n  }\n}\n\nfunction checkFull(el: InfiniteScrollEl, cb: InfiniteScrollCallback) {\n  const { containerEl, instance } = el[SCOPE]\n  const { disabled } = getScrollOptions(el, instance)\n\n  if (disabled || containerEl.clientHeight === 0) return\n\n  if (containerEl.scrollHeight <= containerEl.clientHeight) {\n    cb.call(instance)\n  } else {\n    destroyObserver(el)\n  }\n}\n\nconst InfiniteScroll: ObjectDirective<\n  InfiniteScrollEl,\n  InfiniteScrollCallback\n> = {\n  async mounted(el, binding) {\n    const { instance, value: cb } = binding\n\n    if (!isFunction(cb)) {\n      throwError(SCOPE, \"'v-infinite-scroll' binding value must be a function\")\n    }\n\n    // ensure parentNode mounted\n    await nextTick()\n\n    const { delay, immediate } = getScrollOptions(el, instance)\n    const container = getScrollContainer(el, true)\n    const containerEl =\n      container === window\n        ? document.documentElement\n        : (container as HTMLElement)\n    const onScroll = throttle(handleScroll.bind(null, el, cb), delay)\n\n    if (!container) return\n\n    el[SCOPE] = {\n      instance,\n      container,\n      containerEl,\n      delay,\n      cb,\n      onScroll,\n      lastScrollTop: containerEl.scrollTop,\n    }\n\n    if (immediate) {\n      const observer = new MutationObserver(\n        throttle(checkFull.bind(null, el, cb), CHECK_INTERVAL)\n      )\n      el[SCOPE].observer = observer\n      observer.observe(el, { childList: true, subtree: true })\n      checkFull(el, cb)\n    }\n\n    container.addEventListener('scroll', onScroll)\n  },\n  unmounted(el) {\n    if (!el[SCOPE]) return\n    const { container, onScroll } = el[SCOPE]\n\n    container?.removeEventListener('scroll', onScroll)\n    destroyObserver(el)\n  },\n  async updated(el) {\n    if (!el[SCOPE]) {\n      await nextTick()\n    } else {\n      const { containerEl, cb, observer } = el[SCOPE]\n      if (containerEl.clientHeight && observer) {\n        checkFull(el, cb)\n      }\n    }\n  },\n}\n\nexport default InfiniteScroll\n"], "names": [], "mappings": ";;;;;;;AAQY,MAAC,KAAK,GAAG,mBAAmB;AAC5B,MAAC,cAAc,GAAG,GAAG;AACrB,MAAC,aAAa,GAAG,IAAI;AACrB,MAAC,gBAAgB,GAAG,EAAE;AAClC,MAAM,UAAU,GAAG;AACnB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,aAAa;AAC1B,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,gBAAgB;AAC7B,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,CAAC;AACF,MAAM,gBAAgB,GAAG,CAAC,EAAE,EAAE,QAAQ,KAAK;AAC3C,EAAE,OAAO,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK;AACpE,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,GAAG,MAAM,CAAC;AACnD,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/D,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,OAAO,KAAK,IAAI,GAAG,EAAE,GAAG,YAAY,CAAC;AACnG,IAAI,KAAK,GAAG,KAAK,KAAK,OAAO,GAAG,KAAK,GAAG,KAAK,CAAC;AAC9C,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;AACxB,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,YAAY,GAAG,KAAK,CAAC;AAC3D,IAAI,OAAO,GAAG,CAAC;AACf,GAAG,EAAE,EAAE,CAAC,CAAC;AACT,CAAC,CAAC;AACF,MAAM,eAAe,GAAG,CAAC,EAAE,KAAK;AAChC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACjC,EAAE,IAAI,QAAQ,EAAE;AAChB,IAAI,QAAQ,CAAC,UAAU,EAAE,CAAC;AAC1B,IAAI,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,CAAC;AAC9B,GAAG;AACH,CAAC,CAAC;AACF,MAAM,YAAY,GAAG,CAAC,EAAE,EAAE,EAAE,KAAK;AACjC,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAClF,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAChE,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,WAAW,CAAC;AAChE,EAAE,MAAM,KAAK,GAAG,SAAS,GAAG,aAAa,CAAC;AAC1C,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,aAAa,GAAG,SAAS,CAAC;AACtC,EAAE,IAAI,QAAQ,IAAI,QAAQ,IAAI,KAAK,GAAG,CAAC;AACvC,IAAI,OAAO;AACX,EAAE,IAAI,aAAa,GAAG,KAAK,CAAC;AAC5B,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE;AACxB,IAAI,aAAa,GAAG,YAAY,IAAI,YAAY,GAAG,SAAS,CAAC,IAAI,QAAQ,CAAC;AAC1E,GAAG,MAAM;AACT,IAAI,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;AACnD,IAAI,MAAM,SAAS,GAAG,oBAAoB,CAAC,EAAE,EAAE,WAAW,CAAC,CAAC;AAC5D,IAAI,aAAa,GAAG,SAAS,GAAG,YAAY,IAAI,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,QAAQ,CAAC;AAC1F,GAAG;AACH,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtB,GAAG;AACH,CAAC,CAAC;AACF,SAAS,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE;AAC3B,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9C,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACtD,EAAE,IAAI,QAAQ,IAAI,WAAW,CAAC,YAAY,KAAK,CAAC;AAChD,IAAI,OAAO;AACX,EAAE,IAAI,WAAW,CAAC,YAAY,IAAI,WAAW,CAAC,YAAY,EAAE;AAC5D,IAAI,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AACtB,GAAG,MAAM;AACT,IAAI,eAAe,CAAC,EAAE,CAAC,CAAC;AACxB,GAAG;AACH,CAAC;AACI,MAAC,cAAc,GAAG;AACvB,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE,OAAO,EAAE;AAC7B,IAAI,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,EAAE,GAAG,OAAO,CAAC;AAC5C,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;AACzB,MAAM,UAAU,CAAC,KAAK,EAAE,sDAAsD,CAAC,CAAC;AAChF,KAAK;AACL,IAAI,MAAM,QAAQ,EAAE,CAAC;AACrB,IAAI,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,gBAAgB,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;AAChE,IAAI,MAAM,SAAS,GAAG,kBAAkB,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;AACnD,IAAI,MAAM,WAAW,GAAG,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,eAAe,GAAG,SAAS,CAAC;AACpF,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;AACtE,IAAI,IAAI,CAAC,SAAS;AAClB,MAAM,OAAO;AACb,IAAI,EAAE,CAAC,KAAK,CAAC,GAAG;AAChB,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,WAAW;AACjB,MAAM,KAAK;AACX,MAAM,EAAE;AACR,MAAM,QAAQ;AACd,MAAM,aAAa,EAAE,WAAW,CAAC,SAAS;AAC1C,KAAK,CAAC;AACN,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,MAAM,QAAQ,GAAG,IAAI,gBAAgB,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,cAAc,CAAC,CAAC,CAAC;AACpG,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,QAAQ,GAAG,QAAQ,CAAC;AACpC,MAAM,QAAQ,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/D,MAAM,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACnD,GAAG;AACH,EAAE,SAAS,CAAC,EAAE,EAAE;AAChB,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;AAClB,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AAC9C,IAAI,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACnF,IAAI,eAAe,CAAC,EAAE,CAAC,CAAC;AACxB,GAAG;AACH,EAAE,MAAM,OAAO,CAAC,EAAE,EAAE;AACpB,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE;AACpB,MAAM,MAAM,QAAQ,EAAE,CAAC;AACvB,KAAK,MAAM;AACX,MAAM,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,QAAQ,EAAE,GAAG,EAAE,CAAC,KAAK,CAAC,CAAC;AACtD,MAAM,IAAI,WAAW,CAAC,YAAY,IAAI,QAAQ,EAAE;AAChD,QAAQ,SAAS,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1B,OAAO;AACP,KAAK;AACL,GAAG;AACH;;;;"}