import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/payment_service.dart';
import '../models/order.dart';
import '../models/package.dart';

/// 支付控制器
class PaymentController extends GetxController {
  final PaymentService _paymentService = Get.find<PaymentService>();

  // 响应式变量
  RxList<MembershipPackage> get packages => _paymentService.packages;
  RxList<Order> get orders => _paymentService.orders;
  RxBool get isLoading => _paymentService.isLoading;

  @override
  void onInit() {
    super.onInit();
    loadData();
  }

  /// 加载数据
  Future<void> loadData() async {
    await _paymentService.loadPackages();
    await _paymentService.loadUserOrders();
  }

  /// 购买会员
  Future<void> purchaseMembership(String packageId) async {
    final order = await _paymentService.createOrder(packageId);
    if (order != null) {
      // 显示支付选择对话框
      _showPaymentDialog(order);
    }
  }

  /// 显示支付选择对话框
  void _showPaymentDialog(Order order) {
    Get.dialog(
      AlertDialog(
        title: const Text('选择支付方式'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.payment, color: Colors.green),
              title: const Text('微信支付'),
              subtitle: Text('￥${order.amount}'),
              onTap: () {
                Get.back();
                _paymentService.payWithWechat(order.id);
              },
            ),
            ListTile(
              leading: const Icon(Icons.card_giftcard, color: Colors.orange),
              title: const Text('会员码支付'),
              subtitle: const Text('使用会员码激活'),
              onTap: () {
                Get.back();
                _showMemberCodeDialog(order.id);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示会员码输入对话框
  void _showMemberCodeDialog(String orderId) {
    final codeController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('输入会员码'),
        content: TextField(
          controller: codeController,
          decoration: const InputDecoration(
            labelText: '会员码',
            hintText: '请输入会员码',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _paymentService.payWithMemberCode(orderId, codeController.text.trim());
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }

  /// 取消订单
  Future<void> cancelOrder(String orderId) async {
    await _paymentService.cancelOrder(orderId);
  }

  /// 检查功能权限
  bool canUseFeature(String feature) {
    return _paymentService.canUseFeature(feature);
  }

  /// 检查使用限制
  bool isWithinLimits(String limitType, int currentCount) {
    return _paymentService.isWithinLimits(limitType, currentCount);
  }
}