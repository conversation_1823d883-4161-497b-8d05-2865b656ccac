{"name": "local-pkg", "type": "module", "version": "0.5.1", "packageManager": "pnpm@9.13.2", "description": "Get information on local packages.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/local-pkg#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/local-pkg.git"}, "bugs": {"url": "https://github.com/antfu/local-pkg/issues"}, "keywords": ["package"], "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "engines": {"node": ">=14"}, "scripts": {"prepublishOnly": "nr build", "build": "unbuild", "lint": "eslint .", "release": "bumpp && npm publish", "typecheck": "tsc --noEmit", "test": "vitest run && node ./test/cjs.cjs && node ./test/esm.mjs"}, "dependencies": {"mlly": "^1.7.3", "pkg-types": "^1.2.1"}, "devDependencies": {"@antfu/eslint-config": "^3.9.2", "@antfu/ni": "^0.23.1", "@antfu/utils": "^0.7.10", "@types/chai": "^5.0.1", "@types/node": "^22.9.0", "bumpp": "^9.8.1", "chai": "^5.1.2", "eslint": "^9.15.0", "esno": "^4.8.0", "find-up": "^6.3.0", "typescript": "^5.6.3", "unbuild": "^2.0.0", "vitest": "^2.1.5"}}