{"version": 3, "file": "node-content.js", "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.tsx"], "sourcesContent": ["import { Comment, defineComponent } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isArray } from '@element-plus/utils'\n\nimport type { PropType, VNode } from 'vue'\nimport type { CascaderNode, RenderLabel } from './types'\n\nfunction isVNodeEmpty(vnodes?: VNode[] | VNode) {\n  return !!(isArray(vnodes)\n    ? vnodes.every(({ type }) => type === Comment)\n    : vnodes?.type === Comment)\n}\n\nexport default defineComponent({\n  name: 'NodeContent',\n  props: {\n    node: {\n      type: Object as PropType<CascaderNode>,\n      required: true,\n    },\n    renderLabelFn: Function as PropType<RenderLabel>,\n  },\n  setup(props) {\n    const ns = useNamespace('cascader-node')\n    const { renderLabelFn, node } = props\n    const { data, label: nodeLabel } = node\n\n    const label = () => {\n      const renderLabel = renderLabelFn?.({ node, data })\n      return isVNodeEmpty(renderLabel) ? nodeLabel : renderLabel ?? nodeLabel\n    }\n\n    return () => <span class={ns.e('label')}>{label()}</span>\n  },\n})\n"], "names": ["isVNodeEmpty", "vnodes", "type", "defineComponent", "name", "props", "node", "required", "renderLabelFn", "useNamespace", "ns", "label", "nodeLabel", "renderLabel", "data"], "mappings": ";;;;;;;;;AAOA,EAASA,OAAAA,CAAAA,EAAAA,cAAAA,CAAAA,MAAaC,CAAAA,GAA0B,MAAA,CAAA,KAAA,CAAA,CAAA;IACvC,IAAA;AACaC,GAAAA,KAAAA,IAAAA,KAAAA,WAAAA,CAAAA,GAAAA,CAAAA,MAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,MAAAA,CAAAA,IAAAA,MAAAA,WAAAA,CAAAA,CAAAA;;AAErB,kBAAAC,mBAAA,CAAA;;AAED,EAAA,KAAA,EAAA;AACEC,IAAAA,IAAM,EADuB;AAE7BC,MAAAA,IAAO,EAAA,MAAA;AACLC,MAAAA,QAAM,EAAA,IAAA;AACJJ,KAAAA;AACAK,IAAAA,aAAU,EAAA,QAAA;;AAEZC,EAAAA,KAAAA,CAAAA,KAAAA,EAAAA;IAP2B,MAAA,EAAA,GAAAC,kBAAA,CAAA,eAAA,CAAA,CAAA;;MASxB,aAAQ;AACX,MAAA,IAAMC;KACA,GAAA,KAAA,CAAA;UAAA;AAAiBJ,MAAAA,IAAAA;AAAjB,MAAA,KAAN,EAAA,SAAA;KACM,GAAA,IAAA,CAAA;UAAA,KAAA,GAAA,MAAA;AAAQK,MAAAA,MAAOC,WAAAA,GAAAA,aAAAA,IAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,aAAAA,CAAAA;AAAf,QAA6BN,IAAnC;;OAEMK,CAAAA,CAAAA;MACJ,OAAME,YAAcL,CAAAA,WAAAA,CAAAA,GAAgB,SAAA,GAAA,WAAA,IAAA,IAAA,GAAA,WAAA,GAAA,SAAA,CAAA;;AAAQM,IAAAA,OAAAA,MAAAA,eAAAA,CAAAA,MAAAA,EAAAA;AAAR,MAAA,OAApC,EAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;OACOd,CAAAA,KAAAA,EAAAA,CAAAA,CAAAA,CAAAA;;;;;;"}