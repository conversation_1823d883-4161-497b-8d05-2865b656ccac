import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 生成唯一的 ValueKey
class KeyUtils {
  static int _counter = 0;

  /// 生成唯一的 ValueKey
  static ValueKey<String> generateKey(String prefix) {
    return ValueKey('${prefix}_${++_counter}');
  }

  /// 为 Obx 组件生成唯一的 key
  static ValueKey<String> obxKey(String name) {
    return generateKey('obx_$name');
  }
}

/// 主题化工具类，提供统一的UI组件样式
class ThemeUtils {
  /// 获取当前是否为暗黑模式
  static bool get isDarkMode => Get.theme.brightness == Brightness.dark;

  /// 获取适合当前主题的文字颜色
  static Color get textColor => isDarkMode
      ? const Color(0xFFF7E7CE)  // 香槟金
      : const Color(0xFF1B4332); // 墨绿

  /// 获取适合当前主题的背景颜色
  static Color get backgroundColor => isDarkMode
      ? const Color(0xFF1B2818)  // 深绿背景
      : const Color(0xFFFFFDD0); // 象牙白

  /// 获取适合当前主题的表面颜色
  static Color get surfaceColor => isDarkMode
      ? const Color(0xFF0F1A0C)  // 深墨绿
      : const Color(0xFFF7E7CE); // 香槟金

  /// 显示主题化的Snackbar
  static void showSnackbar(
    String title, 
    String message, {
    SnackPosition position = SnackPosition.BOTTOM,
    Duration duration = const Duration(seconds: 3),
    Color? backgroundColor,
    Color? textColor,
    Widget? icon,
  }) {
    Get.snackbar(
      title,
      message,
      backgroundColor: backgroundColor ?? ThemeUtils.backgroundColor,
      colorText: textColor ?? ThemeUtils.textColor,
      snackPosition: position,
      duration: duration,
      icon: icon,
      borderRadius: 8,
      margin: const EdgeInsets.all(16),
    );
  }

  /// 显示主题化的成功Snackbar
  static void showSuccessSnackbar(String title, String message) {
    showSnackbar(
      title,
      message,
      backgroundColor: isDarkMode ? const Color(0xFF2D5A2D) : const Color(0xFFE8F5E8),
      textColor: isDarkMode ? const Color(0xFF90EE90) : const Color(0xFF2E7D32),
      icon: Icon(
        Icons.check_circle,
        color: isDarkMode ? const Color(0xFF90EE90) : const Color(0xFF2E7D32),
      ),
    );
  }

  /// 显示主题化的错误Snackbar
  static void showErrorSnackbar(String title, String message) {
    showSnackbar(
      title,
      message,
      backgroundColor: isDarkMode ? const Color(0xFF5A2D2D) : const Color(0xFFFFEBEE),
      textColor: isDarkMode ? const Color(0xFFFF6B6B) : const Color(0xFFD32F2F),
      icon: Icon(
        Icons.error,
        color: isDarkMode ? const Color(0xFFFF6B6B) : const Color(0xFFD32F2F),
      ),
    );
  }

  /// 显示主题化的警告Snackbar
  static void showWarningSnackbar(String title, String message) {
    showSnackbar(
      title,
      message,
      backgroundColor: isDarkMode ? const Color(0xFF5A4D2D) : const Color(0xFFFFF8E1),
      textColor: isDarkMode ? const Color(0xFFFFD54F) : const Color(0xFFF57C00),
      icon: Icon(
        Icons.warning,
        color: isDarkMode ? const Color(0xFFFFD54F) : const Color(0xFFF57C00),
      ),
    );
  }

  /// 显示主题化的加载对话框
  static void showLoadingDialog({
    String title = '加载中...',
    String? message,
    bool barrierDismissible = false,
  }) {
    Get.dialog(
      AlertDialog(
        backgroundColor: backgroundColor,
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                inherit: true,
                color: textColor,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (message != null) ...[
              const SizedBox(height: 8),
              Text(
                message,
                style: TextStyle(
                  inherit: true,
                  color: textColor.withOpacity(0.7),
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ),
      barrierDismissible: barrierDismissible,
    );
  }

  /// 显示主题化的确认对话框
  static Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = '确认',
    String cancelText = '取消',
    Color? confirmColor,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return Get.dialog<bool>(
      AlertDialog(
        backgroundColor: backgroundColor,
        title: Text(
          title,
          style: TextStyle(
            inherit: true,
            color: textColor,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          content,
          style: TextStyle(
            inherit: true,
            color: textColor,
            fontSize: 16,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Get.back(result: false);
              onCancel?.call();
            },
            child: Text(
              cancelText,
              style: TextStyle(
                inherit: true,
                color: textColor.withOpacity(0.7),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back(result: true);
              onConfirm?.call();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: confirmColor ?? Get.theme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(confirmText),
          ),
        ],
      ),
    );
  }

  /// 获取主题化的下拉菜单样式
  static TextStyle get dropdownTextStyle => TextStyle(
    inherit: true,
    color: textColor,
    fontSize: 16,
  );

  /// 获取主题化的下拉菜单背景色
  static Color get dropdownBackgroundColor => backgroundColor;

  /// 创建主题化的DropdownMenuItem
  static DropdownMenuItem<T> createDropdownMenuItem<T>({
    required T value,
    required String text,
    Widget? child,
  }) {
    return DropdownMenuItem<T>(
      value: value,
      child: child ?? Text(
        text,
        style: dropdownTextStyle,
      ),
    );
  }
}
