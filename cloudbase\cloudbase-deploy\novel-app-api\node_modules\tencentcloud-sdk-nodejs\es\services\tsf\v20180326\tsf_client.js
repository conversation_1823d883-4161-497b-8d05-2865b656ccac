import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tsf.tencentcloudapi.com", "2018-03-26", clientConfig);
    }
    async CreateTask(req, cb) {
        return this.request("CreateTask", req, cb);
    }
    async DeletePublicConfig(req, cb) {
        return this.request("DeletePublicConfig", req, cb);
    }
    async CreateCluster(req, cb) {
        return this.request("CreateCluster", req, cb);
    }
    async DescribeMicroservices(req, cb) {
        return this.request("DescribeMicroservices", req, cb);
    }
    async ModifyContainerReplicas(req, cb) {
        return this.request("ModifyContainerReplicas", req, cb);
    }
    async ShrinkInstances(req, cb) {
        return this.request("ShrinkInstances", req, cb);
    }
    async DisableUnitRule(req, cb) {
        return this.request("DisableUnitRule", req, cb);
    }
    async DescribePodInstances(req, cb) {
        return this.request("DescribePodInstances", req, cb);
    }
    async CreateConfigWithDetailResp(req, cb) {
        return this.request("CreateConfigWithDetailResp", req, cb);
    }
    async CreateNamespace(req, cb) {
        return this.request("CreateNamespace", req, cb);
    }
    async AddClusterInstances(req, cb) {
        return this.request("AddClusterInstances", req, cb);
    }
    async DeleteLaneRule(req, cb) {
        return this.request("DeleteLaneRule", req, cb);
    }
    async DeleteCluster(req, cb) {
        return this.request("DeleteCluster", req, cb);
    }
    async RedoTaskFlowBatch(req, cb) {
        return this.request("RedoTaskFlowBatch", req, cb);
    }
    async DescribePublicConfigSummary(req, cb) {
        return this.request("DescribePublicConfigSummary", req, cb);
    }
    async DescribeContainerGroups(req, cb) {
        return this.request("DescribeContainerGroups", req, cb);
    }
    async DescribeGroupAttribute(req, cb) {
        return this.request("DescribeGroupAttribute", req, cb);
    }
    async CreateFileConfigWithDetailResp(req, cb) {
        return this.request("CreateFileConfigWithDetailResp", req, cb);
    }
    async CreateConfigTemplate(req, cb) {
        return this.request("CreateConfigTemplate", req, cb);
    }
    async DescribeMsApiList(req, cb) {
        return this.request("DescribeMsApiList", req, cb);
    }
    async RevocationPublicConfig(req, cb) {
        return this.request("RevocationPublicConfig", req, cb);
    }
    async DescribeConfig(req, cb) {
        return this.request("DescribeConfig", req, cb);
    }
    async DescribeOverviewInvocation(req, cb) {
        return this.request("DescribeOverviewInvocation", req, cb);
    }
    async StopTaskExecute(req, cb) {
        return this.request("StopTaskExecute", req, cb);
    }
    async DescribeTaskDetail(req, cb) {
        return this.request("DescribeTaskDetail", req, cb);
    }
    async ModifyCluster(req, cb) {
        return this.request("ModifyCluster", req, cb);
    }
    async DescribeContainerGroupDetail(req, cb) {
        return this.request("DescribeContainerGroupDetail", req, cb);
    }
    async EnableUnitRule(req, cb) {
        return this.request("EnableUnitRule", req, cb);
    }
    async RollbackConfig(req, cb) {
        return this.request("RollbackConfig", req, cb);
    }
    async DescribeImageTags(req, cb) {
        return this.request("DescribeImageTags", req, cb);
    }
    async DescribeFileConfigs(req, cb) {
        return this.request("DescribeFileConfigs", req, cb);
    }
    async CreateConfigTemplateWithDetailResp(req, cb) {
        return this.request("CreateConfigTemplateWithDetailResp", req, cb);
    }
    async DescribeSimpleClusters(req, cb) {
        return this.request("DescribeSimpleClusters", req, cb);
    }
    async DescribeApiDetail(req, cb) {
        return this.request("DescribeApiDetail", req, cb);
    }
    async RevokeFileConfig(req, cb) {
        return this.request("RevokeFileConfig", req, cb);
    }
    async DescribeDeliveryConfig(req, cb) {
        return this.request("DescribeDeliveryConfig", req, cb);
    }
    async DescribeGroupsWithPlugin(req, cb) {
        return this.request("DescribeGroupsWithPlugin", req, cb);
    }
    async EnableLaneRule(req, cb) {
        return this.request("EnableLaneRule", req, cb);
    }
    async DisableUnitRoute(req, cb) {
        return this.request("DisableUnitRoute", req, cb);
    }
    async DisassociateBusinessLogConfig(req, cb) {
        return this.request("DisassociateBusinessLogConfig", req, cb);
    }
    async CreateConfig(req, cb) {
        return this.request("CreateConfig", req, cb);
    }
    async CreateMicroservice(req, cb) {
        return this.request("CreateMicroservice", req, cb);
    }
    async DeleteImageTags(req, cb) {
        return this.request("DeleteImageTags", req, cb);
    }
    async DescribeDownloadInfo(req, cb) {
        return this.request("DescribeDownloadInfo", req, cb);
    }
    async DescribeApplications(req, cb) {
        return this.request("DescribeApplications", req, cb);
    }
    async CreatePathRewritesWithDetailResp(req, cb) {
        return this.request("CreatePathRewritesWithDetailResp", req, cb);
    }
    async DescribeContainerGroupDeployInfo(req, cb) {
        return this.request("DescribeContainerGroupDeployInfo", req, cb);
    }
    async DescribeUnitApiUseDetail(req, cb) {
        return this.request("DescribeUnitApiUseDetail", req, cb);
    }
    async BindPlugin(req, cb) {
        return this.request("BindPlugin", req, cb);
    }
    async DescribeGroupBindedGateways(req, cb) {
        return this.request("DescribeGroupBindedGateways", req, cb);
    }
    async DeleteUnitRule(req, cb) {
        return this.request("DeleteUnitRule", req, cb);
    }
    async DescribeApplication(req, cb) {
        return this.request("DescribeApplication", req, cb);
    }
    async UpdateUnitRule(req, cb) {
        return this.request("UpdateUnitRule", req, cb);
    }
    async CreateUnitRule(req, cb) {
        return this.request("CreateUnitRule", req, cb);
    }
    async UpdateRepository(req, cb) {
        return this.request("UpdateRepository", req, cb);
    }
    async DescribeUsableUnitNamespaces(req, cb) {
        return this.request("DescribeUsableUnitNamespaces", req, cb);
    }
    async DescribeGroupUseDetail(req, cb) {
        return this.request("DescribeGroupUseDetail", req, cb);
    }
    async DescribeImageRepository(req, cb) {
        return this.request("DescribeImageRepository", req, cb);
    }
    async ReassociateBusinessLogConfig(req, cb) {
        return this.request("ReassociateBusinessLogConfig", req, cb);
    }
    async UpdateApiTimeouts(req, cb) {
        return this.request("UpdateApiTimeouts", req, cb);
    }
    async CreateUnitNamespaces(req, cb) {
        return this.request("CreateUnitNamespaces", req, cb);
    }
    async UpdateHealthCheckSettings(req, cb) {
        return this.request("UpdateHealthCheckSettings", req, cb);
    }
    async DescribeTaskRecords(req, cb) {
        return this.request("DescribeTaskRecords", req, cb);
    }
    async ExecuteTask(req, cb) {
        return this.request("ExecuteTask", req, cb);
    }
    async CreateGatewayApi(req, cb) {
        return this.request("CreateGatewayApi", req, cb);
    }
    async DescribeSimpleNamespaces(req, cb) {
        return this.request("DescribeSimpleNamespaces", req, cb);
    }
    async CreateApiRateLimitRule(req, cb) {
        return this.request("CreateApiRateLimitRule", req, cb);
    }
    async DeleteMicroservice(req, cb) {
        return this.request("DeleteMicroservice", req, cb);
    }
    async DeleteFileConfig(req, cb) {
        return this.request("DeleteFileConfig", req, cb);
    }
    async StopContainerGroup(req, cb) {
        return this.request("StopContainerGroup", req, cb);
    }
    async DescribeGroupBusinessLogConfigs(req, cb) {
        return this.request("DescribeGroupBusinessLogConfigs", req, cb);
    }
    async DescribeGroupRelease(req, cb) {
        return this.request("DescribeGroupRelease", req, cb);
    }
    async DescribeCreateGatewayApiStatus(req, cb) {
        return this.request("DescribeCreateGatewayApiStatus", req, cb);
    }
    async DescribeInvocationMetricDataPoint(req, cb) {
        return this.request("DescribeInvocationMetricDataPoint", req, cb);
    }
    async ModifyTask(req, cb) {
        return this.request("ModifyTask", req, cb);
    }
    async UpdateApiRateLimitRule(req, cb) {
        return this.request("UpdateApiRateLimitRule", req, cb);
    }
    async DescribePluginInstances(req, cb) {
        return this.request("DescribePluginInstances", req, cb);
    }
    async UnbindApiGroup(req, cb) {
        return this.request("UnbindApiGroup", req, cb);
    }
    async DescribeGroupInstances(req, cb) {
        return this.request("DescribeGroupInstances", req, cb);
    }
    async CreateLaneRule(req, cb) {
        return this.request("CreateLaneRule", req, cb);
    }
    async DeleteContainerGroup(req, cb) {
        return this.request("DeleteContainerGroup", req, cb);
    }
    async DescribeApiGroups(req, cb) {
        return this.request("DescribeApiGroups", req, cb);
    }
    async DescribeClusters(req, cb) {
        return this.request("DescribeClusters", req, cb);
    }
    async DescribeBusinessLogConfigs(req, cb) {
        return this.request("DescribeBusinessLogConfigs", req, cb);
    }
    async DescribeUnitRulesV2(req, cb) {
        return this.request("DescribeUnitRulesV2", req, cb);
    }
    async CreatePublicConfigWithDetailResp(req, cb) {
        return this.request("CreatePublicConfigWithDetailResp", req, cb);
    }
    async CreateUnitRuleWithDetailResp(req, cb) {
        return this.request("CreateUnitRuleWithDetailResp", req, cb);
    }
    async ReleaseConfig(req, cb) {
        return this.request("ReleaseConfig", req, cb);
    }
    async TerminateTaskFlowBatch(req, cb) {
        return this.request("TerminateTaskFlowBatch", req, cb);
    }
    async UpdateApiRateLimitRules(req, cb) {
        return this.request("UpdateApiRateLimitRules", req, cb);
    }
    async DescribeGroups(req, cb) {
        return this.request("DescribeGroups", req, cb);
    }
    async DescribeFileConfigReleases(req, cb) {
        return this.request("DescribeFileConfigReleases", req, cb);
    }
    async DescribeRepositories(req, cb) {
        return this.request("DescribeRepositories", req, cb);
    }
    async DisableTaskFlow(req, cb) {
        return this.request("DisableTaskFlow", req, cb);
    }
    async CreateAllGatewayApiAsync(req, cb) {
        return this.request("CreateAllGatewayApiAsync", req, cb);
    }
    async CreateRepository(req, cb) {
        return this.request("CreateRepository", req, cb);
    }
    async DescribeClusterInstances(req, cb) {
        return this.request("DescribeClusterInstances", req, cb);
    }
    async CreatePublicConfig(req, cb) {
        return this.request("CreatePublicConfig", req, cb);
    }
    async BindApiGroup(req, cb) {
        return this.request("BindApiGroup", req, cb);
    }
    async DescribeInovcationIndicators(req, cb) {
        return this.request("DescribeInovcationIndicators", req, cb);
    }
    async DescribeDeliveryConfigByGroupId(req, cb) {
        return this.request("DescribeDeliveryConfigByGroupId", req, cb);
    }
    async DescribeSimpleGroups(req, cb) {
        return this.request("DescribeSimpleGroups", req, cb);
    }
    async DescribeConfigs(req, cb) {
        return this.request("DescribeConfigs", req, cb);
    }
    async DescribeStatistics(req, cb) {
        return this.request("DescribeStatistics", req, cb);
    }
    async DeleteConfigTemplate(req, cb) {
        return this.request("DeleteConfigTemplate", req, cb);
    }
    async ReleaseFileConfig(req, cb) {
        return this.request("ReleaseFileConfig", req, cb);
    }
    async DeleteApiRateLimitRule(req, cb) {
        return this.request("DeleteApiRateLimitRule", req, cb);
    }
    async DescribeGroupGateways(req, cb) {
        return this.request("DescribeGroupGateways", req, cb);
    }
    async DescribeUnitNamespaces(req, cb) {
        return this.request("DescribeUnitNamespaces", req, cb);
    }
    async ModifyUploadInfo(req, cb) {
        return this.request("ModifyUploadInfo", req, cb);
    }
    async AddInstances(req, cb) {
        return this.request("AddInstances", req, cb);
    }
    async ModifyNamespace(req, cb) {
        return this.request("ModifyNamespace", req, cb);
    }
    async DescribeConfigSummary(req, cb) {
        return this.request("DescribeConfigSummary", req, cb);
    }
    async DescribeInvocationMetricScatterPlot(req, cb) {
        return this.request("DescribeInvocationMetricScatterPlot", req, cb);
    }
    async CreateFileConfig(req, cb) {
        return this.request("CreateFileConfig", req, cb);
    }
    async DeployContainerApplication(req, cb) {
        return this.request("DeployContainerApplication", req, cb);
    }
    async RedoTask(req, cb) {
        return this.request("RedoTask", req, cb);
    }
    async DescribeContainerGroupAttribute(req, cb) {
        return this.request("DescribeContainerGroupAttribute", req, cb);
    }
    async DescribePkgs(req, cb) {
        return this.request("DescribePkgs", req, cb);
    }
    async CreateApiGroup(req, cb) {
        return this.request("CreateApiGroup", req, cb);
    }
    async DescribePrograms(req, cb) {
        return this.request("DescribePrograms", req, cb);
    }
    async DescribePublicConfig(req, cb) {
        return this.request("DescribePublicConfig", req, cb);
    }
    async DescribeApiGroup(req, cb) {
        return this.request("DescribeApiGroup", req, cb);
    }
    async ModifyApplication(req, cb) {
        return this.request("ModifyApplication", req, cb);
    }
    async DeletePathRewrites(req, cb) {
        return this.request("DeletePathRewrites", req, cb);
    }
    async CreateGroup(req, cb) {
        return this.request("CreateGroup", req, cb);
    }
    async DescribeTaskLastStatus(req, cb) {
        return this.request("DescribeTaskLastStatus", req, cb);
    }
    async DescribeBusinessLogConfig(req, cb) {
        return this.request("DescribeBusinessLogConfig", req, cb);
    }
    async DescribeGatewayAllGroupApis(req, cb) {
        return this.request("DescribeGatewayAllGroupApis", req, cb);
    }
    async DeletePkgs(req, cb) {
        return this.request("DeletePkgs", req, cb);
    }
    async StopTaskBatch(req, cb) {
        return this.request("StopTaskBatch", req, cb);
    }
    async DescribePublicConfigs(req, cb) {
        return this.request("DescribePublicConfigs", req, cb);
    }
    async DescribeConfigTemplate(req, cb) {
        return this.request("DescribeConfigTemplate", req, cb);
    }
    async DisassociateKafkaConfig(req, cb) {
        return this.request("DisassociateKafkaConfig", req, cb);
    }
    async DescribeInvocationMetricDataCurve(req, cb) {
        return this.request("DescribeInvocationMetricDataCurve", req, cb);
    }
    async DeleteGroup(req, cb) {
        return this.request("DeleteGroup", req, cb);
    }
    async RemoveInstances(req, cb) {
        return this.request("RemoveInstances", req, cb);
    }
    async OperateApplicationTcrBinding(req, cb) {
        return this.request("OperateApplicationTcrBinding", req, cb);
    }
    async ExpandGroup(req, cb) {
        return this.request("ExpandGroup", req, cb);
    }
    async DeleteTask(req, cb) {
        return this.request("DeleteTask", req, cb);
    }
    async DescribePathRewrites(req, cb) {
        return this.request("DescribePathRewrites", req, cb);
    }
    async DeleteLane(req, cb) {
        return this.request("DeleteLane", req, cb);
    }
    async UpdateConfigTemplate(req, cb) {
        return this.request("UpdateConfigTemplate", req, cb);
    }
    async DescribeConfigReleases(req, cb) {
        return this.request("DescribeConfigReleases", req, cb);
    }
    async CreateLane(req, cb) {
        return this.request("CreateLane", req, cb);
    }
    async DescribeContainerEvents(req, cb) {
        return this.request("DescribeContainerEvents", req, cb);
    }
    async DescribeGatewayMonitorOverview(req, cb) {
        return this.request("DescribeGatewayMonitorOverview", req, cb);
    }
    async DeployGroup(req, cb) {
        return this.request("DeployGroup", req, cb);
    }
    async DescribeDeliveryConfigs(req, cb) {
        return this.request("DescribeDeliveryConfigs", req, cb);
    }
    async UpdateApiGroup(req, cb) {
        return this.request("UpdateApiGroup", req, cb);
    }
    async StopGroup(req, cb) {
        return this.request("StopGroup", req, cb);
    }
    async ModifyLane(req, cb) {
        return this.request("ModifyLane", req, cb);
    }
    async EnableTask(req, cb) {
        return this.request("EnableTask", req, cb);
    }
    async DescribeMicroservicesByGroupIds(req, cb) {
        return this.request("DescribeMicroservicesByGroupIds", req, cb);
    }
    async CreateMicroserviceWithDetailResp(req, cb) {
        return this.request("CreateMicroserviceWithDetailResp", req, cb);
    }
    async ModifyMicroservice(req, cb) {
        return this.request("ModifyMicroservice", req, cb);
    }
    async EnableTaskFlow(req, cb) {
        return this.request("EnableTaskFlow", req, cb);
    }
    async DescribeUnitRule(req, cb) {
        return this.request("DescribeUnitRule", req, cb);
    }
    async CreatePathRewrites(req, cb) {
        return this.request("CreatePathRewrites", req, cb);
    }
    async DeleteApiGroup(req, cb) {
        return this.request("DeleteApiGroup", req, cb);
    }
    async CreateApplication(req, cb) {
        return this.request("CreateApplication", req, cb);
    }
    async ModifyPathRewrite(req, cb) {
        return this.request("ModifyPathRewrite", req, cb);
    }
    async RedoTaskBatch(req, cb) {
        return this.request("RedoTaskBatch", req, cb);
    }
    async ReleaseApiGroup(req, cb) {
        return this.request("ReleaseApiGroup", req, cb);
    }
    async AssociateBusinessLogConfig(req, cb) {
        return this.request("AssociateBusinessLogConfig", req, cb);
    }
    async DescribePublicConfigReleaseLogs(req, cb) {
        return this.request("DescribePublicConfigReleaseLogs", req, cb);
    }
    async DescribeConfigReleaseLogs(req, cb) {
        return this.request("DescribeConfigReleaseLogs", req, cb);
    }
    async DescribeSimpleApplications(req, cb) {
        return this.request("DescribeSimpleApplications", req, cb);
    }
    async DescribePathRewrite(req, cb) {
        return this.request("DescribePathRewrite", req, cb);
    }
    async ExecuteTaskFlow(req, cb) {
        return this.request("ExecuteTaskFlow", req, cb);
    }
    async DescribeMicroservice(req, cb) {
        return this.request("DescribeMicroservice", req, cb);
    }
    async DeleteServerlessGroup(req, cb) {
        return this.request("DeleteServerlessGroup", req, cb);
    }
    async RedoTaskExecute(req, cb) {
        return this.request("RedoTaskExecute", req, cb);
    }
    async DescribeJvmMonitor(req, cb) {
        return this.request("DescribeJvmMonitor", req, cb);
    }
    async DescribeFlowLastBatchState(req, cb) {
        return this.request("DescribeFlowLastBatchState", req, cb);
    }
    async ReleaseConfigWithDetailResp(req, cb) {
        return this.request("ReleaseConfigWithDetailResp", req, cb);
    }
    async DeleteRepository(req, cb) {
        return this.request("DeleteRepository", req, cb);
    }
    async DisableTask(req, cb) {
        return this.request("DisableTask", req, cb);
    }
    async StartContainerGroup(req, cb) {
        return this.request("StartContainerGroup", req, cb);
    }
    async SearchBusinessLog(req, cb) {
        return this.request("SearchBusinessLog", req, cb);
    }
    async DeployContainerGroup(req, cb) {
        return this.request("DeployContainerGroup", req, cb);
    }
    async ModifyProgram(req, cb) {
        return this.request("ModifyProgram", req, cb);
    }
    async SearchStdoutLog(req, cb) {
        return this.request("SearchStdoutLog", req, cb);
    }
    async DeleteGatewayApi(req, cb) {
        return this.request("DeleteGatewayApi", req, cb);
    }
    async CreateTaskFlow(req, cb) {
        return this.request("CreateTaskFlow", req, cb);
    }
    async DescribeGroup(req, cb) {
        return this.request("DescribeGroup", req, cb);
    }
    async DeleteApplication(req, cb) {
        return this.request("DeleteApplication", req, cb);
    }
    async DescribeApiRateLimitRules(req, cb) {
        return this.request("DescribeApiRateLimitRules", req, cb);
    }
    async DeleteNamespace(req, cb) {
        return this.request("DeleteNamespace", req, cb);
    }
    async DescribeBasicResourceUsage(req, cb) {
        return this.request("DescribeBasicResourceUsage", req, cb);
    }
    async DeleteConfig(req, cb) {
        return this.request("DeleteConfig", req, cb);
    }
    async ReleasePublicConfig(req, cb) {
        return this.request("ReleasePublicConfig", req, cb);
    }
    async DeleteUnitNamespaces(req, cb) {
        return this.request("DeleteUnitNamespaces", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async DraftApiGroup(req, cb) {
        return this.request("DraftApiGroup", req, cb);
    }
    async DisableLaneRule(req, cb) {
        return this.request("DisableLaneRule", req, cb);
    }
    async DescribeGatewayApis(req, cb) {
        return this.request("DescribeGatewayApis", req, cb);
    }
    async ContinueRunFailedTaskBatch(req, cb) {
        return this.request("ContinueRunFailedTaskBatch", req, cb);
    }
    async DescribeApiVersions(req, cb) {
        return this.request("DescribeApiVersions", req, cb);
    }
    async ChangeApiUsableStatus(req, cb) {
        return this.request("ChangeApiUsableStatus", req, cb);
    }
    async ModifyGroup(req, cb) {
        return this.request("ModifyGroup", req, cb);
    }
    async CreateContainGroup(req, cb) {
        return this.request("CreateContainGroup", req, cb);
    }
    async DescribeEnabledUnitRule(req, cb) {
        return this.request("DescribeEnabledUnitRule", req, cb);
    }
    async CreateProgram(req, cb) {
        return this.request("CreateProgram", req, cb);
    }
    async DescribeRepository(req, cb) {
        return this.request("DescribeRepository", req, cb);
    }
    async DescribeInvocationMetricDataDimension(req, cb) {
        return this.request("DescribeInvocationMetricDataDimension", req, cb);
    }
    async DescribeApplicationAttribute(req, cb) {
        return this.request("DescribeApplicationAttribute", req, cb);
    }
    async RevocationConfig(req, cb) {
        return this.request("RevocationConfig", req, cb);
    }
    async DescribeLaneRules(req, cb) {
        return this.request("DescribeLaneRules", req, cb);
    }
    async ModifyLaneRule(req, cb) {
        return this.request("ModifyLaneRule", req, cb);
    }
    async DescribeReleasedConfig(req, cb) {
        return this.request("DescribeReleasedConfig", req, cb);
    }
    async DescribePublicConfigReleases(req, cb) {
        return this.request("DescribePublicConfigReleases", req, cb);
    }
    async AssociateConfigWithGroup(req, cb) {
        return this.request("AssociateConfigWithGroup", req, cb);
    }
    async DescribeLanes(req, cb) {
        return this.request("DescribeLanes", req, cb);
    }
    async DescribeApiUseDetail(req, cb) {
        return this.request("DescribeApiUseDetail", req, cb);
    }
    async DescribeUploadInfo(req, cb) {
        return this.request("DescribeUploadInfo", req, cb);
    }
    async EnableUnitRoute(req, cb) {
        return this.request("EnableUnitRoute", req, cb);
    }
    async UpdateGatewayApi(req, cb) {
        return this.request("UpdateGatewayApi", req, cb);
    }
    async CreateApiRateLimitRuleWithDetailResp(req, cb) {
        return this.request("CreateApiRateLimitRuleWithDetailResp", req, cb);
    }
    async ShrinkGroup(req, cb) {
        return this.request("ShrinkGroup", req, cb);
    }
    async DescribeResourceTaskStatus(req, cb) {
        return this.request("DescribeResourceTaskStatus", req, cb);
    }
    async StartGroup(req, cb) {
        return this.request("StartGroup", req, cb);
    }
    async ModifyContainerGroup(req, cb) {
        return this.request("ModifyContainerGroup", req, cb);
    }
    async DescribeUnitRules(req, cb) {
        return this.request("DescribeUnitRules", req, cb);
    }
}
