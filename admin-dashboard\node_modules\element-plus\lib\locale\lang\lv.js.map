{"version": 3, "file": "lv.js", "sources": ["../../../../../packages/locale/lang/lv.ts"], "sourcesContent": ["export default {\n  name: 'lv',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: '<PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON>ad',\n      today: '<PERSON><PERSON><PERSON>',\n      cancel: 'Atcelt',\n      clear: 'Not<PERSON><PERSON><PERSON><PERSON>',\n      confirm: '<PERSON>i',\n      selectDate: 'Izvēlēties datumu',\n      selectTime: 'Izvēlēties laiku',\n      startDate: 'Sākuma datums',\n      startTime: 'Sākuma laiks',\n      endDate: 'Beigu datums',\n      endTime: 'Beigu laiks',\n      prevYear: 'Iepriek<PERSON>ējais gads',\n      nextYear: 'Nāka<PERSON>is gads',\n      prevMonth: 'I<PERSON>riek<PERSON>ē<PERSON><PERSON> mēnesis',\n      nextMonth: 'Nāka<PERSON>is mēnesis',\n      year: '',\n      month1: 'Janvāris',\n      month2: 'Febru<PERSON>ris',\n      month3: 'Marts',\n      month4: 'Aprī<PERSON>',\n      month5: 'Maijs',\n      month6: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      month7: 'J<PERSON>lijs',\n      month8: 'Augusts',\n      month9: 'Septembris',\n      month10: 'Oktobris',\n      month11: 'Novembris',\n      month12: 'Decembris',\n      // week: 'nedēļa',\n      weeks: {\n        sun: 'Sv',\n        mon: 'Pr',\n        tue: 'Ot',\n        wed: 'Tr',\n        thu: 'Ce',\n        fri: 'Pk',\n        sat: 'Se',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Jūn',\n        jul: 'Jūl',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Ielādē',\n      noMatch: 'Nav atbilstošu datu',\n      noData: 'Nav datu',\n      placeholder: 'Izvēlēties',\n    },\n    mention: {\n      loading: 'Ielādē',\n    },\n    cascader: {\n      noMatch: 'Nav atbilstošu datu',\n      loading: 'Ielādē',\n      placeholder: 'Izvēlēties',\n      noData: 'Nav datu',\n    },\n    pagination: {\n      goto: 'Iet uz',\n      pagesize: '/lapa',\n      total: 'Kopā {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Paziņojums',\n      confirm: 'Labi',\n      cancel: 'Atcelt',\n      error: 'Nederīga ievade',\n    },\n    upload: {\n      deleteTip: 'Nospiediet dzēst lai izņemtu',\n      delete: 'Dzēst',\n      preview: 'Priekšskatīt',\n      continue: 'Turpināt',\n    },\n    table: {\n      emptyText: 'Nav datu',\n      confirmFilter: 'Apstiprināt',\n      resetFilter: 'Atiestatīt',\n      clearFilter: 'Visi',\n      sumText: 'Summa',\n    },\n    tree: {\n      emptyText: 'Nav datu',\n    },\n    transfer: {\n      noMatch: 'Nav atbilstošu datu',\n      noData: 'Nav datu',\n      titles: ['Saraksts 1', 'Saraksts 2'],\n      filterPlaceholder: 'Ievadīt atslēgvārdu',\n      noCheckedFormat: '{total} vienības',\n      hasCheckedFormat: '{checked}/{total} atzīmēti',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,KAAK,EAAE,mBAAmB;AAChC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,UAAU,EAAE,6BAA6B;AAC/C,MAAM,UAAU,EAAE,4BAA4B;AAC9C,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,QAAQ,EAAE,8BAA8B;AAC9C,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,SAAS,EAAE,sCAAsC;AACvD,MAAM,SAAS,EAAE,4BAA4B;AAC7C,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,MAAM,EAAE,gBAAgB;AAC9B,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,WAAW,EAAE,sBAAsB;AACzC,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kBAAkB;AACjC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,kBAAkB;AACjC,MAAM,WAAW,EAAE,sBAAsB;AACzC,MAAM,MAAM,EAAE,UAAU;AACxB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,QAAQ;AACpB,MAAM,QAAQ,EAAE,OAAO;AACvB,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,OAAO,EAAE,MAAM;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,KAAK,EAAE,sBAAsB;AACnC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wCAAwC;AACzD,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,QAAQ,EAAE,eAAe;AAC/B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,UAAU;AAC3B,MAAM,aAAa,EAAE,kBAAkB;AACvC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,UAAU;AAC3B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,CAAC,YAAY,EAAE,YAAY,CAAC;AAC1C,MAAM,iBAAiB,EAAE,oCAAoC;AAC7D,MAAM,eAAe,EAAE,uBAAuB;AAC9C,MAAM,gBAAgB,EAAE,sCAAsC;AAC9D,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}