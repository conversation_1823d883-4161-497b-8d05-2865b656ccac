import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/smart_composer_controller.dart';
import '../../models/smart_composer_models.dart';

/// Smart Composer 设置界面
class SmartComposerSettingsScreen extends StatefulWidget {
  const SmartComposerSettingsScreen({super.key});

  @override
  State<SmartComposerSettingsScreen> createState() => _SmartComposerSettingsScreenState();
}

class _SmartComposerSettingsScreenState extends State<SmartComposerSettingsScreen> {
  final SmartComposerController _controller = Get.find<SmartComposerController>();
  final TextEditingController _systemPromptController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  void _loadSettings() {
    _systemPromptController.text = _controller.settings.value.systemPrompt ?? '';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI 写作助手设置'),
        actions: [
          TextButton(
            onPressed: _saveSettings,
            child: const Text('保存'),
          ),
        ],
      ),
      body: Obx(() => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 默认模型选择
            _buildDefaultModelSection(),
            const SizedBox(height: 24),
            
            // 提供商管理
            _buildProvidersSection(),
            const SizedBox(height: 24),
            
            // 系统提示设置
            _buildSystemPromptSection(),
            const SizedBox(height: 24),
            
            // 快速配置
            _buildQuickSetupSection(),
          ],
        ),
      )),
    );
  }

  Widget _buildDefaultModelSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '默认模型',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            DropdownButtonFormField<String>(
              value: _controller.settings.value.defaultChatModelId,
              decoration: const InputDecoration(
                labelText: '选择默认AI模型',
                border: OutlineInputBorder(),
              ),
              items: _controller.availableModels.map((model) {
                final provider = _controller.availableProviders
                    .where((p) => p.id == model.providerId)
                    .firstOrNull;
                final isConfigured = provider != null && 
                    _controller.isProviderConfigured(provider.id);
                
                return DropdownMenuItem(
                  value: model.id,
                  child: Row(
                    children: [
                      Icon(
                        isConfigured ? Icons.check_circle : Icons.warning,
                        size: 16,
                        color: isConfigured ? Colors.green : Colors.orange,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${model.id} (${provider?.type.displayName ?? '未知'})',
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  _controller.setDefaultModel(value);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProvidersSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'AI 提供商',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                TextButton.icon(
                  onPressed: _showAddProviderDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('添加'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            ..._controller.availableProviders.map((provider) => 
              _buildProviderItem(provider)
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProviderItem(LLMProvider provider) {
    final isConfigured = _controller.isProviderConfigured(provider.id);
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          isConfigured ? Icons.check_circle : Icons.warning,
          color: isConfigured ? Colors.green : Colors.orange,
        ),
        title: Text(provider.type.displayName),
        subtitle: Text(
          isConfigured ? '已配置' : '需要配置 API Key',
          style: TextStyle(
            color: isConfigured ? Colors.green : Colors.orange,
          ),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleProviderAction(value, provider),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete),
                  SizedBox(width: 8),
                  Text('删除'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSystemPromptSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '系统提示',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            TextField(
              controller: _systemPromptController,
              maxLines: 8,
              decoration: const InputDecoration(
                labelText: '自定义系统提示',
                hintText: '输入您希望AI助手遵循的指导原则...',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                TextButton(
                  onPressed: _resetSystemPrompt,
                  child: const Text('重置为默认'),
                ),
                const Spacer(),
                Text(
                  '${_systemPromptController.text.length} 字符',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSetupSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '快速配置',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text(
              '选择一个AI提供商快速开始：',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildQuickSetupButton('OpenAI', LLMProviderType.openai),
                _buildQuickSetupButton('Claude', LLMProviderType.anthropic),
                _buildQuickSetupButton('Gemini', LLMProviderType.gemini),
                _buildQuickSetupButton('DeepSeek', LLMProviderType.deepseek),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSetupButton(String name, LLMProviderType type) {
    return ElevatedButton(
      onPressed: () => _showQuickSetupDialog(type),
      child: Text(name),
    );
  }

  void _handleProviderAction(String action, LLMProvider provider) {
    switch (action) {
      case 'edit':
        _showEditProviderDialog(provider);
        break;
      case 'delete':
        _showDeleteProviderDialog(provider);
        break;
    }
  }

  void _showAddProviderDialog() {
    // TODO: 实现添加提供商对话框
    Get.snackbar('提示', '添加提供商功能正在开发中');
  }

  void _showEditProviderDialog(LLMProvider provider) {
    final apiKeyController = TextEditingController(text: provider.apiKey ?? '');
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('编辑 ${provider.type.displayName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: apiKeyController,
              decoration: const InputDecoration(
                labelText: 'API Key',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final updatedProvider = provider.copyWith(
                apiKey: apiKeyController.text,
              );
              _controller.updateProvider(updatedProvider);
              Navigator.pop(context);
              Get.snackbar('成功', '提供商配置已更新');
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  void _showDeleteProviderDialog(LLMProvider provider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除提供商'),
        content: Text('确定要删除 ${provider.type.displayName} 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              _controller.removeProvider(provider.id);
              Navigator.pop(context);
              Get.snackbar('成功', '提供商已删除');
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showQuickSetupDialog(LLMProviderType type) {
    final apiKeyController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('配置 ${type.displayName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('请输入您的 ${type.displayName} API Key：'),
            const SizedBox(height: 16),
            TextField(
              controller: apiKeyController,
              decoration: const InputDecoration(
                labelText: 'API Key',
                border: OutlineInputBorder(),
              ),
              obscureText: true,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final provider = LLMProvider(
                id: '${type.value}-default',
                type: type,
                apiKey: apiKeyController.text,
              );
              _controller.updateProvider(provider);
              Navigator.pop(context);
              Get.snackbar('成功', '${type.displayName} 配置完成');
            },
            child: const Text('保存'),
          ),
        ],
      ),
    );
  }

  void _resetSystemPrompt() {
    _systemPromptController.text = SmartComposerDefaults.defaultSystemPrompt;
  }

  void _saveSettings() {
    _controller.updateSystemPrompt(_systemPromptController.text);
    Get.snackbar('成功', '设置已保存');
  }

  @override
  void dispose() {
    _systemPromptController.dispose();
    super.dispose();
  }
}
