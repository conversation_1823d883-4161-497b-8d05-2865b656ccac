import{_ as u}from"./_plugin-vue_export-helper-CoKMZnro.js";import{d as m,c as f,b as e,e as t,w as s,q as v,i as k,h as l,E as B,B as _,u as x}from"./index-CAzH2L69.js";const C={class:"not-found-container"},w={class:"not-found-content"},E={class:"error-actions"},b=m({__name:"404",setup(g){const n=x(),d=()=>{n.push("/")},c=()=>{n.back()};return(H,o)=>{const i=_("HomeFilled"),r=B,a=v,p=_("ArrowLeft");return k(),f("div",C,[e("div",w,[o[2]||(o[2]=e("div",{class:"error-code"},"404",-1)),o[3]||(o[3]=e("div",{class:"error-message"},"页面不存在",-1)),o[4]||(o[4]=e("div",{class:"error-description"}," 抱歉，您访问的页面不存在或已被删除 ",-1)),e("div",E,[t(a,{type:"primary",onClick:d},{default:s(()=>[t(r,null,{default:s(()=>[t(i)]),_:1}),o[0]||(o[0]=l(" 返回首页 "))]),_:1,__:[0]}),t(a,{onClick:c},{default:s(()=>[t(r,null,{default:s(()=>[t(p)]),_:1}),o[1]||(o[1]=l(" 返回上页 "))]),_:1,__:[1]})])])])}}}),y=u(b,[["__scopeId","data-v-aa7f08a9"]]);export{y as default};
