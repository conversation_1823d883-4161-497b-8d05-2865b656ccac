"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Client = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * Copyright (c) 2018 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
const abstract_client_1 = require("../../../common/abstract_client");
/**
 * csip client
 * @class
 */
class Client extends abstract_client_1.AbstractClient {
    constructor(clientConfig) {
        super("csip.tencentcloudapi.com", "2022-11-21", clientConfig);
    }
    /**
     * 查询用户行为分析策略列表
     */
    async DescribeUebaRule(req, cb) {
        return this.request("DescribeUebaRule", req, cb);
    }
    /**
     * 获取任务扫描报告列表
     */
    async DescribeTaskLogList(req, cb) {
        return this.request("DescribeTaskLogList", req, cb);
    }
    /**
     * 删除域名和ip请求
     */
    async DeleteDomainAndIp(req, cb) {
        return this.request("DeleteDomainAndIp", req, cb);
    }
    /**
     * 查询云边界分析-暴露路径下主机节点的漏洞列表
     */
    async DescribeVulRiskList(req, cb) {
        return this.request("DescribeVulRiskList", req, cb);
    }
    /**
     * 查询风险规则详情示例
     */
    async DescribeRiskRuleDetail(req, cb) {
        return this.request("DescribeRiskRuleDetail", req, cb);
    }
    /**
     * db资产详情
     */
    async DescribeDbAssetInfo(req, cb) {
        return this.request("DescribeDbAssetInfo", req, cb);
    }
    /**
     * 获取网卡列表
     */
    async DescribeNICAssets(req, cb) {
        return this.request("DescribeNICAssets", req, cb);
    }
    /**
     * 获取调用记录列表
     */
    async DescribeAbnormalCallRecord(req, cb) {
        return this.request("DescribeAbnormalCallRecord", req, cb);
    }
    /**
     * 获取漏洞视角的漏洞风险列表
     */
    async DescribeRiskCenterVULViewVULRiskList(req, cb) {
        return this.request("DescribeRiskCenterVULViewVULRiskList", req, cb);
    }
    /**
     * 域名列表
     */
    async DescribeDomainAssets(req, cb) {
        return this.request("DescribeDomainAssets", req, cb);
    }
    /**
     * 编辑访问密钥/源IP备注
     */
    async UpdateAccessKeyRemark(req, cb) {
        return this.request("UpdateAccessKeyRemark", req, cb);
    }
    /**
     * ip公网列表
     */
    async DescribePublicIpAssets(req, cb) {
        return this.request("DescribePublicIpAssets", req, cb);
    }
    /**
     * csip角色授权绑定接口
     */
    async AddNewBindRoleUser(req, cb) {
        return this.request("AddNewBindRoleUser", req, cb);
    }
    /**
     * 新安全中心风险中心-漏洞列表
     */
    async DescribeVULList(req, cb) {
        return this.request("DescribeVULList", req, cb);
    }
    /**
     * 获取调用记录列表
     */
    async DescribeCallRecord(req, cb) {
        return this.request("DescribeCallRecord", req, cb);
    }
    /**
     * 查询用户的账号列表
     */
    async DescribeAccessKeyUserList(req, cb) {
        return this.request("DescribeAccessKeyUserList", req, cb);
    }
    /**
     * 查询clb监听器列表
     */
    async DescribeListenerList(req, cb) {
        return this.request("DescribeListenerList", req, cb);
    }
    /**
     * 停止扫风险中心扫描任务
     */
    async StopRiskCenterTask(req, cb) {
        return this.request("StopRiskCenterTask", req, cb);
    }
    /**
     * 查询用户的账号详情
     */
    async DescribeAccessKeyUserDetail(req, cb) {
        return this.request("DescribeAccessKeyUserDetail", req, cb);
    }
    /**
     * 风险详情列表示例
     */
    async DescribeRiskDetailList(req, cb) {
        return this.request("DescribeRiskDetailList", req, cb);
    }
    /**
     * 删除风险中心扫描任务
     */
    async DeleteRiskScanTask(req, cb) {
        return this.request("DeleteRiskScanTask", req, cb);
    }
    /**
     * 获取资产视角的弱口令风险列表
     */
    async DescribeRiskCenterAssetViewWeakPasswordRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewWeakPasswordRiskList", req, cb);
    }
    /**
     * 获取资产视角的漏洞风险列表
     */
    async DescribeRiskCenterAssetViewVULRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewVULRiskList", req, cb);
    }
    /**
     * 集群pod列表
     */
    async DescribeClusterPodAssets(req, cb) {
        return this.request("DescribeClusterPodAssets", req, cb);
    }
    /**
     * 资产列表
     */
    async DescribeOtherCloudAssets(req, cb) {
        return this.request("DescribeOtherCloudAssets", req, cb);
    }
    /**
     * 获取资产视角的配置风险列表
     */
    async DescribeRiskCenterAssetViewCFGRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewCFGRiskList", req, cb);
    }
    /**
     * 获取vpc列表
     */
    async DescribeVpcAssets(req, cb) {
        return this.request("DescribeVpcAssets", req, cb);
    }
    /**
     * 获取用户访问密钥资产列表（源IP视角）
     */
    async DescribeSourceIPAsset(req, cb) {
        return this.request("DescribeSourceIPAsset", req, cb);
    }
    /**
     * 获取风险服务列表
     */
    async DescribeRiskCenterServerRiskList(req, cb) {
        return this.request("DescribeRiskCenterServerRiskList", req, cb);
    }
    /**
     * 获取cvm列表
     */
    async DescribeCVMAssets(req, cb) {
        return this.request("DescribeCVMAssets", req, cb);
    }
    /**
     * 修改风险中心扫描任务
     */
    async ModifyRiskCenterScanTask(req, cb) {
        return this.request("ModifyRiskCenterScanTask", req, cb);
    }
    /**
     * 检测AK 异步任务
     */
    async CreateAccessKeyCheckTask(req, cb) {
        return this.request("CreateAccessKeyCheckTask", req, cb);
    }
    /**
     * 获取资产视角的漏洞风险列表
     */
    async DescribeAssetViewVulRiskList(req, cb) {
        return this.request("DescribeAssetViewVulRiskList", req, cb);
    }
    /**
     * 获取报告下载的临时链接
     */
    async DescribeTaskLogURL(req, cb) {
        return this.request("DescribeTaskLogURL", req, cb);
    }
    /**
     * 检查视角下云资源配置风险列表示例
     */
    async DescribeCheckViewRisks(req, cb) {
        return this.request("DescribeCheckViewRisks", req, cb);
    }
    /**
     * 获取内容风险列表
     */
    async DescribeRiskCenterWebsiteRiskList(req, cb) {
        return this.request("DescribeRiskCenterWebsiteRiskList", req, cb);
    }
    /**
     * 云防资产中心统计数据
     */
    async DescribeCFWAssetStatistics(req, cb) {
        return this.request("DescribeCFWAssetStatistics", req, cb);
    }
    /**
     * 高级配置风险规则列表示例
     */
    async DescribeRiskRules(req, cb) {
        return this.request("DescribeRiskRules", req, cb);
    }
    /**
     * 资产视角下云资源配置风险列表
     */
    async DescribeAssetRiskList(req, cb) {
        return this.request("DescribeAssetRiskList", req, cb);
    }
    /**
     * 仓库镜像列表
     */
    async DescribeRepositoryImageAssets(req, cb) {
        return this.request("DescribeRepositoryImageAssets", req, cb);
    }
    /**
     * 创建风险中心扫描任务
     */
    async CreateRiskCenterScanTask(req, cb) {
        return this.request("CreateRiskCenterScanTask", req, cb);
    }
    /**
     * 查询云边界分析路径节点
     */
    async DescribeExposePath(req, cb) {
        return this.request("DescribeExposePath", req, cb);
    }
    /**
     * 获取网关列表
     */
    async DescribeGatewayAssets(req, cb) {
        return this.request("DescribeGatewayAssets", req, cb);
    }
    /**
     * 查询TOP攻击信息
     */
    async DescribeTopAttackInfo(req, cb) {
        return this.request("DescribeTopAttackInfo", req, cb);
    }
    /**
     * 立体防护中心查询漏洞信息
     */
    async DescribeSearchBugInfo(req, cb) {
        return this.request("DescribeSearchBugInfo", req, cb);
    }
    /**
     * 获取漏洞展开详情
     */
    async DescribeVULRiskDetail(req, cb) {
        return this.request("DescribeVULRiskDetail", req, cb);
    }
    /**
     * 访问密钥告警记录列表
     */
    async DescribeAccessKeyAlarm(req, cb) {
        return this.request("DescribeAccessKeyAlarm", req, cb);
    }
    /**
     * 获取漏洞视角的漏洞风险列表
     */
    async DescribeVulViewVulRiskList(req, cb) {
        return this.request("DescribeVulViewVulRiskList", req, cb);
    }
    /**
     * 获取账号调用记录列表
     */
    async DescribeUserCallRecord(req, cb) {
        return this.request("DescribeUserCallRecord", req, cb);
    }
    /**
     * 查询云边界分析-暴露路径下主机节点的高危基线风险列表
     */
    async DescribeHighBaseLineRiskList(req, cb) {
        return this.request("DescribeHighBaseLineRiskList", req, cb);
    }
    /**
     * 获取扫描报告列表
     */
    async DescribeScanReportList(req, cb) {
        return this.request("DescribeScanReportList", req, cb);
    }
    /**
     * 创建域名、ip相关信息
     */
    async CreateDomainAndIp(req, cb) {
        return this.request("CreateDomainAndIp", req, cb);
    }
    /**
     * 告警中心全量告警列表接口
     */
    async DescribeAlertList(req, cb) {
        return this.request("DescribeAlertList", req, cb);
    }
    /**
     * 获取扫描任务列表
     */
    async DescribeScanTaskList(req, cb) {
        return this.request("DescribeScanTaskList", req, cb);
    }
    /**
     * 标记风险或者告警为 已处置/已忽略
     */
    async UpdateAccessKeyAlarmStatus(req, cb) {
        return this.request("UpdateAccessKeyAlarmStatus", req, cb);
    }
    /**
     * 更新自定义策略的开关
     */
    async ModifyUebaRuleSwitch(req, cb) {
        return this.request("ModifyUebaRuleSwitch", req, cb);
    }
    /**
     * 批量告警状态处理接口
     */
    async UpdateAlertStatusList(req, cb) {
        return this.request("UpdateAlertStatusList", req, cb);
    }
    /**
     * 查询云边界分析扫描结果统计信息
     */
    async DescribeScanStatistic(req, cb) {
        return this.request("DescribeScanStatistic", req, cb);
    }
    /**
     * 云边界分析资产分类
     */
    async DescribeExposeAssetCategory(req, cb) {
        return this.request("DescribeExposeAssetCategory", req, cb);
    }
    /**
     * 查询集团账号用户列表
     */
    async DescribeOrganizationUserInfo(req, cb) {
        return this.request("DescribeOrganizationUserInfo", req, cb);
    }
    /**
     * 查询云边界分析-暴露路径下主机节点的进程列表
     */
    async DescribeAssetProcessList(req, cb) {
        return this.request("DescribeAssetProcessList", req, cb);
    }
    /**
     * 发起AK资产同步任务
     */
    async CreateAccessKeySyncTask(req, cb) {
        return this.request("CreateAccessKeySyncTask", req, cb);
    }
    /**
     * 获取端口视角的端口风险列表
     */
    async DescribeRiskCenterPortViewPortRiskList(req, cb) {
        return this.request("DescribeRiskCenterPortViewPortRiskList", req, cb);
    }
    /**
     * 查询集团的子账号列表
     */
    async DescribeSubUserInfo(req, cb) {
        return this.request("DescribeSubUserInfo", req, cb);
    }
    /**
     * 获取资产视角的端口风险列表
     */
    async DescribeRiskCenterAssetViewPortRiskList(req, cb) {
        return this.request("DescribeRiskCenterAssetViewPortRiskList", req, cb);
    }
    /**
     * 查询漏洞风险高级配置
     */
    async DescribeVULRiskAdvanceCFGList(req, cb) {
        return this.request("DescribeVULRiskAdvanceCFGList", req, cb);
    }
    /**
     * 查询集团账号详情
     */
    async DescribeOrganizationInfo(req, cb) {
        return this.request("DescribeOrganizationInfo", req, cb);
    }
    /**
     * 云边界分析资产列表
     */
    async DescribeExposures(req, cb) {
        return this.request("DescribeExposures", req, cb);
    }
    /**
     * 修改风险中心风险状态
     */
    async ModifyRiskCenterRiskStatus(req, cb) {
        return this.request("ModifyRiskCenterRiskStatus", req, cb);
    }
    /**
     * 修改集团账号状态
     */
    async ModifyOrganizationAccountStatus(req, cb) {
        return this.request("ModifyOrganizationAccountStatus", req, cb);
    }
    /**
     * 访问密钥告警记录详情
     */
    async DescribeAccessKeyAlarmDetail(req, cb) {
        return this.request("DescribeAccessKeyAlarmDetail", req, cb);
    }
    /**
     * 获取配置视角的配置风险列表
     */
    async DescribeRiskCenterCFGViewCFGRiskList(req, cb) {
        return this.request("DescribeRiskCenterCFGViewCFGRiskList", req, cb);
    }
    /**
     * 数据库资产列表
     */
    async DescribeDbAssets(req, cb) {
        return this.request("DescribeDbAssets", req, cb);
    }
    /**
     * 集群列表
     */
    async DescribeClusterAssets(req, cb) {
        return this.request("DescribeClusterAssets", req, cb);
    }
    /**
     * 访问密钥风险记录列表
     */
    async DescribeAccessKeyRisk(req, cb) {
        return this.request("DescribeAccessKeyRisk", req, cb);
    }
    /**
     * 获取子网列表
     */
    async DescribeSubnetAssets(req, cb) {
        return this.request("DescribeSubnetAssets", req, cb);
    }
    /**
     * 获取风险中心风险概况示例
     */
    async DescribeCSIPRiskStatistics(req, cb) {
        return this.request("DescribeCSIPRiskStatistics", req, cb);
    }
    /**
     * 访问密钥风险记录详情
     */
    async DescribeAccessKeyRiskDetail(req, cb) {
        return this.request("DescribeAccessKeyRiskDetail", req, cb);
    }
    /**
     * 获取用户访问密钥资产列表
     */
    async DescribeAccessKeyAsset(req, cb) {
        return this.request("DescribeAccessKeyAsset", req, cb);
    }
    /**
     * 获取风险调用记录列表
     */
    async DescribeRiskCallRecord(req, cb) {
        return this.request("DescribeRiskCallRecord", req, cb);
    }
    /**
     * cvm详情
     */
    async DescribeCVMAssetInfo(req, cb) {
        return this.request("DescribeCVMAssetInfo", req, cb);
    }
}
exports.Client = Client;
