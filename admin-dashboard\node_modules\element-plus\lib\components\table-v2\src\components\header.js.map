{"version": 3, "file": "header.js", "sources": ["../../../../../../../packages/components/table-v2/src/components/header.tsx"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  inject,\n  nextTick,\n  onUpdated,\n  ref,\n  unref,\n} from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ensureArray } from '@element-plus/utils'\nimport { tableV2HeaderProps } from '../header'\nimport { enforceUnit } from '../utils'\nimport { TABLE_V2_GRID_INJECTION_KEY } from '../tokens'\n\nimport type { CSSProperties, Ref, UnwrapRef } from 'vue'\nimport type { TableV2HeaderProps } from '../header'\nimport type { UseColumnsReturn } from '../composables/use-columns'\n\nconst COMPONENT_NAME = 'ElTableV2Header'\nconst TableV2Header = defineComponent({\n  name: COMPONENT_NAME,\n  props: tableV2HeaderProps,\n  setup(props, { slots, expose }) {\n    const ns = useNamespace('table-v2')\n    const scrollLeftInfo = inject<Ref<number>>(TABLE_V2_GRID_INJECTION_KEY)\n\n    const headerRef = ref<HTMLElement>()\n\n    const headerStyle = computed(() =>\n      enforceUnit({\n        width: props.width,\n        height: props.height,\n      })\n    )\n\n    const rowStyle = computed(() =>\n      enforceUnit({\n        width: props.rowWidth,\n        height: props.height,\n      })\n    )\n\n    const headerHeights = computed(() => ensureArray(unref(props.headerHeight)))\n\n    const scrollToLeft = (left?: number) => {\n      const headerEl = unref(headerRef)\n      nextTick(() => {\n        headerEl?.scroll &&\n          headerEl.scroll({\n            left,\n          })\n      })\n    }\n\n    const renderFixedRows = () => {\n      const fixedRowClassName = ns.e('fixed-header-row')\n\n      const { columns, fixedHeaderData, rowHeight } = props\n\n      return fixedHeaderData?.map((fixedRowData, fixedRowIndex) => {\n        const style: CSSProperties = enforceUnit({\n          height: rowHeight,\n          width: '100%',\n        })\n\n        return slots.fixed?.({\n          class: fixedRowClassName,\n          columns,\n          rowData: fixedRowData,\n          rowIndex: -(fixedRowIndex + 1),\n          style,\n        })\n      })\n    }\n\n    const renderDynamicRows = () => {\n      const dynamicRowClassName = ns.e('dynamic-header-row')\n      const { columns } = props\n\n      return unref(headerHeights).map((rowHeight, rowIndex) => {\n        const style: CSSProperties = enforceUnit({\n          width: '100%',\n          height: rowHeight,\n        })\n\n        return slots.dynamic?.({\n          class: dynamicRowClassName,\n          columns,\n          headerIndex: rowIndex,\n          style,\n        })\n      })\n    }\n\n    onUpdated(() => {\n      if (scrollLeftInfo?.value) {\n        scrollToLeft(scrollLeftInfo.value)\n      }\n    })\n    expose({\n      /**\n       * @description scroll to position based on the provided value\n       */\n      scrollToLeft,\n    })\n\n    return () => {\n      if (props.height <= 0) return\n\n      return (\n        <div\n          ref={headerRef}\n          class={props.class}\n          style={unref(headerStyle)}\n          role=\"rowgroup\"\n        >\n          <div style={unref(rowStyle)} class={ns.e('header')}>\n            {renderDynamicRows()}\n            {renderFixedRows()}\n          </div>\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2Header\n\nexport type TableV2HeaderInstance = InstanceType<typeof TableV2Header> & {\n  /**\n   * @description scroll to position based on the provided value\n   */\n  scrollToLeft: (left?: number) => void\n}\n\nexport type TableV2HeaderRendererParams = {\n  class: string\n  columns: TableV2HeaderProps['columns']\n  columnsStyles: UnwrapRef<UseColumnsReturn['columnsStyles']>\n  headerIndex: number\n  style: CSSProperties\n}\n\nexport type TableV2HeaderRowRendererParams = {\n  rowData: any\n  rowIndex: number\n} & Omit<TableV2HeaderRendererParams, 'headerIndex'>\n"], "names": ["COMPONENT_NAME", "TableV2Header", "defineComponent", "name", "props", "tableV2HeaderProps", "slots", "expose", "useNamespace", "ns", "inject", "scrollLeftInfo", "ref", "headerRef", "enforceUnit", "computed", "width", "height", "row<PERSON>id<PERSON>", "ensureArray", "unref", "headerHeights", "scrollToLeft", "nextTick", "headerEl", "left", "renderFixedRows", "rowHeight", "fixedHeaderData", "style", "class", "fixedRowClassName", "columns", "rowData", "rowIndex", "renderDynamicRows", "headerIndex", "onUpdated", "_createVNode", "headerStyle"], "mappings": ";;;;;;;;;;;AAmBA,MAAMA,cAAc,GAAG,iBAAvB,CAAA;AACA,MAAMC,aAAa,GAAGC,mBAAe,CAAC;AACpCC,EAAAA,IAAI,EAAEH,cAD8B;AAEpCI,EAAAA,KAAK,EAAEC,yBAF6B;;IAG/B;IAAUC,MAAF;AAASC,GAAAA,EAAAA;AAAT,IAAmB,MAAA,EAAA,GAAAC,kBAAA,CAAA,UAAA,CAAA,CAAA;AAC9B,IAAA,MAAMC,cAAiB,GAAAC,6CAAvB,CAAA,CAAA;AACA,IAAA,MAAMC,SAAc,GAAAC,OAAA,EAAGF,CAAM;IAE7B,MAAMG,WAAYD,GAAAA,YAAlB,CAAA,MAAAE,iBAAA,CAAA;AAEA,MAAA,kBAAoBC;MAEhBC,MAAK,EAAO,KAACA,CADH,MAAA;MAEVC,CAAM,CAAA;AAFI,IAAA,MADd,QAAA,GAAAF,YAAA,CAAA,MAAAD,iBAAA,CAAA;AAOA,MAAA,YAAc,CAAGC,QAAAA;MAEbC,MAAK,EAAO,KAACE,CADH,MAAA;MAEVD,CAAM,CAAA;AAFI,IAAA,MADd,aAAA,GAAAF,YAAA,CAAA,MAAAI,uBAAA,CAAAC,SAAA,CAAA,KAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA;AAOA,IAAA,MAAMC,YAAa,GAAA,CAAGN,IAAQ,KAAC;;MAEzBO,YAAAA,CAAAA,MAAAA;AACJ,QAAA,CAAA,QAAc,IAAA,IAAQ,GAAA,iBAAtB,CAAA,MAAA,KAAA,QAAA,CAAA,MAAA,CAAA;AACAC,UAAAA,IAAQ;AACNC,SAAAA,CAAAA,CAAAA;AAEIC,OAAAA,CAAAA,CAAAA;AADc,KAAA,CAAA;AAGnB,IAAA,MALD,eAAA,GAAA,MAAA;MAFF,MAAA,iBAAA,GAAA,EAAA,CAAA,CAAA,CAAA,kBAAA,CAAA,CAAA;;QAUMC,OAAAA;AACJ,QAAA;QAEM,SAAA;UAAA,KAAA,CAAA;aAAA,eAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,eAAA,CAAA,GAAA,CAAA,CAAA,YAAA,EAAA,aAAA,KAAA;AAA4BC,QAAAA,IAAAA,EAAAA,CAAAA;AAA5B,QAAA,MAAN,KAAA,GAAAb,iBAAA,CAAA;UAEOc,MAAAA,EAAAA,SAAAA;UACCC,KAAAA,EAAAA,MAAuBf;AAC3BG,SAAAA,CAAAA,CAAAA;AACAD,QAAAA,OAAK,CAAE,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA;AAFgC,UAAzC,KAAA,EAAA,iBAAA;UAKOV,OAAAA;AACLwB,UAAAA,OAAOC,EADY,YAAA;UAEnBC,QAFmB,EAAA,EAAA,aAAA,GAAA,CAAA,CAAA;AAGnBC,UAAAA,KAAAA;AACAC,SAAAA,CAAAA,CAAAA;AACAL,OAAAA,CAAAA,CAAAA;AALmB,KAAA,CAAA;AAOtB,IAAA,MAbD,iBAAA,GAAA,MAAA;MALF,MAAA,mBAAA,GAAA,EAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,CAAA;;QAqBMM,OAAAA;AACJ,OAAA,GAAA;MACA,OAAMf,SAAA,CAAA,aAAA,CAAA,CAAA,GAAA,CAAA,CAAA,SAAA,EAAA,QAAA,KAAA;AAAEY,QAAAA,IAAAA,EAAAA,CAAAA;AAAF,QAAA,MAAN,KAAA,GAAAlB,iBAAA,CAAA;UAEOM,KAAAA,EAAAA,MAAMC;UACLQ,MAAAA,EAAAA,SAAuBf;AAC3BE,SAAAA,CAAAA,CAAAA;AACAC,QAAAA,OAAAA,CAAM,EAAEU,GAAAA,KAAAA,CAAAA,OAAAA,KAAAA,IAAAA,GAAAA,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,IAAAA,CAAAA,KAAAA,EAAAA;AAF+B,UAAzC,KAAA,EAAA,mBAAA;UAKOrB,OAAAA;AACLwB,UAAAA,WADqB,EAAA,QAAA;UAErBE,KAFqB;AAGrBI,SAAAA,CAAAA,CAAAA;AACAP,OAAAA,CAAAA,CAAAA;AAJqB,KAAA,CAAA;AAMxB,IAAAQ,aAZD,CAAA,MAAA;MAJF,IAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAA,KAAA,EAAA;;AAmBAA,OAAAA;MACE,CAAI1B;AACFW,IAAAA,MAAAA,CAAAA;AACD,MAAA,YAAA;AACF,KAJQ,CAAT,CAAA;AAKAf,IAAAA,OAAO,MAAA;AACL,MAAA,IAAA,KAAA,CAAA,MAAA,IAAA,CAAA;AACN,QAAA,OAAA;AACA,MAAA,OAAA+B,eAAA,CAAA,KAAA,EAAA;AACMhB,QAAAA,KAAAA,EAAAA,SAAAA;AAJK,QAAP,OAAA,EAAA,KAAA,CAAA,KAAA;AAOA,QAAA,OAAa,EAAAF,SAAA,CAAA,WAAA,CAAA;AACX,QAAA,MAAS,EAACH,UAAU;AAEpB,OAAA,EAAA,CAAAqB,eAAA,CAAA,KAAA,EAAA;AAAA,QAAA,OAESzB,EAFTO,SAAA,CAAA,QAAA,CAAA;QAAA,OAGWhB,EAAAA,EAAAA,CAAAA,CAAAA,CAAK,QAHhB,CAAA;SAIWgB,CAAAA,iBAAMmB,EAAAA,EAAAA,eAJjB,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;;AAAA,GAAA;;AAAA,aAAA,aAOwC;;;;"}