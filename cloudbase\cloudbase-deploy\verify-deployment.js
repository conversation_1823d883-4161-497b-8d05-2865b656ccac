const https = require('https');

// 验证CloudBase部署状态
console.log('🚀 验证腾讯云CloudBase部署状态...\n');

const tests = [
  {
    name: '静态托管根路径',
    url: 'https://api.dznovel.top',
    expected: '静态托管页面'
  },
  {
    name: '云函数根路径',
    url: 'https://api.dznovel.top/api/',
    expected: '云函数欢迎消息'
  },
  {
    name: '健康检查接口',
    url: 'https://api.dznovel.top/api/health',
    expected: '健康状态信息'
  },
  {
    name: '套餐接口',
    url: 'https://api.dznovel.top/api/packages',
    expected: '套餐列表'
  },
  {
    name: 'CloudBase默认域名测试',
    url: 'https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/novel-app-api',
    expected: '云函数直接访问'
  }
];

async function testUrl(test) {
  return new Promise((resolve) => {
    console.log(`🔍 测试: ${test.name}`);
    console.log(`   URL: ${test.url}`);
    
    const req = https.get(test.url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        const result = {
          name: test.name,
          url: test.url,
          status: res.statusCode,
          data: data.substring(0, 500),
          success: res.statusCode === 200
        };
        
        if (result.success) {
          console.log(`   ✅ 成功 - 状态码: ${result.status}`);
          try {
            const jsonData = JSON.parse(result.data);
            console.log(`   📄 响应: ${JSON.stringify(jsonData, null, 2).substring(0, 200)}...`);
          } catch (e) {
            console.log(`   📄 响应: ${result.data.substring(0, 100)}...`);
          }
        } else {
          console.log(`   ❌ 失败 - 状态码: ${result.status}`);
          console.log(`   📄 错误: ${result.data.substring(0, 100)}...`);
        }
        console.log('');
        resolve(result);
      });
    });

    req.on('error', (error) => {
      console.log(`   ❌ 网络错误: ${error.message}`);
      console.log('');
      resolve({
        name: test.name,
        url: test.url,
        status: 'ERROR',
        data: error.message,
        success: false
      });
    });

    req.setTimeout(15000, () => {
      req.destroy();
      console.log(`   ⏰ 请求超时`);
      console.log('');
      resolve({
        name: test.name,
        url: test.url,
        status: 'TIMEOUT',
        data: '请求超时',
        success: false
      });
    });
  });
}

async function runAllTests() {
  const results = [];
  
  for (const test of tests) {
    const result = await testUrl(test);
    results.push(result);
    
    // 等待1秒再进行下一个测试
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  // 生成测试报告
  console.log('📊 测试结果汇总:');
  console.log('='.repeat(60));
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ 成功: ${successful.length}/${results.length}`);
  console.log(`❌ 失败: ${failed.length}/${results.length}`);
  console.log('');
  
  if (successful.length > 0) {
    console.log('✅ 成功的测试:');
    successful.forEach(r => {
      console.log(`   - ${r.name}: ${r.url}`);
    });
    console.log('');
  }
  
  if (failed.length > 0) {
    console.log('❌ 失败的测试:');
    failed.forEach(r => {
      console.log(`   - ${r.name}: ${r.url} (${r.status})`);
    });
    console.log('');
  }
  
  // 提供建议
  console.log('💡 建议:');
  if (failed.some(r => r.url.includes('/api/health'))) {
    console.log('   - 健康检查接口失败，可能需要检查CloudBase路径映射配置');
  }
  if (failed.some(r => r.url.includes('service.tcloudbase.com'))) {
    console.log('   - CloudBase默认域名访问失败，可能需要配置HTTP触发器');
  }
  if (successful.some(r => r.url.includes('/api/'))) {
    console.log('   - 云函数根路径可访问，说明基础配置正确');
  }
  
  console.log('\n🔗 有用的链接:');
  console.log('   - CloudBase控制台: https://console.cloud.tencent.com/tcb/env/index?envId=novel-app-2gywkgnn15cbd6a8');
  console.log('   - 云函数管理: https://console.cloud.tencent.com/tcb/scf?envId=novel-app-2gywkgnn15cbd6a8&rid=4');
  console.log('   - 自定义域名: https://console.cloud.tencent.com/tcb/env/access?envId=novel-app-2gywkgnn15cbd6a8');
  
  return results;
}

// 运行测试
runAllTests().catch(console.error);
