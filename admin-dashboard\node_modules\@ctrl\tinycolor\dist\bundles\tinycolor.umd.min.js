!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).tinycolor=e()}(this,(function(){"use strict";var t={aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aqua:"#00ffff",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",black:"#000000",blanchedalmond:"#ffebcd",blue:"#0000ff",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",cyan:"#00ffff",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",fuchsia:"#ff00ff",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",goldenrod:"#daa520",gold:"#ffd700",gray:"#808080",green:"#008000",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavenderblush:"#fff0f5",lavender:"#e6e6fa",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",lime:"#00ff00",limegreen:"#32cd32",linen:"#faf0e6",magenta:"#ff00ff",maroon:"#800000",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",navy:"#000080",oldlace:"#fdf5e6",olive:"#808000",olivedrab:"#6b8e23",orange:"#ffa500",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",purple:"#800080",rebeccapurple:"#663399",red:"#ff0000",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",silver:"#c0c0c0",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",teal:"#008080",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",white:"#ffffff",whitesmoke:"#f5f5f5",yellow:"#ffff00",yellowgreen:"#9acd32"};function e(t,e){(function(t){return"string"==typeof t&&-1!==t.indexOf(".")&&1===parseFloat(t)})(t)&&(t="100%");var r=function(t){return"string"==typeof t&&-1!==t.indexOf("%")}(t);return t=360===e?t:Math.min(e,Math.max(0,parseFloat(t))),r&&(t=parseInt(String(t*e),10)/100),Math.abs(t-e)<1e-6?1:t=360===e?(t<0?t%e+e:t%e)/parseFloat(String(e)):t%e/parseFloat(String(e))}function r(t){return Math.min(1,Math.max(0,t))}function n(t){return t=parseFloat(t),(isNaN(t)||t<0||t>1)&&(t=1),t}function a(t){return t<=1?"".concat(100*Number(t),"%"):t}function o(t){return 1===t.length?"0"+t:String(t)}function i(t,r,n){t=e(t,255),r=e(r,255),n=e(n,255);var a=Math.max(t,r,n),o=Math.min(t,r,n),i=0,s=0,h=(a+o)/2;if(a===o)s=0,i=0;else{var u=a-o;switch(s=h>.5?u/(2-a-o):u/(a+o),a){case t:i=(r-n)/u+(r<n?6:0);break;case r:i=(n-t)/u+2;break;case n:i=(t-r)/u+4}i/=6}return{h:i,s:s,l:h}}function s(t,e,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+6*r*(e-t):r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function h(t,r,n){t=e(t,255),r=e(r,255),n=e(n,255);var a=Math.max(t,r,n),o=Math.min(t,r,n),i=0,s=a,h=a-o,u=0===a?0:h/a;if(a===o)i=0;else{switch(a){case t:i=(r-n)/h+(r<n?6:0);break;case r:i=(n-t)/h+2;break;case n:i=(t-r)/h+4}i/=6}return{h:i,s:u,v:s}}function u(t,e,r,n){var a=[o(Math.round(t).toString(16)),o(Math.round(e).toString(16)),o(Math.round(r).toString(16))];return n&&a[0].startsWith(a[0].charAt(1))&&a[1].startsWith(a[1].charAt(1))&&a[2].startsWith(a[2].charAt(1))?a[0].charAt(0)+a[1].charAt(0)+a[2].charAt(0):a.join("")}function f(t,e,r,n){return[o(c(n)),o(Math.round(t).toString(16)),o(Math.round(e).toString(16)),o(Math.round(r).toString(16))].join("")}function c(t){return Math.round(255*parseFloat(t)).toString(16)}function l(t){return d(t)/255}function d(t){return parseInt(t,16)}function g(t){var r,o,i,h={r:0,g:0,b:0},u=1,f=null,c=null,l=null,d=!1,g=!1;return"string"==typeof t&&(t=y(t)),"object"==typeof t&&(w(t.r)&&w(t.g)&&w(t.b)?(r=t.r,o=t.g,i=t.b,h={r:255*e(r,255),g:255*e(o,255),b:255*e(i,255)},d=!0,g="%"===String(t.r).substr(-1)?"prgb":"rgb"):w(t.h)&&w(t.s)&&w(t.v)?(f=a(t.s),c=a(t.v),h=function(t,r,n){t=6*e(t,360),r=e(r,100),n=e(n,100);var a=Math.floor(t),o=t-a,i=n*(1-r),s=n*(1-o*r),h=n*(1-(1-o)*r),u=a%6;return{r:255*[n,s,i,i,h,n][u],g:255*[h,n,n,s,i,i][u],b:255*[i,i,h,n,n,s][u]}}(t.h,f,c),d=!0,g="hsv"):w(t.h)&&w(t.s)&&w(t.l)&&(f=a(t.s),l=a(t.l),h=function(t,r,n){var a,o,i;if(t=e(t,360),r=e(r,100),n=e(n,100),0===r)o=n,i=n,a=n;else{var h=n<.5?n*(1+r):n+r-n*r,u=2*n-h;a=s(u,h,t+1/3),o=s(u,h,t),i=s(u,h,t-1/3)}return{r:255*a,g:255*o,b:255*i}}(t.h,f,l),d=!0,g="hsl"),Object.prototype.hasOwnProperty.call(t,"a")&&(u=t.a)),u=n(u),{ok:d,format:t.format||g,r:Math.min(255,Math.max(h.r,0)),g:Math.min(255,Math.max(h.g,0)),b:Math.min(255,Math.max(h.b,0)),a:u}}var b="(?:".concat("[-\\+]?\\d*\\.\\d+%?",")|(?:").concat("[-\\+]?\\d+%?",")"),p="[\\s|\\(]+(".concat(b,")[,|\\s]+(").concat(b,")[,|\\s]+(").concat(b,")\\s*\\)?"),v="[\\s|\\(]+(".concat(b,")[,|\\s]+(").concat(b,")[,|\\s]+(").concat(b,")[,|\\s]+(").concat(b,")\\s*\\)?"),m={CSS_UNIT:new RegExp(b),rgb:new RegExp("rgb"+p),rgba:new RegExp("rgba"+v),hsl:new RegExp("hsl"+p),hsla:new RegExp("hsla"+v),hsv:new RegExp("hsv"+p),hsva:new RegExp("hsva"+v),hex3:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex4:/^#?([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex8:/^#?([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/};function y(e){if(0===(e=e.trim().toLowerCase()).length)return!1;var r=!1;if(t[e])e=t[e],r=!0;else if("transparent"===e)return{r:0,g:0,b:0,a:0,format:"name"};var n=m.rgb.exec(e);return n?{r:n[1],g:n[2],b:n[3]}:(n=m.rgba.exec(e))?{r:n[1],g:n[2],b:n[3],a:n[4]}:(n=m.hsl.exec(e))?{h:n[1],s:n[2],l:n[3]}:(n=m.hsla.exec(e))?{h:n[1],s:n[2],l:n[3],a:n[4]}:(n=m.hsv.exec(e))?{h:n[1],s:n[2],v:n[3]}:(n=m.hsva.exec(e))?{h:n[1],s:n[2],v:n[3],a:n[4]}:(n=m.hex8.exec(e))?{r:d(n[1]),g:d(n[2]),b:d(n[3]),a:l(n[4]),format:r?"name":"hex8"}:(n=m.hex6.exec(e))?{r:d(n[1]),g:d(n[2]),b:d(n[3]),format:r?"name":"hex"}:(n=m.hex4.exec(e))?{r:d(n[1]+n[1]),g:d(n[2]+n[2]),b:d(n[3]+n[3]),a:l(n[4]+n[4]),format:r?"name":"hex8"}:!!(n=m.hex3.exec(e))&&{r:d(n[1]+n[1]),g:d(n[2]+n[2]),b:d(n[3]+n[3]),format:r?"name":"hex"}}function w(t){return Boolean(m.CSS_UNIT.exec(String(t)))}var M=function(){function a(t,e){var r;if(void 0===t&&(t=""),void 0===e&&(e={}),t instanceof a)return t;"number"==typeof t&&(t=function(t){return{r:t>>16,g:(65280&t)>>8,b:255&t}}(t)),this.originalInput=t;var n=g(t);this.originalInput=t,this.r=n.r,this.g=n.g,this.b=n.b,this.a=n.a,this.roundA=Math.round(100*this.a)/100,this.format=null!==(r=e.format)&&void 0!==r?r:n.format,this.gradientType=e.gradientType,this.r<1&&(this.r=Math.round(this.r)),this.g<1&&(this.g=Math.round(this.g)),this.b<1&&(this.b=Math.round(this.b)),this.isValid=n.ok}return a.prototype.isDark=function(){return this.getBrightness()<128},a.prototype.isLight=function(){return!this.isDark()},a.prototype.getBrightness=function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},a.prototype.getLuminance=function(){var t=this.toRgb(),e=t.r/255,r=t.g/255,n=t.b/255;return.2126*(e<=.03928?e/12.92:Math.pow((e+.055)/1.055,2.4))+.7152*(r<=.03928?r/12.92:Math.pow((r+.055)/1.055,2.4))+.0722*(n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4))},a.prototype.getAlpha=function(){return this.a},a.prototype.setAlpha=function(t){return this.a=n(t),this.roundA=Math.round(100*this.a)/100,this},a.prototype.isMonochrome=function(){return 0===this.toHsl().s},a.prototype.toHsv=function(){var t=h(this.r,this.g,this.b);return{h:360*t.h,s:t.s,v:t.v,a:this.a}},a.prototype.toHsvString=function(){var t=h(this.r,this.g,this.b),e=Math.round(360*t.h),r=Math.round(100*t.s),n=Math.round(100*t.v);return 1===this.a?"hsv(".concat(e,", ").concat(r,"%, ").concat(n,"%)"):"hsva(".concat(e,", ").concat(r,"%, ").concat(n,"%, ").concat(this.roundA,")")},a.prototype.toHsl=function(){var t=i(this.r,this.g,this.b);return{h:360*t.h,s:t.s,l:t.l,a:this.a}},a.prototype.toHslString=function(){var t=i(this.r,this.g,this.b),e=Math.round(360*t.h),r=Math.round(100*t.s),n=Math.round(100*t.l);return 1===this.a?"hsl(".concat(e,", ").concat(r,"%, ").concat(n,"%)"):"hsla(".concat(e,", ").concat(r,"%, ").concat(n,"%, ").concat(this.roundA,")")},a.prototype.toHex=function(t){return void 0===t&&(t=!1),u(this.r,this.g,this.b,t)},a.prototype.toHexString=function(t){return void 0===t&&(t=!1),"#"+this.toHex(t)},a.prototype.toHex8=function(t){return void 0===t&&(t=!1),function(t,e,r,n,a){var i=[o(Math.round(t).toString(16)),o(Math.round(e).toString(16)),o(Math.round(r).toString(16)),o(c(n))];return a&&i[0].startsWith(i[0].charAt(1))&&i[1].startsWith(i[1].charAt(1))&&i[2].startsWith(i[2].charAt(1))&&i[3].startsWith(i[3].charAt(1))?i[0].charAt(0)+i[1].charAt(0)+i[2].charAt(0)+i[3].charAt(0):i.join("")}(this.r,this.g,this.b,this.a,t)},a.prototype.toHex8String=function(t){return void 0===t&&(t=!1),"#"+this.toHex8(t)},a.prototype.toHexShortString=function(t){return void 0===t&&(t=!1),1===this.a?this.toHexString(t):this.toHex8String(t)},a.prototype.toRgb=function(){return{r:Math.round(this.r),g:Math.round(this.g),b:Math.round(this.b),a:this.a}},a.prototype.toRgbString=function(){var t=Math.round(this.r),e=Math.round(this.g),r=Math.round(this.b);return 1===this.a?"rgb(".concat(t,", ").concat(e,", ").concat(r,")"):"rgba(".concat(t,", ").concat(e,", ").concat(r,", ").concat(this.roundA,")")},a.prototype.toPercentageRgb=function(){var t=function(t){return"".concat(Math.round(100*e(t,255)),"%")};return{r:t(this.r),g:t(this.g),b:t(this.b),a:this.a}},a.prototype.toPercentageRgbString=function(){var t=function(t){return Math.round(100*e(t,255))};return 1===this.a?"rgb(".concat(t(this.r),"%, ").concat(t(this.g),"%, ").concat(t(this.b),"%)"):"rgba(".concat(t(this.r),"%, ").concat(t(this.g),"%, ").concat(t(this.b),"%, ").concat(this.roundA,")")},a.prototype.toName=function(){if(0===this.a)return"transparent";if(this.a<1)return!1;for(var e="#"+u(this.r,this.g,this.b,!1),r=0,n=Object.entries(t);r<n.length;r++){var a=n[r],o=a[0];if(e===a[1])return o}return!1},a.prototype.toString=function(t){var e=Boolean(t);t=null!=t?t:this.format;var r=!1,n=this.a<1&&this.a>=0;return e||!n||!t.startsWith("hex")&&"name"!==t?("rgb"===t&&(r=this.toRgbString()),"prgb"===t&&(r=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(r=this.toHexString()),"hex3"===t&&(r=this.toHexString(!0)),"hex4"===t&&(r=this.toHex8String(!0)),"hex8"===t&&(r=this.toHex8String()),"name"===t&&(r=this.toName()),"hsl"===t&&(r=this.toHslString()),"hsv"===t&&(r=this.toHsvString()),r||this.toHexString()):"name"===t&&0===this.a?this.toName():this.toRgbString()},a.prototype.toNumber=function(){return(Math.round(this.r)<<16)+(Math.round(this.g)<<8)+Math.round(this.b)},a.prototype.clone=function(){return new a(this.toString())},a.prototype.lighten=function(t){void 0===t&&(t=10);var e=this.toHsl();return e.l+=t/100,e.l=r(e.l),new a(e)},a.prototype.brighten=function(t){void 0===t&&(t=10);var e=this.toRgb();return e.r=Math.max(0,Math.min(255,e.r-Math.round(-t/100*255))),e.g=Math.max(0,Math.min(255,e.g-Math.round(-t/100*255))),e.b=Math.max(0,Math.min(255,e.b-Math.round(-t/100*255))),new a(e)},a.prototype.darken=function(t){void 0===t&&(t=10);var e=this.toHsl();return e.l-=t/100,e.l=r(e.l),new a(e)},a.prototype.tint=function(t){return void 0===t&&(t=10),this.mix("white",t)},a.prototype.shade=function(t){return void 0===t&&(t=10),this.mix("black",t)},a.prototype.desaturate=function(t){void 0===t&&(t=10);var e=this.toHsl();return e.s-=t/100,e.s=r(e.s),new a(e)},a.prototype.saturate=function(t){void 0===t&&(t=10);var e=this.toHsl();return e.s+=t/100,e.s=r(e.s),new a(e)},a.prototype.greyscale=function(){return this.desaturate(100)},a.prototype.spin=function(t){var e=this.toHsl(),r=(e.h+t)%360;return e.h=r<0?360+r:r,new a(e)},a.prototype.mix=function(t,e){void 0===e&&(e=50);var r=this.toRgb(),n=new a(t).toRgb(),o=e/100;return new a({r:(n.r-r.r)*o+r.r,g:(n.g-r.g)*o+r.g,b:(n.b-r.b)*o+r.b,a:(n.a-r.a)*o+r.a})},a.prototype.analogous=function(t,e){void 0===t&&(t=6),void 0===e&&(e=30);var r=this.toHsl(),n=360/e,o=[this];for(r.h=(r.h-(n*t>>1)+720)%360;--t;)r.h=(r.h+n)%360,o.push(new a(r));return o},a.prototype.complement=function(){var t=this.toHsl();return t.h=(t.h+180)%360,new a(t)},a.prototype.monochromatic=function(t){void 0===t&&(t=6);for(var e=this.toHsv(),r=e.h,n=e.s,o=e.v,i=[],s=1/t;t--;)i.push(new a({h:r,s:n,v:o})),o=(o+s)%1;return i},a.prototype.splitcomplement=function(){var t=this.toHsl(),e=t.h;return[this,new a({h:(e+72)%360,s:t.s,l:t.l}),new a({h:(e+216)%360,s:t.s,l:t.l})]},a.prototype.onBackground=function(t){var e=this.toRgb(),r=new a(t).toRgb(),n=e.a+r.a*(1-e.a);return new a({r:(e.r*e.a+r.r*r.a*(1-e.a))/n,g:(e.g*e.a+r.g*r.a*(1-e.a))/n,b:(e.b*e.a+r.b*r.a*(1-e.a))/n,a:n})},a.prototype.triad=function(){return this.polyad(3)},a.prototype.tetrad=function(){return this.polyad(4)},a.prototype.polyad=function(t){for(var e=this.toHsl(),r=e.h,n=[this],o=360/t,i=1;i<t;i++)n.push(new a({h:(r+i*o)%360,s:e.s,l:e.l}));return n},a.prototype.equals=function(t){return this.toRgbString()===new a(t).toRgbString()},a}();function x(t){t>=334&&t<=360&&(t-=360);for(var e=0,r=S;e<r.length;e++){var n=A(r[e]);if(n.hueRange&&t>=n.hueRange[0]&&t<=n.hueRange[1])return n}throw Error("Color not found")}function k(t,e){if(void 0===e)return Math.floor(t[0]+Math.random()*(t[1]+1-t[0]));var r=t[1]||1,n=t[0]||0,a=(e=(9301*e+49297)%233280)/233280;return Math.floor(n+a*(r-n))}function A(t){var e=t.lowerBounds[0][0],r=t.lowerBounds[t.lowerBounds.length-1][0],n=t.lowerBounds[t.lowerBounds.length-1][1],a=t.lowerBounds[0][1];return{name:t.name,hueRange:t.hueRange,lowerBounds:t.lowerBounds,saturationRange:[e,r],brightnessRange:[n,a]}}var S=[{name:"monochrome",hueRange:null,lowerBounds:[[0,0],[100,0]]},{name:"red",hueRange:[-26,18],lowerBounds:[[20,100],[30,92],[40,89],[50,85],[60,78],[70,70],[80,60],[90,55],[100,50]]},{name:"orange",hueRange:[19,46],lowerBounds:[[20,100],[30,93],[40,88],[50,86],[60,85],[70,70],[100,70]]},{name:"yellow",hueRange:[47,62],lowerBounds:[[25,100],[40,94],[50,89],[60,86],[70,84],[80,82],[90,80],[100,75]]},{name:"green",hueRange:[63,178],lowerBounds:[[30,100],[40,90],[50,85],[60,81],[70,74],[80,64],[90,50],[100,40]]},{name:"blue",hueRange:[179,257],lowerBounds:[[20,100],[30,86],[40,80],[50,74],[60,60],[70,52],[80,44],[90,39],[100,35]]},{name:"purple",hueRange:[258,282],lowerBounds:[[20,100],[30,87],[40,79],[50,70],[60,65],[70,59],[80,52],[90,45],[100,42]]},{name:"pink",hueRange:[283,334],lowerBounds:[[20,100],[30,90],[40,86],[60,84],[80,80],[90,75],[100,73]]}];function R(t,e){var r=new M(t),n=new M(e);return(Math.max(r.getLuminance(),n.getLuminance())+.05)/(Math.min(r.getLuminance(),n.getLuminance())+.05)}var H=function(t,e){return void 0===t&&(t=""),void 0===e&&(e={}),new M(t,e)};return H.TinyColor=M,H.readability=R,H.mostReadable=function t(e,r,n){void 0===n&&(n={includeFallbackColors:!1,level:"AA",size:"small"});for(var a=null,o=0,i=n.includeFallbackColors,s=n.level,h=n.size,u=0,f=r;u<f.length;u++){var c=f[u],l=R(e,c);l>o&&(o=l,a=new M(c))}return function(t,e,r){var n,a;void 0===r&&(r={level:"AA",size:"small"});var o=R(t,e);switch((null!==(n=r.level)&&void 0!==n?n:"AA")+(null!==(a=r.size)&&void 0!==a?a:"small")){case"AAsmall":case"AAAlarge":return o>=4.5;case"AAlarge":return o>=3;case"AAAsmall":return o>=7;default:return!1}}(e,a,{level:s,size:h})||!i?a:(n.includeFallbackColors=!1,t(e,["#fff","#000"],n))},H.random=function t(e){if(void 0===e&&(e={}),void 0!==e.count&&null!==e.count){var r=e.count,n=[];for(e.count=void 0;r>n.length;)e.count=null,e.seed&&(e.seed+=1),n.push(t(e));return e.count=r,n}var a=function(t,e){var r=k(function(t){var e=parseInt(t,10);if(!Number.isNaN(e)&&e<360&&e>0)return[e,e];if("string"==typeof t){var r=S.find((function(e){return e.name===t}));if(r){var n=A(r);if(n.hueRange)return n.hueRange}var a=new M(t);if(a.isValid){var o=a.toHsv().h;return[o,o]}}return[0,360]}(t),e);r<0&&(r=360+r);return r}(e.hue,e.seed),o=function(t,e){if("monochrome"===e.hue)return 0;if("random"===e.luminosity)return k([0,100],e.seed);var r=x(t).saturationRange,n=r[0],a=r[1];switch(e.luminosity){case"bright":n=55;break;case"dark":n=a-10;break;case"light":a=55}return k([n,a],e.seed)}(a,e),i=function(t,e,r){var n=function(t,e){for(var r=x(t).lowerBounds,n=0;n<r.length-1;n++){var a=r[n][0],o=r[n][1],i=r[n+1][0],s=r[n+1][1];if(e>=a&&e<=i){var h=(s-o)/(i-a);return h*e+(o-h*a)}}return 0}(t,e),a=100;switch(r.luminosity){case"dark":a=n+20;break;case"light":n=(a+n)/2;break;case"random":n=0,a=100}return k([n,a],r.seed)}(a,o,e),s={h:a,s:o,v:i};return void 0!==e.alpha&&(s.a=e.alpha),new M(s)},H.names=t,H.fromRatio=function(t,e){var r={r:a(t.r),g:a(t.g),b:a(t.b)};return void 0!==t.a&&(r.a=Number(t.a)),new M(r,e)},H.legacyRandom=function(){return new M({r:Math.random(),g:Math.random(),b:Math.random()})},H.toMsFilter=function(t,e){var r=new M(t),n="#"+f(r.r,r.g,r.b,r.a),a=n,o=r.gradientType?"GradientType = 1, ":"";if(e){var i=new M(e);a="#"+f(i.r,i.g,i.b,i.a)}return"progid:DXImageTransform.Microsoft.gradient(".concat(o,"startColorstr=").concat(n,",endColorstr=").concat(a,")")},H.inputToRGB=g,H.stringInputToObject=y,H.isValidCSSUnit=w,H}));
//# sourceMappingURL=tinycolor.umd.min.js.map
