{"version": 3, "file": "da.min.mjs", "sources": ["../../../../packages/locale/lang/da.ts"], "sourcesContent": ["export default {\n  name: 'da',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON>yd',\n    },\n    datepicker: {\n      now: 'Nu',\n      today: 'I dag',\n      cancel: 'Ann<PERSON>er',\n      clear: '<PERSON>yd',\n      confirm: 'OK',\n      selectDate: 'Vælg dato',\n      selectTime: 'Vælg tidspunkt',\n      startDate: 'Startdato',\n      startTime: 'Starttidspunkt',\n      endDate: 'Slutdato',\n      endTime: 'Sluttidspunkt',\n      prevYear: 'Forrige år',\n      nextYear: 'Næste år',\n      prevMonth: 'Forrige måned',\n      nextMonth: 'Næste måned',\n      year: '',\n      month1: 'Januar',\n      month2: 'Februar',\n      month3: 'Marts',\n      month4: 'April',\n      month5: 'Maj',\n      month6: 'Juni',\n      month7: 'Juli',\n      month8: 'August',\n      month9: 'September',\n      month10: 'Oktober',\n      month11: 'November',\n      month12: 'December',\n      week: 'uge',\n      weeks: {\n        sun: '<PERSON>øn',\n        mon: 'Man',\n        tue: 'Tir',\n        wed: 'Ons',\n        thu: 'Tor',\n        fri: 'Fre',\n        sat: 'Lør',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Henter',\n      noMatch: 'Ingen matchende data',\n      noData: 'Ingen data',\n      placeholder: 'Vælg',\n    },\n    mention: {\n      loading: 'Henter',\n    },\n    cascader: {\n      noMatch: 'Ingen matchende data',\n      loading: 'Henter',\n      placeholder: 'Vælg',\n      noData: 'Ingen data',\n    },\n    pagination: {\n      goto: 'Gå til',\n      pagesize: '/side',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      confirm: 'OK',\n      cancel: 'Annuller',\n      error: 'Ugyldig input',\n    },\n    upload: {\n      deleteTip: 'tryk slet for at fjerne',\n      delete: 'Slet',\n      preview: 'Forhåndsvisning',\n      continue: 'Fortsæt',\n    },\n    table: {\n      emptyText: 'Ingen data',\n      confirmFilter: 'Bekræft',\n      resetFilter: 'Nulstil',\n      clearFilter: 'Alle',\n      sumText: 'Sum',\n    },\n    tree: {\n      emptyText: 'Ingen data',\n    },\n    transfer: {\n      noMatch: 'Ingen matchende data',\n      noData: 'Ingen data',\n      titles: ['Liste 1', 'Liste 2'],\n      filterPlaceholder: 'Indtast søgeord',\n      noCheckedFormat: '{total} emner',\n      hasCheckedFormat: '{checked}/{total} valgt',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,SAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,mBAAmB,CAAC,SAAS,CAAC,WAAW,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,kBAAkB,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO,CAAC,QAAQ,CAAC,WAAW,CAAC,SAAS,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,yBAAyB,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,oBAAoB,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,YAAY,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,sBAAsB,CAAC,MAAM,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,yBAAyB,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}