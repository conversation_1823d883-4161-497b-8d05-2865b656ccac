{"version": 3, "file": "constants.js", "sources": ["../../../../../../packages/components/collapse/src/constants.ts"], "sourcesContent": ["import type { Injection<PERSON><PERSON>, Ref } from 'vue'\nimport type { CollapseActiveName } from './collapse'\n\nexport interface CollapseContext {\n  activeNames: Ref<CollapseActiveName[]>\n  handleItemClick: (name: CollapseActiveName) => void\n}\n\nexport const collapseContextKey: InjectionKey<CollapseContext> =\n  Symbol('collapseContextKey')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,kBAAkB,GAAG,MAAM,CAAC,oBAAoB;;;;"}