{"version": 3, "file": "vi.mjs", "sources": ["../../../../../packages/locale/lang/vi.ts"], "sourcesContent": ["export default {\n  name: 'vi',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON>óa',\n    },\n    datepicker: {\n      now: 'Hiện tại',\n      today: 'Hôm nay',\n      cancel: '<PERSON>ủy',\n      clear: '<PERSON>óa',\n      confirm: 'OK',\n      selectDate: 'Chọn ngày',\n      selectTime: 'Chọn giờ',\n      startDate: '<PERSON><PERSON>y bắt đầu',\n      startTime: 'Thời gian bắt đầu',\n      endDate: '<PERSON><PERSON><PERSON> kết thúc',\n      endTime: 'Thời gian kết thúc',\n      prevYear: 'Năm trước',\n      nextYear: 'Năm tới',\n      prevMonth: 'Tháng trước',\n      nextMonth: 'Tháng tới',\n      year: 'Năm',\n      month1: 'Tháng 1',\n      month2: 'Tháng 2',\n      month3: 'Tháng 3',\n      month4: 'Tháng 4',\n      month5: 'Tháng 5',\n      month6: 'Tháng 6',\n      month7: 'Tháng 7',\n      month8: 'Tháng 8',\n      month9: 'Tháng 9',\n      month10: 'Tháng 10',\n      month11: 'Tháng 11',\n      month12: 'Tháng 12',\n      // week: 'week',\n      weeks: {\n        sun: 'CN',\n        mon: 'T2',\n        tue: 'T3',\n        wed: 'T4',\n        thu: 'T5',\n        fri: 'T6',\n        sat: 'T7',\n      },\n      months: {\n        jan: 'Th.1',\n        feb: 'Th.2',\n        mar: 'Th.3',\n        apr: 'Th.4',\n        may: 'Th.5',\n        jun: 'Th.6',\n        jul: 'Th.7',\n        aug: 'Th.8',\n        sep: 'Th.9',\n        oct: 'Th.10',\n        nov: 'Th.11',\n        dec: 'Th.12',\n      },\n    },\n    select: {\n      loading: 'Đang tải',\n      noMatch: 'Dữ liệu không phù hợp',\n      noData: 'Không tìm thấy dữ liệu',\n      placeholder: 'Chọn',\n    },\n    mention: {\n      loading: 'Đang tải',\n    },\n    cascader: {\n      noMatch: 'Dữ liệu không phù hợp',\n      loading: 'Đang tải',\n      placeholder: 'Chọn',\n      noData: 'Không tìm thấy dữ liệu',\n    },\n    pagination: {\n      goto: 'Nhảy tới',\n      pagesize: '/trang',\n      total: 'Tổng {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Thông báo',\n      confirm: 'OK',\n      cancel: 'Hủy',\n      error: 'Dữ liệu không hợp lệ',\n    },\n    upload: {\n      deleteTip: 'Nhấn xoá để xoá',\n      delete: 'Xóa',\n      preview: 'Xem trước',\n      continue: 'Tiếp tục',\n    },\n    table: {\n      emptyText: 'Không có dữ liệu',\n      confirmFilter: 'Xác nhận',\n      resetFilter: 'Làm mới',\n      clearFilter: 'Xóa hết',\n      sumText: 'Tổng',\n    },\n    tour: {\n      next: 'Tiếp',\n      previous: 'Trước',\n      finish: 'Hoàn thành',\n    },\n    tree: {\n      emptyText: 'Không có dữ liệu',\n    },\n    transfer: {\n      noMatch: 'Dữ liệu không phù hợp',\n      noData: 'Không tìm thấy dữ liệu',\n      titles: ['Danh sách 1', 'Danh sách 2'],\n      filterPlaceholder: 'Nhập từ khóa',\n      noCheckedFormat: '{total} mục',\n      hasCheckedFormat: '{checked}/{total} đã chọn ',\n    },\n    image: {\n      error: 'LỖI',\n    },\n    pageHeader: {\n      title: 'Quay lại',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ok',\n      cancelButtonText: 'Huỷ',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,oBAAoB;AAC/B,MAAM,KAAK,EAAE,YAAY;AACzB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,UAAU,EAAE,oBAAoB;AACtC,MAAM,SAAS,EAAE,gCAAgC;AACjD,MAAM,SAAS,EAAE,uCAAuC;AACxD,MAAM,OAAO,EAAE,0BAA0B;AACzC,MAAM,OAAO,EAAE,iCAAiC;AAChD,MAAM,QAAQ,EAAE,0BAA0B;AAC1C,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,SAAS,EAAE,0BAA0B;AAC3C,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,OAAO,EAAE,aAAa;AAC5B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,MAAM;AACnB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,OAAO;AACpB,QAAQ,GAAG,EAAE,OAAO;AACpB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,MAAM,EAAE,6CAA6C;AAC3D,MAAM,WAAW,EAAE,WAAW;AAC9B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,oBAAoB;AACnC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,MAAM,EAAE,6CAA6C;AAC3D,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,oBAAoB;AAChC,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,KAAK,EAAE,mBAAmB;AAChC,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,iBAAiB;AAC9B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,6CAA6C;AAC1D,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,sCAAsC;AACvD,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,qBAAqB;AACpC,MAAM,QAAQ,EAAE,oBAAoB;AACpC,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,kCAAkC;AACnD,MAAM,aAAa,EAAE,kBAAkB;AACvC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,WAAW;AACvB,MAAM,QAAQ,EAAE,iBAAiB;AACjC,MAAM,MAAM,EAAE,kBAAkB;AAChC,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,MAAM,EAAE,6CAA6C;AAC3D,MAAM,MAAM,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;AAClD,MAAM,iBAAiB,EAAE,2BAA2B;AACpD,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,gBAAgB,EAAE,yCAAyC;AACjE,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,UAAU;AACvB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,eAAe;AAC5B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,IAAI;AAC7B,MAAM,gBAAgB,EAAE,UAAU;AAClC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}