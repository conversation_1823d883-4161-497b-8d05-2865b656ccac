import 'package:flutter/foundation.dart';
import 'network_client.dart';

/// Web版本配置管理
class WebConfig {
  static bool _initialized = false;
  
  /// 初始化Web版本配置
  static void initialize({
    String? proxyUrl,
  }) {
    if (!kIsWeb || _initialized) {
      return;
    }

    print('🌐 初始化Web版本配置...');

    // 强制清除可能的旧配置
    _clearOldProxyConfig();

    // 设置代理地址
    if (proxyUrl != null) {
      WebProxyClient.setProxyUrl(proxyUrl);
      print('✅ 使用自定义代理地址: $proxyUrl');
    } else {
      // 自动检测代理地址
      _autoConfigureProxy();
    }

    _initialized = true;
    print('✅ Web版本配置初始化完成');
  }

  /// 清除旧的代理配置
  static void _clearOldProxyConfig() {
    try {
      // 清除可能存在的旧配置
      WebProxyClient.clearProxyUrl();
      print('🧹 已清除旧的代理配置');
    } catch (e) {
      print('⚠️ 清除旧配置时出错: $e');
    }
  }
  
  /// 自动配置代理
  static void _autoConfigureProxy() {
    print('🚀 [版本2025-07-10-v2] 开始自动配置代理...');

    // 根据当前域名自动配置代理
    final currentHost = Uri.base.host;
    final currentUrl = Uri.base.toString();
    String proxyUrl;

    print('🔍 当前主机: $currentHost');
    print('🔍 当前URL: $currentUrl');

    if (currentHost.contains('dznovel.top')) {
      // 生产环境 - 通过Nginx代理，不需要指定端口
      proxyUrl = 'https://www.dznovel.top';
      print('🌐 [新版本] 检测到生产环境，使用代理: $proxyUrl (无端口)');
    } else if (currentHost == 'localhost' || currentHost == '127.0.0.1') {
      // 本地开发环境
      proxyUrl = 'http://localhost:8081';
      print('🏠 [新版本] 检测到本地环境，使用代理: $proxyUrl');
    } else {
      // 其他环境，使用默认配置
      proxyUrl = 'http://localhost:8081';
      print('❓ [新版本] 未知环境，使用默认代理: $proxyUrl');
    }

    WebProxyClient.setProxyUrl(proxyUrl);
    print('✅ [新版本] 代理地址已设置: $proxyUrl');
    print('🔧 [新版本] 完整代理URL将是: $proxyUrl/proxy/');
  }
  
  /// 获取推荐的代理配置
  static Map<String, String> getRecommendedProxyConfig() {
    final currentHost = Uri.base.host;
    
    return {
      'development': 'http://localhost:8081',
      'production': 'https://www.dznovel.top',
      'current': currentHost.contains('dznovel.top')
          ? 'https://www.dznovel.top'
          : 'http://localhost:8081',
    };
  }
  
  /// 测试代理连接
  static Future<bool> testProxyConnection() async {
    if (!kIsWeb) {
      return true;
    }
    
    try {
      final client = WebProxyClient();
      final testUrl = 'https://httpbin.org/get';
      
      print('🧪 测试代理连接: $testUrl');
      
      final response = await client.get(Uri.parse(testUrl)).timeout(
        const Duration(seconds: 10),
      );
      
      final success = response.statusCode == 200;
      print(success ? '✅ 代理连接测试成功' : '❌ 代理连接测试失败: ${response.statusCode}');
      
      client.close();
      return success;
      
    } catch (e) {
      print('❌ 代理连接测试失败: $e');
      return false;
    }
  }
  
  /// 获取Web版本的特殊配置说明
  static String getWebConfigInstructions() {
    return '''
🌐 Web版本配置说明

由于浏览器的安全限制，Web版本需要使用CORS代理服务器来解决跨域和SSL证书验证问题。

📋 部署步骤：

1. 在服务器上部署代理服务器：
   bash deploy_to_bt.sh

2. 配置Nginx反向代理（可选）：
   在宝塔面板中添加location /proxy/配置

3. 测试代理连接：
   curl 'http://your-domain.com:8080/proxy/https://httpbin.org/get'

🔧 当前配置：
代理地址: ${WebProxyClient.getProxyUrl() ?? '未设置'}
当前域名: ${Uri.base.host}

💡 如果遇到连接问题：
1. 确保代理服务器正在运行
2. 检查防火墙设置
3. 验证域名和端口配置
''';
  }
}
