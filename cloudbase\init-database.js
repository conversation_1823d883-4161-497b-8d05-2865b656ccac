#!/usr/bin/env node

/**
 * 岱宗文脉 - CloudBase数据库初始化脚本
 * 创建必要的数据集合和初始数据
 */

const tcb = require('@cloudbase/node-sdk');

// 初始化CloudBase - 检查是否在云函数环境中
const isCloudFunction = !!process.env.SCF_RUNTIME;
let app;

if (isCloudFunction) {
  // 在云函数环境中，不需要密钥
  app = tcb.init({
    env: process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8'
  });
} else {
  // 在本地环境中，需要密钥（但我们跳过本地测试）
  console.log('⚠️ 此脚本需要在CloudBase云函数环境中运行');
  console.log('💡 请将此脚本部署到云函数中执行，或者直接通过CloudBase控制台创建集合');
  process.exit(0);
}

const db = app.database();

async function initDatabase() {
  console.log('🚀 开始初始化CloudBase数据库...');

  try {
    // 1. 创建users集合
    console.log('📝 创建users集合...');
    try {
      await db.createCollection('users');
      console.log('✅ users集合创建成功');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  users集合已存在');
      } else {
        console.error('❌ 创建users集合失败:', error.message);
      }
    }

    // 2. 创建memberData集合
    console.log('📝 创建memberData集合...');
    try {
      await db.createCollection('memberData');
      console.log('✅ memberData集合创建成功');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  memberData集合已存在');
      } else {
        console.error('❌ 创建memberData集合失败:', error.message);
      }
    }

    // 3. 创建packages集合
    console.log('📝 创建packages集合...');
    try {
      await db.createCollection('packages');
      console.log('✅ packages集合创建成功');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  packages集合已存在');
      } else {
        console.error('❌ 创建packages集合失败:', error.message);
      }
    }

    // 4. 初始化packages数据
    console.log('📦 初始化packages数据...');
    const packagesResult = await db.collection('packages').get();
    if (packagesResult.data.length === 0) {
      const initialPackages = [
        {
          id: 'basic',
          name: '基础版',
          description: '适合个人用户的基础功能',
          price: 0,
          duration: 30,
          features: [
            '基础AI创作功能',
            '本地数据存储',
            '基础模板库'
          ],
          isActive: true,
          createdAt: new Date().toISOString()
        },
        {
          id: 'premium',
          name: '高级版',
          description: '适合专业创作者的高级功能',
          price: 29.9,
          duration: 30,
          features: [
            '高级AI创作功能',
            '云端数据同步',
            '完整模板库',
            '角色管理系统',
            '知识库功能'
          ],
          isActive: true,
          createdAt: new Date().toISOString()
        },
        {
          id: 'professional',
          name: '专业版',
          description: '适合团队和商业用户',
          price: 99.9,
          duration: 30,
          features: [
            '全部AI创作功能',
            '无限云端存储',
            '自定义模板',
            '团队协作功能',
            'API接口访问',
            '优先技术支持'
          ],
          isActive: true,
          createdAt: new Date().toISOString()
        }
      ];

      for (const pkg of initialPackages) {
        await db.collection('packages').add(pkg);
      }
      console.log('✅ packages初始数据创建成功');
    } else {
      console.log('ℹ️  packages数据已存在');
    }

    // 4. 创建数据同步相关集合
    console.log('📝 创建sync_records集合...');
    try {
      await db.createCollection('sync_records');
      console.log('✅ sync_records集合创建成功');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  sync_records集合已存在');
      } else {
        console.error('❌ 创建sync_records集合失败:', error.message);
      }
    }

    console.log('📝 创建user_data_chunks集合...');
    try {
      await db.createCollection('user_data_chunks');
      console.log('✅ user_data_chunks集合创建成功');
    } catch (error) {
      if (error.message.includes('already exists')) {
        console.log('ℹ️  user_data_chunks集合已存在');
      } else {
        console.error('❌ 创建user_data_chunks集合失败:', error.message);
      }
    }

    // 5. 检查数据集合状态
    console.log('\n📊 数据库集合状态检查:');
    
    const usersCount = (await db.collection('users').count()).total;
    console.log(`   users: ${usersCount} 条记录`);
    
    const memberDataCount = (await db.collection('memberData').count()).total;
    console.log(`   memberData: ${memberDataCount} 条记录`);
    
    const packagesCount = (await db.collection('packages').count()).total;
    console.log(`   packages: ${packagesCount} 条记录`);

    const syncRecordsCount = (await db.collection('sync_records').count()).total;
    console.log(`   sync_records: ${syncRecordsCount} 条记录`);

    const userDataChunksCount = (await db.collection('user_data_chunks').count()).total;
    console.log(`   user_data_chunks: ${userDataChunksCount} 条记录`);

    console.log('\n🎉 数据库初始化完成！');
    console.log('📚 可用的数据集合:');
    console.log('   - users: 用户数据和同步信息');
    console.log('   - memberData: 会员码管理');
    console.log('   - packages: 套餐配置');
    console.log('   - sync_records: 数据同步记录');
    console.log('   - user_data_chunks: 用户数据分块存储');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    process.exit(1);
  }
}

// 运行初始化
if (require.main === module) {
  initDatabase()
    .then(() => {
      console.log('\n✨ 初始化脚本执行完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 初始化脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = { initDatabase };
