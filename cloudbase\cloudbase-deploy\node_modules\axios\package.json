{"name": "axios", "version": "0.21.4", "description": "Promise based HTTP client for the browser and node.js", "main": "index.js", "scripts": {"test": "grunt test", "start": "node ./sandbox/server.js", "build": "NODE_ENV=production grunt build", "preversion": "npm test", "version": "npm run build && grunt version && git add -A dist && git add CHANGELOG.md bower.json package.json", "postversion": "git push && git push --tags", "examples": "node ./examples/server.js", "coveralls": "cat coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "fix": "eslint --fix lib/**/*.js"}, "repository": {"type": "git", "url": "https://github.com/axios/axios.git"}, "keywords": ["xhr", "http", "ajax", "promise", "node"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/axios/axios/issues"}, "homepage": "https://axios-http.com", "devDependencies": {"coveralls": "^3.0.0", "es6-promise": "^4.2.4", "grunt": "^1.3.0", "grunt-banner": "^0.6.0", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-watch": "^1.0.0", "grunt-eslint": "^23.0.0", "grunt-karma": "^4.0.0", "grunt-mocha-test": "^0.13.3", "grunt-ts": "^6.0.0-beta.19", "grunt-webpack": "^4.0.2", "istanbul-instrumenter-loader": "^1.0.0", "jasmine-core": "^2.4.1", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.0", "karma-jasmine": "^1.1.1", "karma-jasmine-ajax": "^0.1.13", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.6", "karma-sinon": "^1.0.5", "karma-sourcemap-loader": "^0.3.8", "karma-webpack": "^4.0.2", "load-grunt-tasks": "^3.5.2", "minimist": "^1.2.0", "mocha": "^8.2.1", "sinon": "^4.5.0", "terser-webpack-plugin": "^4.2.3", "typescript": "^4.0.5", "url-search-params": "^0.10.0", "webpack": "^4.44.2", "webpack-dev-server": "^3.11.0"}, "browser": {"./lib/adapters/http.js": "./lib/adapters/xhr.js"}, "jsdelivr": "dist/axios.min.js", "unpkg": "dist/axios.min.js", "typings": "./index.d.ts", "dependencies": {"follow-redirects": "^1.14.0"}, "bundlesize": [{"path": "./dist/axios.min.js", "threshold": "5kB"}]}