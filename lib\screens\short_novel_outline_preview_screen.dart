import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ShortNovelOutlinePreviewScreen extends StatefulWidget {
  final String outline;
  final Function(String) onOutlineConfirmed;

  const ShortNovelOutlinePreviewScreen({
    super.key,
    required this.outline,
    required this.onOutlineConfirmed,
  });

  @override
  State<ShortNovelOutlinePreviewScreen> createState() =>
      _ShortNovelOutlinePreviewScreenState();
}

class _ShortNovelOutlinePreviewScreenState
    extends State<ShortNovelOutlinePreviewScreen> {
  late TextEditingController _controller;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.outline);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('大纲预览'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.check : Icons.edit),
            onPressed: () {
              if (_isEditing) {
                // 保存修改
                widget.onOutlineConfirmed(_controller.text);
                setState(() => _isEditing = false);
              } else {
                // 进入编辑模式
                setState(() => _isEditing = true);
              }
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 提示信息
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: const Text(
                '请仔细查看生成的详细大纲，确认无误后点击"确认并生成"按钮开始生成小说内容。',
                style: TextStyle(color: Colors.orange),
              ),
            ),
            const SizedBox(height: 16),
            
            // 大纲内容
            Expanded(
              child: _isEditing
                  ? TextField(
                      controller: _controller,
                      maxLines: null,
                      expands: true,
                      textAlignVertical: TextAlignVertical.top,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: '在此编辑详细大纲...',
                      ),
                      style: const TextStyle(fontSize: 16),
                    )
                  : Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          _controller.text,
                          style: const TextStyle(fontSize: 16, height: 1.5),
                        ),
                      ),
                    ),
            ),
            
            // 操作按钮
            if (!_isEditing) ...[
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        setState(() => _isEditing = true);
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('修改大纲'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        widget.onOutlineConfirmed(_controller.text);
                        Get.back();
                      },
                      icon: const Icon(Icons.create),
                      label: const Text('确认并生成'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.purple,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
