{"version": 3, "file": "input-number.mjs", "sources": ["../../../../../../packages/components/input-number/src/input-number.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.m(inputNumberSize),\n      ns.is('disabled', inputNumberDisabled),\n      ns.is('without-controls', !controls),\n      ns.is('controls-right', controlsAtRight),\n    ]\"\n    @dragstart.prevent\n  >\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"decrease\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.decrease')\"\n      :class=\"[ns.e('decrease'), ns.is('disabled', minDisabled)]\"\n      @keydown.enter=\"decrease\"\n    >\n      <slot name=\"decrease-icon\">\n        <el-icon>\n          <arrow-down v-if=\"controlsAtRight\" />\n          <minus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"increase\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.increase')\"\n      :class=\"[ns.e('increase'), ns.is('disabled', maxDisabled)]\"\n      @keydown.enter=\"increase\"\n    >\n      <slot name=\"increase-icon\">\n        <el-icon>\n          <arrow-up v-if=\"controlsAtRight\" />\n          <plus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <el-input\n      :id=\"id\"\n      ref=\"input\"\n      type=\"number\"\n      :step=\"step\"\n      :model-value=\"displayValue\"\n      :placeholder=\"placeholder\"\n      :readonly=\"readonly\"\n      :disabled=\"inputNumberDisabled\"\n      :size=\"inputNumberSize\"\n      :max=\"max\"\n      :min=\"min\"\n      :name=\"name\"\n      :aria-label=\"ariaLabel\"\n      :validate-event=\"false\"\n      :inputmode=\"inputmode\"\n      @keydown.up.prevent=\"increase\"\n      @keydown.down.prevent=\"decrease\"\n      @blur=\"handleBlur\"\n      @focus=\"handleFocus\"\n      @input=\"handleInput\"\n      @change=\"handleInputChange\"\n    >\n      <template v-if=\"$slots.prefix\" #prefix>\n        <slot name=\"prefix\" />\n      </template>\n      <template v-if=\"$slots.suffix\" #suffix>\n        <slot name=\"suffix\" />\n      </template>\n    </el-input>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, onUpdated, reactive, ref, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { ElInput } from '@element-plus/components/input'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { vRepeatClick } from '@element-plus/directives'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport {\n  debugWarn,\n  isNumber,\n  isString,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport { ArrowDown, ArrowUp, Minus, Plus } from '@element-plus/icons-vue'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { inputNumberEmits, inputNumberProps } from './input-number'\n\nimport type { InputInstance } from '@element-plus/components/input'\n\ndefineOptions({\n  name: 'ElInputNumber',\n})\n\nconst props = defineProps(inputNumberProps)\nconst emit = defineEmits(inputNumberEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('input-number')\nconst input = ref<InputInstance>()\n\ninterface Data {\n  currentValue: number | null | undefined\n  userInput: null | number | string\n}\nconst data = reactive<Data>({\n  currentValue: props.modelValue,\n  userInput: null,\n})\n\nconst { formItem } = useFormItem()\n\nconst minDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue <= props.min\n)\nconst maxDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue >= props.max\n)\n\nconst numPrecision = computed(() => {\n  const stepPrecision = getPrecision(props.step)\n  if (!isUndefined(props.precision)) {\n    if (stepPrecision > props.precision) {\n      debugWarn(\n        'InputNumber',\n        'precision should not be less than the decimal places of step'\n      )\n    }\n    return props.precision\n  } else {\n    return Math.max(getPrecision(props.modelValue), stepPrecision)\n  }\n})\nconst controlsAtRight = computed(() => {\n  return props.controls && props.controlsPosition === 'right'\n})\n\nconst inputNumberSize = useFormSize()\nconst inputNumberDisabled = useFormDisabled()\n\nconst displayValue = computed(() => {\n  if (data.userInput !== null) {\n    return data.userInput\n  }\n  let currentValue: number | string | undefined | null = data.currentValue\n  if (isNil(currentValue)) return ''\n  if (isNumber(currentValue)) {\n    if (Number.isNaN(currentValue)) return ''\n    if (!isUndefined(props.precision)) {\n      currentValue = currentValue.toFixed(props.precision)\n    }\n  }\n  return currentValue\n})\nconst toPrecision = (num: number, pre?: number) => {\n  if (isUndefined(pre)) pre = numPrecision.value\n  if (pre === 0) return Math.round(num)\n  let snum = String(num)\n  const pointPos = snum.indexOf('.')\n  if (pointPos === -1) return num\n  const nums = snum.replace('.', '').split('')\n  const datum = nums[pointPos + pre]\n  if (!datum) return num\n  const length = snum.length\n  if (snum.charAt(length - 1) === '5') {\n    snum = `${snum.slice(0, Math.max(0, length - 1))}6`\n  }\n  return Number.parseFloat(Number(snum).toFixed(pre))\n}\nconst getPrecision = (value: number | null | undefined) => {\n  if (isNil(value)) return 0\n  const valueString = value.toString()\n  const dotPosition = valueString.indexOf('.')\n  let precision = 0\n  if (dotPosition !== -1) {\n    precision = valueString.length - dotPosition - 1\n  }\n  return precision\n}\nconst ensurePrecision = (val: number, coefficient: 1 | -1 = 1) => {\n  if (!isNumber(val)) return data.currentValue\n  if (val >= Number.MAX_SAFE_INTEGER && coefficient === 1) {\n    debugWarn(\n      'InputNumber',\n      'The value has reached the maximum safe integer limit.'\n    )\n    return val\n  } else if (val <= Number.MIN_SAFE_INTEGER && coefficient === -1) {\n    debugWarn(\n      'InputNumber',\n      'The value has reached the minimum safe integer limit.'\n    )\n    return val\n  }\n\n  // Solve the accuracy problem of JS decimal calculation by converting the value to integer.\n  return toPrecision(val + props.step * coefficient)\n}\nconst increase = () => {\n  if (props.readonly || inputNumberDisabled.value || maxDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst decrease = () => {\n  if (props.readonly || inputNumberDisabled.value || minDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value, -1)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst verifyValue = (\n  value: number | string | null | undefined,\n  update?: boolean\n): number | null | undefined => {\n  const { max, min, step, precision, stepStrictly, valueOnClear } = props\n  if (max < min) {\n    throwError('InputNumber', 'min should not be greater than max.')\n  }\n  let newVal = Number(value)\n  if (isNil(value) || Number.isNaN(newVal)) {\n    return null\n  }\n  if (value === '') {\n    if (valueOnClear === null) {\n      return null\n    }\n    newVal = isString(valueOnClear) ? { min, max }[valueOnClear] : valueOnClear\n  }\n  if (stepStrictly) {\n    newVal = toPrecision(Math.round(newVal / step) * step, precision)\n    if (newVal !== value) {\n      update && emit(UPDATE_MODEL_EVENT, newVal)\n    }\n  }\n  if (!isUndefined(precision)) {\n    newVal = toPrecision(newVal, precision)\n  }\n  if (newVal > max || newVal < min) {\n    newVal = newVal > max ? max : min\n    update && emit(UPDATE_MODEL_EVENT, newVal)\n  }\n  return newVal\n}\nconst setCurrentValue = (\n  value: number | string | null | undefined,\n  emitChange = true\n) => {\n  const oldVal = data.currentValue\n  const newVal = verifyValue(value)\n  if (!emitChange) {\n    emit(UPDATE_MODEL_EVENT, newVal!)\n    return\n  }\n  if (oldVal === newVal && value) return\n  data.userInput = null\n  emit(UPDATE_MODEL_EVENT, newVal!)\n  if (oldVal !== newVal) {\n    emit(CHANGE_EVENT, newVal!, oldVal!)\n  }\n  if (props.validateEvent) {\n    formItem?.validate?.('change').catch((err) => debugWarn(err))\n  }\n  data.currentValue = newVal\n}\nconst handleInput = (value: string) => {\n  data.userInput = value\n  const newVal = value === '' ? null : Number(value)\n  emit(INPUT_EVENT, newVal)\n  setCurrentValue(newVal, false)\n}\nconst handleInputChange = (value: string) => {\n  const newVal = value !== '' ? Number(value) : ''\n  if ((isNumber(newVal) && !Number.isNaN(newVal)) || value === '') {\n    setCurrentValue(newVal)\n  }\n  setCurrentValueToModelValue()\n  data.userInput = null\n}\n\nconst focus = () => {\n  input.value?.focus?.()\n}\n\nconst blur = () => {\n  input.value?.blur?.()\n}\n\nconst handleFocus = (event: MouseEvent | FocusEvent) => {\n  emit('focus', event)\n}\n\nconst handleBlur = (event: MouseEvent | FocusEvent) => {\n  data.userInput = null\n  // When non-numeric content is entered into a numeric input box,\n  // the content displayed on the page is not cleared after the value is cleared. #18533\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1398528\n  if (data.currentValue === null && input.value?.input) {\n    input.value.input.value = ''\n  }\n  emit('blur', event)\n  if (props.validateEvent) {\n    formItem?.validate?.('blur').catch((err) => debugWarn(err))\n  }\n}\n\nconst setCurrentValueToModelValue = () => {\n  if (data.currentValue !== props.modelValue) {\n    data.currentValue = props.modelValue\n  }\n}\nconst handleWheel = (e: WheelEvent) => {\n  if (document.activeElement === e.target) e.preventDefault()\n}\n\nwatch(\n  () => props.modelValue,\n  (value, oldValue) => {\n    const newValue = verifyValue(value, true)\n    if (data.userInput === null && newValue !== oldValue) {\n      data.currentValue = newValue\n    }\n  },\n  { immediate: true }\n)\nonMounted(() => {\n  const { min, max, modelValue } = props\n  const innerInput = input.value?.input as HTMLInputElement\n  innerInput.setAttribute('role', 'spinbutton')\n  if (Number.isFinite(max)) {\n    innerInput.setAttribute('aria-valuemax', String(max))\n  } else {\n    innerInput.removeAttribute('aria-valuemax')\n  }\n  if (Number.isFinite(min)) {\n    innerInput.setAttribute('aria-valuemin', String(min))\n  } else {\n    innerInput.removeAttribute('aria-valuemin')\n  }\n  innerInput.setAttribute(\n    'aria-valuenow',\n    data.currentValue || data.currentValue === 0\n      ? String(data.currentValue)\n      : ''\n  )\n  innerInput.setAttribute('aria-disabled', String(inputNumberDisabled.value))\n  if (!isNumber(modelValue) && modelValue != null) {\n    let val: number | null = Number(modelValue)\n    if (Number.isNaN(val)) {\n      val = null\n    }\n    emit(UPDATE_MODEL_EVENT, val!)\n  }\n  innerInput.addEventListener('wheel', handleWheel, { passive: false })\n})\nonUpdated(() => {\n  const innerInput = input.value?.input\n  innerInput?.setAttribute('aria-valuenow', `${data.currentValue ?? ''}`)\n})\ndefineExpose({\n  /** @description get focus the input component */\n  focus,\n  /** @description remove focus the input component */\n  blur,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;;;;;;mCAuGc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,cAAc,CAAA,CAAA;AACtC,IAAA,MAAM,QAAQ,GAAmB,EAAA,CAAA;AAMjC,IAAA,MAAM,OAAO,QAAe,CAAA;AAAA,MAC1B,cAAc,KAAM,CAAA,UAAA;AAAA,MACpB,SAAW,EAAA,IAAA;AAAA,KACZ,CAAA,CAAA;AAED,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,WAAY,EAAA,CAAA;AAEjC,IAAA,MAAM,WAAc,GAAA,QAAA,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAAA,iBACH,GAAA,eAAqB,QAAA,CAAM,gBAAoB,CAAA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAChE,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAA,MAAoB,aAAA,GAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MAClB,gBAAe,CAAA,eAAqB,CAAA,EAAA;AAA0B,QAChE,IAAA,aAAA,GAAA,KAAA,CAAA,SAAA,EAAA;AAEA,UAAM,SAAA,CAAA,eAA8B,8DAAA,CAAA,CAAA;AAClC,SAAM;AACN,QAAA,OAAK,KAAA,CAAA,SAAkB,CAAA;AACrB,OAAI,MAAA;AACF,QAAA,OAAA,IAAA,CAAA,GAAA,CAAA,YAAA,CAAA,KAAA,CAAA,UAAA,CAAA,EAAA,aAAA,CAAA,CAAA;AAAA,OACE;AAAA,KACA,CAAA,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACF,OAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,gBAAA,KAAA,OAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAa,IAAA,MACR,eAAA,GAAA,WAAA,EAAA,CAAA;AACL,IAAA,MAAA,mBAAgB,GAAA,eAAmB,EAAA,CAAA;AAA0B,IAC/D,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,IAAA,IAAA,CAAA,SAAA,KAAA,IAAA,EAAA;AACD,QAAM,OAAA,IAAA,CAAA;AACJ,OAAO;AAA6C,MACrD,IAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AAED,MAAA,IAAM,kBAAkB,CAAY;AACpC,QAAA;AAEA,MAAM,IAAA,QAAA,CAAA,eAAwB;AAC5B,QAAI,IAAA,yBAAyB,CAAA;AAC3B,UAAA,OAAY,EAAA,CAAA;AAAA,QACd,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,SAAA,CAAA,EAAA;AACA,UAAI,eAAmD,YAAK,CAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAC5D,SAAI;AACJ,OAAI;AACF,MAAA,OAAW,YAAM,CAAY;AAC7B,KAAA,CAAA,CAAA;AACE,IAAe,MAAA,WAAA,GAAA,CAAA,GAAA,EAAA,GAAA,KAAa;AAAuB,MACrD,IAAA,WAAA,CAAA,GAAA,CAAA;AAAA,QACF,GAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AACA,MAAO,IAAA,GAAA,KAAA,CAAA;AAAA,QACR,OAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AACD,MAAM,IAAA,IAAA,GAAA,MAAA,CAAc,GAAC,CAAA,CAAA;AACnB,MAAA,MAAgB,QAAA,GAAA,IAAM,CAAA,OAAmB,CAAA,GAAA,CAAA,CAAA;AACzC,MAAA,IAAI,QAAQ,KAAU,CAAA,CAAA;AACtB,QAAI,OAAA;AACJ,MAAM,MAAA,IAAA,GAAA,IAAW,CAAK,OAAA,CAAA,GAAA,EAAW,EAAA,CAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA;AACjC,MAAI,MAAA,KAAA,GAAA,aAAwB,GAAA,GAAA,CAAA,CAAA;AAC5B,MAAA,IAAA,CAAA;AACA,QAAM,OAAA,GAAA,CAAA;AACN,MAAI,YAAe,GAAA,IAAA,CAAA,MAAA,CAAA;AACnB,MAAA,IAAA,WAAe,CAAK,MAAA,GAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACpB,QAAA,IAAS,GAAA,CAAA,EAAA,IAAO,CAAS,KAAA,CAAA,CAAA,EAAC,QAAW,CAAA,CAAA,EAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACnC,OAAO;AAAyC,MAClD,OAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA;AAAkD,IACpD,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,IAAA,KAAA,CAAA,KAAA,CAAA;AACJ,QAAI,OAAA,CAAM,CAAK;AACf,MAAM,MAAA,WAAA,GAAc,MAAM,QAAS,EAAA,CAAA;AACnC,MAAM,MAAA,WAAA,GAAc,WAAY,CAAA,OAAA,CAAQ,GAAG,CAAA,CAAA;AAC3C,MAAA,IAAI,SAAY,GAAA,CAAA,CAAA;AAChB,MAAA,IAAI,gBAAgB,CAAI,CAAA,EAAA;AACtB,QAAY,SAAA,GAAA,WAAA,CAAY,SAAS,WAAc,GAAA,CAAA,CAAA;AAAA,OACjD;AACA,MAAO,OAAA,SAAA,CAAA;AAAA,KACT,CAAA;AACA,IAAA,MAAM,eAAkB,GAAA,CAAC,GAAa,EAAA,WAAA,GAAsB,CAAM,KAAA;AAChE,MAAA,IAAI,CAAC,QAAA,CAAS,GAAG,CAAA;AACjB,QAAA,OAAW,IAAA,CAAA,YAA2B,CAAA;AACpC,MAAA,IAAA,GAAA,IAAA,MAAA,CAAA,gBAAA,IAAA,WAAA,KAAA,CAAA,EAAA;AAAA,QACE,SAAA,CAAA,aAAA,EAAA,uDAAA,CAAA,CAAA;AAAA,QACA,OAAA,GAAA,CAAA;AAAA,OACF,MAAA,IAAA,GAAA,IAAA,MAAA,CAAA,gBAAA,IAAA,WAAA,KAAA,CAAA,CAAA,EAAA;AACA,QAAO,SAAA,CAAA,aAAA,EAAA,uDAAA,CAAA,CAAA;AAAA,QACE,OAAA,GAAA,CAAA;AACT,OAAA;AAAA,MACE,OAAA,WAAA,CAAA,GAAA,GAAA,KAAA,CAAA,IAAA,GAAA,WAAA,CAAA,CAAA;AAAA,KACA,CAAA;AAAA,IACF,MAAA,QAAA,GAAA,MAAA;AACA,MAAO,IAAA,KAAA,CAAA,QAAA,IAAA,mBAAA,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA;AAAA,QACT,OAAA;AAGA,MAAA,MAAA,KAAmB,GAAA,MAAA,CAAA,YAAY,CAAA,KAAkB,CAAA,IAAA,CAAA,CAAA;AAAA,MACnD,MAAA,MAAA,GAAA,eAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAA,eAAiB,CAAM,MAAA,CAAA,CAAA;AACrB,MAAA,IAAI,CAAM,WAAA,EAAA,IAAY,CAAoB,YAAA,CAAA,CAAA;AAC1C,MAAA,2BAAkC,EAAA,CAAA;AAClC,KAAM,CAAA;AACN,IAAA,MAAA,QAAA,GAAA,MAAsB;AACtB,MAAK,IAAA,KAAA,CAAA,QAAa,uBAAiB,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA;AACnC,QAA4B,OAAA;AAAA,MAC9B,MAAA,KAAA,GAAA,MAAA,CAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACA,MAAA,eAAiB,eAAM,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACrB,MAAA,eAAU,CAAA,MAAY,CAAoB,CAAA;AAC1C,MAAA,IAAA,CAAA,WAAc,EAAA,IAAoB,CAAA,YAAA,CAAA,CAAA;AAClC,MAAM,2BAAyB,EAAA,CAAA;AAC/B,KAAA,CAAA;AACA,IAAK,MAAA,WAAA,GAAa,MAAiB,EAAA,MAAA,KAAA;AACnC,MAA4B,MAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,GAAA,KAAA,CAAA;AAAA,MAC9B,IAAA,GAAA,GAAA,GAAA,EAAA;AACA,QAAM,UAAA,CAAA,aAEJ,EAC8B,qCAAA,CAAA,CAAA;AAC9B,OAAA;AACA,MAAA,IAAI,MAAM,GAAK,MAAA,CAAA,KAAA,CAAA,CAAA;AACb,MAAA,IAAA,KAAA,CAAA,sBAA+D,CAAA,MAAA,CAAA,EAAA;AAAA,QACjE,OAAA,IAAA,CAAA;AACA,OAAI;AACJ,MAAA,IAAI,UAAW,EAAA,EAAA;AACb,QAAO,IAAA,YAAA,KAAA,IAAA,EAAA;AAAA,UACT,OAAA,IAAA,CAAA;AACA,SAAA;AACE,QAAA,8BAA2B,CAAA,GAAA,EAAA,GAAA,EAAA,GAAA,EAAA,CAAA,YAAA,CAAA,GAAA,YAAA,CAAA;AACzB,OAAO;AAAA,MACT,IAAA,YAAA,EAAA;AACA,QAAS,MAAA,GAAA,sBAAyB,CAAA,SAAO,IAAI,CAAA,kBAAkB,CAAA,CAAA;AAAA,QACjE,IAAA,MAAA,KAAA,KAAA,EAAA;AACA,UAAI,MAAc,IAAA,IAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;AAChB,SAAA;AACA,OAAA;AACE,MAAU,IAAA,CAAA,WAAA,CAAA;AAA+B,QAC3C,MAAA,GAAA,WAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA;AAAA,OACF;AACA,MAAI,IAAA,MAAa,GAAA,GAAA,IAAA,MAAS,GAAG,GAAA,EAAA;AAC3B,QAAS,MAAA,GAAA,MAAA,GAAA,GAAY,SAAiB,GAAA,CAAA;AAAA,QACxC,MAAA,IAAA,IAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;AACA,OAAI;AACF,MAAS,OAAA,MAAA,CAAA;AACT,KAAU,CAAA;AAA+B,IAC3C,MAAA,eAAA,GAAA,CAAA,KAAA,EAAA,UAAA,GAAA,IAAA,KAAA;AACA,MAAO,IAAA,EAAA,CAAA;AAAA,MACT,MAAA,MAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AACA,MAAA,MAAwB,MAAA,GAAA,WAEtB,CAAA,KAAA,CAAA,CAAA;AAEA,MAAA,IAAA,CAAA,UAAe,EAAK;AACpB,QAAM,IAAA,CAAA,oBAAqB,MAAK,CAAA,CAAA;AAChC,QAAA,OAAiB;AACf,OAAA;AACA,MAAA,IAAA,MAAA,KAAA,MAAA,IAAA,KAAA;AAAA,QACF,OAAA;AACA,MAAI,IAAA,CAAA,SAAA;AACJ,MAAA,IAAA,CAAK,kBAAY,EAAA,MAAA,CAAA,CAAA;AACjB,MAAA,IAAA;AACA,QAAA,iBAAuB,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA;AACrB,OAAK;AAA8B,MACrC,IAAA,KAAA,CAAA,aAAA,EAAA;AACA,QAAA,CAAA,aAAyB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACvB,OAAU;AAAkD,MAC9D,IAAA,CAAA,YAAA,GAAA,MAAA,CAAA;AACA,KAAA,CAAA;AAAoB,IACtB,MAAA,WAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,IAAA,CAAA,SAAA,GAAA,KAAiC,CAAA;AACrC,MAAA,MAAiB,MAAA,GAAA,KAAA,KAAA,EAAA,GAAA,IAAA,GAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACjB,MAAA,IAAA,CAAA,WAAe,EAAA,MAAA,CAAU,CAAK;AAC9B,MAAA,sBAAwB,EAAA,KAAA,CAAA,CAAA;AACxB,KAAA,CAAA;AAA6B,IAC/B,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,MAAA,MAAA,GAAA,KAAA,KAAqB,EAAkB,GAAA,MAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA;AAC3C,MAAA,IAAA,QAAe,CAAA,MAAA,CAAA,IAAA,CAAU,MAAK,CAAA,KAAO,OAAS,CAAA,IAAA,KAAA,KAAA,EAAA,EAAA;AAC9C,QAAK,sBAAoB,CAAC;AACxB,OAAA;AAAsB,MACxB,2BAAA,EAAA,CAAA;AACA,MAA4B,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA;AAC5B,KAAA,CAAA;AAAiB,IACnB,MAAA,KAAA,GAAA,MAAA;AAEA,MAAA,IAAM;AACJ,MAAA,CAAA,EAAA,GAAM,WAAe,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KACvB,CAAA;AAEA,IAAA,MAAM,OAAO,MAAM;AACjB,MAAA,IAAA,EAAM;AAAc,MACtB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,WAAmB,GAAA,CAAA,KAAA,KAAA;AAAA,MACrB,IAAA,CAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,UAAiB,GAAA,CAAA,KAAA,KAAA;AAIjB,MAAA,IAAI,EAAK,EAAA,EAAA,CAAA;AACP,MAAM,IAAA,CAAA,SAAM,OAAc,CAAA;AAAA,MAC5B,IAAA,IAAA,CAAA,YAAA,KAAA,IAAA,KAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,EAAA;AACA,QAAA,WAAa,CAAK,KAAA,CAAA,KAAA,GAAA,EAAA,CAAA;AAClB,OAAA;AACE,MAAU,IAAA,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA;AAAgD,MAC5D,IAAA,KAAA,CAAA,aAAA,EAAA;AAAA,QACF,CAAA,EAAA,GAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAEA,OAAA;AACE,KAAI,CAAA;AACF,IAAA,MAAA,2BAA0B,GAAA,MAAA;AAAA,MAC5B,IAAA,IAAA,CAAA,YAAA,KAAA,KAAA,CAAA,UAAA,EAAA;AAAA,QACF,IAAA,CAAA,YAAA,GAAA,KAAA,CAAA,UAAA,CAAA;AACA,OAAM;AACJ,KAAA,CAAA;AAA0D,IAC5D,MAAA,WAAA,GAAA,CAAA,CAAA,KAAA;AAEA,MAAA,IAAA,QAAA,CAAA,aAAA,KAAA,CAAA,CAAA,MAAA;AAAA,wBACc,EAAA,CAAA;AAAA,KACZ,CAAA;AACE,IAAM,KAAA,CAAA,MAAA,KAAA,CAAA,UAAuB,EAAA,CAAA,KAAA,EAAA,QAAW,KAAA;AACxC,MAAA,MAAI,QAAK,GAAA,WAAsB,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AAC7B,MAAA,IAAA,IAAA,CAAK,SAAe,KAAA,IAAA,IAAA,QAAA,KAAA,QAAA,EAAA;AAAA,QACtB,IAAA,CAAA,YAAA,GAAA,QAAA,CAAA;AAAA,OACF;AAAA,KACA,EAAA,aAAkB,IAAA,EAAA,CAAA,CAAA;AAAA,IACpB,SAAA,CAAA,MAAA;AACA,MAAA,IAAA,EAAA,CAAA;AACE,MAAA,MAAM,EAAE,GAAA,EAAK,GAAK,EAAA,UAAA,EAAe,GAAA,KAAA,CAAA;AACjC,MAAM,MAAA,UAAA,GAAa,MAAM,KAAO,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAChC,MAAW,UAAA,CAAA,YAAA,CAAa,QAAQ,YAAY,CAAA,CAAA;AAC5C,MAAI,IAAA,MAAA,CAAO,QAAS,CAAA,GAAG,CAAG,EAAA;AACxB,QAAA,UAAA,CAAW,YAAa,CAAA,eAAA,EAAiB,MAAO,CAAA,GAAG,CAAC,CAAA,CAAA;AAAA,OAC/C,MAAA;AACL,QAAA,UAAA,CAAW,gBAAgB,eAAe,CAAA,CAAA;AAAA,OAC5C;AACA,MAAI,IAAA,MAAA,CAAO,QAAS,CAAA,GAAG,CAAG,EAAA;AACxB,QAAA,UAAA,CAAW,YAAa,CAAA,eAAA,EAAiB,MAAO,CAAA,GAAG,CAAC,CAAA,CAAA;AAAA,OAC/C,MAAA;AACL,QAAA,UAAA,CAAW,gBAAgB,eAAe,CAAA,CAAA;AAAA,OAC5C;AACA,MAAW,UAAA,CAAA,YAAA,CAAA,eAAA,EAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,KAAA,CAAA,GAAA,MAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AAAA,MACT,UAAA,CAAA,YAAA,CAAA,eAAA,EAAA,MAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,MACA,IAAA,CAAA,mBAA0B,CAAA,IAAA,UAAA,IAAA,MACtB;AACA,QACN,IAAA,GAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA;AACA,QAAA,IAAA,MAAwB,CAAA,KAAA,CAAA,GAAA,CAAA,EAAA;AACxB,UAAI,GAAC,GAAA,IAAS,CAAU;AACtB,SAAI;AACJ,QAAI,IAAA,CAAA,kBAAmB,EAAA,GAAA,CAAA,CAAA;AACrB,OAAM;AAAA,MACR,UAAA,CAAA,gBAAA,CAAA,OAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAA6B,IAC/B,SAAA,CAAA,MAAA;AACA,MAAA,IAAA,EAAA,EAAA,EAAA,CAAW;AAAyD,MACrE,MAAA,UAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AACD,MAAA,UAAgB,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,YAAA,CAAA,eAAA,EAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AACd,KAAM,CAAA,CAAA;AACN,IAAA,MAAA,CAAA;AAAsE,MACvE,KAAA;AACD,MAAa,IAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEX,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAAA,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QAEA,KAAA,EAAAC,cAAA,CAAA;AAAA,UACDC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}