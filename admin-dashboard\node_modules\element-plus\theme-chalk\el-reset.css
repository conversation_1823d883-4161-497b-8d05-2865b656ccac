@charset "UTF-8";body{color:var(--el-text-color-primary);font-family:Inter,"Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;font-size:var(--el-font-size-base);font-weight:400;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-tap-highlight-color:transparent}button,input,textarea{font-family:inherit}a{color:var(--el-color-primary);text-decoration:none}a:focus,a:hover{color:var(--el-color-primary-light-3)}a:active{color:var(--el-color-primary-dark-2)}h1,h2,h3,h4,h5,h6{color:var(--el-text-color-regular);font-weight:inherit}h1:first-child,h2:first-child,h3:first-child,h4:first-child,h5:first-child,h6:first-child{margin-top:0}h1:last-child,h2:last-child,h3:last-child,h4:last-child,h5:last-child,h6:last-child{margin-bottom:0}h1{font-size:calc(var(--el-font-size-base) + 6px)}h2{font-size:calc(var(--el-font-size-base) + 4px)}h3{font-size:calc(var(--el-font-size-base) + 2px)}h4,h5,h6,p{font-size:inherit}p{line-height:1.8}p:first-child{margin-top:0}p:last-child{margin-bottom:0}sub,sup{font-size:calc(var(--el-font-size-base) - 1px)}small{font-size:calc(var(--el-font-size-base) - 2px)}hr{border:0;border-top:1px solid var(--el-border-color-lighter);margin-bottom:20px;margin-top:20px}