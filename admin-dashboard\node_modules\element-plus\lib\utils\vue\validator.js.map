{"version": 3, "file": "validator.js", "sources": ["../../../../../packages/utils/vue/validator.ts"], "sourcesContent": ["import { componentSizes, datePickTypes } from '@element-plus/constants'\n\nimport type { ComponentSize, DatePickType } from '@element-plus/constants'\n\nexport const isValidComponentSize = (val: string): val is ComponentSize | '' =>\n  ['', ...componentSizes].includes(val)\n\nexport const isValidDatePickType = (val: string): val is DatePickType =>\n  ([...datePickTypes] as string[]).includes(val)\n"], "names": ["componentSizes", "datePickTypes"], "mappings": ";;;;;;;AACY,MAAC,oBAAoB,GAAG,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,GAAGA,mBAAc,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE;AACvE,MAAC,mBAAmB,GAAG,CAAC,GAAG,KAAK,CAAC,GAAGC,kBAAa,CAAC,CAAC,QAAQ,CAAC,GAAG;;;;;"}