import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("rum.tencentcloudapi.com", "2021-06-22", clientConfig);
    }
    async DescribeTawAreas(req, cb) {
        return this.request("DescribeTawAreas", req, cb);
    }
    async DescribeRumStatsLogList(req, cb) {
        return this.request("DescribeRumStatsLogList", req, cb);
    }
    async CreateReleaseFile(req, cb) {
        return this.request("CreateReleaseFile", req, cb);
    }
    async DescribeScores(req, cb) {
        return this.request("DescribeScores", req, cb);
    }
    async DescribeRumLogList(req, cb) {
        return this.request("DescribeRumLogList", req, cb);
    }
    async DescribeTawInstances(req, cb) {
        return this.request("DescribeTawInstances", req, cb);
    }
    async DescribeDataPerformancePage(req, cb) {
        return this.request("DescribeDataPerformancePage", req, cb);
    }
    async DescribeDataLogUrlStatistics(req, cb) {
        return this.request("DescribeDataLogUrlStatistics", req, cb);
    }
    async DescribeDataFetchProject(req, cb) {
        return this.request("DescribeDataFetchProject", req, cb);
    }
    async DeleteInstance(req, cb) {
        return this.request("DeleteInstance", req, cb);
    }
    async DescribeRumLogExport(req, cb) {
        return this.request("DescribeRumLogExport", req, cb);
    }
    async DescribeRumLogExports(req, cb) {
        return this.request("DescribeRumLogExports", req, cb);
    }
    async DeleteStarProject(req, cb) {
        return this.request("DeleteStarProject", req, cb);
    }
    async ResumeInstance(req, cb) {
        return this.request("ResumeInstance", req, cb);
    }
    async DescribeError(req, cb) {
        return this.request("DescribeError", req, cb);
    }
    async DescribeRumGroupLog(req, cb) {
        return this.request("DescribeRumGroupLog", req, cb);
    }
    async StopProject(req, cb) {
        return this.request("StopProject", req, cb);
    }
    async DescribeAppSingleCaseList(req, cb) {
        return this.request("DescribeAppSingleCaseList", req, cb);
    }
    async DescribePvList(req, cb) {
        return this.request("DescribePvList", req, cb);
    }
    async DescribeDataWebVitalsPage(req, cb) {
        return this.request("DescribeDataWebVitalsPage", req, cb);
    }
    async DescribeDataStaticUrl(req, cb) {
        return this.request("DescribeDataStaticUrl", req, cb);
    }
    async ModifyProjectLimit(req, cb) {
        return this.request("ModifyProjectLimit", req, cb);
    }
    async DescribeDataPvUrlStatistics(req, cb) {
        return this.request("DescribeDataPvUrlStatistics", req, cb);
    }
    async DescribeAppSingleCaseDetailList(req, cb) {
        return this.request("DescribeAppSingleCaseDetailList", req, cb);
    }
    async DescribeAppMetricsData(req, cb) {
        return this.request("DescribeAppMetricsData", req, cb);
    }
    async DescribeDataSetUrlStatistics(req, cb) {
        return this.request("DescribeDataSetUrlStatistics", req, cb);
    }
    async ModifyInstance(req, cb) {
        return this.request("ModifyInstance", req, cb);
    }
    async DescribeDataStaticResource(req, cb) {
        return this.request("DescribeDataStaticResource", req, cb);
    }
    async DescribeProjectLimits(req, cb) {
        return this.request("DescribeProjectLimits", req, cb);
    }
    async DescribeAppDimensionMetrics(req, cb) {
        return this.request("DescribeAppDimensionMetrics", req, cb);
    }
    async DescribeDataCustomUrl(req, cb) {
        return this.request("DescribeDataCustomUrl", req, cb);
    }
    async CreateWhitelist(req, cb) {
        return this.request("CreateWhitelist", req, cb);
    }
    async DescribeProjects(req, cb) {
        return this.request("DescribeProjects", req, cb);
    }
    async ResumeProject(req, cb) {
        return this.request("ResumeProject", req, cb);
    }
    async CreateStarProject(req, cb) {
        return this.request("CreateStarProject", req, cb);
    }
    async DeleteWhitelist(req, cb) {
        return this.request("DeleteWhitelist", req, cb);
    }
    async DescribeDataFetchUrlInfo(req, cb) {
        return this.request("DescribeDataFetchUrlInfo", req, cb);
    }
    async StopInstance(req, cb) {
        return this.request("StopInstance", req, cb);
    }
    async ModifyProject(req, cb) {
        return this.request("ModifyProject", req, cb);
    }
    async DescribeReleaseFiles(req, cb) {
        return this.request("DescribeReleaseFiles", req, cb);
    }
    async DescribeWhitelists(req, cb) {
        return this.request("DescribeWhitelists", req, cb);
    }
    async DescribeDataEventUrl(req, cb) {
        return this.request("DescribeDataEventUrl", req, cb);
    }
    async DescribeReleaseFileSign(req, cb) {
        return this.request("DescribeReleaseFileSign", req, cb);
    }
    async DescribeDataLogUrlInfo(req, cb) {
        return this.request("DescribeDataLogUrlInfo", req, cb);
    }
    async DescribeUvList(req, cb) {
        return this.request("DescribeUvList", req, cb);
    }
    async CreateProject(req, cb) {
        return this.request("CreateProject", req, cb);
    }
    async DescribeDataStaticProject(req, cb) {
        return this.request("DescribeDataStaticProject", req, cb);
    }
    async DescribeDataPvUrlInfo(req, cb) {
        return this.request("DescribeDataPvUrlInfo", req, cb);
    }
    async DeleteProject(req, cb) {
        return this.request("DeleteProject", req, cb);
    }
    async CreateTawInstance(req, cb) {
        return this.request("CreateTawInstance", req, cb);
    }
    async DescribeDataFetchUrl(req, cb) {
        return this.request("DescribeDataFetchUrl", req, cb);
    }
    async DeleteReleaseFile(req, cb) {
        return this.request("DeleteReleaseFile", req, cb);
    }
    async DescribeDataReportCount(req, cb) {
        return this.request("DescribeDataReportCount", req, cb);
    }
    async DescribeData(req, cb) {
        return this.request("DescribeData", req, cb);
    }
}
