{"name": "local-pkg", "version": "0.4.3", "packageManager": "pnpm@7.5.0", "description": "Get information on local packages.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/local-pkg#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/local-pkg.git"}, "bugs": {"url": "https://github.com/antfu/local-pkg/issues"}, "keywords": ["package"], "sideEffects": false, "exports": {".": {"types": "./index.d.ts", "require": "./index.cjs", "import": "./index.mjs"}}, "main": "index.cjs", "module": "index.mjs", "types": "index.d.ts", "files": ["dist", "index.cjs", "index.mjs", "index.d.ts"], "engines": {"node": ">=14"}, "scripts": {"prepublishOnly": "nr build", "build": "tsup shared.ts --format esm,cjs --dts && esno scripts/postbuild.ts", "lint": "eslint .", "release": "bumpp && npm publish", "test": "node test/cjs.cjs && node test/esm.mjs"}, "devDependencies": {"@antfu/eslint-config": "^0.34.1", "@antfu/ni": "^0.18.8", "@antfu/utils": "^0.7.2", "@types/chai": "^4.3.4", "@types/node": "^18.11.18", "bumpp": "^8.2.1", "chai": "^4.3.7", "eslint": "^8.32.0", "esno": "^0.16.3", "find-up": "^6.3.0", "tsup": "^6.5.0", "typescript": "^4.9.4"}}