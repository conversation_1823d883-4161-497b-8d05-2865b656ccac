{"version": 3, "file": "predefine.mjs", "sources": ["../../../../../../../packages/components/color-picker/src/components/predefine.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('colors')\">\n      <div\n        v-for=\"(item, index) in rgbaColors\"\n        :key=\"colors[index]\"\n        :class=\"[\n          ns.e('color-selector'),\n          ns.is('alpha', item.get('alpha') < 100),\n          { selected: item.selected },\n        ]\"\n        @click=\"handleSelect(index)\"\n      >\n        <div :style=\"{ backgroundColor: item.value }\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject, ref, watch, watchEffect } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { colorPickerContextKey } from '../color-picker'\nimport Color from '../utils/color'\n\nimport type { PropType, Ref } from 'vue'\n\nexport default defineComponent({\n  props: {\n    colors: {\n      type: Array as PropType<string[]>,\n      required: true,\n    },\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n    enableAlpha: {\n      type: Boolean,\n      required: true,\n    },\n  },\n  setup(props) {\n    const ns = useNamespace('color-predefine')\n    const { currentColor } = inject(colorPickerContextKey)!\n\n    const rgbaColors = ref(parseColors(props.colors, props.color)) as Ref<\n      Color[]\n    >\n\n    watch(\n      () => currentColor.value,\n      (val) => {\n        const color = new Color({\n          value: val,\n        })\n\n        rgbaColors.value.forEach((item) => {\n          item.selected = color.compare(item)\n        })\n      }\n    )\n\n    watchEffect(() => {\n      rgbaColors.value = parseColors(props.colors, props.color)\n    })\n\n    function handleSelect(index: number) {\n      props.color.fromString(props.colors[index])\n    }\n\n    function parseColors(colors: string[], color: Color) {\n      return colors.map((value) => {\n        const c = new Color({\n          value,\n        })\n        c.selected = c.compare(color)\n        return c\n      })\n    }\n    return {\n      rgbaColors,\n      handleSelect,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_createElementVNode", "_normalizeClass", "_Fragment", "_renderList", "_normalizeStyle"], "mappings": ";;;;;;AA2BA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,KAAO,EAAA;AAAA,IACL,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,KAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,WAAa,EAAA;AAAA,MACX,IAAM,EAAA,OAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,GACF;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,iBAAiB,CAAA,CAAA;AACzC,IAAA,MAAM,EAAE,YAAA,EAAiB,GAAA,MAAA,CAAO,qBAAqB,CAAA,CAAA;AAErD,IAAA,MAAM,aAAa,GAAI,CAAA,WAAA,CAAY,MAAM,MAAQ,EAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAI7D,IAAA,KAAA,CAAA,MAAA,YAAA,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AAAA,MACE,MAAM,KAAa,GAAA,IAAA,KAAA,CAAA;AAAA,QACV,KAAA,EAAA,GAAA;AACP,OAAM,CAAA,CAAA;AAAkB,MAAA,UACf,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,IAAA,KAAA;AAAA,QACT,IAAC,CAAA,QAAA,GAAA,KAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA;AAED,OAAW,CAAA,CAAA;AACT,KAAK,CAAA,CAAA;AAA6B,IAAA,WACnC,CAAA,MAAA;AAAA,MACH,UAAA,CAAA,KAAA,GAAA,WAAA,CAAA,KAAA,CAAA,MAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAEA,IAAA,SAAA,YAAkB,CAAA,KAAA,EAAA;AAChB,MAAA,KAAA,CAAA,KAAW,CAAQ,UAAA,CAAA,KAAA,CAAA,MAAkB,CAAA,KAAA,CAAA,CAAA,CAAA;AAAmB,KACzD;AAED,IAAA,SAAS,kBAA4B,EAAA,KAAA,EAAA;AACnC,MAAA,OAAY,MAAA,CAAA,GAAA,CAAA,CAAA,KAAiB,KAAA;AAAa,QAC5C,MAAA,CAAA,GAAA,IAAA,KAAA,CAAA;AAEA,UAAS,KAAA;AACP,SAAO,CAAA,CAAA;AACL,QAAM,CAAA,CAAA,QAAI,IAAI,CAAM,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QAClB,OAAA,CAAA,CAAA;AAAA,OAAA,CACF,CAAC;AACD,KAAE;AACF,IAAO,OAAA;AAAA,MACT,UAAC;AAAA,MACH,YAAA;AACA,MAAO,EAAA;AAAA,KACL,CAAA;AAAA,GACA;AAAA,CACA,CAAA,CAAA;AACF,SACF,WAAA,CAAA,IAAA,EAAA,MAAA,EAAA,MAAA,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AACF,EAAC,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;;AArFC,IAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,MAeM,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;AAAA,KAAA,EAAA;AAAA,OAfAH,SAAO,CAAA,IAAA,CAAA,EAAAC,kBAAI,CAAAG,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,IAAA,EAAA,KAAA,KAAA;AAAA,QAAA,OAAAL,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;;AACf,UAAA,KAAA,EAAAE,cAAA,CAAA;AAAA,YAaM,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,gBAAA,CAAA;AAAA,YAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,IAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA;AAAA,YAbA,EAAA,QAAO,EAAA,IAAA,CAAA,QAAA,EAAA;AAAI,WAAA,CAAA;;AACf,SAAA,EAAA;AAAA,UAWMD,kBAAA,CAAA,KAAA,EAAA;AAAA,YAAA,KAAA,EAAAI,cAAA,CAAA,EAAA,eAAA,EAAA,IAAA,CAAA,KAAA,EAAA,CAAA;AAAA,WAVoB,EAAA,IAAA,EAAA,CAAA,CAAA;;AAUpB,OATH,CAAA,EAAA,GAAA,CAAA;AAAiB,KAAA,EAAA,CAAA,CAAA;AACZ,GAAA,EAAA,CAAA,CAAA,CAAA;AAAkB,CAAA;AAA0G,gBAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,eAAA,CAAA,CAAA,CAAA;;;;"}