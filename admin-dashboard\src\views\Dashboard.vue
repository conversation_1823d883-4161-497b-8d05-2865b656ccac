<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">仪表板</h1>
      <p class="page-subtitle">系统概览和关键指标</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="24" :sm="12" :md="6" v-for="stat in stats" :key="stat.key">
        <div class="stat-card" :style="{ background: stat.gradient }">
          <div class="stat-icon">
            <el-icon :size="32">
              <component :is="stat.icon" />
            </el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-trend" :class="stat.trendClass">
              <el-icon size="12">
                <component :is="stat.trendIcon" />
              </el-icon>
              {{ stat.trend }}
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 用户增长趋势 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">用户增长趋势</h3>
            <el-button-group size="small">
              <el-button :type="userChartPeriod === '7d' ? 'primary' : ''" @click="userChartPeriod = '7d'">7天</el-button>
              <el-button :type="userChartPeriod === '30d' ? 'primary' : ''" @click="userChartPeriod = '30d'">30天</el-button>
              <el-button :type="userChartPeriod === '90d' ? 'primary' : ''" @click="userChartPeriod = '90d'">90天</el-button>
            </el-button-group>
          </div>
          <div class="chart-container">
            <v-chart :option="userGrowthOption" style="height: 300px;" />
          </div>
        </div>
      </el-col>

      <!-- 小说创作统计 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">小说创作统计</h3>
          </div>
          <div class="chart-container">
            <v-chart :option="novelStatsOption" style="height: 300px;" />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 数据表格区域 -->
    <el-row :gutter="20" class="tables-row">
      <!-- 最新用户 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">最新注册用户</h3>
            <el-button type="text" @click="$router.push('/users')">查看全部</el-button>
          </div>
          <el-table :data="recentUsers" style="width: 100%">
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="phoneNumber" label="手机号" width="120" />
            <el-table-column label="会员状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isMember ? 'success' : 'info'" size="small">
                  {{ row.isMember ? '会员' : '普通' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="注册时间">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>

      <!-- 热门小说 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">热门小说</h3>
            <el-button type="text" @click="$router.push('/novels')">查看全部</el-button>
          </div>
          <el-table :data="popularNovels" style="width: 100%">
            <el-table-column prop="title" label="小说标题" show-overflow-tooltip />
            <el-table-column prop="genre" label="类型" width="80" />
            <el-table-column label="字数" width="80">
              <template #default="{ row }">
                {{ formatWordCount(row.wordCount) }}
              </template>
            </el-table-column>
            <el-table-column prop="createdAt" label="创建时间" width="100">
              <template #default="{ row }">
                {{ formatDate(row.createdAt) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">系统状态</h3>
        <el-button @click="refreshSystemStatus" :loading="statusLoading" size="small">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
      <el-row :gutter="20">
        <el-col :xs="24" :sm="8" v-for="status in systemStatus" :key="status.name">
          <div class="status-item">
            <div class="status-icon" :class="status.status">
              <el-icon size="20">
                <component :is="status.icon" />
              </el-icon>
            </div>
            <div class="status-content">
              <div class="status-name">{{ status.name }}</div>
              <div class="status-value">{{ status.value }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import VChart from 'vue-echarts'
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { LineChart, PieChart, BarChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
} from 'echarts/components'
import dayjs from 'dayjs'

// 注册 ECharts 组件
use([
  CanvasRenderer,
  LineChart,
  PieChart,
  BarChart,
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  GridComponent
])

const userChartPeriod = ref('30d')
const statusLoading = ref(false)

// 统计数据
const stats = ref([
  {
    key: 'users',
    label: '总用户数',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'User',
    gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    key: 'novels',
    label: '小说总数',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'Reading',
    gradient: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)'
  },
  {
    key: 'memberCodes',
    label: '会员码总数',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'Postcard',
    gradient: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)'
  },
  {
    key: 'syncRecords',
    label: '同步记录数',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'Refresh',
    gradient: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)'
  }
])

// 最新用户数据
const recentUsers = ref([])

// 热门小说数据
const popularNovels = ref([])

// 系统状态
const systemStatus = ref([
  {
    name: 'API服务',
    value: '正常运行',
    status: 'success',
    icon: 'CircleCheckFilled'
  },
  {
    name: '数据库',
    value: '连接正常',
    status: 'success',
    icon: 'CircleCheckFilled'
  },
  {
    name: '存储空间',
    value: '78% 已使用',
    status: 'warning',
    icon: 'WarningFilled'
  }
])

// 用户增长图表配置
const userGrowthOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20', '1/21']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [120, 132, 101, 134, 90, 230, 210],
    type: 'line',
    smooth: true,
    areaStyle: {
      color: {
        type: 'linear',
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [{
          offset: 0, color: 'rgba(64, 158, 255, 0.3)'
        }, {
          offset: 1, color: 'rgba(64, 158, 255, 0.05)'
        }]
      }
    },
    lineStyle: {
      color: '#409eff'
    }
  }]
}))

// 小说统计图表配置
const novelStatsOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [{
    type: 'pie',
    radius: '50%',
    data: [
      { value: 1048, name: '都市' },
      { value: 735, name: '玄幻' },
      { value: 580, name: '科幻' },
      { value: 484, name: '历史' },
      { value: 300, name: '其他' }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}))

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('MM-DD HH:mm')
}

// 格式化字数
const formatWordCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 刷新系统状态
const refreshSystemStatus = async () => {
  statusLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    // 这里应该调用真实的API获取系统状态
  } finally {
    statusLoading.value = false
  }
}

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    const response = await fetch('/api/dashboard', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        const data = result.data

        // 更新统计数据
        stats.value[0].value = data.stats.totalUsers.toString()
        stats.value[1].value = data.stats.totalNovels.toString()
        stats.value[2].value = data.stats.totalMemberCodes.toString() // 显示会员码总数
        stats.value[3].value = data.stats.totalSyncRecords.toString() // 显示总同步记录数

        // 更新最新用户
        recentUsers.value = data.recentUsers || []

        // 更新热门小说
        popularNovels.value = data.popularNovels || []
      }
    }
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 12px;
  padding: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: translate(30px, -30px);
}

.stat-icon {
  opacity: 0.8;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 8px;
}

.stat-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.charts-row,
.tables-row {
  margin-bottom: 24px;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.chart-container {
  width: 100%;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.status-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon.success {
  background: #f6ffed;
  color: #52c41a;
}

.status-icon.warning {
  background: #fffbe6;
  color: #faad14;
}

.status-icon.error {
  background: #fff2f0;
  color: #ff4d4f;
}

.status-content {
  flex: 1;
}

.status-name {
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.status-value {
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式 */
@media (max-width: 768px) {
  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
