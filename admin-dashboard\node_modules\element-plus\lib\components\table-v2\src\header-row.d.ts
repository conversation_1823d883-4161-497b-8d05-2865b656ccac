import type { CSSProperties, ExtractPropTypes, __ExtractPublicPropTypes } from 'vue';
import type { KeyType } from './types';
export declare const tableV2HeaderRowProps: {
    readonly class: StringConstructor;
    readonly columns: {
        readonly type: import("vue").PropType<import("./common").AnyColumn[]>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly columnsStyles: {
        readonly type: import("vue").PropType<Record<KeyType, CSSProperties>>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly headerIndex: NumberConstructor;
    readonly style: {
        readonly type: import("vue").PropType<CSSProperties>;
        readonly required: false;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
};
export type TableV2HeaderRowProps = ExtractPropTypes<typeof tableV2HeaderRowProps>;
export type TableV2HeaderRowPropsPublic = __ExtractPublicPropTypes<typeof tableV2HeaderRowProps>;
