{"version": 3, "file": "panel-time-picker.js", "sources": ["../../../../../../../packages/components/time-picker/src/props/panel-time-picker.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { timePanelSharedProps } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Dayjs } from 'dayjs'\n\nexport const panelTimePickerProps = buildProps({\n  ...timePanelSharedProps,\n  datetimeRole: String,\n  parsedValue: {\n    type: definePropType<Dayjs>(Object),\n  },\n} as const)\n\nexport type PanelTimePickerProps = ExtractPropTypes<typeof panelTimePickerProps>\nexport type PanelTimePickerPropsPublic = __ExtractPublicPropTypes<\n  typeof panelTimePickerProps\n>\n"], "names": ["buildProps", "timePanelSharedProps", "definePropType"], "mappings": ";;;;;;;AAEY,MAAC,oBAAoB,GAAGA,kBAAU,CAAC;AAC/C,EAAE,GAAGC,2BAAoB;AACzB,EAAE,YAAY,EAAE,MAAM;AACtB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,CAAC;;;;"}