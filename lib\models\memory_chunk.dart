import 'dart:math' as math;
import 'package:hive/hive.dart';

part 'memory_chunk.g.dart';

/// 记忆块类型枚举
@HiveType(typeId: 11)
enum MemoryChunkType {
  @HiveField(0)
  worldSetting, // 世界观设定
  @HiveField(1)
  character, // 人物信息
  @HiveField(2)
  location, // 地点描述
  @HiveField(3)
  plot, // 情节信息
  @HiveField(4)
  dialogue, // 对话内容
  @HiveField(5)
  scene, // 场景描述
  @HiveField(6)
  foreshadowing, // 伏笔信息
  @HiveField(7)
  relationship, // 人物关系
  @HiveField(8)
  item, // 物品道具
  @HiveField(9)
  timeline, // 时间线
  @HiveField(10)
  theme, // 主题思想
  @HiveField(11)
  conflict, // 冲突设定
  @HiveField(12)
  other // 其他
}

/// 记忆重要性级别
@HiveType(typeId: 12)
enum MemoryImportance {
  @HiveField(0)
  critical, // 关键信息（主线情节、核心人物）
  @HiveField(1)
  important, // 重要信息（支线情节、重要配角）
  @HiveField(2)
  normal, // 普通信息（场景描述、一般对话）
  @HiveField(3)
  minor // 次要信息（背景细节、临时角色）
}

@HiveType(typeId: 10)
class MemoryChunk extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String content;

  @HiveField(2)
  List<double> embedding;

  @HiveField(3)
  MemoryChunkType type;

  @HiveField(4)
  MemoryImportance importance;

  @HiveField(5)
  Map<String, dynamic> metadata;

  @HiveField(6)
  DateTime createdAt;

  @HiveField(7)
  DateTime updatedAt;

  @HiveField(8)
  String? sourceChapter;

  @HiveField(9)
  String? sourceScene;

  @HiveField(10)
  List<String> tags;

  @HiveField(11)
  String? relatedCharacters;

  @HiveField(12)
  String? relatedLocations;

  @HiveField(13)
  double relevanceScore;

  MemoryChunk({
    required this.id,
    required this.content,
    required this.embedding,
    required this.type,
    this.importance = MemoryImportance.normal,
    this.metadata = const {},
    required this.createdAt,
    required this.updatedAt,
    this.sourceChapter,
    this.sourceScene,
    this.tags = const [],
    this.relatedCharacters,
    this.relatedLocations,
    this.relevanceScore = 0.0,
  });

  /// 计算与查询向量的相似度
  double calculateSimilarity(List<double> queryEmbedding) {
    if (embedding.length != queryEmbedding.length) {
      return 0.0;
    }

    double dotProduct = 0.0;
    double normA = 0.0;
    double normB = 0.0;

    for (int i = 0; i < embedding.length; i++) {
      dotProduct += embedding[i] * queryEmbedding[i];
      normA += embedding[i] * embedding[i];
      normB += queryEmbedding[i] * queryEmbedding[i];
    }

    if (normA == 0.0 || normB == 0.0) {
      return 0.0;
    }

    return dotProduct / (math.sqrt(normA) * math.sqrt(normB));
  }

  /// 获取重要性权重
  double get importanceWeight {
    switch (importance) {
      case MemoryImportance.critical:
        return 1.0;
      case MemoryImportance.important:
        return 0.8;
      case MemoryImportance.normal:
        return 0.6;
      case MemoryImportance.minor:
        return 0.4;
    }
  }

  /// 计算综合相关性分数
  double calculateRelevanceScore(
    List<double> queryEmbedding, {
    List<String>? queryTags,
    String? queryCharacter,
    String? queryLocation,
  }) {
    // 基础相似度分数
    double similarity = calculateSimilarity(queryEmbedding);

    // 重要性权重
    double weightedScore = similarity * importanceWeight;

    // 标签匹配加分
    if (queryTags != null && queryTags.isNotEmpty) {
      int matchingTags = tags.where((tag) => queryTags.contains(tag)).length;
      double tagBonus = (matchingTags / queryTags.length) * 0.1;
      weightedScore += tagBonus;
    }

    // 人物匹配加分
    if (queryCharacter != null && relatedCharacters != null) {
      if (relatedCharacters!.contains(queryCharacter)) {
        weightedScore += 0.1;
      }
    }

    // 地点匹配加分
    if (queryLocation != null && relatedLocations != null) {
      if (relatedLocations!.contains(queryLocation)) {
        weightedScore += 0.1;
      }
    }

    return weightedScore;
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'embedding': embedding,
      'type': type.toString(),
      'importance': importance.toString(),
      'metadata': metadata,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'sourceChapter': sourceChapter,
      'sourceScene': sourceScene,
      'tags': tags,
      'relatedCharacters': relatedCharacters,
      'relatedLocations': relatedLocations,
      'relevanceScore': relevanceScore,
    };
  }

  /// 从JSON创建
  factory MemoryChunk.fromJson(Map<String, dynamic> json) {
    return MemoryChunk(
      id: json['id'],
      content: json['content'],
      embedding: List<double>.from(json['embedding']),
      type: MemoryChunkType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => MemoryChunkType.other,
      ),
      importance: MemoryImportance.values.firstWhere(
        (e) => e.toString() == json['importance'],
        orElse: () => MemoryImportance.normal,
      ),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      sourceChapter: json['sourceChapter'],
      sourceScene: json['sourceScene'],
      tags: List<String>.from(json['tags'] ?? []),
      relatedCharacters: json['relatedCharacters'],
      relatedLocations: json['relatedLocations'],
      relevanceScore: json['relevanceScore']?.toDouble() ?? 0.0,
    );
  }

  @override
  String toString() {
    return 'MemoryChunk(id: $id, type: $type, importance: $importance, content: ${content.length > 50 ? content.substring(0, 50) + '...' : content})';
  }
}
