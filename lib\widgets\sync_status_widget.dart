import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/hybrid_sync_service.dart';

/// 同步状态显示组件
class SyncStatusWidget extends StatelessWidget {
  final bool showDetails;
  final bool showProgress;

  const SyncStatusWidget({
    Key? key,
    this.showDetails = false,
    this.showProgress = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final syncService = Get.find<HybridSyncService>();

    return Obx(() {
      if (!syncService.isInitialized.value) {
        return const _LoadingIndicator();
      }

      return Card(
        margin: const EdgeInsets.all(8.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildHeader(syncService),
              if (showProgress && syncService.isSyncing.value) ...[
                const SizedBox(height: 12),
                _buildProgressBar(syncService),
              ],
              if (showDetails) ...[
                const SizedBox(height: 12),
                _buildDetails(syncService),
              ],
              const SizedBox(height: 12),
              _buildActions(syncService),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildHeader(HybridSyncService syncService) {
    return Row(
      children: [
        _buildStatusIcon(syncService),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '数据同步',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                syncService.syncStatus.value,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        if (syncService.isSyncing.value)
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      ],
    );
  }

  Widget _buildStatusIcon(HybridSyncService syncService) {
    if (syncService.isSyncing.value) {
      return const Icon(Icons.sync, color: Colors.blue);
    }

    final stats = syncService.getSyncStats();
    if (stats['failed'] > 0) {
      return const Icon(Icons.error, color: Colors.red);
    }

    return const Icon(Icons.cloud_done, color: Colors.green);
  }

  Widget _buildProgressBar(HybridSyncService syncService) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LinearProgressIndicator(
          value: syncService.syncProgress.value,
          backgroundColor: Colors.grey[300],
          valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
        ),
        const SizedBox(height: 4),
        Text(
          '${(syncService.syncProgress.value * 100).toInt()}%',
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildDetails(HybridSyncService syncService) {
    final stats = syncService.getSyncStats();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(),
        const Text(
          '同步统计',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('总任务', stats['total'].toString(), Colors.blue),
            _buildStatItem('已完成', stats['completed'].toString(), Colors.green),
            _buildStatItem('失败', stats['failed'].toString(), Colors.red),
            _buildStatItem('进行中', stats['running'].toString(), Colors.orange),
          ],
        ),
        if (stats['lastSyncTime'] != null) ...[
          const SizedBox(height: 8),
          Text(
            '上次同步: ${_formatDateTime(stats['lastSyncTime'])}',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildActions(HybridSyncService syncService) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          onPressed: syncService.isSyncing.value 
            ? null 
            : () => syncService.syncAllData(),
          icon: const Icon(Icons.sync, size: 16),
          label: const Text('全量同步'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            foregroundColor: Colors.white,
          ),
        ),
        OutlinedButton.icon(
          onPressed: syncService.isSyncing.value 
            ? null 
            : () => _showSyncOptions(syncService),
          icon: const Icon(Icons.tune, size: 16),
          label: const Text('选择同步'),
        ),
        TextButton.icon(
          onPressed: () => syncService.cleanupCompletedTasks(),
          icon: const Icon(Icons.cleaning_services, size: 16),
          label: const Text('清理'),
        ),
      ],
    );
  }

  void _showSyncOptions(HybridSyncService syncService) {
    Get.bottomSheet(
      Container(
        padding: const EdgeInsets.all(16),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '选择同步类型',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...SyncTaskType.values.where((type) => type != SyncTaskType.full).map(
              (type) => ListTile(
                leading: Icon(_getTypeIcon(type)),
                title: Text(_getTypeName(type)),
                subtitle: Text(_getTypeDescription(type)),
                onTap: () {
                  Get.back();
                  syncService.syncDataType(type);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getTypeIcon(SyncTaskType type) {
    switch (type) {
      case SyncTaskType.userSettings:
        return Icons.settings;
      case SyncTaskType.novels:
        return Icons.book;
      case SyncTaskType.knowledgeBase:
        return Icons.library_books;
      case SyncTaskType.characters:
        return Icons.people;
      case SyncTaskType.writingStyles:
        return Icons.style;
      case SyncTaskType.full:
        return Icons.sync;
    }
  }

  String _getTypeName(SyncTaskType type) {
    switch (type) {
      case SyncTaskType.userSettings:
        return '用户设置';
      case SyncTaskType.novels:
        return '小说数据';
      case SyncTaskType.knowledgeBase:
        return '知识库';
      case SyncTaskType.characters:
        return '角色数据';
      case SyncTaskType.writingStyles:
        return '文风包';
      case SyncTaskType.full:
        return '全量同步';
    }
  }

  String _getTypeDescription(SyncTaskType type) {
    switch (type) {
      case SyncTaskType.userSettings:
        return '同步用户偏好设置';
      case SyncTaskType.novels:
        return '同步小说和章节数据';
      case SyncTaskType.knowledgeBase:
        return '同步知识库文档';
      case SyncTaskType.characters:
        return '同步角色信息';
      case SyncTaskType.writingStyles:
        return '同步文风包数据';
      case SyncTaskType.full:
        return '同步所有数据';
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }
}

class _LoadingIndicator extends StatelessWidget {
  const _LoadingIndicator();

  @override
  Widget build(BuildContext context) {
    return const Card(
      margin: EdgeInsets.all(8.0),
      child: Padding(
        padding: EdgeInsets.all(16.0),
        child: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
            SizedBox(width: 12),
            Text('正在初始化同步服务...'),
          ],
        ),
      ),
    );
  }
}
