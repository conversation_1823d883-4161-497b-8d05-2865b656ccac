{"name": "@cloudbase/database", "version": "1.4.2", "description": "database for (node.)js sdk ", "main": "dist/commonjs/index.js", "module": "dist/esm/index.js", "scripts": {"eslint": "eslint \"./**/*.ts\"", "eslint-fix": "eslint --fix \"./**/*.ts\"", "test": "echo \"Error: no test specified\" && exit 1", "build": "tsc -p tsconfig.json && tsc -p tsconfig.esm.json"}, "author": "", "license": "ISC", "devDependencies": {"eslint": "^5.15.3", "eslint-config-prettier": "^4.1.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "typescript": "^3.6.2", "typescript-eslint-parser": "^22.0.0", "@types/node": "^13.13.4"}, "sideEffects": false, "dependencies": {"bson": "^4.0.3", "lodash.clonedeep": "4.5.0", "lodash.set": "4.3.2", "lodash.unset": "4.5.2"}}