{"version": 3, "file": "use-carousel.js", "sources": ["../../../../../../packages/components/carousel/src/use-carousel.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  isVNode,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  ref,\n  shallowRef,\n  unref,\n  useSlots,\n  watch,\n} from 'vue'\nimport { throttle } from 'lodash-unified'\nimport { useResizeObserver } from '@vueuse/core'\nimport { debugWarn, flattedChildren, isString } from '@element-plus/utils'\nimport { useOrderedChildren } from '@element-plus/hooks'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { CAROUSEL_ITEM_NAME, carouselContextKey } from './constants'\n\nimport type { SetupContext } from 'vue'\nimport type { CarouselItemContext } from './constants'\nimport type { CarouselEmits, CarouselProps } from './carousel'\n\nconst THROTTLE_TIME = 300\n\nexport const useCarousel = (\n  props: CarouselProps,\n  emit: SetupContext<CarouselEmits>['emit'],\n  componentName: string\n) => {\n  const {\n    children: items,\n    addChild: addItem,\n    removeChild: removeItem,\n    ChildrenSorter: ItemsSorter,\n  } = useOrderedChildren<CarouselItemContext>(\n    getCurrentInstance()!,\n    CAROUSEL_ITEM_NAME\n  )\n\n  const slots = useSlots()\n\n  // refs\n  const activeIndex = ref(-1)\n  const timer = ref<ReturnType<typeof setInterval> | null>(null)\n  const hover = ref(false)\n  const root = ref<HTMLDivElement>()\n  const containerHeight = ref<number>(0)\n  const isItemsTwoLength = ref(true)\n\n  // computed\n  const arrowDisplay = computed(\n    () => props.arrow !== 'never' && !unref(isVertical)\n  )\n\n  const hasLabel = computed(() => {\n    return items.value.some((item) => item.props.label.toString().length > 0)\n  })\n\n  const isCardType = computed(() => props.type === 'card')\n  const isVertical = computed(() => props.direction === 'vertical')\n\n  const containerStyle = computed(() => {\n    if (props.height !== 'auto') {\n      return {\n        height: props.height,\n      }\n    }\n    return {\n      height: `${containerHeight.value}px`,\n      overflow: 'hidden',\n    }\n  })\n\n  // methods\n  const throttledArrowClick = throttle(\n    (index: number) => {\n      setActiveItem(index)\n    },\n    THROTTLE_TIME,\n    { trailing: true }\n  )\n\n  const throttledIndicatorHover = throttle((index: number) => {\n    handleIndicatorHover(index)\n  }, THROTTLE_TIME)\n\n  const isTwoLengthShow = (index: number) => {\n    if (!isItemsTwoLength.value) return true\n    return activeIndex.value <= 1 ? index <= 1 : index > 1\n  }\n\n  function pauseTimer() {\n    if (timer.value) {\n      clearInterval(timer.value)\n      timer.value = null\n    }\n  }\n\n  function startTimer() {\n    if (props.interval <= 0 || !props.autoplay || timer.value) return\n    timer.value = setInterval(() => playSlides(), props.interval)\n  }\n\n  const playSlides = () => {\n    if (activeIndex.value < items.value.length - 1) {\n      activeIndex.value = activeIndex.value + 1\n    } else if (props.loop) {\n      activeIndex.value = 0\n    }\n  }\n\n  function setActiveItem(index: number | string) {\n    if (isString(index)) {\n      const filteredItems = items.value.filter(\n        (item) => item.props.name === index\n      )\n      if (filteredItems.length > 0) {\n        index = items.value.indexOf(filteredItems[0])\n      }\n    }\n    index = Number(index)\n    if (Number.isNaN(index) || index !== Math.floor(index)) {\n      debugWarn(componentName, 'index must be integer.')\n      return\n    }\n    const itemCount = items.value.length\n    const oldIndex = activeIndex.value\n    if (index < 0) {\n      activeIndex.value = props.loop ? itemCount - 1 : 0\n    } else if (index >= itemCount) {\n      activeIndex.value = props.loop ? 0 : itemCount - 1\n    } else {\n      activeIndex.value = index\n    }\n    if (oldIndex === activeIndex.value) {\n      resetItemPosition(oldIndex)\n    }\n    resetTimer()\n  }\n\n  function resetItemPosition(oldIndex?: number) {\n    items.value.forEach((item, index) => {\n      item.translateItem(index, activeIndex.value, oldIndex)\n    })\n  }\n\n  function itemInStage(item: CarouselItemContext, index: number) {\n    const _items = unref(items)\n    const itemCount = _items.length\n    if (itemCount === 0 || !item.states.inStage) return false\n    const nextItemIndex = index + 1\n    const prevItemIndex = index - 1\n    const lastItemIndex = itemCount - 1\n    const isLastItemActive = _items[lastItemIndex].states.active\n    const isFirstItemActive = _items[0].states.active\n    const isNextItemActive = _items[nextItemIndex]?.states?.active\n    const isPrevItemActive = _items[prevItemIndex]?.states?.active\n\n    if ((index === lastItemIndex && isFirstItemActive) || isNextItemActive) {\n      return 'left'\n    } else if ((index === 0 && isLastItemActive) || isPrevItemActive) {\n      return 'right'\n    }\n    return false\n  }\n\n  function handleMouseEnter() {\n    hover.value = true\n    if (props.pauseOnHover) {\n      pauseTimer()\n    }\n  }\n\n  function handleMouseLeave() {\n    hover.value = false\n    startTimer()\n  }\n\n  function handleButtonEnter(arrow: 'left' | 'right') {\n    if (unref(isVertical)) return\n    items.value.forEach((item, index) => {\n      if (arrow === itemInStage(item, index)) {\n        item.states.hover = true\n      }\n    })\n  }\n\n  function handleButtonLeave() {\n    if (unref(isVertical)) return\n    items.value.forEach((item) => {\n      item.states.hover = false\n    })\n  }\n\n  function handleIndicatorClick(index: number) {\n    activeIndex.value = index\n  }\n\n  function handleIndicatorHover(index: number) {\n    if (props.trigger === 'hover' && index !== activeIndex.value) {\n      activeIndex.value = index\n    }\n  }\n\n  function prev() {\n    setActiveItem(activeIndex.value - 1)\n  }\n\n  function next() {\n    setActiveItem(activeIndex.value + 1)\n  }\n\n  function resetTimer() {\n    pauseTimer()\n    if (!props.pauseOnHover) startTimer()\n  }\n\n  function setContainerHeight(height: number) {\n    if (props.height !== 'auto') return\n    containerHeight.value = height\n  }\n\n  function PlaceholderItem() {\n    // fix: https://github.com/element-plus/element-plus/issues/12139\n    const defaultSlots = slots.default?.()\n    if (!defaultSlots) return null\n\n    const flatSlots = flattedChildren(defaultSlots)\n\n    const normalizeSlots = flatSlots.filter((slot) => {\n      return isVNode(slot) && (slot.type as any).name === CAROUSEL_ITEM_NAME\n    })\n\n    if (normalizeSlots?.length === 2 && props.loop && !isCardType.value) {\n      isItemsTwoLength.value = true\n      return normalizeSlots\n    }\n    isItemsTwoLength.value = false\n    return null\n  }\n\n  // watch\n  watch(\n    () => activeIndex.value,\n    (current, prev) => {\n      resetItemPosition(prev)\n      if (isItemsTwoLength.value) {\n        current = current % 2\n        prev = prev % 2\n      }\n      if (prev > -1) {\n        emit(CHANGE_EVENT, current, prev)\n      }\n    }\n  )\n  watch(\n    () => props.autoplay,\n    (autoplay) => {\n      autoplay ? startTimer() : pauseTimer()\n    }\n  )\n  watch(\n    () => props.loop,\n    () => {\n      setActiveItem(activeIndex.value)\n    }\n  )\n\n  watch(\n    () => props.interval,\n    () => {\n      resetTimer()\n    }\n  )\n\n  const resizeObserver = shallowRef<ReturnType<typeof useResizeObserver>>()\n  // lifecycle\n  onMounted(() => {\n    watch(\n      () => items.value,\n      () => {\n        if (items.value.length > 0) setActiveItem(props.initialIndex)\n      },\n      {\n        immediate: true,\n      }\n    )\n\n    resizeObserver.value = useResizeObserver(root.value, () => {\n      resetItemPosition()\n    })\n    startTimer()\n  })\n\n  onBeforeUnmount(() => {\n    pauseTimer()\n    if (root.value && resizeObserver.value) resizeObserver.value.stop()\n  })\n\n  // provide\n  provide(carouselContextKey, {\n    root,\n    isCardType,\n    isVertical,\n    items,\n    loop: props.loop,\n    cardScale: props.cardScale,\n    addItem,\n    removeItem,\n    setActiveItem,\n    setContainerHeight,\n  })\n\n  return {\n    root,\n    activeIndex,\n    arrowDisplay,\n    hasLabel,\n    hover,\n    isCardType,\n    items,\n    isVertical,\n    containerStyle,\n    isItemsTwoLength,\n    handleButtonEnter,\n    handleButtonLeave,\n    handleIndicatorClick,\n    handleMouseEnter,\n    handleMouseLeave,\n    setActiveItem,\n    prev,\n    next,\n    PlaceholderItem,\n    isTwoLengthShow,\n    ItemsSorter,\n    throttledArrowClick,\n    throttledIndicatorHover,\n  }\n}\n"], "names": ["useOrderedChildren", "getCurrentInstance", "CAROUSEL_ITEM_NAME", "useSlots", "ref", "computed", "unref", "throttle", "isString", "debugWarn", "flatted<PERSON><PERSON><PERSON><PERSON>", "isVNode", "watch", "CHANGE_EVENT", "shallowRef", "onMounted", "useResizeObserver", "onBeforeUnmount", "provide", "carouselContextKey"], "mappings": ";;;;;;;;;;;;;;AAmBA,MAAM,aAAa,GAAG,GAAG,CAAC;AACd,MAAC,WAAW,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,aAAa,KAAK;AAC3D,EAAE,MAAM;AACR,IAAI,QAAQ,EAAE,KAAK;AACnB,IAAI,QAAQ,EAAE,OAAO;AACrB,IAAI,WAAW,EAAE,UAAU;AAC3B,IAAI,cAAc,EAAE,WAAW;AAC/B,GAAG,GAAGA,wBAAkB,CAACC,sBAAkB,EAAE,EAAEC,4BAAkB,CAAC,CAAC;AACnE,EAAE,MAAM,KAAK,GAAGC,YAAQ,EAAE,CAAC;AAC3B,EAAE,MAAM,WAAW,GAAGC,OAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,EAAE,MAAM,KAAK,GAAGA,OAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,EAAE,MAAM,KAAK,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AAC3B,EAAE,MAAM,IAAI,GAAGA,OAAG,EAAE,CAAC;AACrB,EAAE,MAAM,eAAe,GAAGA,OAAG,CAAC,CAAC,CAAC,CAAC;AACjC,EAAE,MAAM,gBAAgB,GAAGA,OAAG,CAAC,IAAI,CAAC,CAAC;AACrC,EAAE,MAAM,YAAY,GAAGC,YAAQ,CAAC,MAAM,KAAK,CAAC,KAAK,KAAK,OAAO,IAAI,CAACC,SAAK,CAAC,UAAU,CAAC,CAAC,CAAC;AACrF,EAAE,MAAM,QAAQ,GAAGD,YAAQ,CAAC,MAAM;AAClC,IAAI,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC9E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC;AAC3D,EAAE,MAAM,UAAU,GAAGA,YAAQ,CAAC,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,CAAC,CAAC;AACpE,EAAE,MAAM,cAAc,GAAGA,YAAQ,CAAC,MAAM;AACxC,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE;AACjC,MAAM,OAAO;AACb,QAAQ,MAAM,EAAE,KAAK,CAAC,MAAM;AAC5B,OAAO,CAAC;AACR,KAAK;AACL,IAAI,OAAO;AACX,MAAM,MAAM,EAAE,CAAC,EAAE,eAAe,CAAC,KAAK,CAAC,EAAE,CAAC;AAC1C,MAAM,QAAQ,EAAE,QAAQ;AACxB,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,mBAAmB,GAAGE,sBAAQ,CAAC,CAAC,KAAK,KAAK;AAClD,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;AACzB,GAAG,EAAE,aAAa,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;AACxC,EAAE,MAAM,uBAAuB,GAAGA,sBAAQ,CAAC,CAAC,KAAK,KAAK;AACtD,IAAI,oBAAoB,CAAC,KAAK,CAAC,CAAC;AAChC,GAAG,EAAE,aAAa,CAAC,CAAC;AACpB,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,KAAK;AACrC,IAAI,IAAI,CAAC,gBAAgB,CAAC,KAAK;AAC/B,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,OAAO,WAAW,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAC3D,GAAG,CAAC;AACJ,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;AACrB,MAAM,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACjC,MAAM,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AACzB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAI,KAAK,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,KAAK;AAC7D,MAAM,OAAO;AACb,IAAI,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC,MAAM,UAAU,EAAE,EAAE,KAAK,CAAC,QAAQ,CAAC,CAAC;AAClE,GAAG;AACH,EAAE,MAAM,UAAU,GAAG,MAAM;AAC3B,IAAI,IAAI,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;AACpD,MAAM,WAAW,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;AAChD,KAAK,MAAM,IAAI,KAAK,CAAC,IAAI,EAAE;AAC3B,MAAM,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5B,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AAChC,IAAI,IAAIC,eAAQ,CAAC,KAAK,CAAC,EAAE;AACzB,MAAM,MAAM,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,CAAC;AACpF,MAAM,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE;AACpC,QAAQ,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,OAAO;AACP,KAAK;AACL,IAAI,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AAC1B,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC5D,MAAMC,eAAS,CAAC,aAAa,EAAE,wBAAwB,CAAC,CAAC;AACzD,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACzC,IAAI,MAAM,QAAQ,GAAG,WAAW,CAAC,KAAK,CAAC;AACvC,IAAI,IAAI,KAAK,GAAG,CAAC,EAAE;AACnB,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;AACzD,KAAK,MAAM,IAAI,KAAK,IAAI,SAAS,EAAE;AACnC,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,CAAC,CAAC;AACzD,KAAK,MAAM;AACX,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,KAAK;AACL,IAAI,IAAI,QAAQ,KAAK,WAAW,CAAC,KAAK,EAAE;AACxC,MAAM,iBAAiB,CAAC,QAAQ,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,iBAAiB,CAAC,QAAQ,EAAE;AACvC,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;AACzC,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;AAC7D,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE;AACpC,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACvB,IAAI,MAAM,MAAM,GAAGH,SAAK,CAAC,KAAK,CAAC,CAAC;AAChC,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC;AACpC,IAAI,IAAI,SAAS,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO;AAC/C,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,MAAM,aAAa,GAAG,KAAK,GAAG,CAAC,CAAC;AACpC,IAAI,MAAM,aAAa,GAAG,KAAK,GAAG,CAAC,CAAC;AACpC,IAAI,MAAM,aAAa,GAAG,SAAS,GAAG,CAAC,CAAC;AACxC,IAAI,MAAM,gBAAgB,GAAG,MAAM,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;AACjE,IAAI,MAAM,iBAAiB,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;AACtD,IAAI,MAAM,gBAAgB,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;AAC3H,IAAI,MAAM,gBAAgB,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC;AAC3H,IAAI,IAAI,KAAK,KAAK,aAAa,IAAI,iBAAiB,IAAI,gBAAgB,EAAE;AAC1E,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI,gBAAgB,IAAI,gBAAgB,EAAE;AACpE,MAAM,OAAO,OAAO,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,KAAK,CAAC,YAAY,EAAE;AAC5B,MAAM,UAAU,EAAE,CAAC;AACnB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;AACxB,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,iBAAiB,CAAC,KAAK,EAAE;AACpC,IAAI,IAAIA,SAAK,CAAC,UAAU,CAAC;AACzB,MAAM,OAAO;AACb,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;AACzC,MAAM,IAAI,KAAK,KAAK,WAAW,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE;AAC9C,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;AACjC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,iBAAiB,GAAG;AAC/B,IAAI,IAAIA,SAAK,CAAC,UAAU,CAAC;AACzB,MAAM,OAAO;AACb,IAAI,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAClC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACvC,IAAI,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9B,GAAG;AACH,EAAE,SAAS,oBAAoB,CAAC,KAAK,EAAE;AACvC,IAAI,IAAI,KAAK,CAAC,OAAO,KAAK,OAAO,IAAI,KAAK,KAAK,WAAW,CAAC,KAAK,EAAE;AAClE,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,KAAK;AACL,GAAG;AACH,EAAE,SAAS,IAAI,GAAG;AAClB,IAAI,aAAa,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,SAAS,IAAI,GAAG;AAClB,IAAI,aAAa,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,UAAU,EAAE,CAAC;AACjB,IAAI,IAAI,CAAC,KAAK,CAAC,YAAY;AAC3B,MAAM,UAAU,EAAE,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,kBAAkB,CAAC,MAAM,EAAE;AACtC,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM;AAC/B,MAAM,OAAO;AACb,IAAI,eAAe,CAAC,KAAK,GAAG,MAAM,CAAC;AACnC,GAAG;AACH,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,YAAY,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChF,IAAI,IAAI,CAAC,YAAY;AACrB,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,MAAM,SAAS,GAAGI,qBAAe,CAAC,YAAY,CAAC,CAAC;AACpD,IAAI,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK;AACtD,MAAM,OAAOC,WAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAKT,4BAAkB,CAAC;AACpE,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,MAAM,MAAM,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;AAC5G,MAAM,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC;AACpC,MAAM,OAAO,cAAc,CAAC;AAC5B,KAAK;AACL,IAAI,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;AACnC,IAAI,OAAO,IAAI,CAAC;AAChB,GAAG;AACH,EAAEU,SAAK,CAAC,MAAM,WAAW,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAK;AACrD,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAC7B,IAAI,IAAI,gBAAgB,CAAC,KAAK,EAAE;AAChC,MAAM,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;AAC5B,MAAM,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACpB,MAAM,IAAI,CAACC,kBAAY,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;AACzC,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAED,SAAK,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,CAAC,QAAQ,KAAK;AAC5C,IAAI,QAAQ,GAAG,UAAU,EAAE,GAAG,UAAU,EAAE,CAAC;AAC3C,GAAG,CAAC,CAAC;AACL,EAAEA,SAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM;AAChC,IAAI,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;AACrC,GAAG,CAAC,CAAC;AACL,EAAEA,SAAK,CAAC,MAAM,KAAK,CAAC,QAAQ,EAAE,MAAM;AACpC,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,cAAc,GAAGE,cAAU,EAAE,CAAC;AACtC,EAAEC,aAAS,CAAC,MAAM;AAClB,IAAIH,SAAK,CAAC,MAAM,KAAK,CAAC,KAAK,EAAE,MAAM;AACnC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC;AAChC,QAAQ,aAAa,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;AAC1C,KAAK,EAAE;AACP,MAAM,SAAS,EAAE,IAAI;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,cAAc,CAAC,KAAK,GAAGI,sBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;AAC/D,MAAM,iBAAiB,EAAE,CAAC;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,EAAE,CAAC;AACjB,GAAG,CAAC,CAAC;AACL,EAAEC,mBAAe,CAAC,MAAM;AACxB,IAAI,UAAU,EAAE,CAAC;AACjB,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK;AAC1C,MAAM,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;AAClC,GAAG,CAAC,CAAC;AACL,EAAEC,WAAO,CAACC,4BAAkB,EAAE;AAC9B,IAAI,IAAI;AACR,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,KAAK;AACT,IAAI,IAAI,EAAE,KAAK,CAAC,IAAI;AACpB,IAAI,SAAS,EAAE,KAAK,CAAC,SAAS;AAC9B,IAAI,OAAO;AACX,IAAI,UAAU;AACd,IAAI,aAAa;AACjB,IAAI,kBAAkB;AACtB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,QAAQ;AACZ,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,KAAK;AACT,IAAI,UAAU;AACd,IAAI,cAAc;AAClB,IAAI,gBAAgB;AACpB,IAAI,iBAAiB;AACrB,IAAI,iBAAiB;AACrB,IAAI,oBAAoB;AACxB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB;AACpB,IAAI,aAAa;AACjB,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,eAAe;AACnB,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,mBAAmB;AACvB,IAAI,uBAAuB;AAC3B,GAAG,CAAC;AACJ;;;;"}