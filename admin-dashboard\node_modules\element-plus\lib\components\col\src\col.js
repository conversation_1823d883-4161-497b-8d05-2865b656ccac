'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var runtime = require('../../../utils/vue/props/runtime.js');
var typescript = require('../../../utils/typescript.js');

const colProps = runtime.buildProps({
  tag: {
    type: String,
    default: "div"
  },
  span: {
    type: Number,
    default: 24
  },
  offset: {
    type: Number,
    default: 0
  },
  pull: {
    type: Number,
    default: 0
  },
  push: {
    type: Number,
    default: 0
  },
  xs: {
    type: runtime.definePropType([Number, Object]),
    default: () => typescript.mutable({})
  },
  sm: {
    type: runtime.definePropType([Number, Object]),
    default: () => typescript.mutable({})
  },
  md: {
    type: runtime.definePropType([Number, Object]),
    default: () => typescript.mutable({})
  },
  lg: {
    type: runtime.definePropType([Number, Object]),
    default: () => typescript.mutable({})
  },
  xl: {
    type: runtime.definePropType([Number, Object]),
    default: () => typescript.mutable({})
  }
});

exports.colProps = colProps;
//# sourceMappingURL=col.js.map
