# Generated code do not commit.
file(TO_CMAKE_PATH "D:\\element\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\project\\vs code\\novel\\novel2" PROJECT_DIR)

set(FLUTTER_VERSION "4.3.12" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 4 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 3 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 12 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 0 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=D:\\element\\flutter"
  "PROJECT_DIR=D:\\project\\vs code\\novel\\novel2"
  "FLUTTER_ROOT=D:\\element\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\project\\vs code\\novel\\novel2\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\project\\vs code\\novel\\novel2"
  "FLUTTER_TARGET=D:\\project\\vs code\\novel\\novel2\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\project\\vs code\\novel\\novel2\\.dart_tool\\package_config.json"
)
