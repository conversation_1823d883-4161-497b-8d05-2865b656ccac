{"name": "morgan", "description": "HTTP request logger middleware for node.js", "version": "1.10.0", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)"], "license": "MIT", "keywords": ["express", "http", "logger", "middleware"], "repository": "expressjs/morgan", "dependencies": {"basic-auth": "~2.0.1", "debug": "2.6.9", "depd": "~2.0.0", "on-finished": "~2.3.0", "on-headers": "~1.0.2"}, "devDependencies": {"eslint": "6.8.0", "eslint-config-standard": "14.1.0", "eslint-plugin-import": "2.20.1", "eslint-plugin-markdown": "1.0.2", "eslint-plugin-node": "9.2.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.1", "mocha": "7.1.1", "nyc": "15.0.0", "split": "1.0.1", "supertest": "4.0.2"}, "files": ["LICENSE", "HISTORY.md", "README.md", "index.js"], "engines": {"node": ">= 0.8.0"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --check-leaks --reporter spec --bail", "test-ci": "nyc --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}}