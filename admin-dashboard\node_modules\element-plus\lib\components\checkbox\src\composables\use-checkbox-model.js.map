{"version": 3, "file": "use-checkbox-model.js", "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox-model.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, ref } from 'vue'\nimport { isArray, isUndefined } from '@element-plus/utils'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { checkboxGroupContextKey } from '../constants'\n\nimport type { CheckboxProps } from '../checkbox'\n\nexport const useCheckboxModel = (props: CheckboxProps) => {\n  const selfModel = ref<unknown>(false)\n  const { emit } = getCurrentInstance()!\n  const checkboxGroup = inject(checkboxGroupContextKey, undefined)\n  const isGroup = computed(() => isUndefined(checkboxGroup) === false)\n  const isLimitExceeded = ref(false)\n  const model = computed({\n    get() {\n      return isGroup.value\n        ? checkboxGroup?.modelValue?.value\n        : props.modelValue ?? selfModel.value\n    },\n\n    set(val: unknown) {\n      if (isGroup.value && isArray(val)) {\n        isLimitExceeded.value =\n          checkboxGroup?.max?.value !== undefined &&\n          val.length > checkboxGroup?.max.value &&\n          val.length > model.value.length\n        isLimitExceeded.value === false && checkboxGroup?.changeEvent?.(val)\n      } else {\n        emit(UPDATE_MODEL_EVENT, val)\n        selfModel.value = val\n      }\n    },\n  })\n\n  return {\n    model,\n    isGroup,\n    isLimitExceeded,\n  }\n}\n\nexport type CheckboxModel = ReturnType<typeof useCheckboxModel>\n"], "names": ["ref", "getCurrentInstance", "inject", "checkboxGroupContextKey", "computed", "isUndefined", "isArray", "UPDATE_MODEL_EVENT"], "mappings": ";;;;;;;;;;AAIY,MAAC,gBAAgB,GAAG,CAAC,KAAK,KAAK;AAC3C,EAAE,MAAM,SAAS,GAAGA,OAAG,CAAC,KAAK,CAAC,CAAC;AAC/B,EAAE,MAAM,EAAE,IAAI,EAAE,GAAGC,sBAAkB,EAAE,CAAC;AACxC,EAAE,MAAM,aAAa,GAAGC,UAAM,CAACC,iCAAuB,EAAE,KAAK,CAAC,CAAC,CAAC;AAChE,EAAE,MAAM,OAAO,GAAGC,YAAQ,CAAC,MAAMC,iBAAW,CAAC,aAAa,CAAC,KAAK,KAAK,CAAC,CAAC;AACvE,EAAE,MAAM,eAAe,GAAGL,OAAG,CAAC,KAAK,CAAC,CAAC;AACrC,EAAE,MAAM,KAAK,GAAGI,YAAQ,CAAC;AACzB,IAAI,GAAG,GAAG;AACV,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,OAAO,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC;AACnL,KAAK;AACL,IAAI,GAAG,CAAC,GAAG,EAAE;AACb,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,OAAO,CAAC,KAAK,IAAIE,cAAO,CAAC,GAAG,CAAC,EAAE;AACzC,QAAQ,eAAe,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AACjP,QAAQ,eAAe,CAAC,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC,CAAC;AAC9J,OAAO,MAAM;AACb,QAAQ,IAAI,CAACC,wBAAkB,EAAE,GAAG,CAAC,CAAC;AACtC,QAAQ,SAAS,CAAC,KAAK,GAAG,GAAG,CAAC;AAC9B,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI,OAAO;AACX,IAAI,eAAe;AACnB,GAAG,CAAC;AACJ;;;;"}