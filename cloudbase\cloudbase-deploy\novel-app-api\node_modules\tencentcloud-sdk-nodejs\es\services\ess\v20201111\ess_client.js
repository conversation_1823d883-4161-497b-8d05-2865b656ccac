import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("ess.tencentcloudapi.com", "2020-11-11", clientConfig);
    }
    async StartFlow(req, cb) {
        return this.request("StartFlow", req, cb);
    }
    async DescribeFlowTemplates(req, cb) {
        return this.request("DescribeFlowTemplates", req, cb);
    }
    async CreateUserAutoSignEnableUrl(req, cb) {
        return this.request("CreateUserAutoSignEnableUrl", req, cb);
    }
    async CreateContractDiffTaskWebUrl(req, cb) {
        return this.request("CreateContractDiffTaskWebUrl", req, cb);
    }
    async CreateUserVerifyUrl(req, cb) {
        return this.request("CreateUserVerifyUrl", req, cb);
    }
    async CreateEmbedWebUrl(req, cb) {
        return this.request("CreateEmbedWebUrl", req, cb);
    }
    async DescribeBatchOrganizationRegistrationUrls(req, cb) {
        return this.request("DescribeBatchOrganizationRegistrationUrls", req, cb);
    }
    async DescribeBatchOrganizationRegistrationTasks(req, cb) {
        return this.request("DescribeBatchOrganizationRegistrationTasks", req, cb);
    }
    async CreatePreparedPersonalEsign(req, cb) {
        return this.request("CreatePreparedPersonalEsign", req, cb);
    }
    async CreateFlowSignReview(req, cb) {
        return this.request("CreateFlowSignReview", req, cb);
    }
    async CreateDocument(req, cb) {
        return this.request("CreateDocument", req, cb);
    }
    async DescribeExtendedServiceAuthInfos(req, cb) {
        return this.request("DescribeExtendedServiceAuthInfos", req, cb);
    }
    async DescribeUserAutoSignStatus(req, cb) {
        return this.request("DescribeUserAutoSignStatus", req, cb);
    }
    async CreateFlowGroupByTemplates(req, cb) {
        return this.request("CreateFlowGroupByTemplates", req, cb);
    }
    async VerifyDigitFile(req, cb) {
        return this.request("VerifyDigitFile", req, cb);
    }
    async CreatePartnerAutoSignAuthUrl(req, cb) {
        return this.request("CreatePartnerAutoSignAuthUrl", req, cb);
    }
    async DeleteSealPolicies(req, cb) {
        return this.request("DeleteSealPolicies", req, cb);
    }
    async CreateFlowForwards(req, cb) {
        return this.request("CreateFlowForwards", req, cb);
    }
    async DescribeIntegrationRoles(req, cb) {
        return this.request("DescribeIntegrationRoles", req, cb);
    }
    async DescribeIntegrationEmployees(req, cb) {
        return this.request("DescribeIntegrationEmployees", req, cb);
    }
    async CreatePersonAuthCertificateImage(req, cb) {
        return this.request("CreatePersonAuthCertificateImage", req, cb);
    }
    async CreateFlowGroupSignReview(req, cb) {
        return this.request("CreateFlowGroupSignReview", req, cb);
    }
    async CreateIntegrationUserRoles(req, cb) {
        return this.request("CreateIntegrationUserRoles", req, cb);
    }
    async CreateBatchSignUrl(req, cb) {
        return this.request("CreateBatchSignUrl", req, cb);
    }
    async DescribeInformationExtractionTask(req, cb) {
        return this.request("DescribeInformationExtractionTask", req, cb);
    }
    async CreateFlowReminds(req, cb) {
        return this.request("CreateFlowReminds", req, cb);
    }
    async CreateUserNameChangeUrl(req, cb) {
        return this.request("CreateUserNameChangeUrl", req, cb);
    }
    async DisableUserAutoSign(req, cb) {
        return this.request("DisableUserAutoSign", req, cb);
    }
    async DescribeContractDiffTaskWebUrl(req, cb) {
        return this.request("DescribeContractDiffTaskWebUrl", req, cb);
    }
    async DescribePersonCertificate(req, cb) {
        return this.request("DescribePersonCertificate", req, cb);
    }
    async DeleteIntegrationEmployees(req, cb) {
        return this.request("DeleteIntegrationEmployees", req, cb);
    }
    async CreateUserAutoSignSealUrl(req, cb) {
        return this.request("CreateUserAutoSignSealUrl", req, cb);
    }
    async DescribeOrganizationAuthStatus(req, cb) {
        return this.request("DescribeOrganizationAuthStatus", req, cb);
    }
    async RenewAutoSignLicense(req, cb) {
        return this.request("RenewAutoSignLicense", req, cb);
    }
    async CreateWebThemeConfig(req, cb) {
        return this.request("CreateWebThemeConfig", req, cb);
    }
    async VerifyPdf(req, cb) {
        return this.request("VerifyPdf", req, cb);
    }
    async BindEmployeeUserIdWithClientOpenId(req, cb) {
        return this.request("BindEmployeeUserIdWithClientOpenId", req, cb);
    }
    async CreateOrganizationAuthFile(req, cb) {
        return this.request("CreateOrganizationAuthFile", req, cb);
    }
    async DeleteIntegrationRoleUsers(req, cb) {
        return this.request("DeleteIntegrationRoleUsers", req, cb);
    }
    async CreateFlowGroupByFiles(req, cb) {
        return this.request("CreateFlowGroupByFiles", req, cb);
    }
    async CreateIntegrationDepartment(req, cb) {
        return this.request("CreateIntegrationDepartment", req, cb);
    }
    async CreateFlowEvidenceReport(req, cb) {
        return this.request("CreateFlowEvidenceReport", req, cb);
    }
    async CreateFlow(req, cb) {
        return this.request("CreateFlow", req, cb);
    }
    async CreateReleaseFlow(req, cb) {
        return this.request("CreateReleaseFlow", req, cb);
    }
    async CreateLegalSealQrCode(req, cb) {
        return this.request("CreateLegalSealQrCode", req, cb);
    }
    async CreateSeal(req, cb) {
        return this.request("CreateSeal", req, cb);
    }
    async CreateMultiFlowSignQRCode(req, cb) {
        return this.request("CreateMultiFlowSignQRCode", req, cb);
    }
    async DescribeExtendedServiceAuthDetail(req, cb) {
        return this.request("DescribeExtendedServiceAuthDetail", req, cb);
    }
    async CancelMultiFlowSignQRCode(req, cb) {
        return this.request("CancelMultiFlowSignQRCode", req, cb);
    }
    async DescribeFlowBriefs(req, cb) {
        return this.request("DescribeFlowBriefs", req, cb);
    }
    async DescribeSignFaceVideo(req, cb) {
        return this.request("DescribeSignFaceVideo", req, cb);
    }
    async CreateFlowSignUrl(req, cb) {
        return this.request("CreateFlowSignUrl", req, cb);
    }
    async CreateBatchInitOrganizationUrl(req, cb) {
        return this.request("CreateBatchInitOrganizationUrl", req, cb);
    }
    async CancelUserAutoSignEnableUrl(req, cb) {
        return this.request("CancelUserAutoSignEnableUrl", req, cb);
    }
    async DescribeBillUsageDetail(req, cb) {
        return this.request("DescribeBillUsageDetail", req, cb);
    }
    async DescribeIntegrationDepartments(req, cb) {
        return this.request("DescribeIntegrationDepartments", req, cb);
    }
    async ModifyIntegrationDepartment(req, cb) {
        return this.request("ModifyIntegrationDepartment", req, cb);
    }
    async DescribeOrganizationGroupOrganizations(req, cb) {
        return this.request("DescribeOrganizationGroupOrganizations", req, cb);
    }
    async CreateUserMobileChangeUrl(req, cb) {
        return this.request("CreateUserMobileChangeUrl", req, cb);
    }
    async CreateBatchInformationExtractionTask(req, cb) {
        return this.request("CreateBatchInformationExtractionTask", req, cb);
    }
    async DescribeFileUrls(req, cb) {
        return this.request("DescribeFileUrls", req, cb);
    }
    async OperateSeals(req, cb) {
        return this.request("OperateSeals", req, cb);
    }
    async CreateIntegrationRole(req, cb) {
        return this.request("CreateIntegrationRole", req, cb);
    }
    async OperateTemplate(req, cb) {
        return this.request("OperateTemplate", req, cb);
    }
    async ModifyExtendedService(req, cb) {
        return this.request("ModifyExtendedService", req, cb);
    }
    async CreateEmployeeChangeUrl(req, cb) {
        return this.request("CreateEmployeeChangeUrl", req, cb);
    }
    async ModifyFlowDeadline(req, cb) {
        return this.request("ModifyFlowDeadline", req, cb);
    }
    async CreateFlowBlockchainEvidenceUrl(req, cb) {
        return this.request("CreateFlowBlockchainEvidenceUrl", req, cb);
    }
    async CreateBatchOrganizationRegistrationTasks(req, cb) {
        return this.request("CreateBatchOrganizationRegistrationTasks", req, cb);
    }
    async DescribeUserVerifyStatus(req, cb) {
        return this.request("DescribeUserVerifyStatus", req, cb);
    }
    async ModifyIntegrationRole(req, cb) {
        return this.request("ModifyIntegrationRole", req, cb);
    }
    async CreateBatchOrganizationAuthorizationUrl(req, cb) {
        return this.request("CreateBatchOrganizationAuthorizationUrl", req, cb);
    }
    async CreateEmployeeQualificationSealQrCode(req, cb) {
        return this.request("CreateEmployeeQualificationSealQrCode", req, cb);
    }
    async CreateExtendedServiceAuthInfos(req, cb) {
        return this.request("CreateExtendedServiceAuthInfos", req, cb);
    }
    async CreateOrganizationInfoChangeUrl(req, cb) {
        return this.request("CreateOrganizationInfoChangeUrl", req, cb);
    }
    async CreateConvertTaskApi(req, cb) {
        return this.request("CreateConvertTaskApi", req, cb);
    }
    async ModifyPartnerAutoSignAuthUrl(req, cb) {
        return this.request("ModifyPartnerAutoSignAuthUrl", req, cb);
    }
    async CreateOrganizationAuthUrl(req, cb) {
        return this.request("CreateOrganizationAuthUrl", req, cb);
    }
    async UploadFiles(req, cb) {
        return this.request("UploadFiles", req, cb);
    }
    async CreateIntegrationSubOrganizationActiveRecord(req, cb) {
        return this.request("CreateIntegrationSubOrganizationActiveRecord", req, cb);
    }
    async DescribeFlowComponents(req, cb) {
        return this.request("DescribeFlowComponents", req, cb);
    }
    async CreateFlowByFiles(req, cb) {
        return this.request("CreateFlowByFiles", req, cb);
    }
    async CreateDynamicFlowApprover(req, cb) {
        return this.request("CreateDynamicFlowApprover", req, cb);
    }
    async DescribeUserFlowType(req, cb) {
        return this.request("DescribeUserFlowType", req, cb);
    }
    async CreateModifyAdminAuthorizationUrl(req, cb) {
        return this.request("CreateModifyAdminAuthorizationUrl", req, cb);
    }
    async CreateIntegrationEmployees(req, cb) {
        return this.request("CreateIntegrationEmployees", req, cb);
    }
    async DeleteOrganizationAuthorizations(req, cb) {
        return this.request("DeleteOrganizationAuthorizations", req, cb);
    }
    async CreateBatchQuickSignUrl(req, cb) {
        return this.request("CreateBatchQuickSignUrl", req, cb);
    }
    async ModifyApplicationCallbackInfo(req, cb) {
        return this.request("ModifyApplicationCallbackInfo", req, cb);
    }
    async CancelFlow(req, cb) {
        return this.request("CancelFlow", req, cb);
    }
    async UnbindEmployeeUserIdWithClientOpenId(req, cb) {
        return this.request("UnbindEmployeeUserIdWithClientOpenId", req, cb);
    }
    async CreateBatchCancelFlowUrl(req, cb) {
        return this.request("CreateBatchCancelFlowUrl", req, cb);
    }
    async DeleteIntegrationDepartment(req, cb) {
        return this.request("DeleteIntegrationDepartment", req, cb);
    }
    async CreateFlowApprovers(req, cb) {
        return this.request("CreateFlowApprovers", req, cb);
    }
    async CreatePrepareFlow(req, cb) {
        return this.request("CreatePrepareFlow", req, cb);
    }
    async DescribeBillUsage(req, cb) {
        return this.request("DescribeBillUsage", req, cb);
    }
    async DescribeFlowInfo(req, cb) {
        return this.request("DescribeFlowInfo", req, cb);
    }
    async CreateSealPolicy(req, cb) {
        return this.request("CreateSealPolicy", req, cb);
    }
    async DescribeCancelFlowsTask(req, cb) {
        return this.request("DescribeCancelFlowsTask", req, cb);
    }
    async DescribeOrganizationVerifyStatus(req, cb) {
        return this.request("DescribeOrganizationVerifyStatus", req, cb);
    }
    async CreateOrganizationGroupInvitationLink(req, cb) {
        return this.request("CreateOrganizationGroupInvitationLink", req, cb);
    }
    async DescribeFlowEvidenceReport(req, cb) {
        return this.request("DescribeFlowEvidenceReport", req, cb);
    }
    async DescribeFileCounterSignResult(req, cb) {
        return this.request("DescribeFileCounterSignResult", req, cb);
    }
    async GetTaskResultApi(req, cb) {
        return this.request("GetTaskResultApi", req, cb);
    }
    async ArchiveDynamicFlow(req, cb) {
        return this.request("ArchiveDynamicFlow", req, cb);
    }
    async CreatePrepareFlowGroup(req, cb) {
        return this.request("CreatePrepareFlowGroup", req, cb);
    }
    async UpdateIntegrationEmployees(req, cb) {
        return this.request("UpdateIntegrationEmployees", req, cb);
    }
    async CreateOrganizationBatchSignUrl(req, cb) {
        return this.request("CreateOrganizationBatchSignUrl", req, cb);
    }
    async DescribeThirdPartyAuthCode(req, cb) {
        return this.request("DescribeThirdPartyAuthCode", req, cb);
    }
    async CreateFileCounterSign(req, cb) {
        return this.request("CreateFileCounterSign", req, cb);
    }
    async DescribeOrganizationSeals(req, cb) {
        return this.request("DescribeOrganizationSeals", req, cb);
    }
    async CreateSchemeUrl(req, cb) {
        return this.request("CreateSchemeUrl", req, cb);
    }
    async DeleteExtendedServiceAuthInfos(req, cb) {
        return this.request("DeleteExtendedServiceAuthInfos", req, cb);
    }
}
