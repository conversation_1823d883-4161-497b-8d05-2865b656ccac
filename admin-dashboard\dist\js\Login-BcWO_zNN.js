import{_ as h}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                     *//* empty css                 */import{d as x,r as v,a as S,o as V,u as E,c as I,b as s,e as o,w as t,E as F,f as N,g as z,h as w,i as B,j as u,k as C,l as R,m as j,n as q,p as L,q as O,t as T,s as J,v as p}from"./index-CAzH2L69.js";import{u as K}from"./auth-Bdgn_GBa.js";const M={class:"login-container"},U={class:"login-content"},A={class:"login-form-container"},D={class:"login-header"},P={class:"logo"},G={class:"login-footer"},H={class:"tips"},Q=x({__name:"Login",setup(W){const f=E(),g=K(),m=v(),i=v(!1),a=S({username:"admin",password:"admin123"}),y={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},_=async()=>{if(m.value)try{if(!await m.value.validate())return;i.value=!0;const e=await fetch("/api/admin/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({username:a.username,password:a.password})}),n=await e.json();if(e.ok&&n.success){const{token:r,user:l}=n.data;localStorage.setItem("admin_token",r),localStorage.setItem("admin_user",JSON.stringify(l)),g.token=r,g.user=l,p.success("登录成功"),f.push("/")}else p.error(n.message||"用户名或密码错误")}catch(d){console.error("登录失败:",d),p.error("登录失败，请稍后重试")}finally{i.value=!1}};return V(()=>{localStorage.getItem("admin_token")&&f.push("/")}),(d,e)=>{const n=F,r=j,l=R,k=O,b=z;return B(),I("div",M,[e[5]||(e[5]=s("div",{class:"login-background"},[s("div",{class:"bg-overlay"})],-1)),s("div",U,[s("div",A,[s("div",D,[s("div",P,[o(n,{size:"48",color:"#1890ff"},{default:t(()=>[o(u(C))]),_:1})]),e[2]||(e[2]=s("h1",{class:"title"},"小说应用后台管理系统",-1)),e[3]||(e[3]=s("p",{class:"subtitle"},"管理员登录",-1))]),o(b,{ref_key:"loginFormRef",ref:m,model:a,rules:y,class:"login-form",onKeyup:N(_,["enter"])},{default:t(()=>[o(l,{prop:"username"},{default:t(()=>[o(r,{modelValue:a.username,"onUpdate:modelValue":e[0]||(e[0]=c=>a.username=c),placeholder:"请输入用户名",size:"large","prefix-icon":u(q),clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),o(l,{prop:"password"},{default:t(()=>[o(r,{modelValue:a.password,"onUpdate:modelValue":e[1]||(e[1]=c=>a.password=c),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":u(L),"show-password":"",clearable:""},null,8,["modelValue","prefix-icon"])]),_:1}),o(l,null,{default:t(()=>[o(k,{type:"primary",size:"large",loading:i.value,onClick:_,class:"login-button"},{default:t(()=>[w(T(i.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),s("div",G,[s("p",H,[o(n,null,{default:t(()=>[o(u(J))]),_:1}),e[4]||(e[4]=w(" 默认账号：admin，密码：admin123 "))])])])])])}}}),oe=h(Q,[["__scopeId","data-v-655ec004"]]);export{oe as default};
