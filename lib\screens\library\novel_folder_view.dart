import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:path/path.dart' as path;
import '../../models/novel.dart';
import '../../services/novel_file_manager.dart';
import '../../controllers/smart_composer_controller.dart';
import '../../controllers/novel_controller.dart';
import 'markdown_editor_screen.dart';

/// 小说文件夹视图
/// 显示小说的文件夹结构，每个章节作为独立的 Markdown 文件
class NovelFolderView extends StatefulWidget {
  const NovelFolderView({super.key});

  @override
  State<NovelFolderView> createState() => _NovelFolderViewState();
}

class _NovelFolderViewState extends State<NovelFolderView> {
  final NovelFileManager _fileManager = NovelFileManager();
  final SmartComposerController _smartComposerController = Get.find<SmartComposerController>();
  
  Novel? _novel;
  String? _folderPath;
  List<FileSystemEntity> _files = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeFromArguments();
  }

  void _initializeFromArguments() {
    final args = Get.arguments as Map<String, dynamic>?;
    if (args != null) {
      _novel = args['novel'] as Novel?;
      _folderPath = args['folderPath'] as String?;
      
      if (_folderPath != null) {
        _loadFolderContents();
      }
    }
  }

  Future<void> _loadFolderContents() async {
    if (_folderPath == null) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final directory = Directory(_folderPath!);
      if (await directory.exists()) {
        final entities = await directory.list().toList();
        
        // 排序：文件夹在前，文件在后，按名称排序
        entities.sort((a, b) {
          if (a is Directory && b is File) return -1;
          if (a is File && b is Directory) return 1;
          return a.path.compareTo(b.path);
        });
        
        setState(() {
          _files = entities;
        });
      }
    } catch (e) {
      Get.snackbar('错误', '加载文件夹内容失败：$e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_novel?.title ?? '文件夹视图'),
        actions: [
          IconButton(
            icon: const Icon(Icons.smart_toy),
            tooltip: 'AI 写作助手',
            onPressed: () => _openSmartComposer(),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: '刷新',
            onPressed: _loadFolderContents,
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'open_in_explorer':
                  _openInExplorer();
                  break;
                case 'export_markdown':
                  _exportAsMarkdown();
                  break;
                case 'sync_to_database':
                  _syncToDatabase();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'open_in_explorer',
                child: Row(
                  children: [
                    Icon(Icons.folder_open),
                    SizedBox(width: 8),
                    Text('在文件管理器中打开'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export_markdown',
                child: Row(
                  children: [
                    Icon(Icons.description),
                    SizedBox(width: 8),
                    Text('导出为 Markdown'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'sync_to_database',
                child: Row(
                  children: [
                    Icon(Icons.sync),
                    SizedBox(width: 8),
                    Text('同步到数据库'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildFileList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _showCreateFileDialog,
        tooltip: '新建文件',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildFileList() {
    if (_files.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.folder_open,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '文件夹为空',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _files.length,
      itemBuilder: (context, index) {
        final file = _files[index];
        return _buildFileItem(file);
      },
    );
  }

  Widget _buildFileItem(FileSystemEntity entity) {
    final isDirectory = entity is Directory;
    final fileName = entity.path.split(Platform.pathSeparator).last;
    final isMarkdownFile = fileName.endsWith('.md');
    final isMetadataFile = fileName == 'metadata.json';
    
    IconData icon;
    Color iconColor;
    
    if (isDirectory) {
      icon = Icons.folder;
      iconColor = Colors.amber[700]!;
    } else if (isMarkdownFile) {
      icon = Icons.description;
      iconColor = Colors.blue[700]!;
    } else if (isMetadataFile) {
      icon = Icons.settings;
      iconColor = Colors.grey[600]!;
    } else {
      icon = Icons.insert_drive_file;
      iconColor = Colors.grey[600]!;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(icon, color: iconColor),
        title: Text(fileName),
        subtitle: _buildFileSubtitle(entity),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isMarkdownFile && fileName != 'outline.md')
              IconButton(
                icon: const Icon(Icons.smart_toy),
                tooltip: 'AI 助手',
                onPressed: () => _openSmartComposerForFile(entity as File),
              ),
            PopupMenuButton<String>(
              onSelected: (value) => _handleFileAction(value, entity),
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'open',
                  child: Row(
                    children: [
                      Icon(Icons.open_in_new),
                      SizedBox(width: 8),
                      Text('打开'),
                    ],
                  ),
                ),
                if (isMarkdownFile)
                  const PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(Icons.edit),
                        SizedBox(width: 8),
                        Text('编辑'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'rename',
                  child: Row(
                    children: [
                      Icon(Icons.drive_file_rename_outline),
                      SizedBox(width: 8),
                      Text('重命名'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete_outline),
                      SizedBox(width: 8),
                      Text('删除'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () => _openFile(entity),
      ),
    );
  }

  Widget _buildFileSubtitle(FileSystemEntity entity) {
    if (entity is File) {
      return FutureBuilder<FileStat>(
        future: entity.stat(),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            final stat = snapshot.data!;
            final size = _formatFileSize(stat.size);
            final modified = _formatDate(stat.modified);
            return Text('$size • $modified');
          }
          return const Text('');
        },
      );
    } else {
      return const Text('文件夹');
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }

  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')} ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _openFile(FileSystemEntity entity) {
    if (entity is Directory) {
      // 导航到子文件夹
      Get.to(() => const NovelFolderView(), arguments: {
        'folderPath': entity.path,
      });
    } else if (entity is File) {
      final fileName = entity.path.split(Platform.pathSeparator).last;
      if (fileName.endsWith('.md')) {
        _openMarkdownFile(entity);
      } else {
        Get.snackbar('提示', '暂不支持打开此类型的文件');
      }
    }
  }

  void _openMarkdownFile(File file) {
    Get.to(() => MarkdownEditorScreen(file: file))?.then((_) {
      // 编辑完成后刷新文件列表
      _loadFolderContents();
    });
  }

  void _handleFileAction(String action, FileSystemEntity entity) {
    switch (action) {
      case 'open':
        _openFile(entity);
        break;
      case 'edit':
        if (entity is File) {
          _openMarkdownFile(entity);
        }
        break;
      case 'rename':
        _showRenameDialog(entity);
        break;
      case 'delete':
        _showDeleteDialog(entity);
        break;
    }
  }

  void _showRenameDialog(FileSystemEntity entity) {
    final currentName = entity.path.split(Platform.pathSeparator).last;
    final controller = TextEditingController(text: currentName);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重命名'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: '新名称',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _renameFile(entity, controller.text);
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(FileSystemEntity entity) {
    final fileName = entity.path.split(Platform.pathSeparator).last;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除确认'),
        content: Text('确定要删除 "$fileName" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteFile(entity);
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showCreateFileDialog() {
    final controller = TextEditingController();
    String selectedType = 'chapter'; // chapter, file, folder

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('新建'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('选择类型：'),
              const SizedBox(height: 8),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<String>(
                      title: const Text('章节'),
                      value: 'chapter',
                      groupValue: selectedType,
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                          if (selectedType == 'chapter') {
                            // 自动生成章节文件名
                            final chapterCount = _files.where((f) =>
                              f is File && f.path.contains('第') && f.path.contains('章')).length;
                            controller.text = '第${(chapterCount + 1).toString().padLeft(3, '0')}章_新章节.md';
                          } else if (selectedType == 'folder') {
                            controller.text = '新文件夹/';
                          } else {
                            controller.text = '新文件.md';
                          }
                        });
                      },
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<String>(
                      title: const Text('文件'),
                      value: 'file',
                      groupValue: selectedType,
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                          controller.text = '新文件.md';
                        });
                      },
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Expanded(
                    child: RadioListTile<String>(
                      title: const Text('文件夹'),
                      value: 'folder',
                      groupValue: selectedType,
                      onChanged: (value) {
                        setState(() {
                          selectedType = value!;
                          controller.text = '新文件夹/';
                        });
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              TextField(
                controller: controller,
                decoration: InputDecoration(
                  labelText: selectedType == 'folder' ? '文件夹名称' : '文件名',
                  hintText: selectedType == 'chapter'
                    ? '第001章_新的开始.md'
                    : selectedType == 'folder'
                      ? '新文件夹'
                      : '新文件.md',
                  border: const OutlineInputBorder(),
                ),
                autofocus: true,
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.pop(context);
                String fileName = controller.text.trim();
                if (selectedType == 'folder' && !fileName.endsWith('/')) {
                  fileName += '/';
                }
                _createFile(fileName);
              },
              child: const Text('创建'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _renameFile(FileSystemEntity entity, String newName) async {
    if (newName.trim().isEmpty) {
      Get.snackbar('错误', '文件名不能为空');
      return;
    }

    final currentName = entity.path.split(Platform.pathSeparator).last;
    if (newName == currentName) {
      return; // 名称没有变化
    }

    try {
      final parentDir = entity.parent.path;
      final newPath = path.join(parentDir, newName);

      // 检查新名称是否已存在
      if (await FileSystemEntity.isFile(newPath) || await FileSystemEntity.isDirectory(newPath)) {
        Get.snackbar('错误', '名称 "$newName" 已存在');
        return;
      }

      await entity.rename(newPath);
      Get.snackbar('成功', '已重命名为 "$newName"');
      _loadFolderContents(); // 刷新列表
    } catch (e) {
      Get.snackbar('错误', '重命名失败：$e');
    }
  }

  Future<void> _deleteFile(FileSystemEntity entity) async {
    try {
      final fileName = entity.path.split(Platform.pathSeparator).last;

      // 确认删除
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除 "$fileName" 吗？此操作不可撤销。'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('删除', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await entity.delete(recursive: true);
        Get.snackbar('成功', '已删除 "$fileName"');
        _loadFolderContents(); // 刷新列表
      }
    } catch (e) {
      Get.snackbar('错误', '删除失败：$e');
    }
  }

  Future<void> _createFile(String fileName) async {
    if (_folderPath == null) return;

    try {
      final filePath = path.join(_folderPath!, fileName);

      if (fileName.endsWith('/')) {
        // 创建文件夹
        final directory = Directory(filePath.substring(0, filePath.length - 1));
        await directory.create(recursive: true);
        Get.snackbar('成功', '已创建文件夹 "$fileName"');
      } else {
        // 创建文件
        final file = File(filePath);

        // 检查文件是否已存在
        if (await file.exists()) {
          Get.snackbar('错误', '文件 "$fileName" 已存在');
          return;
        }

        // 根据文件类型创建不同的初始内容
        String initialContent = '';
        if (fileName.endsWith('.md')) {
          if (fileName.startsWith('第') && fileName.contains('章')) {
            // 章节文件
            final chapterMatch = RegExp(r'第(\d+)章').firstMatch(fileName);
            final chapterNumber = chapterMatch?.group(1) ?? '1';
            initialContent = '''# 第$chapterNumber章

## 章节内容

在这里开始写作...

''';
          } else {
            // 普通Markdown文件
            initialContent = '''# ${fileName.replaceAll('.md', '')}

在这里开始写作...

''';
          }
        }

        await file.writeAsString(initialContent);
        Get.snackbar('成功', '已创建文件 "$fileName"');
      }

      _loadFolderContents(); // 刷新列表
    } catch (e) {
      Get.snackbar('错误', '创建失败：$e');
    }
  }

  void _openSmartComposer() {
    Get.snackbar('提示', 'AI写作助手功能已移除，请使用AI编辑器');
  }

  void _openSmartComposerForFile(File file) {
    // TODO: 为特定文件打开 AI 助手
    Get.snackbar('提示', '文件级 AI 助手正在开发中');
  }

  void _openInExplorer() {
    if (_folderPath == null) return;

    try {
      if (Platform.isWindows) {
        Process.run('explorer', [_folderPath!]);
      } else if (Platform.isMacOS) {
        Process.run('open', [_folderPath!]);
      } else if (Platform.isLinux) {
        Process.run('xdg-open', [_folderPath!]);
      } else {
        Get.snackbar('提示', '当前平台不支持此功能');
      }
    } catch (e) {
      Get.snackbar('错误', '打开文件管理器失败：$e');
    }
  }

  void _exportAsMarkdown() async {
    if (_folderPath == null || _novel == null) return;

    try {
      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在导出...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 收集所有章节文件
      final directory = Directory(_folderPath!);
      final files = await directory.list().toList();
      final chapterFiles = files
          .whereType<File>()
          .where((f) => f.path.endsWith('.md') && f.path.contains('第') && f.path.contains('章'))
          .toList();

      // 按章节号排序
      chapterFiles.sort((a, b) {
        final aName = path.basename(a.path);
        final bName = path.basename(b.path);
        final aMatch = RegExp(r'第(\d+)章').firstMatch(aName);
        final bMatch = RegExp(r'第(\d+)章').firstMatch(bName);

        if (aMatch != null && bMatch != null) {
          final aNum = int.tryParse(aMatch.group(1)!) ?? 0;
          final bNum = int.tryParse(bMatch.group(1)!) ?? 0;
          return aNum.compareTo(bNum);
        }
        return aName.compareTo(bName);
      });

      // 合并内容
      final buffer = StringBuffer();
      buffer.writeln('# ${_novel!.title}');
      buffer.writeln();
      buffer.writeln('**类型：** ${_novel!.genre}');
      buffer.writeln('**创建时间：** ${_novel!.createTime}');
      buffer.writeln();

      // 添加大纲（如果存在）
      final outlineFile = File(path.join(_folderPath!, 'outline.md'));
      if (await outlineFile.exists()) {
        final outlineContent = await outlineFile.readAsString();
        buffer.writeln('## 大纲');
        buffer.writeln();
        buffer.writeln(outlineContent);
        buffer.writeln();
      }

      // 添加章节内容
      for (final file in chapterFiles) {
        final content = await file.readAsString();
        buffer.writeln(content);
        buffer.writeln();
        buffer.writeln('---');
        buffer.writeln();
      }

      // 保存到导出文件
      final exportFile = File(path.join(_folderPath!, '${_novel!.title}_完整版.md'));
      await exportFile.writeAsString(buffer.toString());

      Get.back(); // 关闭加载对话框

      Get.snackbar(
        '导出成功',
        '已导出到：${exportFile.path}',
        duration: const Duration(seconds: 5),
      );

      _loadFolderContents(); // 刷新文件列表
    } catch (e) {
      Get.back(); // 关闭加载对话框
      Get.snackbar('错误', '导出失败：$e');
    }
  }

  void _syncToDatabase() async {
    if (_folderPath == null || _novel == null) return;

    try {
      // 显示确认对话框
      final confirmed = await Get.dialog<bool>(
        AlertDialog(
          title: const Text('同步到数据库'),
          content: const Text('这将用文件夹中的内容覆盖数据库中的小说数据。确定要继续吗？'),
          actions: [
            TextButton(
              onPressed: () => Get.back(result: false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Get.back(result: true),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      if (confirmed != true) return;

      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在同步...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      // 使用文件管理器的同步功能
      final updatedNovel = await _fileManager.syncFromFileSystem(_novel!);

      if (updatedNovel != null) {
        // 更新控制器中的小说数据
        final novelController = Get.find<NovelController>();
        await novelController.saveNovel(updatedNovel);

        Get.back(); // 关闭加载对话框
        Get.snackbar('同步成功', '文件夹内容已同步到数据库');
      } else {
        Get.back(); // 关闭加载对话框
        Get.snackbar('同步失败', '无法读取文件夹内容');
      }
    } catch (e) {
      Get.back(); // 关闭加载对话框
      Get.snackbar('错误', '同步失败：$e');
    }
  }
}
