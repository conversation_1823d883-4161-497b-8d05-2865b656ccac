{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/mention/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Mention from './src/mention.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElMention: SFCWithInstall<typeof Mention> = withInstall(Mention)\nexport default ElMention\n\nexport * from './src/mention'\n"], "names": ["withInstall", "Mention"], "mappings": ";;;;;;;;AAEY,MAAC,SAAS,GAAGA,mBAAW,CAACC,oBAAO;;;;;;;"}