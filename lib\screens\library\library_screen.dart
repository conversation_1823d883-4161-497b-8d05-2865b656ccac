import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/controllers/smart_composer_controller.dart';
import 'package:novel_app/controllers/novel_agent_controller.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/screens/novel_detail_screen.dart';
import 'package:novel_app/services/export_service.dart';
import 'package:novel_app/services/novel_file_manager.dart';

import 'package:novel_app/screens/import_screen.dart';
import 'package:novel_app/screens/daizong_ai_assistant_screen.dart';

class LibraryScreen extends GetView<NovelController> {
  final _exportService = ExportService();
  final _fileManager = NovelFileManager();
  final _smartComposerController = Get.put(SmartComposerController());

  // 多选模式状态
  final RxBool isMultiSelectMode = RxBool(false);
  final RxSet<Novel> selectedNovels = RxSet<Novel>();

  LibraryScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Obx(() => Text(isMultiSelectMode.value
            ? '已选择 ${selectedNovels.length} 本小说'
            : '我的书库')),
        actions: [
          Obx(() => isMultiSelectMode.value
              ? Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.select_all),
                      tooltip: '全选',
                      onPressed: _selectAllNovels,
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      tooltip: '批量删除',
                      onPressed: selectedNovels.isNotEmpty ? () => _showBatchDeleteDialog(context) : null,
                    ),
                    IconButton(
                      icon: const Icon(Icons.file_download),
                      tooltip: '批量导出',
                      onPressed: selectedNovels.isNotEmpty ? () => _showBatchExportDialog(context) : null,
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      tooltip: '退出多选',
                      onPressed: _exitMultiSelectMode,
                    ),
                  ],
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.checklist),
                      tooltip: '多选模式',
                      onPressed: _enterMultiSelectMode,
                    ),
                    PopupMenuButton<String>(
                      icon: const Icon(Icons.view_module),
                      tooltip: '视图选项',
                      onSelected: (value) {
                        switch (value) {
                          case 'folder_view':
                            _showFolderViewDialog(context);
                            break;
                          case 'export_all':
                            _showExportAllDialog(context);
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'folder_view',
                          child: Row(
                            children: [
                              Icon(Icons.folder),
                              SizedBox(width: 8),
                              Text('文件夹视图'),
                            ],
                          ),
                        ),
                        const PopupMenuItem(
                          value: 'export_all',
                          child: Row(
                            children: [
                              Icon(Icons.download),
                              SizedBox(width: 8),
                              Text('批量导出'),
                            ],
                          ),
                        ),
                      ],
                    ),
                    IconButton(
                      icon: const Icon(Icons.file_upload),
                      tooltip: '导入小说',
                      onPressed: () => Get.to(() => const ImportScreen()),
                    ),
                  ],
                )),
        ],
      ),
      body: _buildMainContent(),
    );
  }

  /// 构建主内容区域
  Widget _buildMainContent() {
    if (controller.novels.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.library_books,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '书库空空如也',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              '去生成一本新小说吧',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: controller.novels.length,
          itemBuilder: (context, index) {
            final novel = controller.novels[index];
            return Obx(() {
              final isSelected = selectedNovels.contains(novel);
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : null,
                child: InkWell(
                  onTap: () {
                    if (isMultiSelectMode.value) {
                      _toggleNovelSelection(novel);
                    } else {
                      Get.to(() => NovelDetailScreen(novel: novel));
                    }
                  },
                  onLongPress: () {
                    if (!isMultiSelectMode.value) {
                      _enterMultiSelectMode();
                      _toggleNovelSelection(novel);
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            // 多选模式下显示复选框
                            if (isMultiSelectMode.value)
                              Container(
                                margin: const EdgeInsets.only(right: 12),
                                child: Checkbox(
                                  value: isSelected,
                                  onChanged: (bool? value) {
                                    _toggleNovelSelection(novel);
                                  },
                                ),
                              ),
                            // 文件夹图标
                            Container(
                              margin: const EdgeInsets.only(right: 12),
                              child: Icon(
                                novel.useFileSystem ? Icons.folder : Icons.book,
                                size: 32,
                                color: novel.useFileSystem
                                    ? Colors.amber[700]
                                    : Colors.blue[700],
                              ),
                            ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        novel.title,
                                        style: const TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                    // 岱宗AI辅助助手快捷按钮
                                    IconButton(
                                      icon: const Icon(Icons.auto_fix_high),
                                      iconSize: 20,
                                      tooltip: '岱宗AI辅助助手',
                                      onPressed: () => _openNovelAgent(novel),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 4),
                                Row(
                                  children: [
                                    Text(
                                      novel.genre,
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    if (novel.useFileSystem) ...[
                                      const SizedBox(width: 8),
                                      Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 6,
                                          vertical: 2,
                                        ),
                                        decoration: BoxDecoration(
                                          color: Colors.amber[100],
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          '文件夹',
                                          style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.amber[800],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ],
                                ),
                              ],
                            ),
                          ),
                          PopupMenuButton<String>(
                            itemBuilder: (context) => [
                              if (!novel.useFileSystem)
                                const PopupMenuItem(
                                  value: 'convert_to_folder',
                                  child: Row(
                                    children: [
                                      Icon(Icons.folder),
                                      SizedBox(width: 8),
                                      Text('转换为文件夹'),
                                    ],
                                  ),
                                ),
                              if (novel.useFileSystem)
                                const PopupMenuItem(
                                  value: 'open_folder',
                                  child: Row(
                                    children: [
                                      Icon(Icons.folder_open),
                                      SizedBox(width: 8),
                                      Text('打开文件夹'),
                                    ],
                                  ),
                                ),
                              const PopupMenuItem(
                                value: 'ai_editor',
                                child: Row(
                                  children: [
                                    Icon(Icons.auto_fix_high),
                                    SizedBox(width: 8),
                                    Text('岱宗AI辅助助手'),
                                  ],
                                ),
                              ),

                              const PopupMenuItem(
                                value: 'export',
                                child: Row(
                                  children: [
                                    Icon(Icons.file_download),
                                    SizedBox(width: 8),
                                    Text('导出'),
                                  ],
                                ),
                              ),
                              const PopupMenuItem(
                                value: 'delete',
                                child: Row(
                                  children: [
                                    Icon(Icons.delete_outline),
                                    SizedBox(width: 8),
                                    Text('删除'),
                                  ],
                                ),
                              ),
                            ],
                            onSelected: (value) {
                              switch (value) {
                                case 'convert_to_folder':
                                  _convertToFolderStructure(novel);
                                  break;
                                case 'open_folder':
                                  _openNovelFolder(novel);
                                  break;
                                case 'ai_editor':
                                  _openNovelAgent(novel);
                                  break;

                                case 'export':
                                  _showExportDialog(context, novel);
                                  break;
                                case 'delete':
                                  _showDeleteDialog(context, novel);
                                  break;
                              }
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            '${novel.chapters.length}章',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '字数：${novel.wordCount}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            novel.createTime,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
            });
          },
        );
  }

  // 多选模式相关方法
  void _enterMultiSelectMode() {
    isMultiSelectMode.value = true;
    selectedNovels.clear();
  }

  void _exitMultiSelectMode() {
    isMultiSelectMode.value = false;
    selectedNovels.clear();
  }

  void _toggleNovelSelection(Novel novel) {
    if (selectedNovels.contains(novel)) {
      selectedNovels.remove(novel);
    } else {
      selectedNovels.add(novel);
    }
  }

  void _selectAllNovels() {
    selectedNovels.addAll(controller.novels);
  }

  void _showBatchDeleteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('批量删除'),
        content: Text('确定要删除选中的 ${selectedNovels.length} 本小说吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _batchDeleteNovels();
            },
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _batchDeleteNovels() async {
    final novelsToDelete = selectedNovels.toList();
    try {
      for (final novel in novelsToDelete) {
        await controller.deleteNovel(novel);
      }
      Get.snackbar('成功', '已删除 ${novelsToDelete.length} 本小说');
      _exitMultiSelectMode();
    } catch (e) {
      Get.snackbar('错误', '批量删除失败：$e');
    }
  }

  void _showBatchExportDialog(BuildContext context) {
    String selectedFormat = 'txt';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('批量导出'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('将导出 ${selectedNovels.length} 本小说'),
            const SizedBox(height: 16),
            const Text('选择导出格式:'),
            const SizedBox(height: 8),
            StatefulBuilder(
              builder: (context, setState) => Column(
                children: [
                  RadioListTile<String>(
                    title: const Text('TXT 文本'),
                    value: 'txt',
                    groupValue: selectedFormat,
                    onChanged: (value) {
                      setState(() {
                        selectedFormat = value!;
                      });
                    },
                  ),
                  RadioListTile<String>(
                    title: const Text('Markdown'),
                    value: 'md',
                    groupValue: selectedFormat,
                    onChanged: (value) {
                      setState(() {
                        selectedFormat = value!;
                      });
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _batchExportNovels(selectedFormat);
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }

  Future<void> _batchExportNovels(String format) async {
    try {
      // 显示加载对话框
      Get.dialog(
        const Center(
          child: Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在批量导出...'),
                ],
              ),
            ),
          ),
        ),
        barrierDismissible: false,
      );

      final results = <String>[];
      for (final novel in selectedNovels) {
        final result = await _exportService.exportNovel(novel, format);
        results.add('《${novel.title}》: $result');
      }

      Get.back(); // 关闭加载对话框

      // 显示结果
      Get.dialog(
        AlertDialog(
          title: const Text('批量导出结果'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: results.map((result) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Text(result),
                )).toList(),
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Get.back(),
              child: const Text('确定'),
            ),
          ],
        ),
      );

      _exitMultiSelectMode();
    } catch (e) {
      Get.back(); // 关闭加载对话框
      Get.snackbar('错误', '批量导出失败：$e');
    }
  }

  void _showExportDialog(BuildContext context, Novel novel) {
    String selectedFormat = 'txt';
    final selectedChapters = <Chapter>{}.obs;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('导出小说'),
        content: StatefulBuilder(
          builder: (context, setState) => SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('选择导出格式：'),
                const SizedBox(height: 8),
                ...ExportService.supportedFormats.entries.map(
                  (entry) => RadioListTile<String>(
                    title: Text(entry.value),
                    value: entry.key,
                    groupValue: selectedFormat,
                    onChanged: (value) {
                      setState(() => selectedFormat = value!);
                    },
                  ),
                ),
                const Divider(),
                const Text('选择章节：'),
                const SizedBox(height: 8),
                Flexible(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        CheckboxListTile(
                          title: const Text('全选'),
                          value:
                              selectedChapters.length == novel.chapters.length,
                          onChanged: (checked) {
                            if (checked == true) {
                              selectedChapters.addAll(novel.chapters);
                            } else {
                              selectedChapters.clear();
                            }
                          },
                        ),
                        const Divider(),
                        ...novel.chapters
                            .map((chapter) => Obx(() => CheckboxListTile(
                                  title: Text(
                                      '第${chapter.number}章：${chapter.title}'),
                                  value: selectedChapters.contains(chapter),
                                  onChanged: (checked) {
                                    if (checked == true) {
                                      selectedChapters.add(chapter);
                                    } else {
                                      selectedChapters.remove(chapter);
                                    }
                                  },
                                ))),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);

              if (selectedChapters.isEmpty) {
                Get.snackbar('提示', '请选择要导出的章节');
                return;
              }

              // 显示加载对话框
              Get.dialog(
                const Center(
                  child: Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text('正在导出...'),
                        ],
                      ),
                    ),
                  ),
                ),
                barrierDismissible: false,
              );

              try {
                final result = await _exportService.exportNovel(
                  novel,
                  selectedFormat,
                  selectedChapters: selectedChapters.toList(),
                );

                Get.back(); // 关闭加载对话框

                Get.snackbar(
                  '导出结果',
                  result,
                  duration: const Duration(seconds: 5),
                );
              } catch (e) {
                Get.back(); // 关闭加载对话框
                Get.snackbar('错误', '导出失败：$e');
              }
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, Novel novel) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('删除小说'),
        content: Text('确定要删除《${novel.title}》吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              controller.deleteNovel(novel);
              Navigator.pop(context);
            },
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }



  /// 显示文件夹视图对话框
  void _showFolderViewDialog(BuildContext context) {
    // 获取所有使用文件系统的小说
    final folderNovels = controller.novels.where((n) => n.useFileSystem && n.folderPath != null).toList();

    if (folderNovels.isEmpty) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('文件夹视图'),
          content: const Text('暂无使用文件夹存储的小说。\n\n您可以在小说菜单中选择"转换为文件夹"来创建文件夹结构。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('确定'),
            ),
          ],
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择小说文件夹'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: folderNovels.length,
            itemBuilder: (context, index) {
              final novel = folderNovels[index];
              return ListTile(
                leading: const Icon(Icons.folder, color: Colors.amber),
                title: Text(novel.title),
                subtitle: Text(novel.genre),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.pop(context);
                  _openNovelFolder(novel);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        ],
      ),
    );
  }

  /// 显示批量导出对话框
  void _showExportAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('批量导出'),
        content: const Text('批量导出功能正在开发中，敬请期待！'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }



  /// 转换小说为文件夹结构
  Future<void> _convertToFolderStructure(Novel novel) async {
    try {
      Get.dialog(
        const Center(
          child: CircularProgressIndicator(),
        ),
        barrierDismissible: false,
      );

      // 保存到文件系统
      final folderPath = await _fileManager.saveNovelToFileSystem(novel);

      // 更新小说对象
      final updatedNovel = novel.copyWith(
        folderPath: folderPath,
        useFileSystem: true,
      );

      // 保存到控制器
      await controller.saveNovel(updatedNovel);

      Get.back(); // 关闭加载对话框

      Get.snackbar(
        '转换成功',
        '小说已转换为文件夹结构\n路径：$folderPath',
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      Get.back(); // 关闭加载对话框
      Get.snackbar('转换失败', '转换为文件夹结构失败：$e');
    }
  }

  /// 打开小说文件夹
  void _openNovelFolder(Novel novel) {
    if (novel.folderPath == null) {
      Get.snackbar('错误', '小说文件夹路径不存在');
      return;
    }

    // 导航到文件夹视图
    Get.toNamed('/novel_folder_view', arguments: {
      'novel': novel,
      'folderPath': novel.folderPath,
    });
  }

  /// 打开岱宗AI辅助助手
  void _openNovelAgent(Novel novel) async {
    try {
      // 先尝试同步配置
      await _smartComposerController.syncFromApiConfig();

      // 检查是否配置了AI模型
      final defaultModel = _smartComposerController.defaultModel;
      final hasConfiguredProvider = _smartComposerController.availableProviders
          .any((p) => _smartComposerController.isProviderConfigured(p.id));

      if (defaultModel == null || !hasConfiguredProvider) {
        Get.dialog(
          AlertDialog(
            title: const Text('AI模型未配置'),
            content: const Text('请先配置AI模型才能使用岱宗AI辅助助手。\n\n您可以在设置中配置API密钥，然后点击"快速配置"同步到AI助手。'),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  Get.toNamed('/settings');
                },
                child: const Text('去设置'),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  Get.toNamed('/quick_ai_setup');
                },
                child: const Text('快速配置'),
              ),
            ],
          ),
        );
        return;
      }

      // 导航到岱宗AI辅助助手
      Get.to(() => DaizongAIAssistantScreen(novel: novel));

    } catch (e) {
      Get.snackbar(
        '错误',
        '启动AI辅助助手失败: $e',
        backgroundColor: Colors.red.withOpacity(0.1),
        colorText: Colors.red[700],
      );
    }
  }


}
