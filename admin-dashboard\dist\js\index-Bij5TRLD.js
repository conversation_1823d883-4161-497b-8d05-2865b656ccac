import{_ as J}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                        *//* empty css                   *//* empty css                        *//* empty css               *//* empty css                       *//* empty css                 */import{d as Q,r as p,y as b,o as ee,c as T,b as a,e,w as o,ah as te,a3 as ae,t as m,W as oe,q as se,Z as le,v as w,i as h,ai as ne,h as d,F as re,G as de,X as ie,Y as ue,j as x,_ as ce,aj as pe,$ as me,E as _e,B as V,u as ve,H as U,M as he,K as $}from"./index-CAzH2L69.js";import{cE as C}from"./index.esm.min-Cv8CkJd6.js";const fe={class:"analytics-container"},ge={class:"dashboard-card"},ye={class:"time-selector"},be={class:"dashboard-card"},we={class:"card-header"},xe={class:"period-info"},Ce={class:"metric-icon"},Me={class:"metric-content"},ke={class:"metric-value"},Se={class:"metric-label"},De={class:"metric-trend"},Ee={class:"dashboard-card"},Ue={class:"card-header"},Re={class:"chart-container"},Ae={class:"dashboard-card"},Be={class:"chart-container"},Ie={class:"dashboard-card"},ze={class:"chart-container"},Te={class:"dashboard-card"},Ve={class:"chart-container"},$e={class:"dashboard-card"},Ne={class:"card-header"},Pe={class:"dashboard-card"},We={class:"card-header"},Oe={class:"dashboard-card"},Fe={class:"card-header"},Ge={class:"report-actions"},je=Q({__name:"index",setup(He){ve();const f=p("30d"),M=p(null),g=p("new"),k=p(!1),v=p([{key:"users",label:"总用户数",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"User",color:"#1890ff"},{key:"novels",label:"小说总数",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"Reading",color:"#52c41a"},{key:"words",label:"总字数",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"EditPen",color:"#faad14"},{key:"members",label:"会员用户",value:"0",trend:"+0%",trendClass:"trend-up",trendIcon:"ArrowUp",icon:"Postcard",color:"#f5222d"}]),R=p([]),A=p([]),B=p([]),N=b(()=>({tooltip:{trigger:"axis"},legend:{data:["新增用户","活跃用户"]},xAxis:{type:"category",data:["1/15","1/16","1/17","1/18","1/19","1/20","1/21"]},yAxis:{type:"value"},series:[{name:"新增用户",type:"line",data:[12,15,8,18,10,12,15],smooth:!0,itemStyle:{color:"#1890ff"}},{name:"活跃用户",type:"line",data:[65,72,68,78,69,76,89],smooth:!0,itemStyle:{color:"#52c41a"}}]})),P=b(()=>({tooltip:{trigger:"axis"},xAxis:{type:"category",data:["1/15","1/16","1/17","1/18","1/19","1/20","1/21"]},yAxis:{type:"value"},series:[{data:[2,3,1,4,2,5,3],type:"bar",itemStyle:{color:"#faad14"}}]})),W=b(()=>({tooltip:{trigger:"item",formatter:"{a} <br/>{b} : {c}%"},series:[{name:"会员转化",type:"funnel",left:"10%",top:60,bottom:60,width:"80%",min:0,max:100,minSize:"0%",maxSize:"100%",sort:"descending",gap:2,label:{show:!0,position:"inside"},data:[{value:100,name:"访问用户"},{value:80,name:"注册用户"},{value:60,name:"活跃用户"},{value:40,name:"付费意向"},{value:20,name:"会员用户"}]}]})),O=b(()=>({tooltip:{trigger:"item"},series:[{type:"pie",radius:"50%",data:[{value:35,name:"都市"},{value:25,name:"玄幻"},{value:20,name:"科幻"},{value:15,name:"历史"},{value:5,name:"其他"}],emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}}]})),F=()=>({"7d":"最近7天","30d":"最近30天","90d":"最近90天","1y":"最近1年"})[f.value]||"自定义时间段",I=l=>l>=1e4?(l/1e4).toFixed(1)+"万":l.toString(),G=l=>l>=90?"#f5222d":l>=70?"#faad14":"#52c41a",j=l=>{M.value=null,S()},H=l=>{l&&(f.value="custom",S())},L=async()=>{k.value=!0;try{await new Promise(l=>setTimeout(l,2e3)),w.success("报表生成成功")}catch{w.error("报表生成失败")}finally{k.value=!1}},X=()=>{w.info("导出功能开发中...")},S=async()=>{try{const l=await fetch("/api/dashboard",{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(l.ok){const i=await l.json();if(i.success){const n=i.data;v.value[0].value=n.stats.totalUsers.toString(),v.value[1].value=n.stats.totalNovels.toString(),v.value[3].value=n.stats.memberUsers.toString(),R.value=(n.popularNovels||[]).map((u,c)=>({title:u.title,wordCount:u.wordCount||0,popularity:Math.max(50,100-c*10)})),A.value=(n.recentUsers||[]).map((u,c)=>({username:u.username,isMember:u.isMember||!1,novelCount:Math.floor(Math.random()*20)+1,activityScore:Math.max(60,100-c*5)}))}}const t=await fetch("/api/novels/stats",{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(t.ok){const i=await t.json();if(i.success){const n=i.data.totalWords||0;v.value[2].value=n>=1e4?(n/1e4).toFixed(1)+"万":n.toString()}}const _=new Date,D=7,y=[];for(let i=D-1;i>=0;i--){const n=new Date(_);n.setDate(n.getDate()-i),y.push({date:n.toISOString().split("T")[0],newUsers:Math.floor(Math.random()*20)+5,activeUsers:Math.floor(Math.random()*50)+30,newNovels:Math.floor(Math.random()*8)+1,totalWords:Math.floor(Math.random()*2e4)+5e3,memberConversions:Math.floor(Math.random()*5),syncCount:Math.floor(Math.random()*30)+20})}B.value=y}catch(l){console.error("加载分析数据失败:",l),w.error("加载分析数据失败")}};return ee(()=>{S()}),(l,t)=>{const _=ne,D=te,y=ae,i=_e,n=ie,u=oe,c=se,q=ue,r=ce,z=pe,E=le,K=me,Y=V("Document"),Z=V("Download");return h(),T("div",fe,[t[24]||(t[24]=a("div",{class:"page-header"},[a("h1",{class:"page-title"},"数据分析"),a("p",{class:"page-subtitle"},"业务数据分析和报表")],-1)),a("div",ge,[a("div",ye,[e(D,{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=s=>f.value=s),onChange:j},{default:o(()=>[e(_,{label:"7d"},{default:o(()=>t[6]||(t[6]=[d("最近7天")])),_:1,__:[6]}),e(_,{label:"30d"},{default:o(()=>t[7]||(t[7]=[d("最近30天")])),_:1,__:[7]}),e(_,{label:"90d"},{default:o(()=>t[8]||(t[8]=[d("最近90天")])),_:1,__:[8]}),e(_,{label:"1y"},{default:o(()=>t[9]||(t[9]=[d("最近1年")])),_:1,__:[9]})]),_:1},8,["modelValue"]),e(y,{modelValue:M.value,"onUpdate:modelValue":t[1]||(t[1]=s=>M.value=s),type:"daterange","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期",onChange:H,style:{"margin-left":"16px"}},null,8,["modelValue"])])]),a("div",be,[a("div",we,[t[10]||(t[10]=a("h3",{class:"card-title"},"核心指标概览",-1)),a("div",xe,m(F()),1)]),e(u,{gutter:20},{default:o(()=>[(h(!0),T(re,null,de(v.value,s=>(h(),U(n,{xs:12,sm:6,key:s.key},{default:o(()=>[a("div",{class:he(["metric-card",s.trendClass])},[a("div",Ce,[e(i,{size:24,color:s.color},{default:o(()=>[(h(),U($(s.icon)))]),_:2},1032,["color"])]),a("div",Me,[a("div",ke,m(s.value),1),a("div",Se,m(s.label),1),a("div",De,[e(i,{size:"12"},{default:o(()=>[(h(),U($(s.trendIcon)))]),_:2},1024),d(" "+m(s.trend),1)])])],2)]),_:2},1024))),128))]),_:1})]),e(u,{gutter:20},{default:o(()=>[e(n,{xs:24,lg:12},{default:o(()=>[a("div",Ee,[a("div",Ue,[t[13]||(t[13]=a("h3",{class:"card-title"},"用户增长趋势",-1)),e(q,{size:"small"},{default:o(()=>[e(c,{type:g.value==="new"?"primary":"",onClick:t[2]||(t[2]=s=>g.value="new")},{default:o(()=>t[11]||(t[11]=[d(" 新增用户 ")])),_:1,__:[11]},8,["type"]),e(c,{type:g.value==="active"?"primary":"",onClick:t[3]||(t[3]=s=>g.value="active")},{default:o(()=>t[12]||(t[12]=[d(" 活跃用户 ")])),_:1,__:[12]},8,["type"])]),_:1})]),a("div",Re,[e(x(C),{option:N.value,style:{height:"300px"}},null,8,["option"])])])]),_:1}),e(n,{xs:24,lg:12},{default:o(()=>[a("div",Ae,[t[14]||(t[14]=a("div",{class:"card-header"},[a("h3",{class:"card-title"},"小说创作分析")],-1)),a("div",Be,[e(x(C),{option:P.value,style:{height:"300px"}},null,8,["option"])])])]),_:1})]),_:1}),e(u,{gutter:20},{default:o(()=>[e(n,{xs:24,lg:12},{default:o(()=>[a("div",Ie,[t[15]||(t[15]=a("div",{class:"card-header"},[a("h3",{class:"card-title"},"会员转化漏斗")],-1)),a("div",ze,[e(x(C),{option:W.value,style:{height:"300px"}},null,8,["option"])])])]),_:1}),e(n,{xs:24,lg:12},{default:o(()=>[a("div",Te,[t[16]||(t[16]=a("div",{class:"card-header"},[a("h3",{class:"card-title"},"小说类型分布")],-1)),a("div",Ve,[e(x(C),{option:O.value,style:{height:"300px"}},null,8,["option"])])])]),_:1})]),_:1}),e(u,{gutter:20},{default:o(()=>[e(n,{xs:24,lg:12},{default:o(()=>[a("div",$e,[a("div",Ne,[t[18]||(t[18]=a("h3",{class:"card-title"},"热门小说排行",-1)),e(c,{type:"text",onClick:t[4]||(t[4]=s=>l.$router.push("/novels"))},{default:o(()=>t[17]||(t[17]=[d("查看全部")])),_:1,__:[17]})]),e(E,{data:R.value,style:{width:"100%"}},{default:o(()=>[e(r,{type:"index",label:"排名",width:"60"}),e(r,{prop:"title",label:"小说标题","show-overflow-tooltip":""}),e(r,{label:"字数",width:"80"},{default:o(({row:s})=>[d(m(I(s.wordCount)),1)]),_:1}),e(r,{label:"热度",width:"80"},{default:o(({row:s})=>[e(z,{percentage:s.popularity,"stroke-width":6,"show-text":!1,color:G(s.popularity)},null,8,["percentage","color"])]),_:1})]),_:1},8,["data"])])]),_:1}),e(n,{xs:24,lg:12},{default:o(()=>[a("div",Pe,[a("div",We,[t[20]||(t[20]=a("h3",{class:"card-title"},"活跃用户排行",-1)),e(c,{type:"text",onClick:t[5]||(t[5]=s=>l.$router.push("/users"))},{default:o(()=>t[19]||(t[19]=[d("查看全部")])),_:1,__:[19]})]),e(E,{data:A.value,style:{width:"100%"}},{default:o(()=>[e(r,{type:"index",label:"排名",width:"60"}),e(r,{prop:"username",label:"用户名",width:"100"}),e(r,{label:"会员状态",width:"80"},{default:o(({row:s})=>[e(K,{type:s.isMember?"success":"info",size:"small"},{default:o(()=>[d(m(s.isMember?"会员":"普通"),1)]),_:2},1032,["type"])]),_:1}),e(r,{label:"创作数",width:"80"},{default:o(({row:s})=>[d(m(s.novelCount),1)]),_:1}),e(r,{label:"活跃度",width:"100"},{default:o(({row:s})=>[e(z,{percentage:s.activityScore,"stroke-width":6,"show-text":!1,color:"#52c41a"},null,8,["percentage"])]),_:1})]),_:1},8,["data"])])]),_:1})]),_:1}),a("div",Oe,[a("div",Fe,[t[23]||(t[23]=a("h3",{class:"card-title"},"数据报表",-1)),a("div",Ge,[e(c,{onClick:L,loading:k.value},{default:o(()=>[e(i,null,{default:o(()=>[e(Y)]),_:1}),t[21]||(t[21]=d(" 生成报表 "))]),_:1,__:[21]},8,["loading"]),e(c,{onClick:X},{default:o(()=>[e(i,null,{default:o(()=>[e(Z)]),_:1}),t[22]||(t[22]=d(" 导出数据 "))]),_:1,__:[22]})])]),e(E,{data:B.value,style:{width:"100%"}},{default:o(()=>[e(r,{prop:"date",label:"日期",width:"120"}),e(r,{prop:"newUsers",label:"新增用户",width:"100"}),e(r,{prop:"activeUsers",label:"活跃用户",width:"100"}),e(r,{prop:"newNovels",label:"新增小说",width:"100"}),e(r,{prop:"totalWords",label:"新增字数",width:"120"},{default:o(({row:s})=>[d(m(I(s.totalWords)),1)]),_:1}),e(r,{prop:"memberConversions",label:"会员转化",width:"100"}),e(r,{prop:"syncCount",label:"数据同步",width:"100"})]),_:1},8,["data"])])])}}}),tt=J(je,[["__scopeId","data-v-628e9eb3"]]);export{tt as default};
