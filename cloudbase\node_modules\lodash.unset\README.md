# lodash.unset v4.5.2

The [lodash](https://lodash.com/) method `_.unset` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.unset
```

In Node.js:
```js
var unset = require('lodash.unset');
```

See the [documentation](https://lodash.com/docs#unset) or [package source](https://github.com/lodash/lodash/blob/4.5.2-npm-packages/lodash.unset) for more details.
