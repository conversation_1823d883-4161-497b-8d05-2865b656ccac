{"version": 3, "file": "option-item.js", "sources": ["../../../../../../packages/components/select-v2/src/option-item.vue"], "sourcesContent": ["<template>\n  <li\n    :aria-selected=\"selected\"\n    :style=\"style\"\n    :class=\"[\n      ns.be('dropdown', 'item'),\n      ns.is('selected', selected),\n      ns.is('disabled', disabled),\n      ns.is('created', created),\n      ns.is('hovering', hovering),\n    ]\"\n    @mousemove=\"hoverItem\"\n    @click.stop=\"selectOptionClick\"\n  >\n    <slot :item=\"item\" :index=\"index\" :disabled=\"disabled\">\n      <span>{{ getLabel(item) }}</span>\n    </slot>\n  </li>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { useOption } from './useOption'\nimport { useProps } from './useProps'\nimport { optionV2Emits, optionV2Props } from './defaults'\nimport { selectV2InjectionKey } from './token'\n\nexport default defineComponent({\n  props: optionV2Props,\n  emits: optionV2Emits,\n  setup(props, { emit }) {\n    const select = inject(selectV2InjectionKey)!\n    const ns = useNamespace('select')\n    const { hoverItem, selectOptionClick } = useOption(props, { emit })\n    const { getLabel } = useProps(select.props)\n\n    return {\n      ns,\n      hoverItem,\n      selectOptionClick,\n      getLabel,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "optionV2Props", "optionV2Emits", "inject", "selectV2InjectionKey", "useNamespace", "useOption", "useProps", "_createElementBlock", "_normalizeStyle", "_normalizeClass", "_withModifiers", "_renderSlot", "_createElementVNode", "_toDisplayString", "_export_sfc"], "mappings": ";;;;;;;;;;;;AA4BA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,KAAO,EAAAC,sBAAA;AAAA,EACP,KAAO,EAAAC,sBAAA;AAAA,EACP,KAAM,CAAA,KAAA,EAAO,EAAE,IAAA,EAAQ,EAAA;AACrB,IAAM,MAAA,MAAA,GAASC,WAAOC,0BAAoB,CAAA,CAAA;AAC1C,IAAM,MAAA,EAAA,GAAKC,mBAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,EAAE,WAAW,iBAAkB,EAAA,GAAIC,oBAAU,KAAO,EAAA,EAAE,MAAM,CAAA,CAAA;AAClE,IAAA,MAAM,EAAE,QAAA,EAAa,GAAAC,iBAAA,CAAS,OAAO,KAAK,CAAA,CAAA;AAE1C,IAAO,OAAA;AAAA,MACL,EAAA;AAAA,MACA,SAAA;AAAA,MACA,iBAAA;AAAA,MACA,QAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;0BA3CCC,sBAgBK,CAAA,IAAA,EAAA;AAAA,IAfF,eAAe,EAAA,IAAA,CAAA,QAAA;AAAA,IACf,KAAA,EAAKC,mBAAE,IAAK,CAAA,KAAA,CAAA;AAAA,IACZ,KAAK,EAAAC,kBAAA,CAAA;AAAA,MAAU,QAAG,EAAE,CAAA,UAAA,EAAA,MAAA,CAAA;AAAA,MAA4B,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,UAAA,EAAa,IAAQ,CAAA,QAAA,CAAA;AAAA,MAAS,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,UAAA,EAAa,IAAQ,CAAA,QAAA,CAAA;AAAA,MAAS,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,SAAA,EAAY,IAAO,CAAA,OAAA,CAAA;AAAA,MAAS,IAAA,CAAA,EAAA,CAAG,EAAE,CAAA,UAAA,EAAa,IAAQ,CAAA,QAAA,CAAA;AAAA,KAAA,CAAA;IAOjL,WAAW,EAAA,IAAA,CAAA,SAAA;AAAA,IACX,OAAA,EAAKC,kBAAO,IAAiB,CAAA,iBAAA,EAAA,CAAA,MAAA,CAAA,CAAA;AAAA,GAAA,EAAA;IAE9BC,cAEO,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,EAAA;AAAA,MAFA,IAAM,EAAA,IAAA,CAAA,IAAA;AAAA,MAAO,KAAO,EAAA,IAAA,CAAA,KAAA;AAAA,MAAQ,QAAU,EAAA,IAAA,CAAA,QAAA;AAAA,KAAA,EAA7C,MAEO;AAAA,MADLC,sBAAA,CAAA,MAAA,EAAA,IAAA,EAAAC,mBAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAAiC,CAAA;AAAA,GAAA,EAAA,EAAA,EAAA,CAAA,eAAA,EAAA,aAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AAAA,CAAA;AAAX,iBAAA,gBAAAC,iCAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,WAAA,CAAA,EAAA,CAAA,QAAA,EAAA,iBAAA,CAAA,CAAA,CAAA;;;;"}