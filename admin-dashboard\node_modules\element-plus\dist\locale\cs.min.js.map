{"version": 3, "file": "cs.min.js", "sources": ["../../../../packages/locale/lang/cs.ts"], "sourcesContent": ["export default {\n  name: 'cs',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'OK',\n      clear: 'Vymazat',\n    },\n    datepicker: {\n      now: 'Te<PERSON>',\n      today: 'D<PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: 'Vymazat',\n      confirm: 'OK',\n      selectDate: 'Vybrat datum',\n      selectTime: 'Vybrat čas',\n      startDate: 'Datum začátku',\n      startTime: 'Čas začátku',\n      endDate: 'Datum konce',\n      endTime: 'Čas konce',\n      prevYear: 'Předchozí rok',\n      nextYear: 'P<PERSON><PERSON><PERSON>t<PERSON> rok',\n      prevMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mě<PERSON>',\n      nextMonth: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> měsíc',\n      day: 'Den',\n      week: 'Týden',\n      month: 'M<PERSON>s<PERSON>c',\n      year: 'Rok',\n      month1: 'Leden',\n      month2: 'Únor',\n      month3: 'Březen',\n      month4: '<PERSON>en',\n      month5: '<PERSON><PERSON><PERSON><PERSON>',\n      month6: '<PERSON>erve<PERSON>',\n      month7: 'Červene<PERSON>',\n      month8: 'Sr<PERSON>',\n      month9: '<PERSON><PERSON><PERSON><PERSON>',\n      month10: 'Říjen',\n      month11: 'Listopad',\n      month12: 'Prosinec',\n      weeks: {\n        sun: 'Ne',\n        mon: 'Po',\n        tue: 'Út',\n        wed: 'St',\n        thu: 'Čt',\n        fri: 'Pá',\n        sat: 'So',\n      },\n      months: {\n        jan: 'Led',\n        feb: 'Úno',\n        mar: 'Bře',\n        apr: 'Dub',\n        may: 'Kvě',\n        jun: 'Čer',\n        jul: 'Čvc',\n        aug: 'Srp',\n        sep: 'Zář',\n        oct: 'Říj',\n        nov: 'Lis',\n        dec: 'Pro',\n      },\n    },\n    select: {\n      loading: 'Načítání',\n      noMatch: 'Žádná shoda',\n      noData: 'Žádná data',\n      placeholder: 'Vybrat',\n    },\n    mention: {\n      loading: 'Načítání',\n    },\n    cascader: {\n      noMatch: 'Žádná shoda',\n      loading: 'Načítání',\n      placeholder: 'Vybrat',\n      noData: 'Žádná data',\n    },\n    pagination: {\n      goto: 'Jít na',\n      pagesize: 'na stranu',\n      total: 'Celkem {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Zpráva',\n      confirm: 'OK',\n      cancel: 'Zrušit',\n      error: 'Neplatný vstup',\n    },\n    upload: {\n      deleteTip: 'Stisknout pro smazání',\n      delete: 'Vymazat',\n      preview: 'Náhled',\n      continue: 'Pokračovat',\n    },\n    table: {\n      emptyText: 'Žádná data',\n      confirmFilter: 'Potvrdit',\n      resetFilter: 'Resetovat',\n      clearFilter: 'Vše',\n      sumText: 'Celkem',\n    },\n    tree: {\n      emptyText: 'Žádná data',\n    },\n    transfer: {\n      noMatch: 'Žádná shoda',\n      noData: 'Žádná data',\n      titles: ['Seznam 1', 'Seznam 2'],\n      filterPlaceholder: 'Klíčové slovo',\n      noCheckedFormat: '{total} položek',\n      hasCheckedFormat: '{checked}/{total} vybráno',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,iBAAiB,CAAC,SAAS,CAAC,uBAAuB,CAAC,SAAS,CAAC,0BAA0B,CAAC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,4BAA4B,CAAC,SAAS,CAAC,iCAAiC,CAAC,SAAS,CAAC,sCAAsC,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,iBAAiB,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,uBAAuB,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,OAAO,CAAC,wBAAwB,CAAC,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,6BAA6B,CAAC,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,aAAa,CAAC,UAAU,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,uBAAuB,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wBAAwB,CAAC,MAAM,CAAC,uBAAuB,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,eAAe,CAAC,sBAAsB,CAAC,gBAAgB,CAAC,8BAA8B,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;;;;;"}