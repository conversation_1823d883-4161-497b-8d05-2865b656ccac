import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/ai_file_editor_controller.dart';
import '../services/file_edit_service.dart';

/// 文件编辑预览组件
/// 显示AI建议的文件修改，并提供应用/拒绝选项
class FileEditPreviewWidget extends StatelessWidget {
  final PendingFileEdit pendingEdit;
  final VoidCallback? onApplied;
  final VoidCallback? onRejected;

  const FileEditPreviewWidget({
    Key? key,
    required this.pendingEdit,
    this.onApplied,
    this.onRejected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.all(8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部信息
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.edit_document,
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '文件编辑建议',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        pendingEdit.filePath.split('/').last,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  _formatTime(pendingEdit.timestamp),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
          
          // 编辑描述
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '编辑指令:',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  pendingEdit.instruction,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const SizedBox(height: 12),
                Text(
                  '修改描述:',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  pendingEdit.description,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ),
          
          // 文件差异预览
          _buildDiffPreview(context),
          
          // 操作按钮
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed: () => _rejectEdit(context),
                  icon: const Icon(Icons.close),
                  label: const Text('拒绝'),
                  style: TextButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.error,
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton.icon(
                  onPressed: () => _applyEdit(context),
                  icon: const Icon(Icons.check),
                  label: const Text('应用修改'),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建差异预览
  Widget _buildDiffPreview(BuildContext context) {
    final fileEditService = FileEditService();
    final differences = fileEditService.getFileDifferences(
      pendingEdit.originalContent,
      pendingEdit.modifiedContent,
    );

    if (differences.isEmpty) {
      return Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          '没有检测到文件差异',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
          ),
        ),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Theme.of(context).dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.difference,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  '文件差异 (${differences.length} 处修改)',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Container(
            constraints: const BoxConstraints(maxHeight: 200),
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: differences.length,
              itemBuilder: (context, index) {
                final diff = differences[index];
                return _buildDiffLine(context, diff);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建差异行
  Widget _buildDiffLine(BuildContext context, FileDiff diff) {
    Color backgroundColor;
    Color textColor;
    IconData icon;
    String prefix;

    switch (diff.type) {
      case DiffType.added:
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green.shade700;
        icon = Icons.add;
        prefix = '+';
        break;
      case DiffType.deleted:
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red.shade700;
        icon = Icons.remove;
        prefix = '-';
        break;
      case DiffType.modified:
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange.shade700;
        icon = Icons.edit;
        prefix = '~';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).dividerColor.withOpacity(0.3),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            child: Text(
              '${diff.lineNumber}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: textColor.withOpacity(0.7),
                fontFamily: 'monospace',
              ),
            ),
          ),
          Icon(icon, size: 16, color: textColor),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (diff.originalLine != null && diff.type != DiffType.added)
                  Text(
                    '- ${diff.originalLine}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red.shade700,
                      fontFamily: 'monospace',
                      decoration: TextDecoration.lineThrough,
                    ),
                  ),
                if (diff.modifiedLine != null && diff.type != DiffType.deleted)
                  Text(
                    '+ ${diff.modifiedLine}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.green.shade700,
                      fontFamily: 'monospace',
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 应用编辑
  void _applyEdit(BuildContext context) {
    final controller = Get.find<AIFileEditorController>();
    controller.applyPendingEdit(pendingEdit.id);
    onApplied?.call();
  }

  /// 拒绝编辑
  void _rejectEdit(BuildContext context) {
    final controller = Get.find<AIFileEditorController>();
    controller.rejectPendingEdit(pendingEdit.id);
    onRejected?.call();
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
