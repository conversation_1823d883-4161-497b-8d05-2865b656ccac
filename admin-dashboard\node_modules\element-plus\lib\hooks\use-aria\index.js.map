{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-aria/index.ts"], "sourcesContent": ["import { pick } from 'lodash-unified'\nimport { buildProps } from '@element-plus/utils'\n\nexport const ariaProps = buildProps({\n  /**\n   * @description native `aria-label` attribute\n   */\n  ariaLabel: String,\n  /**\n   * @description native `aria-orientation` attribute\n   */\n  ariaOrientation: {\n    type: String,\n    values: ['horizontal', 'vertical', 'undefined'],\n  },\n  /**\n   * @description native `aria-controls` attribute\n   */\n  ariaControls: String,\n})\n\nexport const useAriaProps = <T extends keyof typeof ariaProps>(\n  arias: Array<T>\n) => {\n  return pick<typeof ariaProps, T>(ariaProps, arias)\n}\n"], "names": ["buildProps", "pick"], "mappings": ";;;;;;;AAEY,MAAC,SAAS,GAAGA,kBAAU,CAAC;AACpC,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,WAAW,CAAC;AACnD,GAAG;AACH,EAAE,YAAY,EAAE,MAAM;AACtB,CAAC,EAAE;AACS,MAAC,YAAY,GAAG,CAAC,KAAK,KAAK;AACvC,EAAE,OAAOC,kBAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;AAChC;;;;;"}