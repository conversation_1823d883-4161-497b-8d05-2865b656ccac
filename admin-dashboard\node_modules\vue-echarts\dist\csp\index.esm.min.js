import{watch as e,isRef as t,unref as n,inject as r,computed as o,watchEffect as i,Vue2 as a,defineComponent as u,shallowRef as c,toRefs as s,getCurrentInstance as l,onMounted as v,onBeforeUnmount as f,h as p,nextTick as d}from"vue-demi";import{throttle as h,init as g}from"echarts/core";import{addListener as O,removeListener as m}from"resize-detector";var b=function(){return b=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},b.apply(this,arguments)};"function"==typeof SuppressedError&&SuppressedError;var y=["getWidth","getHeight","getDom","getOption","resize","dispatchAction","convertToPixel","convertFromPixel","containPixel","getDataURL","getConnectedDataURL","appendData","clear","isDisposed","dispose"];function j(e){return t=Object.create(null),y.forEach((function(n){t[n]=function(t){return function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(!e.value)throw new Error("ECharts is not initialized yet.");return e.value[t].apply(e.value,n)}}(n)})),t;var t}var E={autoresize:[Boolean,Object]},_=/^on[^a-z]/,z=function(e){return _.test(e)};function L(e,r){var o=t(e)?n(e):e;return o&&"object"==typeof o&&"value"in o?o.value||r:o||r}var x="ecLoadingOptions";var A={loading:Boolean,loadingOptions:Object},U="x-vue-echarts";a&&a.config.ignoredElements.push(U);var w="ecTheme",D="ecInitOptions",C="ecUpdateOptions",P=/(^&?~?!?)native:/,S=u({name:"echarts",props:b(b({option:Object,theme:{type:[Object,String]},initOptions:Object,updateOptions:Object,group:String,manualUpdate:Boolean},E),A),emits:{},inheritAttrs:!1,setup:function(t,n){var a=n.attrs,u=c(),p=c(),y=c(),E=c(),_=r(w,null),A=r(D,null),U=r(C,null),S=s(t),k=S.autoresize,B=S.manualUpdate,R=S.loading,T=S.loadingOptions,$=o((function(){return E.value||t.option||null})),F=o((function(){return t.theme||L(_,{})})),H=o((function(){return t.initOptions||L(A,{})})),I=o((function(){return t.updateOptions||L(U,{})})),M=o((function(){return function(e){var t={};for(var n in e)z(n)||(t[n]=e[n]);return t}(a)})),W={},Z=l().proxy.$listeners,q={};function G(e){if(p.value){var n=y.value=g(p.value,F.value,H.value);t.group&&(n.group=t.group),Object.keys(q).forEach((function(e){var t=q[e];if(t){var r=e.toLowerCase();"~"===r.charAt(0)&&(r=r.substring(1),t.__once__=!0);var o=n;if(0===r.indexOf("zr:")&&(o=n.getZr(),r=r.substring(3)),t.__once__){delete t.__once__;var i=t;t=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];i.apply(void 0,e),o.off(r,t)}}o.on(r,t)}})),k.value?d((function(){n&&!n.isDisposed()&&n.resize(),r()})):r()}function r(){var t=e||$.value;t&&n.setOption(t,I.value)}}function J(){y.value&&(y.value.dispose(),y.value=void 0)}Z?Object.keys(Z).forEach((function(e){P.test(e)?W[e.replace(P,"$1")]=Z[e]:q[e]=Z[e]})):Object.keys(a).filter((function(e){return z(e)})).forEach((function(e){var t=e.charAt(2).toLowerCase()+e.slice(3);if(0!==t.indexOf("native:"))"Once"===t.substring(t.length-4)&&(t="~".concat(t.substring(0,t.length-4))),q[t]=a[e];else{var n="on".concat(t.charAt(7).toUpperCase()).concat(t.slice(8));W[n]=a[e]}}));var K=null;e(B,(function(n){"function"==typeof K&&(K(),K=null),n||(K=e((function(){return t.option}),(function(e,t){e&&(y.value?y.value.setOption(e,b({notMerge:e!==t},I.value)):G())}),{deep:!0}))}),{immediate:!0}),e([F,H],(function(){J(),G()}),{deep:!0}),i((function(){t.group&&y.value&&(y.value.group=t.group)}));var N=j(y);return function(e,t,n){var a=r(x,{}),u=o((function(){return b(b({},L(a,{})),null==n?void 0:n.value)}));i((function(){var n=e.value;n&&(t.value?n.showLoading(u.value):n.hideLoading())}))}(y,R,T),function(t,n,r){var o=null;e([r,t,n],(function(e,t,n){var r=e[0],i=e[1],a=e[2];if(r&&i&&a){var u=!0===a?{}:a,c=u.throttle,s=void 0===c?100:c,l=u.onResize,v=function(){i.resize(),null==l||l()};o=s?h(v,s):v,O(r,o)}n((function(){r&&o&&m(r,o)}))}))}(y,k,p),v((function(){G()})),f((function(){J()})),b({chart:y,root:u,inner:p,setOption:function(e,n){t.manualUpdate&&(E.value=e),y.value?y.value.setOption(e,n||{}):G(e)},nonEventAttrs:M,nativeListeners:W},N)},render:function(){var e=a?{attrs:this.nonEventAttrs,on:this.nativeListeners}:b(b({},this.nonEventAttrs),this.nativeListeners);return e.ref="root",e.class=e.class?["echarts"].concat(e.class):"echarts",p(U,e,[p("div",{ref:"inner",class:"vue-echarts-inner"})])}});export{D as INIT_OPTIONS_KEY,x as LOADING_OPTIONS_KEY,w as THEME_KEY,C as UPDATE_OPTIONS_KEY,S as default};
//# sourceMappingURL=index.esm.min.js.map
