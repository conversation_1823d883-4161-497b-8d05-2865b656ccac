{"name": "@cloudbase/node-sdk", "version": "3.10.1", "description": "tencent cloud base server sdk for node.js", "main": "dist/index.js", "typings": "types/index.d.ts", "keywords": ["node sdk"], "homepage": "https://github.com/TencentCloudBase/node-sdk#readme", "repository": {"type": "git", "url": "https://github.com/TencentCloudBase/node-sdk"}, "bugs": {"url": "https://github.com/TencentCloudBase/node-sdk/issues"}, "author": "lukejyhuang", "license": "MIT", "engines": {"node": ">=12"}, "files": ["src/", "dist/", "types/", "bin/", "LICENSE", "README.md", "CHANGELOG.md"], "scripts": {"eslint": "npx eslint \"./**/*.ts\"", "eslint-fix": "npx eslint --fix \"./**/*.ts\"", "build": "rm -rf dist/* && npm run tsc", "tsc": "npx tsc -p tsconfig.json", "tsc:w": "npx tsc -p tsconfig.json -w", "test": "npx jest --detect<PERSON>pen<PERSON>andles --verbose --coverage --runInBand", "coverage": "npx jest --detect<PERSON><PERSON>Handles --coverage", "coveralls": "cat ./coverage/lcov.info | coveralls", "prepare": "husky install"}, "dependencies": {"@cloudbase/database": "1.4.2", "@cloudbase/signature-nodejs": "2.0.0", "@cloudbase/wx-cloud-client-sdk": "1.6.1", "agentkeepalive": "^4.3.0", "axios": "0.27.2", "form-data": "^4.0.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.1", "jsonwebtoken": "^9.0.2", "retry": "^0.13.1", "xml2js": "^0.6.2"}, "devDependencies": {"@cloudbase/adapter-interface": "^0.7.0", "@types/axios": "^0.14.0", "@types/jest": "^29.5.1", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^12.12.6", "@types/retry": "^0.12.2", "@typescript-eslint/eslint-plugin": "^6.4.0", "@typescript-eslint/parser": "^6.10.0", "babel-eslint": "^10.0.3", "coveralls": "^3.1.1", "eslint": "^8.38.0", "eslint-config-standard-with-typescript": "^39.1.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^16.2.0", "eslint-plugin-promise": "^6.0.0", "husky": "^8.0.3", "jest": "^29.5.0", "power-assert": "^1.6.1", "ts-jest": "^29.1.0", "typescript": "^5.0.4"}}