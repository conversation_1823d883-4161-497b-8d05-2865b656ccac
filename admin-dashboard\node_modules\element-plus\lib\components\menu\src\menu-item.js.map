{"version": 3, "file": "menu-item.js", "sources": ["../../../../../../packages/components/menu/src/menu-item.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isString,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { RouteLocationRaw } from 'vue-router'\nimport type { MenuItemRegistered } from './types'\n\nexport const menuItemProps = buildProps({\n  /**\n   * @description unique identification\n   */\n  index: {\n    type: definePropType<string | null>([String, null]),\n    // will be required in the next major version\n    // required: true,\n    default: null,\n  },\n  /**\n   * @description Vue Router object\n   */\n  route: {\n    type: definePropType<RouteLocationRaw>([String, Object]),\n  },\n  /**\n   * @description whether disabled\n   */\n  disabled: Boolean,\n} as const)\nexport type MenuItemProps = ExtractPropTypes<typeof menuItemProps>\nexport type MenuItemPropsPublic = __ExtractPublicPropTypes<typeof menuItemProps>\n\nexport const menuItemEmits = {\n  click: (item: MenuItemRegistered) =>\n    isString(item.index) && isArray(item.indexPath),\n}\nexport type MenuItemEmits = typeof menuItemEmits\n"], "names": ["buildProps", "definePropType", "isString", "isArray"], "mappings": ";;;;;;;AAMY,MAAC,aAAa,GAAGA,kBAAU,CAAC;AACxC,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACxC,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,KAAK,EAAE,CAAC,IAAI,KAAKC,eAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAIC,cAAO,CAAC,IAAI,CAAC,SAAS,CAAC;AAClE;;;;;"}