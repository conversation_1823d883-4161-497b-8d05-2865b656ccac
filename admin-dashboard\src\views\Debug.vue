<template>
  <div class="debug-container">
    <h1>🔧 调试页面</h1>
    
    <div class="debug-section">
      <h2>认证状态</h2>
      <p><strong>Token:</strong> {{ token ? '已存在' : '未找到' }}</p>
      <p><strong>Token内容:</strong> {{ token ? token.substring(0, 50) + '...' : '无' }}</p>
      <p><strong>用户信息:</strong> {{ userInfo }}</p>
    </div>
    
    <div class="debug-section">
      <h2>API测试</h2>
      <el-button @click="testUsersAPI" :loading="testing">测试用户列表API</el-button>
      <el-button @click="testLogin" :loading="loginTesting">测试管理员登录</el-button>
      <el-button @click="clearStorage">清空本地存储</el-button>
    </div>
    
    <div class="debug-section" v-if="apiResult">
      <h2>API响应</h2>
      <pre>{{ apiResult }}</pre>
    </div>
    
    <div class="debug-section">
      <h2>用户列表数据</h2>
      <p><strong>加载状态:</strong> {{ loading ? '加载中' : '已完成' }}</p>
      <p><strong>用户数量:</strong> {{ userList.length }}</p>
      <div v-if="userList.length > 0">
        <h3>前5个用户:</h3>
        <ul>
          <li v-for="user in userList.slice(0, 5)" :key="user.id">
            {{ user.username || '无用户名' }} - {{ user.phoneNumber }} - {{ user.isMember ? '会员' : '普通' }}
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElButton } from 'element-plus'

const token = ref(localStorage.getItem('admin_token'))
const userInfo = ref(localStorage.getItem('admin_user'))
const testing = ref(false)
const loginTesting = ref(false)
const apiResult = ref('')
const loading = ref(false)
const userList = ref([])

const testUsersAPI = async () => {
  testing.value = true
  try {
    const response = await fetch('/api/users?page=1&size=10', {
      headers: {
        'Authorization': `Bearer ${token.value}`
      }
    })
    
    const result = await response.json()
    apiResult.value = JSON.stringify(result, null, 2)
    
    if (result.success && result.data.users) {
      userList.value = result.data.users
      ElMessage.success(`获取到 ${result.data.users.length} 个用户`)
    } else {
      ElMessage.error('获取用户列表失败')
    }
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
    ElMessage.error('API请求失败')
  } finally {
    testing.value = false
  }
}

const testLogin = async () => {
  loginTesting.value = true
  try {
    const response = await fetch('/api/admin/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: 'admin',
        password: 'admin123'
      })
    })
    
    const result = await response.json()
    apiResult.value = JSON.stringify(result, null, 2)
    
    if (result.success && result.data.token) {
      localStorage.setItem('admin_token', result.data.token)
      localStorage.setItem('admin_user', JSON.stringify(result.data.user))
      token.value = result.data.token
      userInfo.value = JSON.stringify(result.data.user)
      ElMessage.success('登录成功')
    } else {
      ElMessage.error('登录失败')
    }
  } catch (error) {
    apiResult.value = `错误: ${error.message}`
    ElMessage.error('登录请求失败')
  } finally {
    loginTesting.value = false
  }
}

const clearStorage = () => {
  localStorage.removeItem('admin_token')
  localStorage.removeItem('admin_user')
  token.value = null
  userInfo.value = null
  userList.value = []
  apiResult.value = ''
  ElMessage.success('本地存储已清空')
}

onMounted(() => {
  // 页面加载时自动测试用户API
  if (token.value) {
    testUsersAPI()
  }
})
</script>

<style scoped>
.debug-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.debug-section {
  background: #f5f5f5;
  padding: 20px;
  margin: 20px 0;
  border-radius: 8px;
  border-left: 4px solid #007bff;
}

.debug-section h2 {
  margin-top: 0;
  color: #333;
}

pre {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
  overflow-x: auto;
  max-height: 400px;
}

ul {
  background: #fff;
  padding: 15px;
  border-radius: 4px;
}
</style>
