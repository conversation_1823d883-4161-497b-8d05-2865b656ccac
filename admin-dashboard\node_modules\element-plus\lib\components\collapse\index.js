'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var collapse$1 = require('./src/collapse2.js');
var collapseItem$1 = require('./src/collapse-item2.js');
var collapse = require('./src/collapse.js');
var collapseItem = require('./src/collapse-item.js');
var constants = require('./src/constants.js');
var install = require('../../utils/vue/install.js');

const ElCollapse = install.withInstall(collapse$1["default"], {
  CollapseItem: collapseItem$1["default"]
});
const ElCollapseItem = install.withNoopInstall(collapseItem$1["default"]);

exports.collapseEmits = collapse.collapseEmits;
exports.collapseProps = collapse.collapseProps;
exports.emitChangeFn = collapse.emitChangeFn;
exports.collapseItemProps = collapseItem.collapseItemProps;
exports.collapseContextKey = constants.collapseContextKey;
exports.ElCollapse = ElCollapse;
exports.ElCollapseItem = ElCollapseItem;
exports["default"] = ElCollapse;
//# sourceMappingURL=index.js.map
