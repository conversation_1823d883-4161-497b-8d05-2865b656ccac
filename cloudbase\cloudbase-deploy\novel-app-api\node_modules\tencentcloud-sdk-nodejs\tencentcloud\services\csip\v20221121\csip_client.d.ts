import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { DescribeRiskCenterPortViewPortRiskListRequest, DescribeDbAssetInfoResponse, DeleteRiskScanTaskRequest, DescribeRiskCenterAssetViewWeakPasswordRiskListResponse, DescribeVULRiskAdvanceCFGListResponse, DescribeAssetViewVulRiskListResponse, DescribeRepositoryImageAssetsRequest, DescribeOrganizationUserInfoRequest, ModifyUebaRuleSwitchResponse, CreateAccessKeySyncTaskRequest, AddNewBindRoleUserRequest, DescribeCheckViewRisksRequest, DescribeCallRecordResponse, DescribeSubUserInfoRequest, DescribeRiskCenterAssetViewCFGRiskListResponse, DescribeClusterPodAssetsResponse, DescribeRepositoryImageAssetsResponse, DescribeCheckViewRisksResponse, DescribeRiskCallRecordResponse, DescribeScanReportListRequest, DescribeClusterAssetsRequest, DescribeSubnetAssetsRequest, DescribeTopAttackInfoResponse, DescribeVULRiskDetailResponse, DescribeDomainAssetsRequest, ModifyRiskCenterScanTaskResponse, DescribeNICAssetsRequest, DescribeRiskCenterWebsiteRiskListResponse, DescribeRiskCenterCFGViewCFGRiskListRequest, DescribeAssetProcessListRequest, DescribeAssetProcessListResponse, DescribeAbnormalCallRecordRequest, UpdateAccessKeyAlarmStatusRequest, DescribeCSIPRiskStatisticsResponse, DescribeAccessKeyUserListRequest, DeleteDomainAndIpRequest, DescribeRiskDetailListRequest, DescribeRiskRuleDetailRequest, DescribeRiskCenterPortViewPortRiskListResponse, CreateAccessKeyCheckTaskResponse, DescribeOrganizationInfoRequest, DescribeUebaRuleResponse, CreateAccessKeySyncTaskResponse, DescribeSourceIPAssetResponse, DescribeScanReportListResponse, DescribeSearchBugInfoRequest, DescribeOrganizationUserInfoResponse, DescribeVULRiskDetailRequest, DescribeListenerListResponse, DescribeAccessKeyAssetResponse, DescribeAccessKeyRiskDetailResponse, CreateRiskCenterScanTaskResponse, DescribeSubnetAssetsResponse, DescribeRiskCenterAssetViewPortRiskListRequest, StopRiskCenterTaskResponse, DescribeOrganizationInfoResponse, DescribeVpcAssetsRequest, ModifyRiskCenterRiskStatusRequest, DescribeExposePathRequest, DescribeAccessKeyRiskRequest, DescribeRiskCenterAssetViewWeakPasswordRiskListRequest, DescribeUserCallRecordRequest, DescribeTopAttackInfoRequest, DescribeCVMAssetsResponse, UpdateAccessKeyRemarkResponse, DescribePublicIpAssetsRequest, DescribeCVMAssetInfoRequest, UpdateAccessKeyAlarmStatusResponse, CreateRiskCenterScanTaskRequest, UpdateAlertStatusListResponse, DescribeAssetRiskListResponse, DescribeRiskCenterVULViewVULRiskListResponse, DescribeAccessKeyAssetRequest, DescribeRiskCenterVULViewVULRiskListRequest, UpdateAlertStatusListRequest, StopRiskCenterTaskRequest, DescribeScanTaskListResponse, DescribeScanTaskListRequest, DescribeAccessKeyAlarmDetailRequest, ModifyRiskCenterRiskStatusResponse, DescribeClusterPodAssetsRequest, DescribeCVMAssetInfoResponse, DescribePublicIpAssetsResponse, DescribeVulViewVulRiskListRequest, DescribeAssetRiskListRequest, DescribeRiskRulesRequest, DescribeTaskLogURLResponse, DescribeSourceIPAssetRequest, DescribeVulRiskListRequest, DescribeAccessKeyRiskResponse, DescribeDbAssetInfoRequest, DescribeAssetViewVulRiskListRequest, DescribeUebaRuleRequest, DescribeVulViewVulRiskListResponse, CreateAccessKeyCheckTaskRequest, DescribeRiskCenterWebsiteRiskListRequest, DescribeCallRecordRequest, DescribeAccessKeyAlarmResponse, DescribeCFWAssetStatisticsResponse, DescribeGatewayAssetsResponse, DescribeRiskCenterAssetViewCFGRiskListRequest, DescribeRiskRuleDetailResponse, DescribeAccessKeyUserDetailRequest, DescribeVpcAssetsResponse, DescribeHighBaseLineRiskListResponse, DeleteDomainAndIpResponse, DescribeExposeAssetCategoryResponse, AddNewBindRoleUserResponse, DescribeRiskRulesResponse, DescribeAlertListResponse, ModifyOrganizationAccountStatusResponse, DescribeRiskDetailListResponse, DescribeExposePathResponse, DescribeDbAssetsRequest, DescribeAccessKeyAlarmRequest, DescribeRiskCenterAssetViewVULRiskListRequest, DescribeExposuresRequest, DescribeAlertListRequest, CreateDomainAndIpRequest, DescribeRiskCenterCFGViewCFGRiskListResponse, DescribeAccessKeyUserDetailResponse, DescribeSearchBugInfoResponse, DescribeAccessKeyRiskDetailRequest, ModifyOrganizationAccountStatusRequest, DescribeClusterAssetsResponse, DescribeVULListResponse, DescribeExposeAssetCategoryRequest, DescribeAbnormalCallRecordResponse, DescribeSubUserInfoResponse, DescribeVulRiskListResponse, DescribeCSIPRiskStatisticsRequest, DescribeScanStatisticResponse, DescribeDomainAssetsResponse, DescribeExposuresResponse, DescribeNICAssetsResponse, DescribeRiskCenterAssetViewVULRiskListResponse, DescribeCVMAssetsRequest, DescribeRiskCenterServerRiskListRequest, DescribeUserCallRecordResponse, DescribeTaskLogListRequest, DescribeAccessKeyAlarmDetailResponse, DescribeOtherCloudAssetsRequest, ModifyRiskCenterScanTaskRequest, DescribeRiskCenterServerRiskListResponse, DescribeListenerListRequest, ModifyUebaRuleSwitchRequest, DescribeVULListRequest, DeleteRiskScanTaskResponse, DescribeScanStatisticRequest, DescribeGatewayAssetsRequest, DescribeCFWAssetStatisticsRequest, DescribeAccessKeyUserListResponse, DescribeRiskCallRecordRequest, DescribeOtherCloudAssetsResponse, CreateDomainAndIpResponse, UpdateAccessKeyRemarkRequest, DescribeTaskLogListResponse, DescribeDbAssetsResponse, DescribeRiskCenterAssetViewPortRiskListResponse, DescribeVULRiskAdvanceCFGListRequest, DescribeHighBaseLineRiskListRequest, DescribeTaskLogURLRequest } from "./csip_models";
/**
 * csip client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 查询用户行为分析策略列表
     */
    DescribeUebaRule(req: DescribeUebaRuleRequest, cb?: (error: string, rep: DescribeUebaRuleResponse) => void): Promise<DescribeUebaRuleResponse>;
    /**
     * 获取任务扫描报告列表
     */
    DescribeTaskLogList(req: DescribeTaskLogListRequest, cb?: (error: string, rep: DescribeTaskLogListResponse) => void): Promise<DescribeTaskLogListResponse>;
    /**
     * 删除域名和ip请求
     */
    DeleteDomainAndIp(req: DeleteDomainAndIpRequest, cb?: (error: string, rep: DeleteDomainAndIpResponse) => void): Promise<DeleteDomainAndIpResponse>;
    /**
     * 查询云边界分析-暴露路径下主机节点的漏洞列表
     */
    DescribeVulRiskList(req: DescribeVulRiskListRequest, cb?: (error: string, rep: DescribeVulRiskListResponse) => void): Promise<DescribeVulRiskListResponse>;
    /**
     * 查询风险规则详情示例
     */
    DescribeRiskRuleDetail(req: DescribeRiskRuleDetailRequest, cb?: (error: string, rep: DescribeRiskRuleDetailResponse) => void): Promise<DescribeRiskRuleDetailResponse>;
    /**
     * db资产详情
     */
    DescribeDbAssetInfo(req: DescribeDbAssetInfoRequest, cb?: (error: string, rep: DescribeDbAssetInfoResponse) => void): Promise<DescribeDbAssetInfoResponse>;
    /**
     * 获取网卡列表
     */
    DescribeNICAssets(req: DescribeNICAssetsRequest, cb?: (error: string, rep: DescribeNICAssetsResponse) => void): Promise<DescribeNICAssetsResponse>;
    /**
     * 获取调用记录列表
     */
    DescribeAbnormalCallRecord(req: DescribeAbnormalCallRecordRequest, cb?: (error: string, rep: DescribeAbnormalCallRecordResponse) => void): Promise<DescribeAbnormalCallRecordResponse>;
    /**
     * 获取漏洞视角的漏洞风险列表
     */
    DescribeRiskCenterVULViewVULRiskList(req: DescribeRiskCenterVULViewVULRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterVULViewVULRiskListResponse) => void): Promise<DescribeRiskCenterVULViewVULRiskListResponse>;
    /**
     * 域名列表
     */
    DescribeDomainAssets(req: DescribeDomainAssetsRequest, cb?: (error: string, rep: DescribeDomainAssetsResponse) => void): Promise<DescribeDomainAssetsResponse>;
    /**
     * 编辑访问密钥/源IP备注
     */
    UpdateAccessKeyRemark(req: UpdateAccessKeyRemarkRequest, cb?: (error: string, rep: UpdateAccessKeyRemarkResponse) => void): Promise<UpdateAccessKeyRemarkResponse>;
    /**
     * ip公网列表
     */
    DescribePublicIpAssets(req: DescribePublicIpAssetsRequest, cb?: (error: string, rep: DescribePublicIpAssetsResponse) => void): Promise<DescribePublicIpAssetsResponse>;
    /**
     * csip角色授权绑定接口
     */
    AddNewBindRoleUser(req?: AddNewBindRoleUserRequest, cb?: (error: string, rep: AddNewBindRoleUserResponse) => void): Promise<AddNewBindRoleUserResponse>;
    /**
     * 新安全中心风险中心-漏洞列表
     */
    DescribeVULList(req: DescribeVULListRequest, cb?: (error: string, rep: DescribeVULListResponse) => void): Promise<DescribeVULListResponse>;
    /**
     * 获取调用记录列表
     */
    DescribeCallRecord(req: DescribeCallRecordRequest, cb?: (error: string, rep: DescribeCallRecordResponse) => void): Promise<DescribeCallRecordResponse>;
    /**
     * 查询用户的账号列表
     */
    DescribeAccessKeyUserList(req: DescribeAccessKeyUserListRequest, cb?: (error: string, rep: DescribeAccessKeyUserListResponse) => void): Promise<DescribeAccessKeyUserListResponse>;
    /**
     * 查询clb监听器列表
     */
    DescribeListenerList(req: DescribeListenerListRequest, cb?: (error: string, rep: DescribeListenerListResponse) => void): Promise<DescribeListenerListResponse>;
    /**
     * 停止扫风险中心扫描任务
     */
    StopRiskCenterTask(req: StopRiskCenterTaskRequest, cb?: (error: string, rep: StopRiskCenterTaskResponse) => void): Promise<StopRiskCenterTaskResponse>;
    /**
     * 查询用户的账号详情
     */
    DescribeAccessKeyUserDetail(req: DescribeAccessKeyUserDetailRequest, cb?: (error: string, rep: DescribeAccessKeyUserDetailResponse) => void): Promise<DescribeAccessKeyUserDetailResponse>;
    /**
     * 风险详情列表示例
     */
    DescribeRiskDetailList(req: DescribeRiskDetailListRequest, cb?: (error: string, rep: DescribeRiskDetailListResponse) => void): Promise<DescribeRiskDetailListResponse>;
    /**
     * 删除风险中心扫描任务
     */
    DeleteRiskScanTask(req: DeleteRiskScanTaskRequest, cb?: (error: string, rep: DeleteRiskScanTaskResponse) => void): Promise<DeleteRiskScanTaskResponse>;
    /**
     * 获取资产视角的弱口令风险列表
     */
    DescribeRiskCenterAssetViewWeakPasswordRiskList(req: DescribeRiskCenterAssetViewWeakPasswordRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterAssetViewWeakPasswordRiskListResponse) => void): Promise<DescribeRiskCenterAssetViewWeakPasswordRiskListResponse>;
    /**
     * 获取资产视角的漏洞风险列表
     */
    DescribeRiskCenterAssetViewVULRiskList(req: DescribeRiskCenterAssetViewVULRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterAssetViewVULRiskListResponse) => void): Promise<DescribeRiskCenterAssetViewVULRiskListResponse>;
    /**
     * 集群pod列表
     */
    DescribeClusterPodAssets(req: DescribeClusterPodAssetsRequest, cb?: (error: string, rep: DescribeClusterPodAssetsResponse) => void): Promise<DescribeClusterPodAssetsResponse>;
    /**
     * 资产列表
     */
    DescribeOtherCloudAssets(req: DescribeOtherCloudAssetsRequest, cb?: (error: string, rep: DescribeOtherCloudAssetsResponse) => void): Promise<DescribeOtherCloudAssetsResponse>;
    /**
     * 获取资产视角的配置风险列表
     */
    DescribeRiskCenterAssetViewCFGRiskList(req: DescribeRiskCenterAssetViewCFGRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterAssetViewCFGRiskListResponse) => void): Promise<DescribeRiskCenterAssetViewCFGRiskListResponse>;
    /**
     * 获取vpc列表
     */
    DescribeVpcAssets(req: DescribeVpcAssetsRequest, cb?: (error: string, rep: DescribeVpcAssetsResponse) => void): Promise<DescribeVpcAssetsResponse>;
    /**
     * 获取用户访问密钥资产列表（源IP视角）
     */
    DescribeSourceIPAsset(req: DescribeSourceIPAssetRequest, cb?: (error: string, rep: DescribeSourceIPAssetResponse) => void): Promise<DescribeSourceIPAssetResponse>;
    /**
     * 获取风险服务列表
     */
    DescribeRiskCenterServerRiskList(req: DescribeRiskCenterServerRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterServerRiskListResponse) => void): Promise<DescribeRiskCenterServerRiskListResponse>;
    /**
     * 获取cvm列表
     */
    DescribeCVMAssets(req: DescribeCVMAssetsRequest, cb?: (error: string, rep: DescribeCVMAssetsResponse) => void): Promise<DescribeCVMAssetsResponse>;
    /**
     * 修改风险中心扫描任务
     */
    ModifyRiskCenterScanTask(req: ModifyRiskCenterScanTaskRequest, cb?: (error: string, rep: ModifyRiskCenterScanTaskResponse) => void): Promise<ModifyRiskCenterScanTaskResponse>;
    /**
     * 检测AK 异步任务
     */
    CreateAccessKeyCheckTask(req: CreateAccessKeyCheckTaskRequest, cb?: (error: string, rep: CreateAccessKeyCheckTaskResponse) => void): Promise<CreateAccessKeyCheckTaskResponse>;
    /**
     * 获取资产视角的漏洞风险列表
     */
    DescribeAssetViewVulRiskList(req: DescribeAssetViewVulRiskListRequest, cb?: (error: string, rep: DescribeAssetViewVulRiskListResponse) => void): Promise<DescribeAssetViewVulRiskListResponse>;
    /**
     * 获取报告下载的临时链接
     */
    DescribeTaskLogURL(req: DescribeTaskLogURLRequest, cb?: (error: string, rep: DescribeTaskLogURLResponse) => void): Promise<DescribeTaskLogURLResponse>;
    /**
     * 检查视角下云资源配置风险列表示例
     */
    DescribeCheckViewRisks(req: DescribeCheckViewRisksRequest, cb?: (error: string, rep: DescribeCheckViewRisksResponse) => void): Promise<DescribeCheckViewRisksResponse>;
    /**
     * 获取内容风险列表
     */
    DescribeRiskCenterWebsiteRiskList(req: DescribeRiskCenterWebsiteRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterWebsiteRiskListResponse) => void): Promise<DescribeRiskCenterWebsiteRiskListResponse>;
    /**
     * 云防资产中心统计数据
     */
    DescribeCFWAssetStatistics(req?: DescribeCFWAssetStatisticsRequest, cb?: (error: string, rep: DescribeCFWAssetStatisticsResponse) => void): Promise<DescribeCFWAssetStatisticsResponse>;
    /**
     * 高级配置风险规则列表示例
     */
    DescribeRiskRules(req: DescribeRiskRulesRequest, cb?: (error: string, rep: DescribeRiskRulesResponse) => void): Promise<DescribeRiskRulesResponse>;
    /**
     * 资产视角下云资源配置风险列表
     */
    DescribeAssetRiskList(req: DescribeAssetRiskListRequest, cb?: (error: string, rep: DescribeAssetRiskListResponse) => void): Promise<DescribeAssetRiskListResponse>;
    /**
     * 仓库镜像列表
     */
    DescribeRepositoryImageAssets(req: DescribeRepositoryImageAssetsRequest, cb?: (error: string, rep: DescribeRepositoryImageAssetsResponse) => void): Promise<DescribeRepositoryImageAssetsResponse>;
    /**
     * 创建风险中心扫描任务
     */
    CreateRiskCenterScanTask(req: CreateRiskCenterScanTaskRequest, cb?: (error: string, rep: CreateRiskCenterScanTaskResponse) => void): Promise<CreateRiskCenterScanTaskResponse>;
    /**
     * 查询云边界分析路径节点
     */
    DescribeExposePath(req: DescribeExposePathRequest, cb?: (error: string, rep: DescribeExposePathResponse) => void): Promise<DescribeExposePathResponse>;
    /**
     * 获取网关列表
     */
    DescribeGatewayAssets(req: DescribeGatewayAssetsRequest, cb?: (error: string, rep: DescribeGatewayAssetsResponse) => void): Promise<DescribeGatewayAssetsResponse>;
    /**
     * 查询TOP攻击信息
     */
    DescribeTopAttackInfo(req: DescribeTopAttackInfoRequest, cb?: (error: string, rep: DescribeTopAttackInfoResponse) => void): Promise<DescribeTopAttackInfoResponse>;
    /**
     * 立体防护中心查询漏洞信息
     */
    DescribeSearchBugInfo(req: DescribeSearchBugInfoRequest, cb?: (error: string, rep: DescribeSearchBugInfoResponse) => void): Promise<DescribeSearchBugInfoResponse>;
    /**
     * 获取漏洞展开详情
     */
    DescribeVULRiskDetail(req: DescribeVULRiskDetailRequest, cb?: (error: string, rep: DescribeVULRiskDetailResponse) => void): Promise<DescribeVULRiskDetailResponse>;
    /**
     * 访问密钥告警记录列表
     */
    DescribeAccessKeyAlarm(req: DescribeAccessKeyAlarmRequest, cb?: (error: string, rep: DescribeAccessKeyAlarmResponse) => void): Promise<DescribeAccessKeyAlarmResponse>;
    /**
     * 获取漏洞视角的漏洞风险列表
     */
    DescribeVulViewVulRiskList(req: DescribeVulViewVulRiskListRequest, cb?: (error: string, rep: DescribeVulViewVulRiskListResponse) => void): Promise<DescribeVulViewVulRiskListResponse>;
    /**
     * 获取账号调用记录列表
     */
    DescribeUserCallRecord(req: DescribeUserCallRecordRequest, cb?: (error: string, rep: DescribeUserCallRecordResponse) => void): Promise<DescribeUserCallRecordResponse>;
    /**
     * 查询云边界分析-暴露路径下主机节点的高危基线风险列表
     */
    DescribeHighBaseLineRiskList(req: DescribeHighBaseLineRiskListRequest, cb?: (error: string, rep: DescribeHighBaseLineRiskListResponse) => void): Promise<DescribeHighBaseLineRiskListResponse>;
    /**
     * 获取扫描报告列表
     */
    DescribeScanReportList(req: DescribeScanReportListRequest, cb?: (error: string, rep: DescribeScanReportListResponse) => void): Promise<DescribeScanReportListResponse>;
    /**
     * 创建域名、ip相关信息
     */
    CreateDomainAndIp(req: CreateDomainAndIpRequest, cb?: (error: string, rep: CreateDomainAndIpResponse) => void): Promise<CreateDomainAndIpResponse>;
    /**
     * 告警中心全量告警列表接口
     */
    DescribeAlertList(req: DescribeAlertListRequest, cb?: (error: string, rep: DescribeAlertListResponse) => void): Promise<DescribeAlertListResponse>;
    /**
     * 获取扫描任务列表
     */
    DescribeScanTaskList(req: DescribeScanTaskListRequest, cb?: (error: string, rep: DescribeScanTaskListResponse) => void): Promise<DescribeScanTaskListResponse>;
    /**
     * 标记风险或者告警为 已处置/已忽略
     */
    UpdateAccessKeyAlarmStatus(req: UpdateAccessKeyAlarmStatusRequest, cb?: (error: string, rep: UpdateAccessKeyAlarmStatusResponse) => void): Promise<UpdateAccessKeyAlarmStatusResponse>;
    /**
     * 更新自定义策略的开关
     */
    ModifyUebaRuleSwitch(req: ModifyUebaRuleSwitchRequest, cb?: (error: string, rep: ModifyUebaRuleSwitchResponse) => void): Promise<ModifyUebaRuleSwitchResponse>;
    /**
     * 批量告警状态处理接口
     */
    UpdateAlertStatusList(req: UpdateAlertStatusListRequest, cb?: (error: string, rep: UpdateAlertStatusListResponse) => void): Promise<UpdateAlertStatusListResponse>;
    /**
     * 查询云边界分析扫描结果统计信息
     */
    DescribeScanStatistic(req: DescribeScanStatisticRequest, cb?: (error: string, rep: DescribeScanStatisticResponse) => void): Promise<DescribeScanStatisticResponse>;
    /**
     * 云边界分析资产分类
     */
    DescribeExposeAssetCategory(req: DescribeExposeAssetCategoryRequest, cb?: (error: string, rep: DescribeExposeAssetCategoryResponse) => void): Promise<DescribeExposeAssetCategoryResponse>;
    /**
     * 查询集团账号用户列表
     */
    DescribeOrganizationUserInfo(req: DescribeOrganizationUserInfoRequest, cb?: (error: string, rep: DescribeOrganizationUserInfoResponse) => void): Promise<DescribeOrganizationUserInfoResponse>;
    /**
     * 查询云边界分析-暴露路径下主机节点的进程列表
     */
    DescribeAssetProcessList(req: DescribeAssetProcessListRequest, cb?: (error: string, rep: DescribeAssetProcessListResponse) => void): Promise<DescribeAssetProcessListResponse>;
    /**
     * 发起AK资产同步任务
     */
    CreateAccessKeySyncTask(req: CreateAccessKeySyncTaskRequest, cb?: (error: string, rep: CreateAccessKeySyncTaskResponse) => void): Promise<CreateAccessKeySyncTaskResponse>;
    /**
     * 获取端口视角的端口风险列表
     */
    DescribeRiskCenterPortViewPortRiskList(req: DescribeRiskCenterPortViewPortRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterPortViewPortRiskListResponse) => void): Promise<DescribeRiskCenterPortViewPortRiskListResponse>;
    /**
     * 查询集团的子账号列表
     */
    DescribeSubUserInfo(req: DescribeSubUserInfoRequest, cb?: (error: string, rep: DescribeSubUserInfoResponse) => void): Promise<DescribeSubUserInfoResponse>;
    /**
     * 获取资产视角的端口风险列表
     */
    DescribeRiskCenterAssetViewPortRiskList(req: DescribeRiskCenterAssetViewPortRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterAssetViewPortRiskListResponse) => void): Promise<DescribeRiskCenterAssetViewPortRiskListResponse>;
    /**
     * 查询漏洞风险高级配置
     */
    DescribeVULRiskAdvanceCFGList(req: DescribeVULRiskAdvanceCFGListRequest, cb?: (error: string, rep: DescribeVULRiskAdvanceCFGListResponse) => void): Promise<DescribeVULRiskAdvanceCFGListResponse>;
    /**
     * 查询集团账号详情
     */
    DescribeOrganizationInfo(req: DescribeOrganizationInfoRequest, cb?: (error: string, rep: DescribeOrganizationInfoResponse) => void): Promise<DescribeOrganizationInfoResponse>;
    /**
     * 云边界分析资产列表
     */
    DescribeExposures(req: DescribeExposuresRequest, cb?: (error: string, rep: DescribeExposuresResponse) => void): Promise<DescribeExposuresResponse>;
    /**
     * 修改风险中心风险状态
     */
    ModifyRiskCenterRiskStatus(req: ModifyRiskCenterRiskStatusRequest, cb?: (error: string, rep: ModifyRiskCenterRiskStatusResponse) => void): Promise<ModifyRiskCenterRiskStatusResponse>;
    /**
     * 修改集团账号状态
     */
    ModifyOrganizationAccountStatus(req: ModifyOrganizationAccountStatusRequest, cb?: (error: string, rep: ModifyOrganizationAccountStatusResponse) => void): Promise<ModifyOrganizationAccountStatusResponse>;
    /**
     * 访问密钥告警记录详情
     */
    DescribeAccessKeyAlarmDetail(req: DescribeAccessKeyAlarmDetailRequest, cb?: (error: string, rep: DescribeAccessKeyAlarmDetailResponse) => void): Promise<DescribeAccessKeyAlarmDetailResponse>;
    /**
     * 获取配置视角的配置风险列表
     */
    DescribeRiskCenterCFGViewCFGRiskList(req: DescribeRiskCenterCFGViewCFGRiskListRequest, cb?: (error: string, rep: DescribeRiskCenterCFGViewCFGRiskListResponse) => void): Promise<DescribeRiskCenterCFGViewCFGRiskListResponse>;
    /**
     * 数据库资产列表
     */
    DescribeDbAssets(req: DescribeDbAssetsRequest, cb?: (error: string, rep: DescribeDbAssetsResponse) => void): Promise<DescribeDbAssetsResponse>;
    /**
     * 集群列表
     */
    DescribeClusterAssets(req: DescribeClusterAssetsRequest, cb?: (error: string, rep: DescribeClusterAssetsResponse) => void): Promise<DescribeClusterAssetsResponse>;
    /**
     * 访问密钥风险记录列表
     */
    DescribeAccessKeyRisk(req: DescribeAccessKeyRiskRequest, cb?: (error: string, rep: DescribeAccessKeyRiskResponse) => void): Promise<DescribeAccessKeyRiskResponse>;
    /**
     * 获取子网列表
     */
    DescribeSubnetAssets(req: DescribeSubnetAssetsRequest, cb?: (error: string, rep: DescribeSubnetAssetsResponse) => void): Promise<DescribeSubnetAssetsResponse>;
    /**
     * 获取风险中心风险概况示例
     */
    DescribeCSIPRiskStatistics(req: DescribeCSIPRiskStatisticsRequest, cb?: (error: string, rep: DescribeCSIPRiskStatisticsResponse) => void): Promise<DescribeCSIPRiskStatisticsResponse>;
    /**
     * 访问密钥风险记录详情
     */
    DescribeAccessKeyRiskDetail(req: DescribeAccessKeyRiskDetailRequest, cb?: (error: string, rep: DescribeAccessKeyRiskDetailResponse) => void): Promise<DescribeAccessKeyRiskDetailResponse>;
    /**
     * 获取用户访问密钥资产列表
     */
    DescribeAccessKeyAsset(req: DescribeAccessKeyAssetRequest, cb?: (error: string, rep: DescribeAccessKeyAssetResponse) => void): Promise<DescribeAccessKeyAssetResponse>;
    /**
     * 获取风险调用记录列表
     */
    DescribeRiskCallRecord(req: DescribeRiskCallRecordRequest, cb?: (error: string, rep: DescribeRiskCallRecordResponse) => void): Promise<DescribeRiskCallRecordResponse>;
    /**
     * cvm详情
     */
    DescribeCVMAssetInfo(req: DescribeCVMAssetInfoRequest, cb?: (error: string, rep: DescribeCVMAssetInfoResponse) => void): Promise<DescribeCVMAssetInfoResponse>;
}
