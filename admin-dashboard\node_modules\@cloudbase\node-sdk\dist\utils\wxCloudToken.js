"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadWxCloudbaseAccesstoken = exports.getWxCloudToken = exports.CLOUDBASE_ACCESS_TOKEN_PATH = void 0;
// 由定时触发器触发时（TRIGGER_SRC=timer）：优先使用 WX_TRIGGER_API_TOKEN_V0，不存在的话，为了兼容兼容旧的开发者工具，也是使用 WX_API_TOKEN
// 非定时触发器触发时（TRIGGER_SRC!=timer）: 使用 WX_API_TOKEN
const fs = __importStar(require("fs"));
const cloudbase_1 = require("../cloudbase");
const cloudplatform_1 = require("./cloudplatform");
exports.CLOUDBASE_ACCESS_TOKEN_PATH = '/.tencentcloudbase/wx/cloudbase_access_token';
function getWxCloudToken() {
    const { TRIGGER_SRC, WX_TRIGGER_API_TOKEN_V0, WX_API_TOKEN, WX_CLOUDBASE_ACCESSTOKEN = '' } = cloudbase_1.CloudBase.getCloudbaseContext();
    const wxCloudToken = {};
    if (TRIGGER_SRC === 'timer') {
        wxCloudToken.wxCloudApiToken = WX_TRIGGER_API_TOKEN_V0 || WX_API_TOKEN || '';
    }
    else {
        wxCloudToken.wxCloudApiToken = WX_API_TOKEN || '';
    }
    // 只在不存在 wxCloudApiToken 时，才尝试读取 wxCloudbaseAccesstoken
    if (!wxCloudToken.wxCloudApiToken) {
        wxCloudToken.wxCloudbaseAccesstoken = WX_CLOUDBASE_ACCESSTOKEN || loadWxCloudbaseAccesstoken();
    }
    return wxCloudToken;
}
exports.getWxCloudToken = getWxCloudToken;
const maxCacheAge = 10 * 60 * 1000;
const cloudbaseAccessTokenInfo = { token: '', timestamp: 0 };
function loadWxCloudbaseAccesstoken() {
    if (cloudbaseAccessTokenInfo.token && Date.now() - cloudbaseAccessTokenInfo.timestamp < maxCacheAge) {
        return cloudbaseAccessTokenInfo.token;
    }
    try {
        if ((0, cloudplatform_1.checkIsInCBR)() && fs.existsSync(exports.CLOUDBASE_ACCESS_TOKEN_PATH)) {
            cloudbaseAccessTokenInfo.token = fs.readFileSync(exports.CLOUDBASE_ACCESS_TOKEN_PATH).toString();
            cloudbaseAccessTokenInfo.timestamp = Date.now();
            return cloudbaseAccessTokenInfo.token;
        }
    }
    catch (e) {
        console.warn('[TCB][ERROR]: loadWxCloudbaseAccesstoken error: ', e.message);
    }
    return '';
}
exports.loadWxCloudbaseAccesstoken = loadWxCloudbaseAccesstoken;
