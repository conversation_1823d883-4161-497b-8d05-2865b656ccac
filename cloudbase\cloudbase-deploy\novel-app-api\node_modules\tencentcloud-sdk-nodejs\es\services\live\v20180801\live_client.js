import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("live.tencentcloudapi.com", "2018-08-01", clientConfig);
    }
    async DescribeCasterList(req, cb) {
        return this.request("DescribeCasterList", req, cb);
    }
    async DropLiveStream(req, cb) {
        return this.request("DropLiveStream", req, cb);
    }
    async DescribeLiveWatermarks(req, cb) {
        return this.request("DescribeLiveWatermarks", req, cb);
    }
    async DescribeConcurrentRecordStreamNum(req, cb) {
        return this.request("DescribeConcurrentRecordStreamNum", req, cb);
    }
    async CreateLiveRecord(req, cb) {
        return this.request("CreateLiveRecord", req, cb);
    }
    async UpdateLiveWatermark(req, cb) {
        return this.request("UpdateLiveWatermark", req, cb);
    }
    async ModifyLiveRecordTemplate(req, cb) {
        return this.request("ModifyLiveRecordTemplate", req, cb);
    }
    async DescribeRecordTask(req, cb) {
        return this.request("DescribeRecordTask", req, cb);
    }
    async StopCasterPvw(req, cb) {
        return this.request("StopCasterPvw", req, cb);
    }
    async CreateLiveWatermarkRule(req, cb) {
        return this.request("CreateLiveWatermarkRule", req, cb);
    }
    async DescribeLiveStreamEventList(req, cb) {
        return this.request("DescribeLiveStreamEventList", req, cb);
    }
    async CreateCommonMixStream(req, cb) {
        return this.request("CreateCommonMixStream", req, cb);
    }
    async DescribeHttpStatusInfoList(req, cb) {
        return this.request("DescribeHttpStatusInfoList", req, cb);
    }
    async DescribeProvinceIspPlayInfoList(req, cb) {
        return this.request("DescribeProvinceIspPlayInfoList", req, cb);
    }
    async ModifyLivePlayDomain(req, cb) {
        return this.request("ModifyLivePlayDomain", req, cb);
    }
    async DescribeStreamPushInfoList(req, cb) {
        return this.request("DescribeStreamPushInfoList", req, cb);
    }
    async StartLiveStreamMonitor(req, cb) {
        return this.request("StartLiveStreamMonitor", req, cb);
    }
    async DescribeLiveSnapshotRules(req, cb) {
        return this.request("DescribeLiveSnapshotRules", req, cb);
    }
    async DescribeLiveCloudEffectList(req, cb) {
        return this.request("DescribeLiveCloudEffectList", req, cb);
    }
    async ReleaseCaster(req, cb) {
        return this.request("ReleaseCaster", req, cb);
    }
    async DescribeTimeShiftStreamList(req, cb) {
        return this.request("DescribeTimeShiftStreamList", req, cb);
    }
    async AddCasterInputInfo(req, cb) {
        return this.request("AddCasterInputInfo", req, cb);
    }
    async CreateLivePadTemplate(req, cb) {
        return this.request("CreateLivePadTemplate", req, cb);
    }
    async DescribeLiveSnapshotTemplates(req, cb) {
        return this.request("DescribeLiveSnapshotTemplates", req, cb);
    }
    async DescribeLiveDomainReferer(req, cb) {
        return this.request("DescribeLiveDomainReferer", req, cb);
    }
    async DescribeLiveTranscodeTemplate(req, cb) {
        return this.request("DescribeLiveTranscodeTemplate", req, cb);
    }
    async DescribeLiveTimeShiftWriteSizeInfoList(req, cb) {
        return this.request("DescribeLiveTimeShiftWriteSizeInfoList", req, cb);
    }
    async ModifyLiveDomainReferer(req, cb) {
        return this.request("ModifyLiveDomainReferer", req, cb);
    }
    async DescribeTopClientIpSumInfoList(req, cb) {
        return this.request("DescribeTopClientIpSumInfoList", req, cb);
    }
    async CreateCasterInputPushUrl(req, cb) {
        return this.request("CreateCasterInputPushUrl", req, cb);
    }
    async DescribeCasterOutputInfos(req, cb) {
        return this.request("DescribeCasterOutputInfos", req, cb);
    }
    async CreateScreenshotTask(req, cb) {
        return this.request("CreateScreenshotTask", req, cb);
    }
    async DescribeLiveRecordTemplates(req, cb) {
        return this.request("DescribeLiveRecordTemplates", req, cb);
    }
    async StopLiveStreamMonitor(req, cb) {
        return this.request("StopLiveStreamMonitor", req, cb);
    }
    async AuthenticateDomainOwner(req, cb) {
        return this.request("AuthenticateDomainOwner", req, cb);
    }
    async ModifyLiveSnapshotTemplate(req, cb) {
        return this.request("ModifyLiveSnapshotTemplate", req, cb);
    }
    async DescribeVisitTopSumInfoList(req, cb) {
        return this.request("DescribeVisitTopSumInfoList", req, cb);
    }
    async ModifyCasterMarkWordInfo(req, cb) {
        return this.request("ModifyCasterMarkWordInfo", req, cb);
    }
    async AddCasterLayoutInfo(req, cb) {
        return this.request("AddCasterLayoutInfo", req, cb);
    }
    async DescribeLiveDomainCert(req, cb) {
        return this.request("DescribeLiveDomainCert", req, cb);
    }
    async AddLiveWatermark(req, cb) {
        return this.request("AddLiveWatermark", req, cb);
    }
    async DescribeAreaBillBandwidthAndFluxList(req, cb) {
        return this.request("DescribeAreaBillBandwidthAndFluxList", req, cb);
    }
    async DescribeCasterMarkWordInfos(req, cb) {
        return this.request("DescribeCasterMarkWordInfos", req, cb);
    }
    async DeleteCasterLayoutInfo(req, cb) {
        return this.request("DeleteCasterLayoutInfo", req, cb);
    }
    async DeleteLiveWatermarkRule(req, cb) {
        return this.request("DeleteLiveWatermarkRule", req, cb);
    }
    async DeleteLivePadTemplate(req, cb) {
        return this.request("DeleteLivePadTemplate", req, cb);
    }
    async DeleteLiveCallbackRule(req, cb) {
        return this.request("DeleteLiveCallbackRule", req, cb);
    }
    async ModifyPullStreamConfig(req, cb) {
        return this.request("ModifyPullStreamConfig", req, cb);
    }
    async CreateLiveSnapshotTemplate(req, cb) {
        return this.request("CreateLiveSnapshotTemplate", req, cb);
    }
    async DescribeLiveStreamOnlineList(req, cb) {
        return this.request("DescribeLiveStreamOnlineList", req, cb);
    }
    async EnableOptimalSwitching(req, cb) {
        return this.request("EnableOptimalSwitching", req, cb);
    }
    async DeleteLiveCallbackTemplate(req, cb) {
        return this.request("DeleteLiveCallbackTemplate", req, cb);
    }
    async ModifyCasterMarkPicInfo(req, cb) {
        return this.request("ModifyCasterMarkPicInfo", req, cb);
    }
    async DescribeCasterPlayUrl(req, cb) {
        return this.request("DescribeCasterPlayUrl", req, cb);
    }
    async DescribeLivePushAuthKey(req, cb) {
        return this.request("DescribeLivePushAuthKey", req, cb);
    }
    async DeletePullStreamConfig(req, cb) {
        return this.request("DeletePullStreamConfig", req, cb);
    }
    async DescribeTimeShiftRecordDetail(req, cb) {
        return this.request("DescribeTimeShiftRecordDetail", req, cb);
    }
    async DescribeLiveRecordTemplate(req, cb) {
        return this.request("DescribeLiveRecordTemplate", req, cb);
    }
    async DescribeLivePadTemplate(req, cb) {
        return this.request("DescribeLivePadTemplate", req, cb);
    }
    async StopScreenshotTask(req, cb) {
        return this.request("StopScreenshotTask", req, cb);
    }
    async AddCasterMarkWordInfo(req, cb) {
        return this.request("AddCasterMarkWordInfo", req, cb);
    }
    async DescribeMonitorReport(req, cb) {
        return this.request("DescribeMonitorReport", req, cb);
    }
    async DescribePullStreamConfigs(req, cb) {
        return this.request("DescribePullStreamConfigs", req, cb);
    }
    async DescribeLiveStreamMonitor(req, cb) {
        return this.request("DescribeLiveStreamMonitor", req, cb);
    }
    async SendLiveCloudEffect(req, cb) {
        return this.request("SendLiveCloudEffect", req, cb);
    }
    async DeleteLiveWatermark(req, cb) {
        return this.request("DeleteLiveWatermark", req, cb);
    }
    async ModifyCaster(req, cb) {
        return this.request("ModifyCaster", req, cb);
    }
    async DescribeLiveDomainCertBindings(req, cb) {
        return this.request("DescribeLiveDomainCertBindings", req, cb);
    }
    async CreateLiveTimeShiftRule(req, cb) {
        return this.request("CreateLiveTimeShiftRule", req, cb);
    }
    async DescribePlayErrorCodeSumInfoList(req, cb) {
        return this.request("DescribePlayErrorCodeSumInfoList", req, cb);
    }
    async AddDelayLiveStream(req, cb) {
        return this.request("AddDelayLiveStream", req, cb);
    }
    async DescribeUploadStreamNums(req, cb) {
        return this.request("DescribeUploadStreamNums", req, cb);
    }
    async DescribeStreamDayPlayInfoList(req, cb) {
        return this.request("DescribeStreamDayPlayInfoList", req, cb);
    }
    async DescribeLivePadStreamList(req, cb) {
        return this.request("DescribeLivePadStreamList", req, cb);
    }
    async ModifyLiveTranscodeTemplate(req, cb) {
        return this.request("ModifyLiveTranscodeTemplate", req, cb);
    }
    async DeleteLiveTranscodeRule(req, cb) {
        return this.request("DeleteLiveTranscodeRule", req, cb);
    }
    async DescribeLiveTranscodeTotalInfo(req, cb) {
        return this.request("DescribeLiveTranscodeTotalInfo", req, cb);
    }
    async DeleteLiveSnapshotRule(req, cb) {
        return this.request("DeleteLiveSnapshotRule", req, cb);
    }
    async DeleteLivePullStreamTask(req, cb) {
        return this.request("DeleteLivePullStreamTask", req, cb);
    }
    async DescribeLiveForbidStreamList(req, cb) {
        return this.request("DescribeLiveForbidStreamList", req, cb);
    }
    async ModifyLivePadTemplate(req, cb) {
        return this.request("ModifyLivePadTemplate", req, cb);
    }
    async DescribeLiveWatermark(req, cb) {
        return this.request("DescribeLiveWatermark", req, cb);
    }
    async DescribeLiveCert(req, cb) {
        return this.request("DescribeLiveCert", req, cb);
    }
    async ModifyLiveDomainCertBindings(req, cb) {
        return this.request("ModifyLiveDomainCertBindings", req, cb);
    }
    async DescribeDeliverLogDownList(req, cb) {
        return this.request("DescribeDeliverLogDownList", req, cb);
    }
    async CreateCasterPgm(req, cb) {
        return this.request("CreateCasterPgm", req, cb);
    }
    async ModifyLivePullStreamTask(req, cb) {
        return this.request("ModifyLivePullStreamTask", req, cb);
    }
    async DescribeLiveEnhanceInfoList(req, cb) {
        return this.request("DescribeLiveEnhanceInfoList", req, cb);
    }
    async DescribePullTransformPushInfo(req, cb) {
        return this.request("DescribePullTransformPushInfo", req, cb);
    }
    async CreateLiveCallbackTemplate(req, cb) {
        return this.request("CreateLiveCallbackTemplate", req, cb);
    }
    async DeleteAuditKeywords(req, cb) {
        return this.request("DeleteAuditKeywords", req, cb);
    }
    async DescribeLiveDomains(req, cb) {
        return this.request("DescribeLiveDomains", req, cb);
    }
    async ModifyCasterOutputInfo(req, cb) {
        return this.request("ModifyCasterOutputInfo", req, cb);
    }
    async CreateCasterPvw(req, cb) {
        return this.request("CreateCasterPvw", req, cb);
    }
    async ResumeLiveStream(req, cb) {
        return this.request("ResumeLiveStream", req, cb);
    }
    async DescribeCasterMarkPicInfos(req, cb) {
        return this.request("DescribeCasterMarkPicInfos", req, cb);
    }
    async DescribeLiveCallbackTemplate(req, cb) {
        return this.request("DescribeLiveCallbackTemplate", req, cb);
    }
    async DeleteLiveDomain(req, cb) {
        return this.request("DeleteLiveDomain", req, cb);
    }
    async ModifyLiveCallbackTemplate(req, cb) {
        return this.request("ModifyLiveCallbackTemplate", req, cb);
    }
    async AddCasterMarkPicInfo(req, cb) {
        return this.request("AddCasterMarkPicInfo", req, cb);
    }
    async DeleteLiveTimeShiftTemplate(req, cb) {
        return this.request("DeleteLiveTimeShiftTemplate", req, cb);
    }
    async DescribeGroupProIspPlayInfoList(req, cb) {
        return this.request("DescribeGroupProIspPlayInfoList", req, cb);
    }
    async DescribeStreamPlayInfoList(req, cb) {
        return this.request("DescribeStreamPlayInfoList", req, cb);
    }
    async DescribeCasterUserStatus(req, cb) {
        return this.request("DescribeCasterUserStatus", req, cb);
    }
    async DescribeCasterLayoutInfos(req, cb) {
        return this.request("DescribeCasterLayoutInfos", req, cb);
    }
    async DescribeLivePullStreamTaskStatus(req, cb) {
        return this.request("DescribeLivePullStreamTaskStatus", req, cb);
    }
    async DescribeLiveSnapshotTemplate(req, cb) {
        return this.request("DescribeLiveSnapshotTemplate", req, cb);
    }
    async DescribeLiveCallbackTemplates(req, cb) {
        return this.request("DescribeLiveCallbackTemplates", req, cb);
    }
    async StopRecordTask(req, cb) {
        return this.request("StopRecordTask", req, cb);
    }
    async ModifyLiveTimeShiftTemplate(req, cb) {
        return this.request("ModifyLiveTimeShiftTemplate", req, cb);
    }
    async CreateLiveStreamMonitor(req, cb) {
        return this.request("CreateLiveStreamMonitor", req, cb);
    }
    async CreateLivePadRule(req, cb) {
        return this.request("CreateLivePadRule", req, cb);
    }
    async StopLiveRecord(req, cb) {
        return this.request("StopLiveRecord", req, cb);
    }
    async ModifyLivePlayAuthKey(req, cb) {
        return this.request("ModifyLivePlayAuthKey", req, cb);
    }
    async DeleteCaster(req, cb) {
        return this.request("DeleteCaster", req, cb);
    }
    async DeleteLiveTranscodeTemplate(req, cb) {
        return this.request("DeleteLiveTranscodeTemplate", req, cb);
    }
    async DescribeScreenShotSheetNumList(req, cb) {
        return this.request("DescribeScreenShotSheetNumList", req, cb);
    }
    async UnBindLiveDomainCert(req, cb) {
        return this.request("UnBindLiveDomainCert", req, cb);
    }
    async DeleteRecordTask(req, cb) {
        return this.request("DeleteRecordTask", req, cb);
    }
    async DescribeLiveTranscodeDetailInfo(req, cb) {
        return this.request("DescribeLiveTranscodeDetailInfo", req, cb);
    }
    async DescribeLogDownloadList(req, cb) {
        return this.request("DescribeLogDownloadList", req, cb);
    }
    async DescribeLiveTimeShiftTemplates(req, cb) {
        return this.request("DescribeLiveTimeShiftTemplates", req, cb);
    }
    async DescribeLiveRecordRules(req, cb) {
        return this.request("DescribeLiveRecordRules", req, cb);
    }
    async DeleteCasterOutputInfo(req, cb) {
        return this.request("DeleteCasterOutputInfo", req, cb);
    }
    async ModifyCasterLayoutInfo(req, cb) {
        return this.request("ModifyCasterLayoutInfo", req, cb);
    }
    async AddCasterOutputInfo(req, cb) {
        return this.request("AddCasterOutputInfo", req, cb);
    }
    async DescribeLiveXP2PDetailInfoList(req, cb) {
        return this.request("DescribeLiveXP2PDetailInfoList", req, cb);
    }
    async DescribeLiveDelayInfoList(req, cb) {
        return this.request("DescribeLiveDelayInfoList", req, cb);
    }
    async DescribeLiveTimeShiftRules(req, cb) {
        return this.request("DescribeLiveTimeShiftRules", req, cb);
    }
    async DeleteLiveRecordRule(req, cb) {
        return this.request("DeleteLiveRecordRule", req, cb);
    }
    async DescribeLiveDomain(req, cb) {
        return this.request("DescribeLiveDomain", req, cb);
    }
    async DeleteLivePadRule(req, cb) {
        return this.request("DeleteLivePadRule", req, cb);
    }
    async CreateLiveCallbackRule(req, cb) {
        return this.request("CreateLiveCallbackRule", req, cb);
    }
    async DescribeLiveCallbackRules(req, cb) {
        return this.request("DescribeLiveCallbackRules", req, cb);
    }
    async DescribePlayErrorCodeDetailInfoList(req, cb) {
        return this.request("DescribePlayErrorCodeDetailInfoList", req, cb);
    }
    async DescribeLiveTimeShiftBillInfoList(req, cb) {
        return this.request("DescribeLiveTimeShiftBillInfoList", req, cb);
    }
    async DescribeLiveStreamPublishedList(req, cb) {
        return this.request("DescribeLiveStreamPublishedList", req, cb);
    }
    async DescribeScreenshotTask(req, cb) {
        return this.request("DescribeScreenshotTask", req, cb);
    }
    async DescribePushBandwidthAndFluxList(req, cb) {
        return this.request("DescribePushBandwidthAndFluxList", req, cb);
    }
    async DeleteScreenshotTask(req, cb) {
        return this.request("DeleteScreenshotTask", req, cb);
    }
    async ForbidLiveStream(req, cb) {
        return this.request("ForbidLiveStream", req, cb);
    }
    async AddLiveDomain(req, cb) {
        return this.request("AddLiveDomain", req, cb);
    }
    async ModifyPullStreamStatus(req, cb) {
        return this.request("ModifyPullStreamStatus", req, cb);
    }
    async DescribeDeliverBandwidthList(req, cb) {
        return this.request("DescribeDeliverBandwidthList", req, cb);
    }
    async SwitchBackupStream(req, cb) {
        return this.request("SwitchBackupStream", req, cb);
    }
    async DescribeLiveDomainPlayInfoList(req, cb) {
        return this.request("DescribeLiveDomainPlayInfoList", req, cb);
    }
    async CreateAuditKeywords(req, cb) {
        return this.request("CreateAuditKeywords", req, cb);
    }
    async DescribeCallbackRecordsList(req, cb) {
        return this.request("DescribeCallbackRecordsList", req, cb);
    }
    async StopLivePadStream(req, cb) {
        return this.request("StopLivePadStream", req, cb);
    }
    async CreateLiveRecordRule(req, cb) {
        return this.request("CreateLiveRecordRule", req, cb);
    }
    async CreateLivePullStreamTask(req, cb) {
        return this.request("CreateLivePullStreamTask", req, cb);
    }
    async DescribeLiveTranscodeTemplates(req, cb) {
        return this.request("DescribeLiveTranscodeTemplates", req, cb);
    }
    async CreateLiveRecordTemplate(req, cb) {
        return this.request("CreateLiveRecordTemplate", req, cb);
    }
    async DescribeBillBandwidthAndFluxList(req, cb) {
        return this.request("DescribeBillBandwidthAndFluxList", req, cb);
    }
    async ForbidLiveDomain(req, cb) {
        return this.request("ForbidLiveDomain", req, cb);
    }
    async CreateLiveTranscodeRule(req, cb) {
        return this.request("CreateLiveTranscodeRule", req, cb);
    }
    async DeleteLiveTimeShiftRule(req, cb) {
        return this.request("DeleteLiveTimeShiftRule", req, cb);
    }
    async RestartLivePullStreamTask(req, cb) {
        return this.request("RestartLivePullStreamTask", req, cb);
    }
    async DescribeLivePadTemplates(req, cb) {
        return this.request("DescribeLivePadTemplates", req, cb);
    }
    async ModifyLivePushAuthKey(req, cb) {
        return this.request("ModifyLivePushAuthKey", req, cb);
    }
    async DescribeLiveWatermarkRules(req, cb) {
        return this.request("DescribeLiveWatermarkRules", req, cb);
    }
    async DescribeLivePadRules(req, cb) {
        return this.request("DescribeLivePadRules", req, cb);
    }
    async DeleteCasterInputInfo(req, cb) {
        return this.request("DeleteCasterInputInfo", req, cb);
    }
    async DeleteLiveRecord(req, cb) {
        return this.request("DeleteLiveRecord", req, cb);
    }
    async DescribeLiveStreamMonitorList(req, cb) {
        return this.request("DescribeLiveStreamMonitorList", req, cb);
    }
    async CreateLiveSnapshotRule(req, cb) {
        return this.request("CreateLiveSnapshotRule", req, cb);
    }
    async CreateLiveTimeShiftTemplate(req, cb) {
        return this.request("CreateLiveTimeShiftTemplate", req, cb);
    }
    async DeleteLiveStreamMonitor(req, cb) {
        return this.request("DeleteLiveStreamMonitor", req, cb);
    }
    async DescribeAuditKeywords(req, cb) {
        return this.request("DescribeAuditKeywords", req, cb);
    }
    async DescribeLivePullStreamTasks(req, cb) {
        return this.request("DescribeLivePullStreamTasks", req, cb);
    }
    async StopCasterPgm(req, cb) {
        return this.request("StopCasterPgm", req, cb);
    }
    async DescribeProIspPlaySumInfoList(req, cb) {
        return this.request("DescribeProIspPlaySumInfoList", req, cb);
    }
    async DescribeAllStreamPlayInfoList(req, cb) {
        return this.request("DescribeAllStreamPlayInfoList", req, cb);
    }
    async DescribeLivePlayAuthKey(req, cb) {
        return this.request("DescribeLivePlayAuthKey", req, cb);
    }
    async CreateCasterPgmFromPvw(req, cb) {
        return this.request("CreateCasterPgmFromPvw", req, cb);
    }
    async DescribeCasterTransitionTypes(req, cb) {
        return this.request("DescribeCasterTransitionTypes", req, cb);
    }
    async DescribeCasterDisplayInfo(req, cb) {
        return this.request("DescribeCasterDisplayInfo", req, cb);
    }
    async ModifyLiveStreamMonitor(req, cb) {
        return this.request("ModifyLiveStreamMonitor", req, cb);
    }
    async CreateCaster(req, cb) {
        return this.request("CreateCaster", req, cb);
    }
    async DescribeLiveStreamState(req, cb) {
        return this.request("DescribeLiveStreamState", req, cb);
    }
    async DescribeCasterInputInfos(req, cb) {
        return this.request("DescribeCasterInputInfos", req, cb);
    }
    async DeleteLiveRecordTemplate(req, cb) {
        return this.request("DeleteLiveRecordTemplate", req, cb);
    }
    async CopyCaster(req, cb) {
        return this.request("CopyCaster", req, cb);
    }
    async ModifyCasterInputInfo(req, cb) {
        return this.request("ModifyCasterInputInfo", req, cb);
    }
    async DeleteCasterMarkPicInfo(req, cb) {
        return this.request("DeleteCasterMarkPicInfo", req, cb);
    }
    async DeleteCasterMarkWordInfo(req, cb) {
        return this.request("DeleteCasterMarkWordInfo", req, cb);
    }
    async DescribeBackupStreamList(req, cb) {
        return this.request("DescribeBackupStreamList", req, cb);
    }
    async ResumeDelayLiveStream(req, cb) {
        return this.request("ResumeDelayLiveStream", req, cb);
    }
    async CreateRecordTask(req, cb) {
        return this.request("CreateRecordTask", req, cb);
    }
    async CreateLiveTranscodeTemplate(req, cb) {
        return this.request("CreateLiveTranscodeTemplate", req, cb);
    }
    async DescribeLiveCerts(req, cb) {
        return this.request("DescribeLiveCerts", req, cb);
    }
    async DescribeLiveTranscodeRules(req, cb) {
        return this.request("DescribeLiveTranscodeRules", req, cb);
    }
    async EnableLiveDomain(req, cb) {
        return this.request("EnableLiveDomain", req, cb);
    }
    async CancelCommonMixStream(req, cb) {
        return this.request("CancelCommonMixStream", req, cb);
    }
    async DescribeCaster(req, cb) {
        return this.request("DescribeCaster", req, cb);
    }
    async DescribeLivePackageInfo(req, cb) {
        return this.request("DescribeLivePackageInfo", req, cb);
    }
    async CreatePullStreamConfig(req, cb) {
        return this.request("CreatePullStreamConfig", req, cb);
    }
    async DescribeTranscodeTaskNum(req, cb) {
        return this.request("DescribeTranscodeTaskNum", req, cb);
    }
    async DescribeLiveStreamPushInfoList(req, cb) {
        return this.request("DescribeLiveStreamPushInfoList", req, cb);
    }
    async StartLivePadStream(req, cb) {
        return this.request("StartLivePadStream", req, cb);
    }
    async DeleteLiveSnapshotTemplate(req, cb) {
        return this.request("DeleteLiveSnapshotTemplate", req, cb);
    }
}
