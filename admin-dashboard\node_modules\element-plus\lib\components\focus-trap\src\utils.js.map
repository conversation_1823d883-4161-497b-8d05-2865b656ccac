{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/focus-trap/src/utils.ts"], "sourcesContent": ["import { onBeforeUnmount, onMounted, ref } from 'vue'\nimport { isElement, isFocusable } from '@element-plus/utils'\nimport { FOCUSOUT_PREVENTED, FOCUSOUT_PREVENTED_OPTS } from './tokens'\n\nconst focusReason = ref<'pointer' | 'keyboard'>()\nconst lastUserFocusTimestamp = ref<number>(0)\nconst lastAutomatedFocusTimestamp = ref<number>(0)\nlet focusReasonUserCount = 0\n\nexport type FocusLayer = {\n  paused: boolean\n  pause: () => void\n  resume: () => void\n}\n\nexport type FocusStack = FocusLayer[]\n\nexport const obtainAllFocusableElements = (\n  element: HTMLElement\n): HTMLElement[] => {\n  const nodes: HTMLElement[] = []\n  const walker = document.createTreeWalker(element, NodeFilter.SHOW_ELEMENT, {\n    acceptNode: (\n      node: Element & {\n        disabled: boolean\n        hidden: boolean\n        type: string\n        tabIndex: number\n      }\n    ) => {\n      const isHiddenInput = node.tagName === 'INPUT' && node.type === 'hidden'\n      if (node.disabled || node.hidden || isHiddenInput)\n        return NodeFilter.FILTER_SKIP\n      return node.tabIndex >= 0 || node === document.activeElement\n        ? NodeFilter.FILTER_ACCEPT\n        : NodeFilter.FILTER_SKIP\n    },\n  })\n  while (walker.nextNode()) nodes.push(walker.currentNode as HTMLElement)\n\n  return nodes\n}\n\nexport const getVisibleElement = (\n  elements: HTMLElement[],\n  container: HTMLElement\n) => {\n  for (const element of elements) {\n    if (!isHidden(element, container)) return element\n  }\n}\n\nexport const isHidden = (element: HTMLElement, container: HTMLElement) => {\n  if (process.env.NODE_ENV === 'test') return false\n  if (getComputedStyle(element).visibility === 'hidden') return true\n\n  while (element) {\n    if (container && element === container) return false\n    if (getComputedStyle(element).display === 'none') return true\n    element = element.parentElement as HTMLElement\n  }\n\n  return false\n}\n\nexport const getEdges = (container: HTMLElement) => {\n  const focusable = obtainAllFocusableElements(container)\n  const first = getVisibleElement(focusable, container)\n  const last = getVisibleElement(focusable.reverse(), container)\n  return [first, last]\n}\n\nconst isSelectable = (\n  element: any\n): element is HTMLInputElement & { select: () => void } => {\n  return element instanceof HTMLInputElement && 'select' in element\n}\n\nexport const tryFocus = (\n  element?: HTMLElement | { focus: () => void } | null,\n  shouldSelect?: boolean\n) => {\n  if (element && element.focus) {\n    const prevFocusedElement = document.activeElement\n    let cleanup: boolean = false\n\n    if (\n      isElement(element) &&\n      !isFocusable(element) &&\n      !element.getAttribute('tabindex')\n    ) {\n      element.setAttribute('tabindex', '-1')\n      cleanup = true\n    }\n\n    element.focus({ preventScroll: true })\n    lastAutomatedFocusTimestamp.value = window.performance.now()\n\n    if (\n      element !== prevFocusedElement &&\n      isSelectable(element) &&\n      shouldSelect\n    ) {\n      element.select()\n    }\n    if (isElement(element) && cleanup) {\n      element.removeAttribute('tabindex')\n    }\n  }\n}\n\nfunction removeFromStack<T>(list: T[], item: T) {\n  const copy = [...list]\n\n  const idx = list.indexOf(item)\n\n  if (idx !== -1) {\n    copy.splice(idx, 1)\n  }\n  return copy\n}\n\nconst createFocusableStack = () => {\n  let stack = [] as FocusStack\n\n  const push = (layer: FocusLayer) => {\n    const currentLayer = stack[0]\n\n    if (currentLayer && layer !== currentLayer) {\n      currentLayer.pause()\n    }\n\n    stack = removeFromStack(stack, layer)\n    stack.unshift(layer)\n  }\n\n  const remove = (layer: FocusLayer) => {\n    stack = removeFromStack(stack, layer)\n    stack[0]?.resume?.()\n  }\n\n  return {\n    push,\n    remove,\n  }\n}\n\nexport const focusFirstDescendant = (\n  elements: HTMLElement[],\n  shouldSelect = false\n) => {\n  const prevFocusedElement = document.activeElement\n  for (const element of elements) {\n    tryFocus(element, shouldSelect)\n    if (document.activeElement !== prevFocusedElement) return\n  }\n}\n\nexport const focusableStack = createFocusableStack()\n\nexport const isFocusCausedByUserEvent = (): boolean => {\n  return lastUserFocusTimestamp.value > lastAutomatedFocusTimestamp.value\n}\n\nconst notifyFocusReasonPointer = () => {\n  focusReason.value = 'pointer'\n  lastUserFocusTimestamp.value = window.performance.now()\n}\n\nconst notifyFocusReasonKeydown = () => {\n  focusReason.value = 'keyboard'\n  lastUserFocusTimestamp.value = window.performance.now()\n}\n\nexport const useFocusReason = (): {\n  focusReason: typeof focusReason\n  lastUserFocusTimestamp: typeof lastUserFocusTimestamp\n  lastAutomatedFocusTimestamp: typeof lastAutomatedFocusTimestamp\n} => {\n  onMounted(() => {\n    if (focusReasonUserCount === 0) {\n      document.addEventListener('mousedown', notifyFocusReasonPointer)\n      document.addEventListener('touchstart', notifyFocusReasonPointer)\n      document.addEventListener('keydown', notifyFocusReasonKeydown)\n    }\n    focusReasonUserCount++\n  })\n\n  onBeforeUnmount(() => {\n    focusReasonUserCount--\n    if (focusReasonUserCount <= 0) {\n      document.removeEventListener('mousedown', notifyFocusReasonPointer)\n      document.removeEventListener('touchstart', notifyFocusReasonPointer)\n      document.removeEventListener('keydown', notifyFocusReasonKeydown)\n    }\n  })\n\n  return {\n    focusReason,\n    lastUserFocusTimestamp,\n    lastAutomatedFocusTimestamp,\n  }\n}\n\nexport const createFocusOutPreventedEvent = (\n  detail: CustomEventInit['detail']\n) => {\n  return new CustomEvent(FOCUSOUT_PREVENTED, {\n    ...FOCUSOUT_PREVENTED_OPTS,\n    detail,\n  })\n}\n"], "names": ["ref", "isElement", "isFocusable", "onMounted", "onBeforeUnmount", "FOCUSOUT_PREVENTED", "FOCUSOUT_PREVENTED_OPTS"], "mappings": ";;;;;;;;;AAGA,MAAM,WAAW,GAAGA,OAAG,EAAE,CAAC;AAC1B,MAAM,sBAAsB,GAAGA,OAAG,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,2BAA2B,GAAGA,OAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,IAAI,oBAAoB,GAAG,CAAC,CAAC;AACjB,MAAC,0BAA0B,GAAG,CAAC,OAAO,KAAK;AACvD,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;AACnB,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,gBAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,YAAY,EAAE;AAC7E,IAAI,UAAU,EAAE,CAAC,IAAI,KAAK;AAC1B,MAAM,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC;AAC/E,MAAM,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,aAAa;AACvD,QAAQ,OAAO,UAAU,CAAC,WAAW,CAAC;AACtC,MAAM,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,KAAK,QAAQ,CAAC,aAAa,GAAG,UAAU,CAAC,aAAa,GAAG,UAAU,CAAC,WAAW,CAAC;AACvH,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE;AAC1B,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACnC,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACU,MAAC,iBAAiB,GAAG,CAAC,QAAQ,EAAE,SAAS,KAAK;AAC1D,EAAE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,SAAS,CAAC;AACrC,MAAM,OAAO,OAAO,CAAC;AACrB,GAAG;AACH,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,KAAK;AAChD,EAAE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;AACrC,IAAI,OAAO,KAAK,CAAC;AACjB,EAAE,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,UAAU,KAAK,QAAQ;AACvD,IAAI,OAAO,IAAI,CAAC;AAChB,EAAE,OAAO,OAAO,EAAE;AAClB,IAAI,IAAI,SAAS,IAAI,OAAO,KAAK,SAAS;AAC1C,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,IAAI,gBAAgB,CAAC,OAAO,CAAC,CAAC,OAAO,KAAK,MAAM;AACpD,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,OAAO,GAAG,OAAO,CAAC,aAAa,CAAC;AACpC,GAAG;AACH,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,SAAS,KAAK;AACvC,EAAE,MAAM,SAAS,GAAG,0BAA0B,CAAC,SAAS,CAAC,CAAC;AAC1D,EAAE,MAAM,KAAK,GAAG,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;AACxD,EAAE,MAAM,IAAI,GAAG,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,CAAC,CAAC;AACjE,EAAE,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACvB,EAAE;AACF,MAAM,YAAY,GAAG,CAAC,OAAO,KAAK;AAClC,EAAE,OAAO,OAAO,YAAY,gBAAgB,IAAI,QAAQ,IAAI,OAAO,CAAC;AACpE,CAAC,CAAC;AACU,MAAC,QAAQ,GAAG,CAAC,OAAO,EAAE,YAAY,KAAK;AACnD,EAAE,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,EAAE;AAChC,IAAI,MAAM,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC;AACtD,IAAI,IAAI,OAAO,GAAG,KAAK,CAAC;AACxB,IAAI,IAAIC,eAAS,CAAC,OAAO,CAAC,IAAI,CAACC,gBAAW,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;AAC1F,MAAM,OAAO,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC7C,MAAM,OAAO,GAAG,IAAI,CAAC;AACrB,KAAK;AACL,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;AAC3C,IAAI,2BAA2B,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AACjE,IAAI,IAAI,OAAO,KAAK,kBAAkB,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,YAAY,EAAE;AACjF,MAAM,OAAO,CAAC,MAAM,EAAE,CAAC;AACvB,KAAK;AACL,IAAI,IAAID,eAAS,CAAC,OAAO,CAAC,IAAI,OAAO,EAAE;AACvC,MAAM,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;AAC1C,KAAK;AACL,GAAG;AACH,EAAE;AACF,SAAS,eAAe,CAAC,IAAI,EAAE,IAAI,EAAE;AACrC,EAAE,MAAM,IAAI,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;AACzB,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjC,EAAE,IAAI,GAAG,KAAK,CAAC,CAAC,EAAE;AAClB,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACxB,GAAG;AACH,EAAE,OAAO,IAAI,CAAC;AACd,CAAC;AACD,MAAM,oBAAoB,GAAG,MAAM;AACnC,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB,EAAE,MAAM,IAAI,GAAG,CAAC,KAAK,KAAK;AAC1B,IAAI,MAAM,YAAY,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAClC,IAAI,IAAI,YAAY,IAAI,KAAK,KAAK,YAAY,EAAE;AAChD,MAAM,YAAY,CAAC,KAAK,EAAE,CAAC;AAC3B,KAAK;AACL,IAAI,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1C,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACzB,GAAG,CAAC;AACJ,EAAE,MAAM,MAAM,GAAG,CAAC,KAAK,KAAK;AAC5B,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,KAAK,GAAG,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC1C,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvF,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,IAAI;AACR,IAAI,MAAM;AACV,GAAG,CAAC;AACJ,CAAC,CAAC;AACU,MAAC,oBAAoB,GAAG,CAAC,QAAQ,EAAE,YAAY,GAAG,KAAK,KAAK;AACxE,EAAE,MAAM,kBAAkB,GAAG,QAAQ,CAAC,aAAa,CAAC;AACpD,EAAE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AAClC,IAAI,QAAQ,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;AACpC,IAAI,IAAI,QAAQ,CAAC,aAAa,KAAK,kBAAkB;AACrD,MAAM,OAAO;AACb,GAAG;AACH,EAAE;AACU,MAAC,cAAc,GAAG,oBAAoB,GAAG;AACzC,MAAC,wBAAwB,GAAG,MAAM;AAC9C,EAAE,OAAO,sBAAsB,CAAC,KAAK,GAAG,2BAA2B,CAAC,KAAK,CAAC;AAC1E,EAAE;AACF,MAAM,wBAAwB,GAAG,MAAM;AACvC,EAAE,WAAW,CAAC,KAAK,GAAG,SAAS,CAAC;AAChC,EAAE,sBAAsB,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AAC1D,CAAC,CAAC;AACF,MAAM,wBAAwB,GAAG,MAAM;AACvC,EAAE,WAAW,CAAC,KAAK,GAAG,UAAU,CAAC;AACjC,EAAE,sBAAsB,CAAC,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;AAC1D,CAAC,CAAC;AACU,MAAC,cAAc,GAAG,MAAM;AACpC,EAAEE,aAAS,CAAC,MAAM;AAClB,IAAI,IAAI,oBAAoB,KAAK,CAAC,EAAE;AACpC,MAAM,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;AACvE,MAAM,QAAQ,CAAC,gBAAgB,CAAC,YAAY,EAAE,wBAAwB,CAAC,CAAC;AACxE,MAAM,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,oBAAoB,EAAE,CAAC;AAC3B,GAAG,CAAC,CAAC;AACL,EAAEC,mBAAe,CAAC,MAAM;AACxB,IAAI,oBAAoB,EAAE,CAAC;AAC3B,IAAI,IAAI,oBAAoB,IAAI,CAAC,EAAE;AACnC,MAAM,QAAQ,CAAC,mBAAmB,CAAC,WAAW,EAAE,wBAAwB,CAAC,CAAC;AAC1E,MAAM,QAAQ,CAAC,mBAAmB,CAAC,YAAY,EAAE,wBAAwB,CAAC,CAAC;AAC3E,MAAM,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,wBAAwB,CAAC,CAAC;AACxE,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,sBAAsB;AAC1B,IAAI,2BAA2B;AAC/B,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,4BAA4B,GAAG,CAAC,MAAM,KAAK;AACxD,EAAE,OAAO,IAAI,WAAW,CAACC,yBAAkB,EAAE;AAC7C,IAAI,GAAGC,8BAAuB;AAC9B,IAAI,MAAM;AACV,GAAG,CAAC,CAAC;AACL;;;;;;;;;;;;;"}