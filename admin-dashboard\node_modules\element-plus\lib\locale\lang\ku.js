'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var ku = {
  name: "ku",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Temam",
      clear: "Paqij bike"
    },
    datepicker: {
      now: "<PERSON>ha",
      today: "\xCEro",
      cancel: "Betal bike",
      clear: "Paqij bike",
      confirm: "Temam",
      selectDate: "D\xEErok\xEA bibij\xEAre",
      selectTime: "Dem\xEA bibij\xEAre",
      startDate: "D\xEEroka Destp\xEAk\xEA",
      startTime: "Dema Destp\xEAk\xEA",
      endDate: "D\xEEroka Daw\xEE",
      endTime: "Dema Daw\xEE",
      prevYear: "Sala P\xEA\u015F",
      nextYear: "Sala Pa\u015F",
      prevMonth: "Meha P\xEA\u015F",
      nextMonth: "Meha Pa\u015F",
      year: "Sal",
      month1: "R\xEAbendan",
      month2: "Re\u015Femeh",
      month3: "Adar",
      month4: "Avr\xEAl",
      month5: "Gulan",
      month6: "P\xFB\u015Fber",
      month7: "T\xEErmeh",
      month8: "Gilav\xEAj",
      month9: "Rezber",
      month10: "Kew\xE7\xEAr",
      month11: "Sarmawaz",
      month12: "Berfanbar",
      weeks: {
        sun: "Yek",
        mon: "Du\u015F",
        tue: "S\xEA\u015F",
        wed: "\xC7ar",
        thu: "P\xEAn",
        fri: "\xCEn",
        sat: "\u015Eem"
      },
      months: {
        jan: "R\xEAb",
        feb: "Re\u015F",
        mar: "Ada",
        apr: "Avr",
        may: "Gul",
        jun: "P\xFB\u015F",
        jul: "T\xEEr",
        aug: "Gil",
        sep: "Rez",
        oct: "Kew",
        nov: "Sar",
        dec: "Ber"
      }
    },
    select: {
      loading: "Bardibe",
      noMatch: "Li hembere ve agah\xEE tune",
      noData: "Agah\xEE tune",
      placeholder: "Bibij\xEAre"
    },
    mention: {
      loading: "Bardibe"
    },
    cascader: {
      noMatch: "Li hembere ve agah\xEE tune",
      loading: "Bardibe",
      placeholder: "Bibij\xEAre",
      noData: "Agah\xEE tune"
    },
    pagination: {
      goto: "Bi\xE7e",
      pagesize: "/rupel",
      total: "Tevah\xEE {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Peyam",
      confirm: "Temam",
      cancel: "Betal bike",
      error: "Beyana \xE7ewt"
    },
    upload: {
      deleteTip: 'ji bo rake p\xEAl "delete" bike',
      delete: "Rake",
      preview: "P\xEA\u015Fd\xEEtin",
      continue: "Berdewam"
    },
    table: {
      emptyText: "Agah\xEE tune",
      confirmFilter: "Pi\u015Ftrast bike",
      resetFilter: "J\xEA bibe",
      clearFilter: "Hem\xFB",
      sumText: "Kom"
    },
    tree: {
      emptyText: "Agah\xEE tune"
    },
    transfer: {
      noMatch: "Li hembere ve agah\xEE tune",
      noData: "Agah\xEE tune",
      titles: ["L\xEEste 1", "L\xEEste 2"],
      filterPlaceholder: "Biniv\xEEse",
      noCheckedFormat: "{total} lib",
      hasCheckedFormat: "{checked}/{total} bijartin"
    },
    image: {
      error: "FAILED"
    },
    pageHeader: {
      title: "Back"
    },
    popconfirm: {
      confirmButtonText: "Yes",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

exports["default"] = ku;
//# sourceMappingURL=ku.js.map
