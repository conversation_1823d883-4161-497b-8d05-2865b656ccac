import 'package:json_annotation/json_annotation.dart';
import 'package:hive/hive.dart';

part 'text_modification.g.dart';

/// 文本修改类型
@HiveType(typeId: 30)
enum ModificationType {
  @HiveField(0)
  @JsonValue('replace')
  replace,
  @HiveField(1)
  @JsonValue('insert')
  insert,
  @HiveField(2)
  @JsonValue('delete')
  delete,
}

/// 单个文本修改建议
@HiveType(typeId: 31)
@JsonSerializable()
class TextModification {
  /// 修改ID
  @HiveField(0)
  final String id;

  /// 修改类型
  @HiveField(1)
  final ModificationType type;

  /// 起始行号（从1开始）
  @HiveField(2)
  final int startLine;

  /// 结束行号（包含）
  @HiveField(3)
  final int endLine;

  /// 原始文本内容
  @HiveField(4)
  final String originalText;

  /// 修改后的文本内容
  @HiveField(5)
  final String newText;

  /// 修改原因说明
  @HiveField(6)
  final String reason;

  /// 修改状态
  @HiveField(7)
  final ModificationStatus status;

  const TextModification({
    required this.id,
    required this.type,
    required this.startLine,
    required this.endLine,
    required this.originalText,
    required this.newText,
    required this.reason,
    this.status = ModificationStatus.pending,
  });

  factory TextModification.fromJson(Map<String, dynamic> json) =>
      _$TextModificationFromJson(json);

  Map<String, dynamic> toJson() => _$TextModificationToJson(this);

  /// 创建副本并更新状态
  TextModification copyWith({
    String? id,
    ModificationType? type,
    int? startLine,
    int? endLine,
    String? originalText,
    String? newText,
    String? reason,
    ModificationStatus? status,
  }) {
    return TextModification(
      id: id ?? this.id,
      type: type ?? this.type,
      startLine: startLine ?? this.startLine,
      endLine: endLine ?? this.endLine,
      originalText: originalText ?? this.originalText,
      newText: newText ?? this.newText,
      reason: reason ?? this.reason,
      status: status ?? this.status,
    );
  }

  /// 获取修改的行数范围
  int get lineCount => endLine - startLine + 1;

  /// 是否为插入操作
  bool get isInsert => type == ModificationType.insert;

  /// 是否为删除操作
  bool get isDelete => type == ModificationType.delete;

  /// 是否为替换操作
  bool get isReplace => type == ModificationType.replace;
}

/// 修改状态
@HiveType(typeId: 32)
enum ModificationStatus {
  /// 待处理
  @HiveField(0)
  pending,
  /// 已接受
  @HiveField(1)
  accepted,
  /// 已拒绝
  @HiveField(2)
  rejected,
  /// 已应用
  @HiveField(3)
  applied,
}

/// AI创作模式响应
@HiveType(typeId: 33)
@JsonSerializable()
class CreativeEditResponse {
  /// 响应类型标识
  @HiveField(0)
  final String responseType;

  /// 修改建议的简要说明
  @HiveField(1)
  final String summary;

  /// 修改建议列表
  @HiveField(2)
  final List<TextModification> modifications;

  /// 整体修改思路和预期效果的详细说明
  @HiveField(3)
  final String overallExplanation;

  const CreativeEditResponse({
    required this.responseType,
    required this.summary,
    required this.modifications,
    required this.overallExplanation,
  });

  factory CreativeEditResponse.fromJson(Map<String, dynamic> json) =>
      _$CreativeEditResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CreativeEditResponseToJson(this);

  /// 获取待处理的修改
  List<TextModification> get pendingModifications =>
      modifications.where((m) => m.status == ModificationStatus.pending).toList();

  /// 获取已接受的修改
  List<TextModification> get acceptedModifications =>
      modifications.where((m) => m.status == ModificationStatus.accepted).toList();

  /// 获取已拒绝的修改
  List<TextModification> get rejectedModifications =>
      modifications.where((m) => m.status == ModificationStatus.rejected).toList();

  /// 是否有待处理的修改
  bool get hasPendingModifications => pendingModifications.isNotEmpty;

  /// 创建副本并更新修改状态
  CreativeEditResponse copyWithModificationStatus(String modificationId, ModificationStatus status) {
    final updatedModifications = modifications.map((mod) {
      if (mod.id == modificationId) {
        return mod.copyWith(status: status);
      }
      return mod;
    }).toList();

    return CreativeEditResponse(
      responseType: responseType,
      summary: summary,
      modifications: updatedModifications,
      overallExplanation: overallExplanation,
    );
  }

  /// 批量更新修改状态
  CreativeEditResponse copyWithBatchStatus(ModificationStatus status) {
    final updatedModifications = modifications.map((mod) {
      if (mod.status == ModificationStatus.pending) {
        return mod.copyWith(status: status);
      }
      return mod;
    }).toList();

    return CreativeEditResponse(
      responseType: responseType,
      summary: summary,
      modifications: updatedModifications,
      overallExplanation: overallExplanation,
    );
  }
}

/// 文本差异行
class DiffLine {
  /// 行内容
  final String content;
  
  /// 差异类型
  final DiffType type;
  
  /// 原始行号（如果适用）
  final int? originalLineNumber;
  
  /// 新行号（如果适用）
  final int? newLineNumber;

  const DiffLine({
    required this.content,
    required this.type,
    this.originalLineNumber,
    this.newLineNumber,
  });
}

/// 差异类型
enum DiffType {
  /// 未修改的行
  unchanged,
  /// 删除的行
  deleted,
  /// 新增的行
  added,
  /// 修改的行（原始）
  modifiedOriginal,
  /// 修改的行（新版本）
  modifiedNew,
}

/// 文本差异结果
class TextDiff {
  /// 差异行列表
  final List<DiffLine> lines;
  
  /// 修改统计
  final DiffStats stats;

  const TextDiff({
    required this.lines,
    required this.stats,
  });
}

/// 差异统计
class DiffStats {
  /// 新增行数
  final int addedLines;
  
  /// 删除行数
  final int deletedLines;
  
  /// 修改行数
  final int modifiedLines;

  const DiffStats({
    required this.addedLines,
    required this.deletedLines,
    required this.modifiedLines,
  });

  /// 总变更行数
  int get totalChanges => addedLines + deletedLines + modifiedLines;
}
