"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.callFunction = void 0;
const tcbapicaller = __importStar(require("../utils/tcbapirequester"));
const utils_1 = require("../utils/utils");
const code_1 = require("../const/code");
const cloudbase_1 = require("../cloudbase");
const cloudrun_1 = require("../cloudrun");
async function callFunction(cloudbase, callFunctionOptions, opts) {
    // cloudrunfunctions
    if (callFunctionOptions.type === 'cloudrun') {
        const resp = await (0, cloudrun_1.callContainer)(cloudbase, callFunctionOptions, opts);
        return {
            requestId: resp.requestId,
            result: resp.data
        };
    }
    // cloudfunctions
    const { name, qualifier, data } = callFunctionOptions;
    const { TCB_ROUTE_KEY } = cloudbase_1.CloudBase.getCloudbaseContext();
    let transformData;
    try {
        transformData = data ? JSON.stringify(data) : '';
    }
    catch (e) {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, e), { code: code_1.ERROR.INVALID_PARAM.code, message: '对象出现了循环引用' }));
    }
    if (!name) {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: '函数名不能为空' }));
    }
    const params = {
        action: 'functions.invokeFunction',
        function_name: name,
        qualifier,
        // async: async,
        request_data: transformData
    };
    return await tcbapicaller.request({
        config: cloudbase.config,
        params,
        method: 'post',
        opts,
        headers: Object.assign({ 'content-type': 'application/json' }, (TCB_ROUTE_KEY ? { 'X-TCB-Route-Key': TCB_ROUTE_KEY } : {}))
    }).then(res => {
        if (res.code) {
            return res;
        }
        let result;
        try {
            result = JSON.parse(res.data.response_data);
        }
        catch (e) {
            result = res.data.response_data;
        }
        return {
            result,
            requestId: res.requestId
        };
    });
}
exports.callFunction = callFunction;
