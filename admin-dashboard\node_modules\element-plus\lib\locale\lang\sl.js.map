{"version": 3, "file": "sl.js", "sources": ["../../../../../packages/locale/lang/sl.ts"], "sourcesContent": ["export default {\n  name: 'sl',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'V redu',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON>da<PERSON>',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON><PERSON>',\n      confirm: '<PERSON>tr<PERSON>',\n      selectDate: 'Izberi datum',\n      selectTime: 'Izberi čas',\n      startDate: 'Začetni datum',\n      startTime: 'Začetni čas',\n      endDate: 'Končni datum',\n      endTime: 'Končni čas',\n      prevYear: 'Prejšnje leto',\n      nextYear: 'Naslednje leto',\n      prevMonth: 'Prejšnji mesec',\n      nextMonth: 'Naslednji mesec',\n      year: '',\n      month1: 'Jan',\n      month2: 'Feb',\n      month3: 'Mar',\n      month4: 'Apr',\n      month5: 'Maj',\n      month6: 'Jun',\n      month7: 'Jul',\n      month8: 'Avg',\n      month9: 'Sep',\n      month10: 'Okt',\n      month11: 'Nov',\n      month12: 'Dec',\n      week: 'teden',\n      weeks: {\n        sun: 'Ned',\n        mon: 'Pon',\n        tue: 'Tor',\n        wed: 'Sre',\n        thu: 'Čet',\n        fri: 'Pet',\n        sat: 'Sob',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Maj',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Avg',\n        sep: 'Sep',\n        oct: 'Okt',\n        nov: 'Nov',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Nalaganje',\n      noMatch: 'Ni ustreznih podatkov',\n      noData: 'Ni podatkov',\n      placeholder: 'Izberi',\n    },\n    mention: {\n      loading: 'Nalaganje',\n    },\n    cascader: {\n      noMatch: 'Ni ustreznih podatkov',\n      loading: 'Nalaganje',\n      placeholder: 'Izberi',\n      noData: 'Ni podatkov',\n    },\n    pagination: {\n      goto: 'Pojdi na',\n      pagesize: '/stran',\n      total: 'Skupno {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Sporočilo',\n      confirm: 'V redu',\n      cancel: 'Prekliči',\n      error: 'Nedovoljen vnos',\n    },\n    upload: {\n      deleteTip: 'press delete to remove', // to be translated\n      delete: 'Izbriši',\n      preview: 'Predogled',\n      continue: 'Nadaljuj',\n    },\n    table: {\n      emptyText: 'Ni podatkov',\n      confirmFilter: 'Potrdi',\n      resetFilter: 'Ponastavi',\n      clearFilter: 'Vse',\n      sumText: 'Skupno',\n    },\n    tree: {\n      emptyText: 'Ni podatkov',\n    },\n    transfer: {\n      noMatch: 'Ni ustreznih podatkov',\n      noData: 'Ni podatkov',\n      titles: ['Seznam 1', 'Seznam 2'],\n      filterPlaceholder: 'Vnesi ključno besedo',\n      noCheckedFormat: '{total} elementov',\n      hasCheckedFormat: '{checked}/{total} izbranih',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,KAAK,EAAE,cAAc;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,cAAc;AAC3B,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,UAAU,EAAE,cAAc;AAChC,MAAM,UAAU,EAAE,iBAAiB;AACnC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,uBAAuB;AACxC,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,OAAO,EAAE,sBAAsB;AACrC,MAAM,QAAQ,EAAE,oBAAoB;AACpC,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,UAAU;AACvB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,WAAW,EAAE,QAAQ;AAC3B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,WAAW;AAC1B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,MAAM,EAAE,aAAa;AAC3B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,UAAU;AACtB,MAAM,QAAQ,EAAE,QAAQ;AACxB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gBAAgB;AAC7B,MAAM,OAAO,EAAE,QAAQ;AACvB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,iBAAiB;AAC9B,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,QAAQ,EAAE,UAAU;AAC1B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,aAAa;AAC9B,MAAM,aAAa,EAAE,QAAQ;AAC7B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,OAAO,EAAE,QAAQ;AACvB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,aAAa;AAC9B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,uBAAuB;AACtC,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,MAAM,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;AACtC,MAAM,iBAAiB,EAAE,2BAA2B;AACpD,MAAM,eAAe,EAAE,mBAAmB;AAC1C,MAAM,gBAAgB,EAAE,4BAA4B;AACpD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}