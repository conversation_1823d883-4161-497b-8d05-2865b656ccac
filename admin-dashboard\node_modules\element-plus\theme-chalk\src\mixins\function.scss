@use 'config';
@use 'sass:meta';
@use 'sass:string';
@use 'sass:math';
@use 'sass:color';

// BEM support Func
@function selectorToString($selector) {
  $selector: meta.inspect($selector);
  $selector: string.slice($selector, 2, -2);
  @return $selector;
}

@function containsModifier($selector) {
  $selector: selectorToString($selector);

  @if string.index($selector, config.$modifier-separator) {
    @return true;
  } @else {
    @return false;
  }
}

@function containWhenFlag($selector) {
  $selector: selectorToString($selector);

  @if string.index($selector, '.' + config.$state-prefix) {
    @return true;
  } @else {
    @return false;
  }
}

@function containPseudoClass($selector) {
  $selector: selectorToString($selector);

  @if string.index($selector, ':') {
    @return true;
  } @else {
    @return false;
  }
}

@function hitAllSpecialNestRule($selector) {
  @return containsModifier($selector) or containWhenFlag($selector) or
    containPseudoClass($selector);
}

// join var name
// joinVarName(('button', 'text-color')) => '--el-button-text-color'
@function joinVarName($list) {
  $name: '--' + config.$namespace;
  @each $item in $list {
    @if $item != '' {
      $name: $name + '-' + $item;
    }
  }
  @return $name;
}

// getCssVarName('button', 'text-color') => '--el-button-text-color'
@function getCssVarName($args...) {
  @return joinVarName($args);
}

// getCssVar('button', 'text-color') => var(--el-button-text-color)
@function getCssVar($args...) {
  @return var(#{joinVarName($args)});
}

// getCssVarWithDefault(('button', 'text-color'), red) => var(--el-button-text-color, red)
@function getCssVarWithDefault($args, $default) {
  @return var(#{joinVarName($args)}, #{$default});
}

// bem('block', 'element', 'modifier') => 'el-block__element--modifier'
@function bem($block, $element: '', $modifier: '') {
  $name: config.$namespace + config.$common-separator + $block;

  @if $element != '' {
    $name: $name + config.$element-separator + $element;
  }

  @if $modifier != '' {
    $name: $name + config.$modifier-separator + $modifier;
  }

  // @debug $name;
  @return $name;
}

@function roundColor($color) {
  $r: math.round(color.channel($color, 'red'));
  $g: math.round(color.channel($color, 'green'));
  $b: math.round(color.channel($color, 'blue'));
  $a: color.channel($color, 'alpha');

  @return rgba($r, $g, $b, $a);
}
