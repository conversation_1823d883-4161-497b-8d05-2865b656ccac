import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("apigateway.tencentcloudapi.com", "2018-08-08", clientConfig);
    }
    async CreateService(req, cb) {
        return this.request("CreateService", req, cb);
    }
    async DescribeUsagePlansStatus(req, cb) {
        return this.request("DescribeUsagePlansStatus", req, cb);
    }
    async DescribeAPIDocDetail(req, cb) {
        return this.request("DescribeAPIDocDetail", req, cb);
    }
    async DescribeApiAppBindApisStatus(req, cb) {
        return this.request("DescribeApiAppBindApisStatus", req, cb);
    }
    async DescribeUsagePlanSecretIds(req, cb) {
        return this.request("DescribeUsagePlanSecretIds", req, cb);
    }
    async DescribeServiceForApiApp(req, cb) {
        return this.request("DescribeServiceForApiApp", req, cb);
    }
    async ModifyIPStrategy(req, cb) {
        return this.request("ModifyIPStrategy", req, cb);
    }
    async UpdateService(req, cb) {
        return this.request("UpdateService", req, cb);
    }
    async ModifyPlugin(req, cb) {
        return this.request("ModifyPlugin", req, cb);
    }
    async DeleteApiApp(req, cb) {
        return this.request("DeleteApiApp", req, cb);
    }
    async DescribePluginsByApi(req, cb) {
        return this.request("DescribePluginsByApi", req, cb);
    }
    async DescribeServicesStatus(req, cb) {
        return this.request("DescribeServicesStatus", req, cb);
    }
    async DescribeAPIDocs(req, cb) {
        return this.request("DescribeAPIDocs", req, cb);
    }
    async CreateUsagePlan(req, cb) {
        return this.request("CreateUsagePlan", req, cb);
    }
    async DescribeAllPluginApis(req, cb) {
        return this.request("DescribeAllPluginApis", req, cb);
    }
    async UnBindIPStrategy(req, cb) {
        return this.request("UnBindIPStrategy", req, cb);
    }
    async ResetAPIDocPassword(req, cb) {
        return this.request("ResetAPIDocPassword", req, cb);
    }
    async DescribePluginApis(req, cb) {
        return this.request("DescribePluginApis", req, cb);
    }
    async DisableApiKey(req, cb) {
        return this.request("DisableApiKey", req, cb);
    }
    async DescribeApiEnvironmentStrategy(req, cb) {
        return this.request("DescribeApiEnvironmentStrategy", req, cb);
    }
    async DescribeApiAppsStatus(req, cb) {
        return this.request("DescribeApiAppsStatus", req, cb);
    }
    async DescribeApiApp(req, cb) {
        return this.request("DescribeApiApp", req, cb);
    }
    async ModifyUsagePlan(req, cb) {
        return this.request("ModifyUsagePlan", req, cb);
    }
    async DescribeUpstreams(req, cb) {
        return this.request("DescribeUpstreams", req, cb);
    }
    async DescribeApi(req, cb) {
        return this.request("DescribeApi", req, cb);
    }
    async UnbindApiApp(req, cb) {
        return this.request("UnbindApiApp", req, cb);
    }
    async DeleteUsagePlan(req, cb) {
        return this.request("DeleteUsagePlan", req, cb);
    }
    async CreateApiApp(req, cb) {
        return this.request("CreateApiApp", req, cb);
    }
    async DescribeApiKeysStatus(req, cb) {
        return this.request("DescribeApiKeysStatus", req, cb);
    }
    async CreateAPIDoc(req, cb) {
        return this.request("CreateAPIDoc", req, cb);
    }
    async DescribeIPStrategyApisStatus(req, cb) {
        return this.request("DescribeIPStrategyApisStatus", req, cb);
    }
    async UnReleaseService(req, cb) {
        return this.request("UnReleaseService", req, cb);
    }
    async DescribeServiceEnvironmentReleaseHistory(req, cb) {
        return this.request("DescribeServiceEnvironmentReleaseHistory", req, cb);
    }
    async DeleteApi(req, cb) {
        return this.request("DeleteApi", req, cb);
    }
    async UpdateApiAppKey(req, cb) {
        return this.request("UpdateApiAppKey", req, cb);
    }
    async CreateExclusiveInstances(req, cb) {
        return this.request("CreateExclusiveInstances", req, cb);
    }
    async DescribeServiceSubDomainMappings(req, cb) {
        return this.request("DescribeServiceSubDomainMappings", req, cb);
    }
    async BindEnvironment(req, cb) {
        return this.request("BindEnvironment", req, cb);
    }
    async ModifyAPIDoc(req, cb) {
        return this.request("ModifyAPIDoc", req, cb);
    }
    async UnBindSecretIds(req, cb) {
        return this.request("UnBindSecretIds", req, cb);
    }
    async DescribeIPStrategy(req, cb) {
        return this.request("DescribeIPStrategy", req, cb);
    }
    async ModifyUpstream(req, cb) {
        return this.request("ModifyUpstream", req, cb);
    }
    async DescribeServiceReleaseVersion(req, cb) {
        return this.request("DescribeServiceReleaseVersion", req, cb);
    }
    async DescribeApisStatus(req, cb) {
        return this.request("DescribeApisStatus", req, cb);
    }
    async CreateApiKey(req, cb) {
        return this.request("CreateApiKey", req, cb);
    }
    async ModifySubDomain(req, cb) {
        return this.request("ModifySubDomain", req, cb);
    }
    async DeleteIPStrategy(req, cb) {
        return this.request("DeleteIPStrategy", req, cb);
    }
    async ReleaseService(req, cb) {
        return this.request("ReleaseService", req, cb);
    }
    async DescribeExclusiveInstances(req, cb) {
        return this.request("DescribeExclusiveInstances", req, cb);
    }
    async DescribeService(req, cb) {
        return this.request("DescribeService", req, cb);
    }
    async CreatePlugin(req, cb) {
        return this.request("CreatePlugin", req, cb);
    }
    async DescribePlugins(req, cb) {
        return this.request("DescribePlugins", req, cb);
    }
    async DeleteApiKey(req, cb) {
        return this.request("DeleteApiKey", req, cb);
    }
    async CreateApi(req, cb) {
        return this.request("CreateApi", req, cb);
    }
    async DescribeExclusiveInstanceDetail(req, cb) {
        return this.request("DescribeExclusiveInstanceDetail", req, cb);
    }
    async AttachPlugin(req, cb) {
        return this.request("AttachPlugin", req, cb);
    }
    async BindApiApp(req, cb) {
        return this.request("BindApiApp", req, cb);
    }
    async DescribeApiBindApiAppsStatus(req, cb) {
        return this.request("DescribeApiBindApiAppsStatus", req, cb);
    }
    async DescribeLogSearch(req, cb) {
        return this.request("DescribeLogSearch", req, cb);
    }
    async DeleteService(req, cb) {
        return this.request("DeleteService", req, cb);
    }
    async ModifyApiIncrement(req, cb) {
        return this.request("ModifyApiIncrement", req, cb);
    }
    async DescribeApiUsagePlan(req, cb) {
        return this.request("DescribeApiUsagePlan", req, cb);
    }
    async DescribeInstancesNetworkConfig(req, cb) {
        return this.request("DescribeInstancesNetworkConfig", req, cb);
    }
    async DescribeIPStrategysStatus(req, cb) {
        return this.request("DescribeIPStrategysStatus", req, cb);
    }
    async ModifyServiceEnvironmentStrategy(req, cb) {
        return this.request("ModifyServiceEnvironmentStrategy", req, cb);
    }
    async ModifyExclusiveInstance(req, cb) {
        return this.request("ModifyExclusiveInstance", req, cb);
    }
    async CreateUpstream(req, cb) {
        return this.request("CreateUpstream", req, cb);
    }
    async DescribeUsagePlanEnvironments(req, cb) {
        return this.request("DescribeUsagePlanEnvironments", req, cb);
    }
    async DescribePlugin(req, cb) {
        return this.request("DescribePlugin", req, cb);
    }
    async DetachPlugin(req, cb) {
        return this.request("DetachPlugin", req, cb);
    }
    async DescribeServiceEnvironmentList(req, cb) {
        return this.request("DescribeServiceEnvironmentList", req, cb);
    }
    async DeleteUpstream(req, cb) {
        return this.request("DeleteUpstream", req, cb);
    }
    async UnBindSubDomain(req, cb) {
        return this.request("UnBindSubDomain", req, cb);
    }
    async DescribeServiceEnvironmentStrategy(req, cb) {
        return this.request("DescribeServiceEnvironmentStrategy", req, cb);
    }
    async DeleteServiceSubDomainMapping(req, cb) {
        return this.request("DeleteServiceSubDomainMapping", req, cb);
    }
    async DescribeApiKey(req, cb) {
        return this.request("DescribeApiKey", req, cb);
    }
    async DescribeUpstreamBindApis(req, cb) {
        return this.request("DescribeUpstreamBindApis", req, cb);
    }
    async ImportOpenApi(req, cb) {
        return this.request("ImportOpenApi", req, cb);
    }
    async ModifyService(req, cb) {
        return this.request("ModifyService", req, cb);
    }
    async BindSecretIds(req, cb) {
        return this.request("BindSecretIds", req, cb);
    }
    async DescribeApiForApiApp(req, cb) {
        return this.request("DescribeApiForApiApp", req, cb);
    }
    async BuildAPIDoc(req, cb) {
        return this.request("BuildAPIDoc", req, cb);
    }
    async ModifyApiEnvironmentStrategy(req, cb) {
        return this.request("ModifyApiEnvironmentStrategy", req, cb);
    }
    async ModifyApi(req, cb) {
        return this.request("ModifyApi", req, cb);
    }
    async DemoteServiceUsagePlan(req, cb) {
        return this.request("DemoteServiceUsagePlan", req, cb);
    }
    async DescribeServiceSubDomains(req, cb) {
        return this.request("DescribeServiceSubDomains", req, cb);
    }
    async DescribeServiceUsagePlan(req, cb) {
        return this.request("DescribeServiceUsagePlan", req, cb);
    }
    async DescribeExclusiveInstanceRegions(req, cb) {
        return this.request("DescribeExclusiveInstanceRegions", req, cb);
    }
    async UpdateApiKey(req, cb) {
        return this.request("UpdateApiKey", req, cb);
    }
    async DeletePlugin(req, cb) {
        return this.request("DeletePlugin", req, cb);
    }
    async BindIPStrategy(req, cb) {
        return this.request("BindIPStrategy", req, cb);
    }
    async EnableApiKey(req, cb) {
        return this.request("EnableApiKey", req, cb);
    }
    async CreateIPStrategy(req, cb) {
        return this.request("CreateIPStrategy", req, cb);
    }
    async DeleteAPIDoc(req, cb) {
        return this.request("DeleteAPIDoc", req, cb);
    }
    async DescribeExclusiveInstancesStatus(req, cb) {
        return this.request("DescribeExclusiveInstancesStatus", req, cb);
    }
    async UnBindEnvironment(req, cb) {
        return this.request("UnBindEnvironment", req, cb);
    }
    async ModifyApiApp(req, cb) {
        return this.request("ModifyApiApp", req, cb);
    }
    async DescribeUsagePlan(req, cb) {
        return this.request("DescribeUsagePlan", req, cb);
    }
    async BindSubDomain(req, cb) {
        return this.request("BindSubDomain", req, cb);
    }
}
