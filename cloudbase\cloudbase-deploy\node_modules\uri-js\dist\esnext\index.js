import { SCHEMES } from "./uri";
import http from "./schemes/http";
SCHEMES[http.scheme] = http;
import https from "./schemes/https";
SCHEMES[https.scheme] = https;
import ws from "./schemes/ws";
SCHEMES[ws.scheme] = ws;
import wss from "./schemes/wss";
SCHEMES[wss.scheme] = wss;
import mailto from "./schemes/mailto";
SCHEMES[mailto.scheme] = mailto;
import urn from "./schemes/urn";
SCHEMES[urn.scheme] = urn;
import uuid from "./schemes/urn-uuid";
SCHEMES[uuid.scheme] = uuid;
export * from "./uri";
//# sourceMappingURL=index.js.map