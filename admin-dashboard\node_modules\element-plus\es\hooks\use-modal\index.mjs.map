{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-modal/index.ts"], "sourcesContent": ["import { watch } from 'vue'\nimport { isClient, useEventListener } from '@vueuse/core'\nimport { EVENT_CODE } from '@element-plus/constants'\n\nimport type { Ref } from 'vue'\n\ntype ModalInstance = {\n  handleClose: () => void\n}\n\nconst modalStack: ModalInstance[] = []\n\nconst closeModal = (e: KeyboardEvent) => {\n  if (modalStack.length === 0) return\n  if (e.code === EVENT_CODE.esc) {\n    e.stopPropagation()\n    const topModal = modalStack[modalStack.length - 1]\n    topModal.handleClose()\n  }\n}\n\nexport const useModal = (instance: ModalInstance, visibleRef: Ref<boolean>) => {\n  watch(visibleRef, (val) => {\n    if (val) {\n      modalStack.push(instance)\n    } else {\n      modalStack.splice(modalStack.indexOf(instance), 1)\n    }\n  })\n}\n\nif (isClient) useEventListener(document, 'keydown', closeModal)\n"], "names": [], "mappings": ";;;;AAGA,MAAM,UAAU,GAAG,EAAE,CAAC;AACtB,MAAM,UAAU,GAAG,CAAC,CAAC,KAAK;AAC1B,EAAE,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC;AAC7B,IAAI,OAAO;AACX,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,GAAG,EAAE;AACjC,IAAI,CAAC,CAAC,eAAe,EAAE,CAAC;AACxB,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACvD,IAAI,QAAQ,CAAC,WAAW,EAAE,CAAC;AAC3B,GAAG;AACH,CAAC,CAAC;AACU,MAAC,QAAQ,GAAG,CAAC,QAAQ,EAAE,UAAU,KAAK;AAClD,EAAE,KAAK,CAAC,UAAU,EAAE,CAAC,GAAG,KAAK;AAC7B,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAChC,KAAK,MAAM;AACX,MAAM,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC;AACzD,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE;AACF,IAAI,QAAQ;AACZ,EAAE,gBAAgB,CAAC,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;;;;"}