{"version": 3, "file": "hi.min.js", "sources": ["../../../../packages/locale/lang/hi.ts"], "sourcesContent": ["export default {\n  name: 'hi',\n  el: {\n    breadcrumb: {\n      label: 'ब्रेडक्रंब',\n    },\n    colorpicker: {\n      confirm: 'ठीक है',\n      clear: 'हटाएँ',\n      defaultLabel: 'कलर पिकर',\n      description:\n        'मौजूदा रंग {color} है. कोई नया रंग चुनने के लिए एंटर दबाएँ.',\n      alphaLabel: 'अल्फा मान चुनें',\n    },\n    datepicker: {\n      now: 'अभी',\n      today: 'आज',\n      cancel: 'कैंसिल करें',\n      clear: 'हटाएँ',\n      confirm: 'ठीक है',\n      dateTablePrompt:\n        'महीने का दिन चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें',\n      monthTablePrompt:\n        'महीने चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें ',\n      yearTablePrompt:\n        'साल चुनने के लिए एरो कुंजी का इस्तेमाल करें और एंटर करें',\n      selectedDate: 'चुनी गई तारीख',\n      selectDate: 'तारीख चुनें',\n      selectTime: 'समय चुनें',\n      startDate: 'शुरू होने की तारीख',\n      startTime: 'शुरू होने का समय',\n      endDate: 'खत्म होने की तारीख',\n      endTime: 'खत्म होने का समय',\n      prevYear: 'पिछला साल',\n      nextYear: 'अगला साल',\n      prevMonth: 'पिछला महीना',\n      nextMonth: 'अगला महीना',\n      year: '',\n      month1: 'जनवरी',\n      month2: 'फरवरी',\n      month3: 'मार्च',\n      month4: 'अप्रैल',\n      month5: 'मई',\n      month6: 'जून',\n      month7: 'जुलाई',\n      month8: 'अगस्त',\n      month9: 'सितंबर',\n      month10: 'अक्टूबर',\n      month11: 'नवंबर',\n      month12: 'दिसंबर',\n      week: 'सप्ताह',\n      weeks: {\n        sun: 'रवि',\n        mon: 'सोम',\n        tue: 'मंगल',\n        wed: 'बुध',\n        thu: 'गुरु',\n        fri: 'शुक्र',\n        sat: 'शनि',\n      },\n      weeksFull: {\n        sun: 'रविवार',\n        mon: 'सोमवार',\n        tue: 'मंगलवार',\n        wed: 'बुधवार',\n        thu: 'गुरुवार',\n        fri: 'शुक्रवार',\n        sat: 'शनिवार',\n      },\n      months: {\n        jan: 'जन.',\n        feb: 'फर.',\n        mar: 'मार्च',\n        apr: 'अप्रैल',\n        may: 'मई',\n        jun: 'जून',\n        jul: 'जुलाई',\n        aug: 'अग.',\n        sep: 'सितं.',\n        oct: 'अक्तू.',\n        nov: 'नवं.',\n        dec: 'दिसं.',\n      },\n    },\n    inputNumber: {\n      decrease: 'संख्या घटाएँ',\n      increase: 'संख्या बढ़ाएँ',\n    },\n    select: {\n      loading: 'लोड हो रहा है',\n      noMatch: 'कोई मैचिंग डेटा नहीं है',\n      noData: 'कोई डेटा नहीं है',\n      placeholder: 'चुनें',\n    },\n    mention: {\n      loading: 'लोड हो रहा है',\n    },\n    dropdown: {\n      toggleDropdown: 'ड्रॉपडाउन को टॉगल करें',\n    },\n    cascader: {\n      noMatch: 'कोई मैचिंग डेटा नहीं है',\n      loading: 'लोड हो रहा है',\n      placeholder: 'चुनें',\n      noData: 'कोई डेटा नहीं है',\n    },\n    pagination: {\n      goto: 'पर जाएँ',\n      pagesize: '/पेज',\n      total: 'कुल {total}',\n      pageClassifier: '',\n      page: 'पेज',\n      prev: 'पिछले पेज पर जाएँ',\n      next: 'अगले पेज पर जाएँ',\n      currentPage: 'पेज {pager}',\n      prevPages: 'पिछले {pager} पेज',\n      nextPages: 'अगले {pager} पेज',\n      deprecationWarning:\n        'पुरानी पद्धति के उपयोग का पता चला, अधिक जानकारी के लिए एल-पेजिनेशन का डॉक्यूमेंटेशन देखें',\n    },\n    dialog: {\n      close: 'यह डायलॉग बंद करें',\n    },\n    drawer: {\n      close: 'यह डायलॉग बंद करें',\n    },\n    messagebox: {\n      title: 'मैसेज',\n      confirm: 'ठीक है',\n      cancel: 'कैंसिल करें',\n      error: 'अवैध इनपुट',\n      close: 'यह डायलॉग बंद करें',\n    },\n    upload: {\n      deleteTip: 'हटाने के लिए डिलीट दबाएँ',\n      delete: 'हटाएँ',\n      preview: 'प्रीव्यू',\n      continue: 'जारी रखें',\n    },\n    slider: {\n      defaultLabel: '{min} और {max} के बीच स्लाइडर',\n      defaultRangeStartLabel: 'शुरूआती वैल्यू चुनें',\n      defaultRangeEndLabel: 'समाप्ति की वैल्यू चुनें',\n    },\n    table: {\n      emptyText: 'कोई डेटा नहीं है',\n      confirmFilter: 'पुष्टि करें',\n      resetFilter: 'रीसेट करें',\n      clearFilter: 'सभी',\n      sumText: 'जोड़े',\n    },\n    tour: {\n      next: 'अगला',\n      previous: 'पिछला',\n      finish: 'पूरा करें',\n    },\n    tree: {\n      emptyText: 'कोई डेटा नहीं है',\n    },\n    transfer: {\n      noMatch: 'कोई मैचिंग डेटा नहीं है',\n      noData: 'कोई डेटा नहीं है',\n      titles: ['लिस्ट 1', 'लिस्ट 2'],\n      filterPlaceholder: 'कीवर्ड डालें',\n      noCheckedFormat: '{total} आइटम',\n      hasCheckedFormat: '{checked}/{total} चेक किया गया',\n    },\n    image: {\n      error: 'नहीं हो सका',\n    },\n    pageHeader: {\n      title: 'पीछे जाएँ ',\n    },\n    popconfirm: {\n      confirmButtonText: 'हाँ',\n      cancelButtonText: 'नहीं',\n    },\n    carousel: {\n      leftArrow: 'कैरोसेल तीर बाएँ',\n      rightArrow: 'कैरोसेल तीर दाएँ',\n      indicator: 'कैरोसेल इंडेक्स {index} पर स्विच करें',\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA,WAAc,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,iCAAiC,CAAC,KAAK,CAAC,gCAAgC,CAAC,YAAY,CAAC,6CAA6C,CAAC,WAAW,CAAC,gQAAgQ,CAAC,UAAU,CAAC,kFAAkF,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,CAAC,+DAA+D,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,iCAAiC,CAAC,eAAe,CAAC,uUAAuU,CAAC,gBAAgB,CAAC,wSAAwS,CAAC,eAAe,CAAC,2RAA2R,CAAC,YAAY,CAAC,sEAAsE,CAAC,UAAU,CAAC,+DAA+D,CAAC,UAAU,CAAC,mDAAmD,CAAC,SAAS,CAAC,+FAA+F,CAAC,SAAS,CAAC,mFAAmF,CAAC,OAAO,CAAC,+FAA+F,CAAC,OAAO,CAAC,mFAAmF,CAAC,QAAQ,CAAC,mDAAmD,CAAC,QAAQ,CAAC,6CAA6C,CAAC,SAAS,CAAC,+DAA+D,CAAC,SAAS,CAAC,yDAAyD,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,gCAAgC,CAAC,MAAM,CAAC,sCAAsC,CAAC,OAAO,CAAC,4CAA4C,CAAC,OAAO,CAAC,gCAAgC,CAAC,OAAO,CAAC,sCAAsC,CAAC,IAAI,CAAC,sCAAsC,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,0BAA0B,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,4CAA4C,CAAC,GAAG,CAAC,kDAAkD,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,sCAAsC,CAAC,GAAG,CAAC,cAAc,CAAC,GAAG,CAAC,oBAAoB,CAAC,GAAG,CAAC,gCAAgC,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,2BAA2B,CAAC,GAAG,CAAC,iCAAiC,CAAC,GAAG,CAAC,qBAAqB,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,qEAAqE,CAAC,QAAQ,CAAC,2EAA2E,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,iEAAiE,CAAC,OAAO,CAAC,wHAAwH,CAAC,MAAM,CAAC,mFAAmF,CAAC,WAAW,CAAC,gCAAgC,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,iEAAiE,CAAC,CAAC,QAAQ,CAAC,CAAC,cAAc,CAAC,uHAAuH,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wHAAwH,CAAC,OAAO,CAAC,iEAAiE,CAAC,WAAW,CAAC,gCAAgC,CAAC,MAAM,CAAC,mFAAmF,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,uCAAuC,CAAC,QAAQ,CAAC,qBAAqB,CAAC,KAAK,CAAC,4BAA4B,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,yFAAyF,CAAC,IAAI,CAAC,mFAAmF,CAAC,WAAW,CAAC,4BAA4B,CAAC,SAAS,CAAC,2DAA2D,CAAC,SAAS,CAAC,qDAAqD,CAAC,kBAAkB,CAAC,wcAAwc,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,+FAA+F,CAAC,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,+FAA+F,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC,OAAO,CAAC,iCAAiC,CAAC,MAAM,CAAC,+DAA+D,CAAC,KAAK,CAAC,yDAAyD,CAAC,KAAK,CAAC,+FAA+F,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,8HAA8H,CAAC,MAAM,CAAC,gCAAgC,CAAC,OAAO,CAAC,kDAAkD,CAAC,QAAQ,CAAC,mDAAmD,CAAC,CAAC,MAAM,CAAC,CAAC,YAAY,CAAC,qGAAqG,CAAC,sBAAsB,CAAC,gHAAgH,CAAC,oBAAoB,CAAC,6HAA6H,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,mFAAmF,CAAC,aAAa,CAAC,+DAA+D,CAAC,WAAW,CAAC,yDAAyD,CAAC,WAAW,CAAC,oBAAoB,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,gCAAgC,CAAC,MAAM,CAAC,mDAAmD,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,mFAAmF,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,wHAAwH,CAAC,MAAM,CAAC,mFAAmF,CAAC,MAAM,CAAC,CAAC,kCAAkC,CAAC,kCAAkC,CAAC,CAAC,iBAAiB,CAAC,qEAAqE,CAAC,eAAe,CAAC,kCAAkC,CAAC,gBAAgB,CAAC,kFAAkF,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,0DAA0D,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,oDAAoD,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,0BAA0B,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,wFAAwF,CAAC,UAAU,CAAC,wFAAwF,CAAC,SAAS,CAAC,oKAAoK,CAAC,CAAC,CAAC;;;;;;;;"}