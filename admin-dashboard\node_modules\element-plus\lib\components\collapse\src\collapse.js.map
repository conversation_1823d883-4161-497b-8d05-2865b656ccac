{"version": 3, "file": "collapse.js", "sources": ["../../../../../../packages/components/collapse/src/collapse.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isArray,\n  isNumber,\n  isString,\n  mutable,\n} from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { Arrayable, Awaitable } from '@element-plus/utils'\n\nexport type CollapseActiveName = string | number\nexport type CollapseModelValue = Arrayable<CollapseActiveName>\n\nexport type CollapseIconPositionType = 'left' | 'right'\n\nexport const emitChangeFn = (value: CollapseModelValue) =>\n  isNumber(value) || isString(value) || isArray(value)\n\nexport const collapseProps = buildProps({\n  /**\n   * @description whether to activate accordion mode\n   */\n  accordion: Boolean,\n  /**\n   * @description currently active panel, the type is `string` in accordion mode, otherwise it is `array`\n   */\n  modelValue: {\n    type: definePropType<CollapseModelValue>([Array, String, Number]),\n    default: () => mutable([] as const),\n  },\n  /**\n   * @description set expand icon position\n   */\n  expandIconPosition: {\n    type: definePropType<CollapseIconPositionType>([String]),\n    default: 'right',\n  },\n  /**\n   * @description before-collapse hook before the collapse state changes. If `false` is returned or a `Promise` is returned and then is rejected, will stop collapsing\n   */\n  beforeCollapse: {\n    type: definePropType<(name: CollapseActiveName) => Awaitable<boolean>>(\n      Function\n    ),\n  },\n} as const)\nexport type CollapseProps = ExtractPropTypes<typeof collapseProps>\nexport type CollapsePropsPublic = __ExtractPublicPropTypes<typeof collapseProps>\n\nexport const collapseEmits = {\n  [UPDATE_MODEL_EVENT]: emitChangeFn,\n  [CHANGE_EVENT]: emitChangeFn,\n}\nexport type CollapseEmits = typeof collapseEmits\n"], "names": ["isNumber", "isString", "isArray", "buildProps", "definePropType", "mutable", "UPDATE_MODEL_EVENT", "CHANGE_EVENT"], "mappings": ";;;;;;;;;;AASY,MAAC,YAAY,GAAG,CAAC,KAAK,KAAKA,cAAQ,CAAC,KAAK,CAAC,IAAIC,eAAQ,CAAC,KAAK,CAAC,IAAIC,cAAO,CAAC,KAAK,EAAE;AAChF,MAAC,aAAa,GAAGC,kBAAU,CAAC;AACxC,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,MAAMC,kBAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,EAAE,kBAAkB,EAAE;AACtB,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,CAAC,CAAC;AAClC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,cAAc,EAAE;AAClB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,CAACE,wBAAkB,GAAG,YAAY;AACpC,EAAE,CAACC,kBAAY,GAAG,YAAY;AAC9B;;;;;;"}