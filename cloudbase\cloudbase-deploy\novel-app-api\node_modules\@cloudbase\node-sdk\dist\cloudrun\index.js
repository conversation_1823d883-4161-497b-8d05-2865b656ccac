"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.callContainer = void 0;
const tcbopenapirequester = __importStar(require("../utils/tcbopenapirequester"));
const utils_1 = require("../utils/utils");
const code_1 = require("../const/code");
async function callContainer(cloudbase, callContainerOptions, opts) {
    // 这里先不对齐了，代码先保留
    // if (callContainerOptions.header && callContainerOptions.header['X-WX-SERVICE'] !== '') {
    //   if (!callContainerOptions.name) {
    //     callContainerOptions.name = callContainerOptions.header['X-WX-SERVICE']
    //   }
    //   if (callContainerOptions.header['X-WX-SERVICE'] !== callContainerOptions.name) {
    //     throw E({
    //       ...ERROR.INVALID_PARAM,
    //       message: '服务名冲突'
    //     })
    //   }
    // }
    const { name, data } = callContainerOptions;
    if (!name) {
        throw (0, utils_1.E)(Object.assign(Object.assign({}, code_1.ERROR.INVALID_PARAM), { message: '服务名不能为空' }));
    }
    return await tcbopenapirequester.request({
        cloudrun: { name },
        config: cloudbase.config,
        method: callContainerOptions.method || 'POST',
        path: callContainerOptions.path || '',
        headers: Object.assign({}, {
            'Content-Type': 'application/json; charset=utf-8'
        }, callContainerOptions.header),
        data,
        opts
    }).then(resp => {
        try {
            resp.data = typeof resp.body === 'string' ? JSON.parse(resp.body) : resp.body;
        }
        catch (e) {
            // ignore
        }
        return {
            requestId: resp.headers['x-request-id'] || resp.headers['x-cloudbase-request-id'],
            statusCode: resp.statusCode,
            header: resp.headers,
            data: resp.data
        };
    });
}
exports.callContainer = callContainer;
