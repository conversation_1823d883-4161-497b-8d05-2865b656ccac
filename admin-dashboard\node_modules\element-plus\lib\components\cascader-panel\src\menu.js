'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var index$3 = require('../../scrollbar/index.js');
var iconsVue = require('@element-plus/icons-vue');
var index$4 = require('../../icon/index.js');
var node = require('./node2.js');
var types = require('./types.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');
var index = require('../../../hooks/use-namespace/index.js');
var index$1 = require('../../../hooks/use-locale/index.js');
var index$2 = require('../../../hooks/use-id/index.js');

const __default__ = vue.defineComponent({
  name: "ElCascaderMenu"
});
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...__default__,
  props: {
    nodes: {
      type: Array,
      required: true
    },
    index: {
      type: Number,
      required: true
    }
  },
  setup(__props) {
    const props = __props;
    const instance = vue.getCurrentInstance();
    const ns = index.useNamespace("cascader-menu");
    const { t } = index$1.useLocale();
    const id = index$2.useId();
    let activeNode;
    let hoverTimer;
    const panel = vue.inject(types.CASCADER_PANEL_INJECTION_KEY);
    const hoverZone = vue.ref();
    const isEmpty = vue.computed(() => !props.nodes.length);
    const isLoading = vue.computed(() => !panel.initialLoaded);
    const menuId = vue.computed(() => `${id.value}-${props.index}`);
    const handleExpand = (e) => {
      activeNode = e.target;
    };
    const handleMouseMove = (e) => {
      if (!panel.isHoverMenu || !activeNode || !hoverZone.value)
        return;
      if (activeNode.contains(e.target)) {
        clearHoverTimer();
        const el = instance.vnode.el;
        const { left } = el.getBoundingClientRect();
        const { offsetWidth, offsetHeight } = el;
        const startX = e.clientX - left;
        const top = activeNode.offsetTop;
        const bottom = top + activeNode.offsetHeight;
        hoverZone.value.innerHTML = `
          <path style="pointer-events: auto;" fill="transparent" d="M${startX} ${top} L${offsetWidth} 0 V${top} Z" />
          <path style="pointer-events: auto;" fill="transparent" d="M${startX} ${bottom} L${offsetWidth} ${offsetHeight} V${bottom} Z" />
        `;
      } else if (!hoverTimer) {
        hoverTimer = window.setTimeout(clearHoverZone, panel.config.hoverThreshold);
      }
    };
    const clearHoverTimer = () => {
      if (!hoverTimer)
        return;
      clearTimeout(hoverTimer);
      hoverTimer = void 0;
    };
    const clearHoverZone = () => {
      if (!hoverZone.value)
        return;
      hoverZone.value.innerHTML = "";
      clearHoverTimer();
    };
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createBlock(vue.unref(index$3.ElScrollbar), {
        key: vue.unref(menuId),
        tag: "ul",
        role: "menu",
        class: vue.normalizeClass(vue.unref(ns).b()),
        "wrap-class": vue.unref(ns).e("wrap"),
        "view-class": [vue.unref(ns).e("list"), vue.unref(ns).is("empty", vue.unref(isEmpty))],
        onMousemove: handleMouseMove,
        onMouseleave: clearHoverZone
      }, {
        default: vue.withCtx(() => {
          var _a;
          return [
            (vue.openBlock(true), vue.createElementBlock(vue.Fragment, null, vue.renderList(__props.nodes, (node$1) => {
              return vue.openBlock(), vue.createBlock(node["default"], {
                key: node$1.uid,
                node: node$1,
                "menu-id": vue.unref(menuId),
                onExpand: handleExpand
              }, null, 8, ["node", "menu-id"]);
            }), 128)),
            vue.unref(isLoading) ? (vue.openBlock(), vue.createElementBlock("div", {
              key: 0,
              class: vue.normalizeClass(vue.unref(ns).e("empty-text"))
            }, [
              vue.createVNode(vue.unref(index$4.ElIcon), {
                size: "14",
                class: vue.normalizeClass(vue.unref(ns).is("loading"))
              }, {
                default: vue.withCtx(() => [
                  vue.createVNode(vue.unref(iconsVue.Loading))
                ]),
                _: 1
              }, 8, ["class"]),
              vue.createTextVNode(" " + vue.toDisplayString(vue.unref(t)("el.cascader.loading")), 1)
            ], 2)) : vue.unref(isEmpty) ? (vue.openBlock(), vue.createElementBlock("div", {
              key: 1,
              class: vue.normalizeClass(vue.unref(ns).e("empty-text"))
            }, [
              vue.renderSlot(_ctx.$slots, "empty", {}, () => [
                vue.createTextVNode(vue.toDisplayString(vue.unref(t)("el.cascader.noData")), 1)
              ])
            ], 2)) : ((_a = vue.unref(panel)) == null ? void 0 : _a.isHoverMenu) ? (vue.openBlock(), vue.createElementBlock(vue.Fragment, { key: 2 }, [
              vue.createCommentVNode(" eslint-disable-next-line vue/html-self-closing "),
              (vue.openBlock(), vue.createElementBlock("svg", {
                ref_key: "hoverZone",
                ref: hoverZone,
                class: vue.normalizeClass(vue.unref(ns).e("hover-zone"))
              }, null, 2))
            ], 2112)) : vue.createCommentVNode("v-if", true)
          ];
        }),
        _: 3
      }, 8, ["class", "wrap-class", "view-class"]);
    };
  }
});
var ElCascaderMenu = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "menu.vue"]]);

exports["default"] = ElCascaderMenu;
//# sourceMappingURL=menu.js.map
