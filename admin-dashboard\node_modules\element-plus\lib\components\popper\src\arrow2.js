'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
var constants = require('./constants.js');
var pluginVue_exportHelper = require('../../../_virtual/plugin-vue_export-helper.js');
var index = require('../../../hooks/use-namespace/index.js');

const __default__ = vue.defineComponent({
  name: "ElPopperArrow",
  inheritAttrs: false
});
const _sfc_main = /* @__PURE__ */ vue.defineComponent({
  ...__default__,
  setup(__props, { expose }) {
    const ns = index.useNamespace("popper");
    const { arrowRef, arrowStyle } = vue.inject(constants.POPPER_CONTENT_INJECTION_KEY, void 0);
    vue.onBeforeUnmount(() => {
      arrowRef.value = void 0;
    });
    expose({
      arrowRef
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("span", {
        ref_key: "arrowRef",
        ref: arrowRef,
        class: vue.normalizeClass(vue.unref(ns).e("arrow")),
        style: vue.normalizeStyle(vue.unref(arrowStyle)),
        "data-popper-arrow": ""
      }, null, 6);
    };
  }
});
var ElPopperArrow = /* @__PURE__ */ pluginVue_exportHelper["default"](_sfc_main, [["__file", "arrow.vue"]]);

exports["default"] = ElPopperArrow;
//# sourceMappingURL=arrow2.js.map
