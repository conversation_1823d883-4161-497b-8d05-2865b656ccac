{"name": "bson", "description": "A bson parser for node.js and the browser", "keywords": ["mongodb", "bson", "parser"], "files": ["lib", "src", "dist", "bson.d.ts", "etc/prepare.js", "bower.json"], "types": "bson.d.ts", "version": "4.7.2", "author": {"name": "The MongoDB NodeJS Team", "email": "<EMAIL>"}, "license": "Apache-2.0", "contributors": [], "repository": "mongodb/js-bson", "bugs": {"url": "https://jira.mongodb.org/projects/NODE/issues/"}, "devDependencies": {"@babel/plugin-external-helpers": "^7.10.4", "@babel/preset-env": "^7.11.0", "@istanbuljs/nyc-config-typescript": "^1.0.1", "@microsoft/api-extractor": "^7.28.0", "@rollup/plugin-babel": "^5.2.0", "@rollup/plugin-commonjs": "^15.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^9.0.0", "@rollup/plugin-replace": "^4.0.0", "@rollup/plugin-typescript": "^6.0.0", "@types/node": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.30.0", "@typescript-eslint/parser": "^5.30.0", "array-includes": "^3.1.3", "array.prototype.flatmap": "^1.3.0", "benchmark": "^2.1.4", "chai": "^4.2.0", "eslint": "^8.18.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.1.0", "eslint-plugin-tsdoc": "^0.2.16", "karma": "^6.3.4", "karma-chai": "^0.1.0", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-rollup-preprocessor": "^7.0.5", "mocha": "5.2.0", "node-fetch": "^2.6.1", "nyc": "^15.1.0", "object.entries": "^1.1.5", "prettier": "^2.7.1", "rimraf": "^3.0.2", "rollup": "^2.26.5", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-globals": "^1.4.0", "rollup-plugin-node-polyfills": "^0.2.1", "rollup-plugin-polyfill-node": "^0.7.0", "standard-version": "^9.5.0", "ts-node": "^9.0.0", "tsd": "^0.21.0", "typescript": "^4.7.4", "typescript-cached-transpile": "0.0.6", "uuid": "^8.3.2"}, "tsd": {"directory": "test/types", "compilerOptions": {"strict": true, "target": "esnext", "module": "commonjs", "moduleResolution": "node"}}, "config": {"native": false}, "main": "lib/bson.js", "module": "dist/bson.esm.js", "browser": {"./lib/bson.js": "./dist/bson.browser.umd.js", "./dist/bson.esm.js": "./dist/bson.browser.esm.js"}, "engines": {"node": ">=6.9.0"}, "scripts": {"docs": "typedoc", "test": "npm run build && npm run test-node && npm run test-browser", "test-node": "mocha test/node test/*_tests.js", "test-tsd": "npm run build:dts && tsd", "test-browser": "node --max-old-space-size=4096 ./node_modules/.bin/karma start karma.conf.js", "build:ts": "tsc", "build:dts": "npm run build:ts && api-extractor run --typescript-compiler-folder node_modules/typescript --local && rimraf 'lib/**/*.d.ts*'", "build:bundle": "rollup -c rollup.config.js", "build": "npm run build:dts && npm run build:bundle", "lint": "eslint -v && eslint --ext '.js,.ts' --max-warnings=0 src test && tsc -v && tsc --noEmit && npm run test-tsd", "format": "eslint --ext '.js,.ts' src test --fix", "coverage": "nyc npm run test-node", "coverage:html": "npm run coverage && open ./coverage/index.html", "prepare": "node etc/prepare.js", "release": "standard-version -i HISTORY.md"}, "dependencies": {"buffer": "^5.6.0"}}