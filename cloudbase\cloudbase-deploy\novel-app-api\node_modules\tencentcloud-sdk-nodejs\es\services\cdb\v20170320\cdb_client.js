import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cdb.tencentcloudapi.com", "2017-03-20", clientConfig);
    }
    async DescribeDBInstanceGTID(req, cb) {
        return this.request("DescribeDBInstanceGTID", req, cb);
    }
    async CreateRoInstanceIp(req, cb) {
        return this.request("CreateRoInstanceIp", req, cb);
    }
    async CreateAuditPolicy(req, cb) {
        return this.request("CreateAuditPolicy", req, cb);
    }
    async DescribeTableColumns(req, cb) {
        return this.request("DescribeTableColumns", req, cb);
    }
    async OpenSSL(req, cb) {
        return this.request("OpenSSL", req, cb);
    }
    async DescribeDBFeatures(req, cb) {
        return this.request("DescribeDBFeatures", req, cb);
    }
    async CreateAuditRuleTemplate(req, cb) {
        return this.request("CreateAuditRuleTemplate", req, cb);
    }
    async BalanceRoGroupLoad(req, cb) {
        return this.request("BalanceRoGroupLoad", req, cb);
    }
    async IsolateDBInstance(req, cb) {
        return this.request("IsolateDBInstance", req, cb);
    }
    async ModifyRoGroupVipVport(req, cb) {
        return this.request("ModifyRoGroupVipVport", req, cb);
    }
    async RestartDBInstances(req, cb) {
        return this.request("RestartDBInstances", req, cb);
    }
    async ModifyInstanceTag(req, cb) {
        return this.request("ModifyInstanceTag", req, cb);
    }
    async DescribeTimeWindow(req, cb) {
        return this.request("DescribeTimeWindow", req, cb);
    }
    async DescribeInstanceUpgradeCheckJob(req, cb) {
        return this.request("DescribeInstanceUpgradeCheckJob", req, cb);
    }
    async DescribeBackupOverview(req, cb) {
        return this.request("DescribeBackupOverview", req, cb);
    }
    async StopReplication(req, cb) {
        return this.request("StopReplication", req, cb);
    }
    async ModifyDBInstanceLogToCLS(req, cb) {
        return this.request("ModifyDBInstanceLogToCLS", req, cb);
    }
    async StopCpuExpand(req, cb) {
        return this.request("StopCpuExpand", req, cb);
    }
    async ModifyDBInstanceName(req, cb) {
        return this.request("ModifyDBInstanceName", req, cb);
    }
    async DescribeCdbZoneConfig(req, cb) {
        return this.request("DescribeCdbZoneConfig", req, cb);
    }
    async CloseAuditService(req, cb) {
        return this.request("CloseAuditService", req, cb);
    }
    async StopRollback(req, cb) {
        return this.request("StopRollback", req, cb);
    }
    async DescribeInstanceUpgradeType(req, cb) {
        return this.request("DescribeInstanceUpgradeType", req, cb);
    }
    async OfflineIsolatedInstances(req, cb) {
        return this.request("OfflineIsolatedInstances", req, cb);
    }
    async CreateAuditLogFile(req, cb) {
        return this.request("CreateAuditLogFile", req, cb);
    }
    async OpenDBInstanceGTID(req, cb) {
        return this.request("OpenDBInstanceGTID", req, cb);
    }
    async VerifyRootAccount(req, cb) {
        return this.request("VerifyRootAccount", req, cb);
    }
    async DescribeRollbackTaskDetail(req, cb) {
        return this.request("DescribeRollbackTaskDetail", req, cb);
    }
    async CheckMigrateCluster(req, cb) {
        return this.request("CheckMigrateCluster", req, cb);
    }
    async ModifyBackupDownloadRestriction(req, cb) {
        return this.request("ModifyBackupDownloadRestriction", req, cb);
    }
    async DescribeParamTemplates(req, cb) {
        return this.request("DescribeParamTemplates", req, cb);
    }
    async DescribeTasks(req, cb) {
        return this.request("DescribeTasks", req, cb);
    }
    async DescribeBackupConfig(req, cb) {
        return this.request("DescribeBackupConfig", req, cb);
    }
    async OpenDBInstanceEncryption(req, cb) {
        return this.request("OpenDBInstanceEncryption", req, cb);
    }
    async CloseWanService(req, cb) {
        return this.request("CloseWanService", req, cb);
    }
    async DescribeDefaultParams(req, cb) {
        return this.request("DescribeDefaultParams", req, cb);
    }
    async DescribeAuditPolicies(req, cb) {
        return this.request("DescribeAuditPolicies", req, cb);
    }
    async DeleteDatabase(req, cb) {
        return this.request("DeleteDatabase", req, cb);
    }
    async DescribeTagsOfInstanceIds(req, cb) {
        return this.request("DescribeTagsOfInstanceIds", req, cb);
    }
    async DescribeDatabases(req, cb) {
        return this.request("DescribeDatabases", req, cb);
    }
    async DescribeErrorLogData(req, cb) {
        return this.request("DescribeErrorLogData", req, cb);
    }
    async SwitchDBInstanceMasterSlave(req, cb) {
        return this.request("SwitchDBInstanceMasterSlave", req, cb);
    }
    async CreateDatabase(req, cb) {
        return this.request("CreateDatabase", req, cb);
    }
    async DescribeCPUExpandStrategyInfo(req, cb) {
        return this.request("DescribeCPUExpandStrategyInfo", req, cb);
    }
    async DisassociateSecurityGroups(req, cb) {
        return this.request("DisassociateSecurityGroups", req, cb);
    }
    async RenewDBInstance(req, cb) {
        return this.request("RenewDBInstance", req, cb);
    }
    async DescribeTables(req, cb) {
        return this.request("DescribeTables", req, cb);
    }
    async DescribeAccountPrivileges(req, cb) {
        return this.request("DescribeAccountPrivileges", req, cb);
    }
    async DescribeDataBackupOverview(req, cb) {
        return this.request("DescribeDataBackupOverview", req, cb);
    }
    async ReleaseIsolatedDBInstances(req, cb) {
        return this.request("ReleaseIsolatedDBInstances", req, cb);
    }
    async CreateCloneInstance(req, cb) {
        return this.request("CreateCloneInstance", req, cb);
    }
    async ModifyAuditConfig(req, cb) {
        return this.request("ModifyAuditConfig", req, cb);
    }
    async ModifyInstancePasswordComplexity(req, cb) {
        return this.request("ModifyInstancePasswordComplexity", req, cb);
    }
    async ModifyTimeWindow(req, cb) {
        return this.request("ModifyTimeWindow", req, cb);
    }
    async DeleteDeployGroups(req, cb) {
        return this.request("DeleteDeployGroups", req, cb);
    }
    async SwitchForUpgrade(req, cb) {
        return this.request("SwitchForUpgrade", req, cb);
    }
    async DeleteAuditRuleTemplates(req, cb) {
        return this.request("DeleteAuditRuleTemplates", req, cb);
    }
    async DescribeBackups(req, cb) {
        return this.request("DescribeBackups", req, cb);
    }
    async DescribeCdbProxyInfo(req, cb) {
        return this.request("DescribeCdbProxyInfo", req, cb);
    }
    async CreateParamTemplate(req, cb) {
        return this.request("CreateParamTemplate", req, cb);
    }
    async CreateDBInstanceHour(req, cb) {
        return this.request("CreateDBInstanceHour", req, cb);
    }
    async AddTimeWindow(req, cb) {
        return this.request("AddTimeWindow", req, cb);
    }
    async DescribeProxySupportParam(req, cb) {
        return this.request("DescribeProxySupportParam", req, cb);
    }
    async DescribeLocalBinlogConfig(req, cb) {
        return this.request("DescribeLocalBinlogConfig", req, cb);
    }
    async CreateBackup(req, cb) {
        return this.request("CreateBackup", req, cb);
    }
    async ModifyDBInstanceVipVport(req, cb) {
        return this.request("ModifyDBInstanceVipVport", req, cb);
    }
    async DescribeDBInstanceConfig(req, cb) {
        return this.request("DescribeDBInstanceConfig", req, cb);
    }
    async CreateCdbProxyAddress(req, cb) {
        return this.request("CreateCdbProxyAddress", req, cb);
    }
    async DeleteAuditPolicy(req, cb) {
        return this.request("DeleteAuditPolicy", req, cb);
    }
    async DescribeProjectSecurityGroups(req, cb) {
        return this.request("DescribeProjectSecurityGroups", req, cb);
    }
    async DescribeSlowLogs(req, cb) {
        return this.request("DescribeSlowLogs", req, cb);
    }
    async InquiryPriceUpgradeInstances(req, cb) {
        return this.request("InquiryPriceUpgradeInstances", req, cb);
    }
    async ModifyLocalBinlogConfig(req, cb) {
        return this.request("ModifyLocalBinlogConfig", req, cb);
    }
    async CreateDBInstance(req, cb) {
        return this.request("CreateDBInstance", req, cb);
    }
    async ModifyParamTemplate(req, cb) {
        return this.request("ModifyParamTemplate", req, cb);
    }
    async DescribeInstanceParams(req, cb) {
        return this.request("DescribeInstanceParams", req, cb);
    }
    async DescribeBackupEncryptionStatus(req, cb) {
        return this.request("DescribeBackupEncryptionStatus", req, cb);
    }
    async DescribeProxyCustomConf(req, cb) {
        return this.request("DescribeProxyCustomConf", req, cb);
    }
    async DescribeDeployGroupList(req, cb) {
        return this.request("DescribeDeployGroupList", req, cb);
    }
    async StopDBImportJob(req, cb) {
        return this.request("StopDBImportJob", req, cb);
    }
    async AnalyzeAuditLogs(req, cb) {
        return this.request("AnalyzeAuditLogs", req, cb);
    }
    async CreateAccounts(req, cb) {
        return this.request("CreateAccounts", req, cb);
    }
    async AdjustCdbProxy(req, cb) {
        return this.request("AdjustCdbProxy", req, cb);
    }
    async ModifyProtectMode(req, cb) {
        return this.request("ModifyProtectMode", req, cb);
    }
    async UpgradeDBInstanceEngineVersion(req, cb) {
        return this.request("UpgradeDBInstanceEngineVersion", req, cb);
    }
    async DescribeAuditLogFiles(req, cb) {
        return this.request("DescribeAuditLogFiles", req, cb);
    }
    async DescribeBackupDecryptionKey(req, cb) {
        return this.request("DescribeBackupDecryptionKey", req, cb);
    }
    async DescribeInstanceParamRecords(req, cb) {
        return this.request("DescribeInstanceParamRecords", req, cb);
    }
    async DescribeBackupSummaries(req, cb) {
        return this.request("DescribeBackupSummaries", req, cb);
    }
    async DescribeParamTemplateInfo(req, cb) {
        return this.request("DescribeParamTemplateInfo", req, cb);
    }
    async DescribeBinlogBackupOverview(req, cb) {
        return this.request("DescribeBinlogBackupOverview", req, cb);
    }
    async SwitchDrInstanceToMaster(req, cb) {
        return this.request("SwitchDrInstanceToMaster", req, cb);
    }
    async DeleteAccounts(req, cb) {
        return this.request("DeleteAccounts", req, cb);
    }
    async DescribeDBInstanceInfo(req, cb) {
        return this.request("DescribeDBInstanceInfo", req, cb);
    }
    async DescribeClusterInfo(req, cb) {
        return this.request("DescribeClusterInfo", req, cb);
    }
    async DescribeRollbackRangeTime(req, cb) {
        return this.request("DescribeRollbackRangeTime", req, cb);
    }
    async CloseCdbProxyAddress(req, cb) {
        return this.request("CloseCdbProxyAddress", req, cb);
    }
    async DeleteBackup(req, cb) {
        return this.request("DeleteBackup", req, cb);
    }
    async DescribeRoMinScale(req, cb) {
        return this.request("DescribeRoMinScale", req, cb);
    }
    async ModifyCdbProxyAddressDesc(req, cb) {
        return this.request("ModifyCdbProxyAddressDesc", req, cb);
    }
    async ModifyAccountHost(req, cb) {
        return this.request("ModifyAccountHost", req, cb);
    }
    async StartReplication(req, cb) {
        return this.request("StartReplication", req, cb);
    }
    async AdjustCdbProxyAddress(req, cb) {
        return this.request("AdjustCdbProxyAddress", req, cb);
    }
    async DescribeAuditInstanceList(req, cb) {
        return this.request("DescribeAuditInstanceList", req, cb);
    }
    async DescribeAuditConfig(req, cb) {
        return this.request("DescribeAuditConfig", req, cb);
    }
    async ResetPassword(req, cb) {
        return this.request("ResetPassword", req, cb);
    }
    async ModifyInstanceParam(req, cb) {
        return this.request("ModifyInstanceParam", req, cb);
    }
    async CloseCDBProxy(req, cb) {
        return this.request("CloseCDBProxy", req, cb);
    }
    async ModifyNameOrDescByDpId(req, cb) {
        return this.request("ModifyNameOrDescByDpId", req, cb);
    }
    async ModifyAccountMaxUserConnections(req, cb) {
        return this.request("ModifyAccountMaxUserConnections", req, cb);
    }
    async DescribeAsyncRequestInfo(req, cb) {
        return this.request("DescribeAsyncRequestInfo", req, cb);
    }
    async DescribeAuditLogs(req, cb) {
        return this.request("DescribeAuditLogs", req, cb);
    }
    async DescribeDBInstanceRebootTime(req, cb) {
        return this.request("DescribeDBInstanceRebootTime", req, cb);
    }
    async DescribeDBInstances(req, cb) {
        return this.request("DescribeDBInstances", req, cb);
    }
    async ModifyRoGroupInfo(req, cb) {
        return this.request("ModifyRoGroupInfo", req, cb);
    }
    async CreateAuditRule(req, cb) {
        return this.request("CreateAuditRule", req, cb);
    }
    async DescribeDBInstanceCharset(req, cb) {
        return this.request("DescribeDBInstanceCharset", req, cb);
    }
    async AssociateSecurityGroups(req, cb) {
        return this.request("AssociateSecurityGroups", req, cb);
    }
    async DescribeDBPrice(req, cb) {
        return this.request("DescribeDBPrice", req, cb);
    }
    async ModifyAccountPrivileges(req, cb) {
        return this.request("ModifyAccountPrivileges", req, cb);
    }
    async DescribeDBImportRecords(req, cb) {
        return this.request("DescribeDBImportRecords", req, cb);
    }
    async DescribeSSLStatus(req, cb) {
        return this.request("DescribeSSLStatus", req, cb);
    }
    async DescribeDBSwitchRecords(req, cb) {
        return this.request("DescribeDBSwitchRecords", req, cb);
    }
    async CreateRotationPassword(req, cb) {
        return this.request("CreateRotationPassword", req, cb);
    }
    async CreateDBImportJob(req, cb) {
        return this.request("CreateDBImportJob", req, cb);
    }
    async DescribeAccounts(req, cb) {
        return this.request("DescribeAccounts", req, cb);
    }
    async DescribeSlowLogData(req, cb) {
        return this.request("DescribeSlowLogData", req, cb);
    }
    async ReloadBalanceProxyNode(req, cb) {
        return this.request("ReloadBalanceProxyNode", req, cb);
    }
    async ModifyBackupEncryptionStatus(req, cb) {
        return this.request("ModifyBackupEncryptionStatus", req, cb);
    }
    async DescribeBackupDownloadRestriction(req, cb) {
        return this.request("DescribeBackupDownloadRestriction", req, cb);
    }
    async ResetRootAccount(req, cb) {
        return this.request("ResetRootAccount", req, cb);
    }
    async ModifyAuditRuleTemplates(req, cb) {
        return this.request("ModifyAuditRuleTemplates", req, cb);
    }
    async SubmitInstanceUpgradeCheckJob(req, cb) {
        return this.request("SubmitInstanceUpgradeCheckJob", req, cb);
    }
    async DeleteRotationPassword(req, cb) {
        return this.request("DeleteRotationPassword", req, cb);
    }
    async ModifyAccountPassword(req, cb) {
        return this.request("ModifyAccountPassword", req, cb);
    }
    async DescribeUploadedFiles(req, cb) {
        return this.request("DescribeUploadedFiles", req, cb);
    }
    async ModifyAccountDescription(req, cb) {
        return this.request("ModifyAccountDescription", req, cb);
    }
    async DescribeCpuExpandHistory(req, cb) {
        return this.request("DescribeCpuExpandHistory", req, cb);
    }
    async OpenAuditService(req, cb) {
        return this.request("OpenAuditService", req, cb);
    }
    async DeleteAuditLogFile(req, cb) {
        return this.request("DeleteAuditLogFile", req, cb);
    }
    async DescribeAuditRuleTemplates(req, cb) {
        return this.request("DescribeAuditRuleTemplates", req, cb);
    }
    async ModifyBackupConfig(req, cb) {
        return this.request("ModifyBackupConfig", req, cb);
    }
    async DescribeAuditRules(req, cb) {
        return this.request("DescribeAuditRules", req, cb);
    }
    async DescribeRemoteBackupConfig(req, cb) {
        return this.request("DescribeRemoteBackupConfig", req, cb);
    }
    async ModifyCdbProxyAddressVipAndVPort(req, cb) {
        return this.request("ModifyCdbProxyAddressVipAndVPort", req, cb);
    }
    async ModifyDBInstanceProject(req, cb) {
        return this.request("ModifyDBInstanceProject", req, cb);
    }
    async DescribeDBInstanceLogToCLS(req, cb) {
        return this.request("DescribeDBInstanceLogToCLS", req, cb);
    }
    async DescribeAuditRuleTemplateModifyHistory(req, cb) {
        return this.request("DescribeAuditRuleTemplateModifyHistory", req, cb);
    }
    async CreateCdbProxy(req, cb) {
        return this.request("CreateCdbProxy", req, cb);
    }
    async ModifyDBInstanceReadOnlyStatus(req, cb) {
        return this.request("ModifyDBInstanceReadOnlyStatus", req, cb);
    }
    async ModifyAutoRenewFlag(req, cb) {
        return this.request("ModifyAutoRenewFlag", req, cb);
    }
    async SwitchCDBProxy(req, cb) {
        return this.request("SwitchCDBProxy", req, cb);
    }
    async ModifyAuditService(req, cb) {
        return this.request("ModifyAuditService", req, cb);
    }
    async ModifyCdbProxyParam(req, cb) {
        return this.request("ModifyCdbProxyParam", req, cb);
    }
    async StartBatchRollback(req, cb) {
        return this.request("StartBatchRollback", req, cb);
    }
    async DescribeDeviceMonitorInfo(req, cb) {
        return this.request("DescribeDeviceMonitorInfo", req, cb);
    }
    async OpenWanService(req, cb) {
        return this.request("OpenWanService", req, cb);
    }
    async ModifyAuditRule(req, cb) {
        return this.request("ModifyAuditRule", req, cb);
    }
    async ModifyDBInstanceSecurityGroups(req, cb) {
        return this.request("ModifyDBInstanceSecurityGroups", req, cb);
    }
    async DeleteAuditRule(req, cb) {
        return this.request("DeleteAuditRule", req, cb);
    }
    async StartCpuExpand(req, cb) {
        return this.request("StartCpuExpand", req, cb);
    }
    async DescribeSupportedPrivileges(req, cb) {
        return this.request("DescribeSupportedPrivileges", req, cb);
    }
    async DeleteParamTemplate(req, cb) {
        return this.request("DeleteParamTemplate", req, cb);
    }
    async UpgradeCDBProxyVersion(req, cb) {
        return this.request("UpgradeCDBProxyVersion", req, cb);
    }
    async DescribeBinlogs(req, cb) {
        return this.request("DescribeBinlogs", req, cb);
    }
    async DescribeDBSecurityGroups(req, cb) {
        return this.request("DescribeDBSecurityGroups", req, cb);
    }
    async DescribeRoGroups(req, cb) {
        return this.request("DescribeRoGroups", req, cb);
    }
    async DescribeCloneList(req, cb) {
        return this.request("DescribeCloneList", req, cb);
    }
    async DescribeInstanceAlarmEvents(req, cb) {
        return this.request("DescribeInstanceAlarmEvents", req, cb);
    }
    async UpgradeDBInstance(req, cb) {
        return this.request("UpgradeDBInstance", req, cb);
    }
    async CloseSSL(req, cb) {
        return this.request("CloseSSL", req, cb);
    }
    async CreateDeployGroup(req, cb) {
        return this.request("CreateDeployGroup", req, cb);
    }
    async ModifyRemoteBackupConfig(req, cb) {
        return this.request("ModifyRemoteBackupConfig", req, cb);
    }
    async DeleteTimeWindow(req, cb) {
        return this.request("DeleteTimeWindow", req, cb);
    }
}
