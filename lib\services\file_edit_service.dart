import 'dart:io';
import 'dart:convert';

/// 文件编辑服务
/// 提供AI驱动的文件编辑功能，类似于Cursor IDE
class FileEditService {
  /// 文件编辑结果
  static const String editSuccess = 'success';
  static const String editFailed = 'failed';
  static const String editCancelled = 'cancelled';

  /// 读取文件内容
  Future<String?> readFileContent(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return null;
      }
      return await file.readAsString(encoding: utf8);
    } catch (e) {
      print('读取文件失败: $e');
      return null;
    }
  }

  /// 写入文件内容
  Future<bool> writeFileContent(String filePath, String content) async {
    try {
      final file = File(filePath);
      
      // 确保目录存在
      final directory = file.parent;
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }
      
      await file.writeAsString(content, encoding: utf8);
      return true;
    } catch (e) {
      print('写入文件失败: $e');
      return false;
    }
  }

  /// 应用文件修改
  /// [originalContent] 原始文件内容
  /// [modifiedContent] AI修改后的内容
  /// [filePath] 文件路径
  Future<FileEditResult> applyFileEdit({
    required String filePath,
    required String originalContent,
    required String modifiedContent,
    String? description,
  }) async {
    try {
      // 创建备份
      final backupPath = await _createBackup(filePath, originalContent);
      
      // 应用修改
      final success = await writeFileContent(filePath, modifiedContent);
      
      if (success) {
        return FileEditResult(
          status: editSuccess,
          filePath: filePath,
          backupPath: backupPath,
          description: description ?? '文件已成功修改',
          originalContent: originalContent,
          modifiedContent: modifiedContent,
          timestamp: DateTime.now(),
        );
      } else {
        return FileEditResult(
          status: editFailed,
          filePath: filePath,
          description: '写入文件失败',
          originalContent: originalContent,
          modifiedContent: modifiedContent,
          timestamp: DateTime.now(),
        );
      }
    } catch (e) {
      return FileEditResult(
        status: editFailed,
        filePath: filePath,
        description: '应用修改失败: $e',
        originalContent: originalContent,
        modifiedContent: modifiedContent,
        timestamp: DateTime.now(),
      );
    }
  }

  /// 创建文件备份
  Future<String> _createBackup(String filePath, String content) async {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final backupPath = '${filePath}.backup_$timestamp';
    
    await writeFileContent(backupPath, content);
    return backupPath;
  }

  /// 撤销文件修改
  Future<bool> undoFileEdit(FileEditResult editResult) async {
    if (editResult.backupPath == null) {
      return false;
    }
    
    try {
      final backupFile = File(editResult.backupPath!);
      if (!await backupFile.exists()) {
        return false;
      }
      
      final backupContent = await backupFile.readAsString(encoding: utf8);
      final success = await writeFileContent(editResult.filePath, backupContent);
      
      if (success) {
        // 删除备份文件
        await backupFile.delete();
      }
      
      return success;
    } catch (e) {
      print('撤销修改失败: $e');
      return false;
    }
  }

  /// 获取文件差异
  List<FileDiff> getFileDifferences(String originalContent, String modifiedContent) {
    final originalLines = originalContent.split('\n');
    final modifiedLines = modifiedContent.split('\n');
    
    final differences = <FileDiff>[];
    final maxLines = originalLines.length > modifiedLines.length 
        ? originalLines.length 
        : modifiedLines.length;
    
    for (int i = 0; i < maxLines; i++) {
      final originalLine = i < originalLines.length ? originalLines[i] : null;
      final modifiedLine = i < modifiedLines.length ? modifiedLines[i] : null;
      
      if (originalLine != modifiedLine) {
        differences.add(FileDiff(
          lineNumber: i + 1,
          originalLine: originalLine,
          modifiedLine: modifiedLine,
          type: _getDiffType(originalLine, modifiedLine),
        ));
      }
    }
    
    return differences;
  }

  DiffType _getDiffType(String? originalLine, String? modifiedLine) {
    if (originalLine == null) return DiffType.added;
    if (modifiedLine == null) return DiffType.deleted;
    return DiffType.modified;
  }
}

/// 文件编辑结果
class FileEditResult {
  final String status;
  final String filePath;
  final String? backupPath;
  final String description;
  final String originalContent;
  final String modifiedContent;
  final DateTime timestamp;

  FileEditResult({
    required this.status,
    required this.filePath,
    this.backupPath,
    required this.description,
    required this.originalContent,
    required this.modifiedContent,
    required this.timestamp,
  });

  bool get isSuccess => status == FileEditService.editSuccess;
  bool get isFailed => status == FileEditService.editFailed;
  bool get isCancelled => status == FileEditService.editCancelled;
}

/// 文件差异
class FileDiff {
  final int lineNumber;
  final String? originalLine;
  final String? modifiedLine;
  final DiffType type;

  FileDiff({
    required this.lineNumber,
    this.originalLine,
    this.modifiedLine,
    required this.type,
  });
}

/// 差异类型
enum DiffType {
  added,    // 新增行
  deleted,  // 删除行
  modified, // 修改行
}
