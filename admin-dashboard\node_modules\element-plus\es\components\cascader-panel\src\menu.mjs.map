{"version": 3, "file": "menu.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/menu.vue"], "sourcesContent": ["<template>\n  <el-scrollbar\n    :key=\"menuId\"\n    tag=\"ul\"\n    role=\"menu\"\n    :class=\"ns.b()\"\n    :wrap-class=\"ns.e('wrap')\"\n    :view-class=\"[ns.e('list'), ns.is('empty', isEmpty)]\"\n    @mousemove=\"handleMouseMove\"\n    @mouseleave=\"clearHoverZone\"\n  >\n    <el-cascader-node\n      v-for=\"node in nodes\"\n      :key=\"node.uid\"\n      :node=\"node\"\n      :menu-id=\"menuId\"\n      @expand=\"handleExpand\"\n    />\n    <div v-if=\"isLoading\" :class=\"ns.e('empty-text')\">\n      <el-icon size=\"14\" :class=\"ns.is('loading')\">\n        <loading />\n      </el-icon>\n      {{ t('el.cascader.loading') }}\n    </div>\n    <div v-else-if=\"isEmpty\" :class=\"ns.e('empty-text')\">\n      <slot name=\"empty\">{{ t('el.cascader.noData') }}</slot>\n    </div>\n    <!-- eslint-disable-next-line vue/html-self-closing -->\n    <svg\n      v-else-if=\"panel?.isHoverMenu\"\n      ref=\"hoverZone\"\n      :class=\"ns.e('hover-zone')\"\n    ></svg>\n  </el-scrollbar>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, getCurrentInstance, inject, ref } from 'vue'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport { useId, useLocale, useNamespace } from '@element-plus/hooks'\nimport { Loading } from '@element-plus/icons-vue'\nimport ElIcon from '@element-plus/components/icon'\nimport ElCascaderNode from './node.vue'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\n\nimport type { default as CascaderNode } from './node'\nimport type { PropType } from 'vue'\n\ndefineOptions({\n  name: 'ElCascaderMenu',\n})\n\nconst props = defineProps({\n  nodes: {\n    type: Array as PropType<CascaderNode[]>,\n    required: true,\n  },\n  index: {\n    type: Number,\n    required: true,\n  },\n})\n\nconst instance = getCurrentInstance()!\nconst ns = useNamespace('cascader-menu')\n\nconst { t } = useLocale()\nconst id = useId()\nlet activeNode: HTMLElement\nlet hoverTimer: number | undefined\n\nconst panel = inject(CASCADER_PANEL_INJECTION_KEY)!\n\nconst hoverZone = ref<SVGSVGElement>()\n\nconst isEmpty = computed(() => !props.nodes.length)\nconst isLoading = computed(() => !panel.initialLoaded)\nconst menuId = computed(() => `${id.value}-${props.index}`)\n\nconst handleExpand = (e: MouseEvent) => {\n  activeNode = e.target as HTMLElement\n}\n\nconst handleMouseMove = (e: MouseEvent) => {\n  if (!panel.isHoverMenu || !activeNode || !hoverZone.value) return\n\n  if (activeNode.contains(e.target as HTMLElement)) {\n    clearHoverTimer()\n\n    const el = instance.vnode.el as HTMLElement\n    const { left } = el.getBoundingClientRect()\n    const { offsetWidth, offsetHeight } = el\n    const startX = e.clientX - left\n    const top = activeNode.offsetTop\n    const bottom = top + activeNode.offsetHeight\n\n    hoverZone.value.innerHTML = `\n          <path style=\"pointer-events: auto;\" fill=\"transparent\" d=\"M${startX} ${top} L${offsetWidth} 0 V${top} Z\" />\n          <path style=\"pointer-events: auto;\" fill=\"transparent\" d=\"M${startX} ${bottom} L${offsetWidth} ${offsetHeight} V${bottom} Z\" />\n        `\n  } else if (!hoverTimer) {\n    hoverTimer = window.setTimeout(clearHoverZone, panel.config.hoverThreshold)\n  }\n}\n\nconst clearHoverTimer = () => {\n  if (!hoverTimer) return\n  clearTimeout(hoverTimer)\n  hoverTimer = undefined\n}\n\nconst clearHoverZone = () => {\n  if (!hoverZone.value) return\n  hoverZone.value.innerHTML = ''\n  clearHoverTimer()\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;mCAgDc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;AAaA,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,EAAA,GAAK,aAAa,eAAe,CAAA,CAAA;AAEvC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAA,MAAM,KAAK,KAAM,EAAA,CAAA;AACjB,IAAI,IAAA,UAAA,CAAA;AACJ,IAAI,IAAA,UAAA,CAAA;AAEJ,IAAM,MAAA,KAAA,GAAQ,OAAO,4BAA4B,CAAA,CAAA;AAEjD,IAAA,MAAM,YAAY,GAAmB,EAAA,CAAA;AAErC,IAAA,MAAM,UAAU,QAAS,CAAA,MAAM,CAAC,KAAA,CAAM,MAAM,MAAM,CAAA,CAAA;AAClD,IAAA,MAAM,SAAY,GAAA,QAAA,CAAS,MAAM,CAAC,MAAM,aAAa,CAAA,CAAA;AACrD,IAAM,MAAA,MAAA,GAAS,SAAS,MAAM,CAAA,EAAG,GAAG,KAAK,CAAA,CAAA,EAAI,KAAM,CAAA,KAAK,CAAE,CAAA,CAAA,CAAA;AAE1D,IAAM,MAAA,YAAA,GAAe,CAAC,CAAkB,KAAA;AACtC,MAAA,UAAA,GAAa,CAAE,CAAA,MAAA,CAAA;AAAA,KACjB,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,CAAkB,KAAA;AACzC,MAAA,IAAI,CAAC,KAAM,CAAA,WAAA,IAAe,CAAC,UAAc,IAAA,CAAC,UAAU,KAAO;AAE3D,QAAA,OAAe;AACb,MAAgB,IAAA,UAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA;AAEhB,QAAM;AACN,QAAA,MAAM,EAAE,GAAA,QAAS,CAAG,KAAsB,CAAA,EAAA,CAAA;AAC1C,QAAM,MAAA,EAAE,IAAa,EAAA,GAAA,EAAA,CAAA,qBAAiB,EAAA,CAAA;AACtC,QAAM,MAAA,EAAA,WAAqB,EAAA,YAAA,EAAA,GAAA,EAAA,CAAA;AAC3B,QAAA,MAAM,MAAM,GAAW,CAAA,CAAA,OAAA,GAAA,IAAA,CAAA;AACvB,QAAM,MAAA,GAAA,GAAA,UAA0B,CAAA,SAAA,CAAA;AAEhC,QAAA,MAAA,YAA4B,GAAA,UAAA,CAAA,YAAA,CAAA;AAAA,QAAA,SAAA,CAAA,KAAA,CAAA,SAAA,GAAA,CAAA;AAC8E,qEACvC,EAAA,MAAM,IAAI,GAAM,CAAA,EAAA,EAAA,gBAAoB,EAAA,GAAA,CAAA;AAAuB,qEAAA,EAAA,MAAA,CAAA,CAAA,EAAA,MAAA,CAAA,EAAA,EAAA,WAAA,CAAA,CAAA,EAAA,YAAA,CAAA,EAAA,EAAA,MAAA,CAAA;AAAA,QAEhI,CAAA,CAAA;AACE,OAAA,MAAA,IAAA,CAAA,UAAoB,EAAA;AAAsD,QAC5E,UAAA,GAAA,MAAA,CAAA,UAAA,CAAA,cAAA,EAAA,KAAA,CAAA,MAAA,CAAA,cAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA,CAAA;AACE,IAAA,MAAI,eAAa,GAAA,MAAA;AACjB,MAAA,IAAA,CAAA,UAAuB;AACvB,QAAa,OAAA;AAAA,MACf,YAAA,CAAA,UAAA,CAAA,CAAA;AAEA,MAAA;AACE,KAAI,CAAA;AACJ,IAAA,MAAA,cAA4B,GAAA,MAAA;AAC5B,MAAgB,IAAA,CAAA,SAAA,CAAA,KAAA;AAAA,QAClB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}