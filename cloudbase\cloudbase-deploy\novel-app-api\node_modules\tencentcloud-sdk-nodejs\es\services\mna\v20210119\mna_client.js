import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("mna.tencentcloudapi.com", "2021-01-19", clientConfig);
    }
    async SetNotifyUrl(req, cb) {
        return this.request("SetNotifyUrl", req, cb);
    }
    async UpdateHardware(req, cb) {
        return this.request("UpdateHardware", req, cb);
    }
    async DownloadActiveDeviceCount(req, cb) {
        return this.request("DownloadActiveDeviceCount", req, cb);
    }
    async ModifyPackageRenewFlag(req, cb) {
        return this.request("ModifyPackageRenewFlag", req, cb);
    }
    async GetFlowStatistic(req, cb) {
        return this.request("GetFlowStatistic", req, cb);
    }
    async GetFlowStatisticByGroup(req, cb) {
        return this.request("GetFlowStatisticByGroup", req, cb);
    }
    async GetMultiFlowStatistic(req, cb) {
        return this.request("GetMultiFlowStatistic", req, cb);
    }
    async GetDevices(req, cb) {
        return this.request("GetDevices", req, cb);
    }
    async GetPublicKey(req, cb) {
        return this.request("GetPublicKey", req, cb);
    }
    async GroupAddDevice(req, cb) {
        return this.request("GroupAddDevice", req, cb);
    }
    async ActivateHardware(req, cb) {
        return this.request("ActivateHardware", req, cb);
    }
    async GetFlowPackages(req, cb) {
        return this.request("GetFlowPackages", req, cb);
    }
    async AddHardware(req, cb) {
        return this.request("AddHardware", req, cb);
    }
    async GetDevice(req, cb) {
        return this.request("GetDevice", req, cb);
    }
    async DeleteL3Conn(req, cb) {
        return this.request("DeleteL3Conn", req, cb);
    }
    async DeleteGroup(req, cb) {
        return this.request("DeleteGroup", req, cb);
    }
    async GetActiveDeviceCount(req, cb) {
        return this.request("GetActiveDeviceCount", req, cb);
    }
    async DeleteDevice(req, cb) {
        return this.request("DeleteDevice", req, cb);
    }
    async UpdateDevice(req, cb) {
        return this.request("UpdateDevice", req, cb);
    }
    async OrderPerLicense(req, cb) {
        return this.request("OrderPerLicense", req, cb);
    }
    async GetNetMonitor(req, cb) {
        return this.request("GetNetMonitor", req, cb);
    }
    async GetDevicePayMode(req, cb) {
        return this.request("GetDevicePayMode", req, cb);
    }
    async GetGroupDetail(req, cb) {
        return this.request("GetGroupDetail", req, cb);
    }
    async UpdateL3Cidr(req, cb) {
        return this.request("UpdateL3Cidr", req, cb);
    }
    async GetVendorHardware(req, cb) {
        return this.request("GetVendorHardware", req, cb);
    }
    async AddL3Conn(req, cb) {
        return this.request("AddL3Conn", req, cb);
    }
    async CreateEncryptedKey(req, cb) {
        return this.request("CreateEncryptedKey", req, cb);
    }
    async GetFlowAlarmInfo(req, cb) {
        return this.request("GetFlowAlarmInfo", req, cb);
    }
    async GroupDeleteDevice(req, cb) {
        return this.request("GroupDeleteDevice", req, cb);
    }
    async OrderFlowPackage(req, cb) {
        return this.request("OrderFlowPackage", req, cb);
    }
    async GetStatisticData(req, cb) {
        return this.request("GetStatisticData", req, cb);
    }
    async GetHardwareList(req, cb) {
        return this.request("GetHardwareList", req, cb);
    }
    async UpdateL3Switch(req, cb) {
        return this.request("UpdateL3Switch", req, cb);
    }
    async AddDevice(req, cb) {
        return this.request("AddDevice", req, cb);
    }
    async GetFlowStatisticByRegion(req, cb) {
        return this.request("GetFlowStatisticByRegion", req, cb);
    }
    async GetGroupList(req, cb) {
        return this.request("GetGroupList", req, cb);
    }
    async UpdateGroup(req, cb) {
        return this.request("UpdateGroup", req, cb);
    }
    async AddGroup(req, cb) {
        return this.request("AddGroup", req, cb);
    }
    async GetL3ConnList(req, cb) {
        return this.request("GetL3ConnList", req, cb);
    }
    async UpdateL3Conn(req, cb) {
        return this.request("UpdateL3Conn", req, cb);
    }
}
