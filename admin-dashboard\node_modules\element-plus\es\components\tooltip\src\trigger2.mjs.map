{"version": 3, "file": "trigger2.mjs", "sources": ["../../../../../../packages/components/tooltip/src/trigger.vue"], "sourcesContent": ["<template>\n  <el-popper-trigger\n    :id=\"id\"\n    :virtual-ref=\"virtualRef\"\n    :open=\"open\"\n    :virtual-triggering=\"virtualTriggering\"\n    :class=\"ns.e('trigger')\"\n    @blur=\"onBlur\"\n    @click=\"onClick\"\n    @contextmenu=\"onContextMenu\"\n    @focus=\"onFocus\"\n    @mouseenter=\"onMouseenter\"\n    @mouseleave=\"onMouseleave\"\n    @keydown=\"onKeydown\"\n  >\n    <slot />\n  </el-popper-trigger>\n</template>\n\n<script lang=\"ts\" setup>\nimport { inject, ref, toRef, unref } from 'vue'\nimport { ElPopperTrigger } from '@element-plus/components/popper'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { TOOLTIP_INJECTION_KEY } from './constants'\nimport { useTooltipTriggerProps } from './trigger'\nimport { whenTrigger } from './utils'\n\nimport type { OnlyChildExpose } from '@element-plus/components/slot'\n\ndefineOptions({\n  name: 'ElTooltipTrigger',\n})\n\nconst props = defineProps(useTooltipTriggerProps)\n\nconst ns = useNamespace('tooltip')\nconst { controlled, id, open, onOpen, onClose, onToggle } = inject(\n  TOOLTIP_INJECTION_KEY,\n  undefined\n)!\n\nconst triggerRef = ref<OnlyChildExpose | null>(null)\n\nconst stopWhenControlledOrDisabled = () => {\n  if (unref(controlled) || props.disabled) {\n    return true\n  }\n}\nconst trigger = toRef(props, 'trigger')\nconst onMouseenter = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'hover', onOpen)\n)\nconst onMouseleave = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'hover', onClose)\n)\nconst onClick = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'click', (e) => {\n    // distinguish left click\n    if ((e as MouseEvent).button === 0) {\n      onToggle(e)\n    }\n  })\n)\n\nconst onFocus = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'focus', onOpen)\n)\n\nconst onBlur = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'focus', onClose)\n)\n\nconst onContextMenu = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'contextmenu', (e: Event) => {\n    e.preventDefault()\n    onToggle(e)\n  })\n)\n\nconst onKeydown = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  (e: KeyboardEvent) => {\n    const { code } = e\n    if (props.triggerKeys.includes(code)) {\n      e.preventDefault()\n      onToggle(e)\n    }\n  }\n)\n\ndefineExpose({\n  /**\n   * @description trigger element\n   */\n  triggerRef,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref", "_normalizeClass", "_withCtx", "_renderSlot"], "mappings": ";;;;;;;;;;mCA8Bc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAK,aAAa,SAAS,CAAA,CAAA;AACjC,IAAA,MAAM,EAAE,UAAY,EAAA,EAAA,EAAI,MAAM,MAAQ,EAAA,OAAA,EAAS,UAAa,GAAA,MAAA,CAAA,qBAAA,EAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IAC1D,MAAA,UAAA,GAAA,GAAA,CAAA,IAAA,CAAA,CAAA;AAAA,IACA,MAAA,4BAAA,GAAA,MAAA;AAAA,MACF,IAAA,KAAA,CAAA,UAAA,CAAA,IAAA,KAAA,CAAA,QAAA,EAAA;AAEA,QAAM,OAAA,IAAA,CAAA;AAEN,OAAA;AACE,KAAA,CAAA;AACE,IAAO,MAAA,OAAA,GAAA,KAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA;AAAA,IACT,MAAA,YAAA,GAAA,oBAAA,CAAA,4BAAA,EAAA,WAAA,CAAA,OAAA,EAAA,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,oBAAA,CAAA,4BAAA,EAAA,WAAA,CAAA,OAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AACA,IAAM,MAAA,OAAA,GAAU,oBAAsB,CAAA,4BAAA,EAAA,WAAA,CAAA,OAAA,EAAA,OAAA,EAAA,CAAA,CAAA,KAAA;AACtC,MAAA,IAAM,CAAe,CAAA,MAAA,KAAA,CAAA,EAAA;AAAA,QACnB,QAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACA;AAAoC,KACtC,CAAA,CAAA,CAAA;AACA,IAAA,MAAM,OAAe,GAAA,oBAAA,CAAA,4BAAA,EAAA,WAAA,CAAA,OAAA,EAAA,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA;AAAA,IACnB,MAAA,MAAA,GAAA,oBAAA,CAAA,4BAAA,EAAA,WAAA,CAAA,OAAA,EAAA,OAAA,EAAA,OAAA,CAAA,CAAA,CAAA;AAAA,IACA,MAAA,aAAqB,GAAA,oBAAgB,CAAA,4BAAA,EAAA,WAAA,CAAA,OAAA,EAAA,aAAA,EAAA,CAAA,CAAA,KAAA;AAAA,MACvC,CAAA,CAAA,cAAA,EAAA,CAAA;AACA,MAAA,QAAgB,CAAA,CAAA,CAAA,CAAA;AAAA,KACd,CAAA,CAAA,CAAA;AAAA,IAAA,MACY,SAAA,GAAA,oBAAyB,CAAA,4BAAA,EAAA,CAAA,CAAA,KAAA;AAEnC,MAAK,MAAA,EAAiB,WAAW;AAC/B,MAAA,IAAA,KAAA,CAAA,WAAU,CAAA,QAAA,CAAA,IAAA,CAAA,EAAA;AAAA,QACZ,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,QACD,QAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACH;AAEA,KAAA,CAAA,CAAA;AAAgB,IACd,MAAA,CAAA;AAAA,MACA,UAAA;AAAoC,KACtC,CAAA,CAAA;AAEA,IAAA,OAAe,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACb,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,eAAA,CAAA,EAAA;AAAA,QACA,EAAA,EAAAA,KAAY,CAAS,EAAA,CAAA;AAAgB,QACvC,aAAA,EAAA,IAAA,CAAA,UAAA;AAEA,QAAA,IAAsB,EAAAA,KAAA,CAAA,IAAA,CAAA;AAAA,QACpB,oBAAA,EAAA,IAAA,CAAA,iBAAA;AAAA,QACY,KAAA,EAAAC,cAAwB,CAAAD,KAAA,CAAA,EAAA,CAAA,CAAA,CAAC,CAAa,SAAA,CAAA,CAAA;AAChD,QAAA,MAAiB,EAAAA,KAAA,CAAA,MAAA,CAAA;AACjB,QAAA,OAAA,EAASA,KAAC,CAAA,OAAA,CAAA;AAAA,QACX,aAAA,EAAAA,KAAA,CAAA,aAAA,CAAA;AAAA,QACH,OAAA,EAAAA,KAAA,CAAA,OAAA,CAAA;AAEA,QAAA,YAAkB,EAAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QAChB,YAAA,EAAAA,KAAA,CAAA,YAAA,CAAA;AAAA,QACsB,SAAA,EAAAA,KAAA,CAAA,SAAA,CAAA;AACpB,OAAM,EAAA;AACN,QAAA,OAAU,EAAAE,OAAA,CAAA,MAAqB;AAC7B,UAAAC,UAAiB,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AACjB,SAAA,CAAA;AAAU,QACZ,CAAA,EAAA,CAAA;AAAA,OACF,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,aAAA,EAAA,MAAA,EAAA,oBAAA,EAAA,OAAA,EAAA,QAAA,EAAA,SAAA,EAAA,eAAA,EAAA,SAAA,EAAA,cAAA,EAAA,cAAA,EAAA,WAAA,CAAA,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,GAAa;AAAA,CAAA,CAAA,CAAA;AAAA,uBAAA,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,aAAA,CAAA,CAAA,CAAA;;;;"}