import 'package:novel_app/models/text_modification.dart';

/// 文本差异计算服务
class TextDiffService {
  /// 计算两个文本之间的差异
  static TextDiff calculateDiff(String originalText, String newText) {
    final originalLines = originalText.split('\n');
    final newLines = newText.split('\n');
    
    final diffLines = <DiffLine>[];
    final lcs = _longestCommonSubsequence(originalLines, newLines);
    
    int originalIndex = 0;
    int newIndex = 0;
    int lcsIndex = 0;
    
    int addedCount = 0;
    int deletedCount = 0;
    int modifiedCount = 0;
    
    while (originalIndex < originalLines.length || newIndex < newLines.length) {
      if (lcsIndex < lcs.length && 
          originalIndex < originalLines.length &&
          newIndex < newLines.length &&
          originalLines[originalIndex] == lcs[lcsIndex] &&
          newLines[newIndex] == lcs[lcsIndex]) {
        // 未修改的行
        diffLines.add(DiffLine(
          content: originalLines[originalIndex],
          type: DiffType.unchanged,
          originalLineNumber: originalIndex + 1,
          newLineNumber: newIndex + 1,
        ));
        originalIndex++;
        newIndex++;
        lcsIndex++;
      } else if (originalIndex < originalLines.length &&
                 (lcsIndex >= lcs.length || originalLines[originalIndex] != lcs[lcsIndex])) {
        // 删除的行
        diffLines.add(DiffLine(
          content: originalLines[originalIndex],
          type: DiffType.deleted,
          originalLineNumber: originalIndex + 1,
        ));
        originalIndex++;
        deletedCount++;
      } else if (newIndex < newLines.length &&
                 (lcsIndex >= lcs.length || newLines[newIndex] != lcs[lcsIndex])) {
        // 新增的行
        diffLines.add(DiffLine(
          content: newLines[newIndex],
          type: DiffType.added,
          newLineNumber: newIndex + 1,
        ));
        newIndex++;
        addedCount++;
      }
    }
    
    final stats = DiffStats(
      addedLines: addedCount,
      deletedLines: deletedCount,
      modifiedLines: modifiedCount,
    );
    
    return TextDiff(lines: diffLines, stats: stats);
  }

  /// 应用修改建议到原始文本
  static String applyModifications(String originalText, List<TextModification> modifications) {
    print('🔧 应用修改到文本: 原始长度=${originalText.length}, 修改数量=${modifications.length}');

    final lines = originalText.split('\n');
    print('  原始行数: ${lines.length}');

    final acceptedMods = modifications
        .where((mod) => mod.status == ModificationStatus.accepted)
        .toList();

    print('  已接受的修改数量: ${acceptedMods.length}');

    // 按行号倒序排序，从后往前应用修改，避免行号偏移问题
    acceptedMods.sort((a, b) => b.startLine.compareTo(a.startLine));

    for (final mod in acceptedMods) {
      print('  应用修改: ${mod.type.name} 行${mod.startLine}-${mod.endLine}');
      switch (mod.type) {
        case ModificationType.replace:
          _applyReplace(lines, mod);
          break;
        case ModificationType.insert:
          _applyInsert(lines, mod);
          break;
        case ModificationType.delete:
          _applyDelete(lines, mod);
          break;
      }
      print('    修改后行数: ${lines.length}');
    }

    final result = lines.join('\n');
    print('  最终结果长度: ${result.length}');
    return result;
  }

  /// 预览应用修改后的文本
  static String previewModifications(String originalText, List<TextModification> modifications) {
    final tempModifications = modifications.map((mod) => 
        mod.copyWith(status: ModificationStatus.accepted)).toList();
    return applyModifications(originalText, tempModifications);
  }

  /// 计算修改建议的差异预览
  static TextDiff calculateModificationDiff(String originalText, List<TextModification> modifications) {
    print('🔄 计算修改差异: 原始文本长度=${originalText.length}, 修改数量=${modifications.length}');

    if (modifications.isEmpty) {
      print('  无修改建议，返回空差异');
      return TextDiff(
        lines: [],
        stats: DiffStats(addedLines: 0, deletedLines: 0, modifiedLines: 0),
      );
    }

    final previewText = previewModifications(originalText, modifications);
    print('  预览文本长度=${previewText.length}');

    final diff = calculateDiff(originalText, previewText);
    print('  差异统计: +${diff.stats.addedLines} -${diff.stats.deletedLines} ~${diff.stats.modifiedLines}');

    return diff;
  }

  /// 应用替换修改
  static void _applyReplace(List<String> lines, TextModification mod) {
    final startIndex = mod.startLine - 1;
    final endIndex = mod.endLine - 1;
    
    if (startIndex >= 0 && endIndex < lines.length && startIndex <= endIndex) {
      final newLines = mod.newText.split('\n');
      lines.replaceRange(startIndex, endIndex + 1, newLines);
    }
  }

  /// 应用插入修改
  static void _applyInsert(List<String> lines, TextModification mod) {
    final insertIndex = mod.startLine - 1;
    
    if (insertIndex >= 0 && insertIndex <= lines.length) {
      final newLines = mod.newText.split('\n');
      lines.insertAll(insertIndex, newLines);
    }
  }

  /// 应用删除修改
  static void _applyDelete(List<String> lines, TextModification mod) {
    final startIndex = mod.startLine - 1;
    final endIndex = mod.endLine - 1;
    
    if (startIndex >= 0 && endIndex < lines.length && startIndex <= endIndex) {
      lines.removeRange(startIndex, endIndex + 1);
    }
  }

  /// 计算最长公共子序列（用于差异计算）
  static List<String> _longestCommonSubsequence(List<String> a, List<String> b) {
    final m = a.length;
    final n = b.length;
    final dp = List.generate(m + 1, (_) => List.filled(n + 1, 0));
    
    for (int i = 1; i <= m; i++) {
      for (int j = 1; j <= n; j++) {
        if (a[i - 1] == b[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = dp[i - 1][j] > dp[i][j - 1] ? dp[i - 1][j] : dp[i][j - 1];
        }
      }
    }
    
    // 回溯构建LCS
    final lcs = <String>[];
    int i = m, j = n;
    while (i > 0 && j > 0) {
      if (a[i - 1] == b[j - 1]) {
        lcs.insert(0, a[i - 1]);
        i--;
        j--;
      } else if (dp[i - 1][j] > dp[i][j - 1]) {
        i--;
      } else {
        j--;
      }
    }
    
    return lcs;
  }

  /// 验证修改建议的有效性
  static bool validateModification(String originalText, TextModification modification) {
    final lines = originalText.split('\n');
    final startIndex = modification.startLine - 1;
    final endIndex = modification.endLine - 1;
    
    // 检查行号范围
    if (startIndex < 0 || endIndex >= lines.length || startIndex > endIndex) {
      return false;
    }
    
    // 检查原始文本是否匹配
    final actualOriginalText = lines.sublist(startIndex, endIndex + 1).join('\n');
    return actualOriginalText == modification.originalText;
  }

  /// 批量验证修改建议
  static List<TextModification> validateModifications(String originalText, List<TextModification> modifications) {
    return modifications.where((mod) => validateModification(originalText, mod)).toList();
  }

  /// 检测修改冲突
  static List<String> detectConflicts(List<TextModification> modifications) {
    final conflicts = <String>[];
    
    for (int i = 0; i < modifications.length; i++) {
      for (int j = i + 1; j < modifications.length; j++) {
        final mod1 = modifications[i];
        final mod2 = modifications[j];
        
        // 检查行号范围是否重叠
        if (_rangesOverlap(mod1.startLine, mod1.endLine, mod2.startLine, mod2.endLine)) {
          conflicts.add('修改 ${mod1.id} 和 ${mod2.id} 存在行号冲突');
        }
      }
    }
    
    return conflicts;
  }

  /// 检查两个范围是否重叠
  static bool _rangesOverlap(int start1, int end1, int start2, int end2) {
    return start1 <= end2 && start2 <= end1;
  }
}
