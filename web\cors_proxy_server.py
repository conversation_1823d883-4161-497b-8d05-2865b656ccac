#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CORS代理服务器
用于解决Web版岱宗文脉的跨域访问问题和SSL证书验证问题

功能：
1. 代理转发API请求
2. 添加CORS头部
3. 处理SSL证书验证问题
4. 支持多种API服务商

使用方法：
python cors_proxy_server.py [port]

默认端口：8080
"""

import sys
import json
import ssl
import urllib.request
import urllib.parse
import urllib.error
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CORSProxyHandler(BaseHTTPRequestHandler):
    """CORS代理请求处理器"""
    
    def __init__(self, *args, **kwargs):
        # 创建忽略SSL证书验证的上下文
        self.ssl_context = ssl.create_default_context()
        self.ssl_context.check_hostname = False
        self.ssl_context.verify_mode = ssl.CERT_NONE
        super().__init__(*args, **kwargs)
    
    def do_OPTIONS(self):
        """处理预检请求"""
        self.send_response(200)
        self.add_cors_headers()
        self.end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        self.handle_request('GET')
    
    def do_POST(self):
        """处理POST请求"""
        self.handle_request('POST')
    
    def add_cors_headers(self):
        """添加CORS头部"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 
                        'Origin, X-Requested-With, Content-Type, Accept, Authorization, X-API-Key')
        self.send_header('Access-Control-Max-Age', '86400')
    
    def get_target_url(self, path):
        """从路径中提取目标URL"""
        if path.startswith('/proxy/'):
            # 格式: /proxy/https://api.example.com/path
            target_url = path[7:]  # 移除 '/proxy/' 前缀
            if not target_url.startswith(('http://', 'https://')):
                target_url = 'https://' + target_url
            return target_url
        elif path.startswith('/api/'):
            # 格式: /api/openai/v1/chat/completions
            # 这里可以根据需要映射到不同的API服务器
            api_mappings = {
                'openai': 'https://api.openai-proxy.org',
                'google': 'https://generativelanguage.googleapis.com',
                'anthropic': 'https://api.anthropic.com',
            }
            
            parts = path.split('/')
            if len(parts) >= 3:
                provider = parts[2]
                if provider in api_mappings:
                    remaining_path = '/'.join(parts[3:])
                    return f"{api_mappings[provider]}/{remaining_path}"
        
        return None
    
    def handle_request(self, method):
        """处理HTTP请求"""
        try:
            # 解析目标URL
            target_url = self.get_target_url(self.path)
            if not target_url:
                self.send_error(400, "Invalid proxy path")
                return
            
            logger.info(f"{method} {self.path} -> {target_url}")
            
            # 准备请求数据
            request_data = None
            if method == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    request_data = self.rfile.read(content_length)
            
            # 准备请求头
            headers = {}
            for header_name, header_value in self.headers.items():
                # 跳过一些不需要转发的头部
                if header_name.lower() not in ['host', 'connection', 'content-length']:
                    headers[header_name] = header_value
            
            # 创建请求
            req = urllib.request.Request(
                target_url,
                data=request_data,
                headers=headers,
                method=method
            )
            
            # 发送请求（忽略SSL证书验证）
            try:
                with urllib.request.urlopen(req, context=self.ssl_context, timeout=30) as response:
                    # 读取响应
                    response_data = response.read()
                    
                    # 发送响应
                    self.send_response(response.status)
                    self.add_cors_headers()
                    
                    # 转发响应头
                    for header_name, header_value in response.headers.items():
                        if header_name.lower() not in ['connection', 'transfer-encoding']:
                            self.send_header(header_name, header_value)
                    
                    self.end_headers()
                    self.wfile.write(response_data)
                    
                    logger.info(f"代理成功: {response.status} {len(response_data)} bytes")
                    
            except urllib.error.HTTPError as e:
                # HTTP错误也需要转发
                error_data = e.read()
                self.send_response(e.code)
                self.add_cors_headers()
                
                # 转发错误响应头
                for header_name, header_value in e.headers.items():
                    if header_name.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header_name, header_value)
                
                self.end_headers()
                self.wfile.write(error_data)
                
                logger.warning(f"代理HTTP错误: {e.code} {len(error_data)} bytes")
                
        except Exception as e:
            logger.error(f"代理请求失败: {e}")
            self.send_error(500, f"Proxy error: {str(e)}")
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        logger.info(f"{self.address_string()} - {format % args}")

def main():
    """主函数"""
    # 获取端口号
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效的端口号: {sys.argv[1]}")
            sys.exit(1)
    
    # 启动服务器
    try:
        server = HTTPServer(('0.0.0.0', port), CORSProxyHandler)
        print(f"🚀 CORS代理服务器已启动")
        print(f"📡 监听地址: http://0.0.0.0:{port}")
        print(f"🌐 使用方法: http://localhost:{port}/proxy/https://api.example.com/path")
        print(f"🔧 或者: http://localhost:{port}/api/openai/v1/chat/completions")
        print("按 Ctrl+C 停止服务器")
        print("-" * 60)
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器失败: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
