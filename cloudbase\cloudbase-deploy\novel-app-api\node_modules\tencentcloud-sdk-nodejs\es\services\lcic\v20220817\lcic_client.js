import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("lcic.tencentcloudapi.com", "2022-08-17", clientConfig);
    }
    async DescribeAppDetail(req, cb) {
        return this.request("DescribeAppDetail", req, cb);
    }
    async DescribeRoomForbiddenUser(req, cb) {
        return this.request("DescribeRoomForbiddenUser", req, cb);
    }
    async DescribeRoomStatistics(req, cb) {
        return this.request("DescribeRoomStatistics", req, cb);
    }
    async SendRoomNotificationMessage(req, cb) {
        return this.request("SendRoomNotificationMessage", req, cb);
    }
    async DescribeRecordStream(req, cb) {
        return this.request("DescribeRecordStream", req, cb);
    }
    async DescribeDocuments(req, cb) {
        return this.request("DescribeDocuments", req, cb);
    }
    async DescribeDocumentsByRoom(req, cb) {
        return this.request("DescribeDocumentsByRoom", req, cb);
    }
    async ModifyRoom(req, cb) {
        return this.request("ModifyRoom", req, cb);
    }
    async DeleteAppCustomContent(req, cb) {
        return this.request("DeleteAppCustomContent", req, cb);
    }
    async GetRoomMessage(req, cb) {
        return this.request("GetRoomMessage", req, cb);
    }
    async SetWatermark(req, cb) {
        return this.request("SetWatermark", req, cb);
    }
    async ModifyUserProfile(req, cb) {
        return this.request("ModifyUserProfile", req, cb);
    }
    async GetWatermark(req, cb) {
        return this.request("GetWatermark", req, cb);
    }
    async DescribeWhiteBoardSnapshot(req, cb) {
        return this.request("DescribeWhiteBoardSnapshot", req, cb);
    }
    async DescribeGroup(req, cb) {
        return this.request("DescribeGroup", req, cb);
    }
    async ModifyApp(req, cb) {
        return this.request("ModifyApp", req, cb);
    }
    async DescribeDocument(req, cb) {
        return this.request("DescribeDocument", req, cb);
    }
    async DescribeSdkAppIdUsers(req, cb) {
        return this.request("DescribeSdkAppIdUsers", req, cb);
    }
    async StartRoom(req, cb) {
        return this.request("StartRoom", req, cb);
    }
    async DeleteGroupMember(req, cb) {
        return this.request("DeleteGroupMember", req, cb);
    }
    async UnblockKickedUser(req, cb) {
        return this.request("UnblockKickedUser", req, cb);
    }
    async CreateDocument(req, cb) {
        return this.request("CreateDocument", req, cb);
    }
    async DescribeSupervisors(req, cb) {
        return this.request("DescribeSupervisors", req, cb);
    }
    async DeleteGroup(req, cb) {
        return this.request("DeleteGroup", req, cb);
    }
    async DescribeUser(req, cb) {
        return this.request("DescribeUser", req, cb);
    }
    async DescribeGroupMemberList(req, cb) {
        return this.request("DescribeGroupMemberList", req, cb);
    }
    async ModifyGroup(req, cb) {
        return this.request("ModifyGroup", req, cb);
    }
    async BatchRegister(req, cb) {
        return this.request("BatchRegister", req, cb);
    }
    async DeleteSupervisor(req, cb) {
        return this.request("DeleteSupervisor", req, cb);
    }
    async BatchDeleteGroupMember(req, cb) {
        return this.request("BatchDeleteGroupMember", req, cb);
    }
    async GetRoomEvent(req, cb) {
        return this.request("GetRoomEvent", req, cb);
    }
    async DeleteUser(req, cb) {
        return this.request("DeleteUser", req, cb);
    }
    async DescribeRoom(req, cb) {
        return this.request("DescribeRoom", req, cb);
    }
    async DescribeUserDetail(req, cb) {
        return this.request("DescribeUserDetail", req, cb);
    }
    async DeleteRecord(req, cb) {
        return this.request("DeleteRecord", req, cb);
    }
    async SendRoomNormalMessage(req, cb) {
        return this.request("SendRoomNormalMessage", req, cb);
    }
    async DescribeRecord(req, cb) {
        return this.request("DescribeRecord", req, cb);
    }
    async DescribeGroupList(req, cb) {
        return this.request("DescribeGroupList", req, cb);
    }
    async BatchAddGroupMember(req, cb) {
        return this.request("BatchAddGroupMember", req, cb);
    }
    async AddGroupMember(req, cb) {
        return this.request("AddGroupMember", req, cb);
    }
    async RegisterUser(req, cb) {
        return this.request("RegisterUser", req, cb);
    }
    async DescribeRecordTask(req, cb) {
        return this.request("DescribeRecordTask", req, cb);
    }
    async UnbindDocumentFromRoom(req, cb) {
        return this.request("UnbindDocumentFromRoom", req, cb);
    }
    async BindDocumentToRoom(req, cb) {
        return this.request("BindDocumentToRoom", req, cb);
    }
    async LoginOriginId(req, cb) {
        return this.request("LoginOriginId", req, cb);
    }
    async ForbidSendMsg(req, cb) {
        return this.request("ForbidSendMsg", req, cb);
    }
    async LoginUser(req, cb) {
        return this.request("LoginUser", req, cb);
    }
    async CreateGroupWithMembers(req, cb) {
        return this.request("CreateGroupWithMembers", req, cb);
    }
    async DescribeDeveloper(req, cb) {
        return this.request("DescribeDeveloper", req, cb);
    }
    async CreateSupervisor(req, cb) {
        return this.request("CreateSupervisor", req, cb);
    }
    async DescribeQuestionList(req, cb) {
        return this.request("DescribeQuestionList", req, cb);
    }
    async SetAppCustomContent(req, cb) {
        return this.request("SetAppCustomContent", req, cb);
    }
    async BatchDescribeDocument(req, cb) {
        return this.request("BatchDescribeDocument", req, cb);
    }
    async DescribeCurrentMemberList(req, cb) {
        return this.request("DescribeCurrentMemberList", req, cb);
    }
    async BatchCreateRoom(req, cb) {
        return this.request("BatchCreateRoom", req, cb);
    }
    async DescribeAnswerList(req, cb) {
        return this.request("DescribeAnswerList", req, cb);
    }
    async DeleteDocument(req, cb) {
        return this.request("DeleteDocument", req, cb);
    }
    async StopRecord(req, cb) {
        return this.request("StopRecord", req, cb);
    }
    async BatchDeleteRecord(req, cb) {
        return this.request("BatchDeleteRecord", req, cb);
    }
    async EndRoom(req, cb) {
        return this.request("EndRoom", req, cb);
    }
    async SetMarquee(req, cb) {
        return this.request("SetMarquee", req, cb);
    }
    async GetRooms(req, cb) {
        return this.request("GetRooms", req, cb);
    }
    async KickUserFromRoom(req, cb) {
        return this.request("KickUserFromRoom", req, cb);
    }
    async DeleteRoom(req, cb) {
        return this.request("DeleteRoom", req, cb);
    }
    async DeleteWhiteBoardSnapshot(req, cb) {
        return this.request("DeleteWhiteBoardSnapshot", req, cb);
    }
    async StartRecord(req, cb) {
        return this.request("StartRecord", req, cb);
    }
    async CreateGroupWithSubGroup(req, cb) {
        return this.request("CreateGroupWithSubGroup", req, cb);
    }
    async DescribeMarquee(req, cb) {
        return this.request("DescribeMarquee", req, cb);
    }
    async BatchCreateGroupWithMembers(req, cb) {
        return this.request("BatchCreateGroupWithMembers", req, cb);
    }
    async DescribeScoreList(req, cb) {
        return this.request("DescribeScoreList", req, cb);
    }
    async CreateRoom(req, cb) {
        return this.request("CreateRoom", req, cb);
    }
}
