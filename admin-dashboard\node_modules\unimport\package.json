{"name": "unimport", "type": "module", "version": "3.14.6", "description": "Unified utils for auto importing APIs in modules", "license": "MIT", "repository": "unjs/unimport", "sideEffects": false, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./unplugin": {"import": "./dist/unplugin.mjs", "require": "./dist/unplugin.cjs"}, "./addons": {"import": "./dist/addons.mjs", "require": "./dist/addons.cjs"}, "./*": "./*"}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["*.d.ts", "dist"], "dependencies": {"@rollup/pluginutils": "^5.1.4", "acorn": "^8.14.0", "escape-string-regexp": "^5.0.0", "estree-walker": "^3.0.3", "fast-glob": "^3.3.3", "local-pkg": "^1.0.0", "magic-string": "^0.30.17", "mlly": "^1.7.4", "pathe": "^2.0.1", "picomatch": "^4.0.2", "pkg-types": "^1.3.0", "scule": "^1.3.0", "strip-literal": "^2.1.1", "unplugin": "^1.16.1"}, "devDependencies": {"@antfu/eslint-config": "^3.14.0", "@types/estree": "^1.0.6", "@types/node": "^22.10.6", "@types/picomatch": "^3.0.1", "@vitest/coverage-v8": "^2.1.8", "bumpp": "^9.10.0", "conventional-changelog-cli": "^5.0.0", "eslint": "^9.18.0", "h3": "^1.13.1", "jquery": "^3.7.1", "lit": "^3.2.1", "typescript": "^5.7.3", "unbuild": "^3.3.1", "vitest": "^2.1.8", "vue-tsc": "^2.2.0"}, "scripts": {"build": "unbuild", "dev": "vitest dev", "lint": "eslint .", "play": "pnpm -C playground run dev", "play:build": "pnpm -C playground run build", "typecheck": "vue-tsc --noEmit", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "release": "pnpm run test --run && bumpp -x \"pnpm run changelog\" --all && pnpm publish", "test": "vitest --coverage"}}