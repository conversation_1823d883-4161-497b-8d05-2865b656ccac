{"version": 3, "file": "td-wrapper.mjs", "sources": ["../../../../../../../packages/components/table/src/table-body/td-wrapper.vue"], "sourcesContent": ["<template>\n  <td :colspan=\"colspan\" :rowspan=\"rowspan\"><slot /></td>\n</template>\n\n<script setup lang=\"ts\">\ndefineOptions({\n  name: 'TableTdWrapper',\n})\n\ndefineProps({\n  colspan: {\n    type: Number,\n    default: 1,\n  },\n  rowspan: {\n    type: Number,\n    default: 1,\n  },\n})\n</script>\n"], "names": [], "mappings": ";;;mCAKc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}