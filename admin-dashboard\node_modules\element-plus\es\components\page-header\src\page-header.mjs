import { Back } from '@element-plus/icons-vue';
import { buildProps } from '../../../utils/vue/props/runtime.mjs';
import { iconPropType } from '../../../utils/vue/icon.mjs';

const pageHeaderProps = buildProps({
  icon: {
    type: iconPropType,
    default: () => Back
  },
  title: String,
  content: {
    type: String,
    default: ""
  }
});
const pageHeaderEmits = {
  back: () => true
};

export { pageHeaderEmits, pageHeaderProps };
//# sourceMappingURL=page-header.mjs.map
