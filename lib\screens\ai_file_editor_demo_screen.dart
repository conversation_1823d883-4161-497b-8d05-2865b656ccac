import 'dart:io';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/ai_file_editor_controller.dart';
import '../controllers/smart_composer_controller.dart';
import '../widgets/file_edit_preview_widget.dart';
import '../utils/ai_config_validator.dart';

/// AI文件编辑器演示页面
class AIFileEditorDemoScreen extends StatefulWidget {
  const AIFileEditorDemoScreen({Key? key}) : super(key: key);

  @override
  State<AIFileEditorDemoScreen> createState() => _AIFileEditorDemoScreenState();
}

class _AIFileEditorDemoScreenState extends State<AIFileEditorDemoScreen> {
  final AIFileEditorController _fileController = Get.put(AIFileEditorController());
  final SmartComposerController _smartController = Get.find<SmartComposerController>();
  final TextEditingController _instructionController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI文件编辑器演示'),
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelp,
          ),
        ],
      ),
      body: Column(
        children: [
          // 当前文件信息
          _buildCurrentFileInfo(),
          
          // 编辑指令输入
          _buildInstructionInput(),
          
          // 待处理编辑列表
          Expanded(
            child: _buildPendingEditsList(),
          ),
          
          // 编辑历史
          _buildEditHistory(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createDemoFile,
        child: const Icon(Icons.add),
        tooltip: '创建演示文件',
      ),
    );
  }

  /// 构建当前文件信息
  Widget _buildCurrentFileInfo() {
    return Obx(() {
      final filePath = _fileController.currentFilePath.value;
      if (filePath.isEmpty) {
        return Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Icon(Icons.info_outline),
                const SizedBox(width: 8),
                const Text('请先创建或选择一个文件进行编辑'),
                const Spacer(),
                ElevatedButton(
                  onPressed: _createDemoFile,
                  child: const Text('创建演示文件'),
                ),
              ],
            ),
          ),
        );
      }

      return Card(
        margin: const EdgeInsets.all(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.edit_document),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '当前编辑文件: ${filePath.split('/').last}',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      _fileController.currentFilePath.value = '';
                      _fileController.currentFileContent.value = '';
                    },
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                '文件路径: $filePath',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 8),
              Text(
                '文件大小: ${_fileController.currentFileContent.value.length} 字符',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建编辑指令输入
  Widget _buildInstructionInput() {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI编辑指令',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _instructionController,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: '输入您想要AI执行的编辑指令，例如：\n- 添加注释\n- 重构代码\n- 修复语法错误',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Obx(() => ElevatedButton.icon(
                      onPressed: _fileController.isEditing.value ? null : _requestEdit,
                      icon: _fileController.isEditing.value
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.auto_fix_high),
                      label: Text(_fileController.isEditing.value ? '处理中...' : '请求AI编辑'),
                    )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建待处理编辑列表
  Widget _buildPendingEditsList() {
    return Obx(() {
      final pendingEdits = _fileController.pendingEdits;
      if (pendingEdits.isEmpty) {
        return const Center(
          child: Text('暂无待处理的编辑建议'),
        );
      }

      return ListView.builder(
        itemCount: pendingEdits.length,
        itemBuilder: (context, index) {
          final edit = pendingEdits[index];
          return FileEditPreviewWidget(
            pendingEdit: edit,
            onApplied: () => setState(() {}),
            onRejected: () => setState(() {}),
          );
        },
      );
    });
  }

  /// 构建编辑历史
  Widget _buildEditHistory() {
    return Obx(() {
      final history = _fileController.editHistory;
      if (history.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        height: 100,
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '编辑历史 (${history.length})',
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                const Spacer(),
                TextButton(
                  onPressed: _fileController.undoLastEdit,
                  child: const Text('撤销上次编辑'),
                ),
              ],
            ),
            Expanded(
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: history.length,
                itemBuilder: (context, index) {
                  final edit = history[index];
                  return Container(
                    width: 200,
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Theme.of(context).dividerColor),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          edit.description,
                          style: Theme.of(context).textTheme.bodySmall,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const Spacer(),
                        Text(
                          _formatTime(edit.timestamp),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 请求AI编辑
  void _requestEdit() async {
    final instruction = _instructionController.text.trim();
    if (instruction.isEmpty) {
      Get.snackbar('提示', '请输入编辑指令');
      return;
    }

    if (_fileController.currentFilePath.value.isEmpty) {
      Get.snackbar('提示', '请先选择要编辑的文件');
      return;
    }

    // 验证配置
    final settings = _smartController.settings.value;
    final validationResult = AIConfigValidator.validateSettings(settings);

    if (!validationResult.isValid) {
      _showConfigErrorDialog(validationResult);
      return;
    }

    // 获取默认模型配置
    final modelId = settings.defaultChatModelId;
    if (modelId == null) {
      Get.snackbar('错误', '请先配置AI模型');
      return;
    }

    final model = settings.chatModels.firstWhereOrNull((m) => m.id == modelId);
    final provider = model != null
        ? settings.providers.firstWhereOrNull((p) => p.id == model.providerId)
        : null;

    if (model == null || provider == null) {
      Get.snackbar('错误', '模型配置不完整');
      return;
    }

    // 验证具体的模型和提供商配置
    final providerValidation = AIConfigValidator.validateProvider(provider);
    final modelValidation = AIConfigValidator.validateChatModel(model, provider);

    if (!providerValidation.isValid || !modelValidation.isValid) {
      final errors = [...providerValidation.errors, ...modelValidation.errors];
      Get.snackbar('配置错误', errors.join('\n'));
      return;
    }

    try {
      await _fileController.requestAIEdit(
        instruction: instruction,
        model: model,
        provider: provider,
      );

      _instructionController.clear();
    } catch (e) {
      Get.snackbar('编辑失败', e.toString());
    }
  }

  /// 显示配置错误对话框
  void _showConfigErrorDialog(ValidationResult result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('配置错误'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (result.errors.isNotEmpty) ...[
              const Text('错误:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red)),
              ...result.errors.map((error) => Text('• $error')),
              const SizedBox(height: 8),
            ],
            if (result.warnings.isNotEmpty) ...[
              const Text('警告:', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.orange)),
              ...result.warnings.map((warning) => Text('• $warning')),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Get.toNamed('/settings');
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  /// 创建演示文件
  void _createDemoFile() async {
    final demoContent = '''# 演示文件

这是一个用于测试AI文件编辑功能的演示文件。

## 功能特性

- AI驱动的文件编辑
- 实时预览修改
- 编辑历史记录
- 撤销功能

## 示例代码

```dart
void main() {
  print('Hello, AI File Editor!');
}
```

您可以尝试以下编辑指令：
1. "添加更多注释"
2. "重构代码结构"
3. "添加错误处理"
4. "优化性能"
''';

    // 创建临时文件路径
    final filePath = '/tmp/demo_file_${DateTime.now().millisecondsSinceEpoch}.md';

    // 直接写入文件
    try {
      final file = File(filePath);
      await file.writeAsString(demoContent);
      final success = await _fileController.openFile(filePath);

      if (success) {
        Get.snackbar('成功', '演示文件已创建并打开');
      } else {
        Get.snackbar('错误', '打开演示文件失败');
      }
    } catch (e) {
      Get.snackbar('错误', '创建演示文件失败: $e');
    }
  }

  /// 显示帮助信息
  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('使用帮助'),
        content: const Text(
          '1. 点击"创建演示文件"按钮创建一个测试文件\n'
          '2. 在编辑指令框中输入您想要AI执行的操作\n'
          '3. 点击"请求AI编辑"按钮\n'
          '4. 查看AI生成的编辑建议\n'
          '5. 选择"应用修改"或"拒绝"编辑建议\n'
          '6. 可以随时撤销最近的编辑',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  @override
  void dispose() {
    _instructionController.dispose();
    super.dispose();
  }
}
