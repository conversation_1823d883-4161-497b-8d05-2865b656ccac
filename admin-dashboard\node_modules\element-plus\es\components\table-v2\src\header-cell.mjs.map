{"version": 3, "file": "header-cell.mjs", "sources": ["../../../../../../packages/components/table-v2/src/header-cell.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { classType, column } from './common'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const tableV2HeaderCell = buildProps({\n  class: classType,\n  columnIndex: Number,\n  column,\n})\n\nexport type TableV2HeaderCell = ExtractPropTypes<typeof tableV2HeaderCell>\nexport type TableV2HeaderCellPublic = __ExtractPublicPropTypes<\n  typeof tableV2HeaderCell\n>\n"], "names": [], "mappings": ";;;AAEY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,KAAK,EAAE,SAAS;AAClB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,MAAM;AACR,CAAC;;;;"}