{"version": 3, "file": "skeleton.mjs", "sources": ["../../../../../../packages/components/skeleton/src/skeleton.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type Skeleton from './skeleton.vue'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { ThrottleType } from '@element-plus/hooks'\n\nexport const skeletonProps = buildProps({\n  /**\n   * @description whether showing the animation\n   */\n  animated: Boolean,\n  /**\n   * @description how many fake items to render to the DOM\n   */\n  count: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description whether showing the real DOM\n   */\n  rows: {\n    type: Number,\n    default: 3,\n  },\n  /**\n   * @description numbers of the row, only useful when no template slot were given\n   */\n  loading: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description rendering delay in milliseconds\n   */\n  throttle: {\n    type: definePropType<ThrottleType>([Number, Object]),\n  },\n} as const)\nexport type SkeletonProps = ExtractPropTypes<typeof skeletonProps>\nexport type SkeletonPropsPublic = __ExtractPublicPropTypes<typeof skeletonProps>\n\nexport type SkeletonInstance = InstanceType<typeof Skeleton> & unknown\n"], "names": [], "mappings": ";;AACY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,GAAG;AACH,CAAC;;;;"}