const https = require('https');

// 测试CloudBase部署状态 - 使用自定义域名
const testUrls = [
  'https://api.dznovel.top',
  'https://api.dznovel.top/health',
  'https://api.dznovel.top/auth/login'
];

console.log('🚀 测试CloudBase部署状态...\n');

async function testUrl(url) {
  return new Promise((resolve) => {
    const req = https.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        resolve({
          url,
          status: res.statusCode,
          data: data.substring(0, 200) // 只显示前200个字符
        });
      });
    });

    req.on('error', (error) => {
      resolve({
        url,
        status: 'ERROR',
        data: error.message
      });
    });

    req.setTimeout(10000, () => {
      req.destroy();
      resolve({
        url,
        status: 'TIMEOUT',
        data: '请求超时'
      });
    });
  });
}

async function runTests() {
  for (const url of testUrls) {
    console.log(`🔍 测试: ${url}`);
    const result = await testUrl(url);
    
    if (result.status === 200) {
      console.log(`✅ 成功 - 状态码: ${result.status}`);
      console.log(`📄 响应: ${result.data}`);
    } else {
      console.log(`❌ 失败 - 状态: ${result.status}`);
      console.log(`📄 错误: ${result.data}`);
    }
    console.log('');
  }

  console.log('🏁 测试完成！');
  console.log('\n💡 如果所有测试都失败，可能需要：');
  console.log('1. 在CloudBase控制台为函数配置HTTP触发器');
  console.log('2. 检查函数是否正确部署');
  console.log('3. 确认环境ID是否正确');
}

runTests().catch(console.error);
