{"version": 3, "file": "descriptions-row.js", "sources": ["../../../../../../packages/components/descriptions/src/descriptions-row.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { DescriptionItemVNode } from './description-item'\n\nexport const descriptionsRowProps = buildProps({\n  row: {\n    type: definePropType<DescriptionItemVNode[]>(Array),\n    default: () => [],\n  },\n} as const)\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,oBAAoB,GAAGA,kBAAU,CAAC;AAC/C,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAEC,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,CAAC;;;;"}