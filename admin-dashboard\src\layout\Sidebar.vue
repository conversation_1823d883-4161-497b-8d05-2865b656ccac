<template>
  <div class="sidebar-container">
    <div class="sidebar-logo">
      <div class="logo-content">
        <el-icon size="28" color="#1890ff">
          <Reading />
        </el-icon>
        <span v-show="!appStore.sidebarCollapsed" class="logo-text">
          小说管理
        </span>
      </div>
    </div>

    <el-menu
      :default-active="activeMenu"
      :collapse="appStore.sidebarCollapsed"
      :unique-opened="true"
      background-color="#001529"
      text-color="#rgba(255, 255, 255, 0.65)"
      active-text-color="#fff"
      @select="handleMenuSelect"
    >
      <template v-for="route in menuRoutes" :key="route.path">
        <el-menu-item
          v-if="!route.meta?.hidden"
          :index="route.path"
          class="menu-item"
        >
          <el-icon>
            <component :is="route.meta?.icon || 'Document'" />
          </el-icon>
          <template #title>
            <span>{{ route.meta?.title }}</span>
          </template>
        </el-menu-item>
      </template>
    </el-menu>

    <!-- 底部信息 -->
    <div class="sidebar-footer" v-show="!appStore.sidebarCollapsed">
      <div class="footer-info">
        <div class="version">v{{ appStore.systemInfo.version }}</div>
        <div class="status">
          <el-icon color="#52c41a" size="12">
            <CircleCheckFilled />
          </el-icon>
          <span>系统正常</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores/app'
import { useRouter, useRoute } from 'vue-router'

const appStore = useAppStore()
const router = useRouter()
const route = useRoute()

// 获取菜单路由
const menuRoutes = computed(() => {
  const routes = router.getRoutes()
  const layoutRoute = routes.find(r => r.name === 'Layout')
  return layoutRoute?.children || []
})

// 当前激活的菜单
const activeMenu = computed(() => {
  return route.path
})

// 处理菜单选择
const handleMenuSelect = (index: string) => {
  if (index !== route.path) {
    router.push(index)
  }
}
</script>

<style scoped>
.sidebar-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-logo {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #1f1f1f;
}

.logo-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
}

.logo-text {
  color: #fff;
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
}

.el-menu {
  flex: 1;
  border: none;
  overflow-y: auto;
}

.menu-item {
  margin: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

.menu-item.is-active {
  background-color: #1890ff !important;
  color: #fff !important;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #1f1f1f;
}

.footer-info {
  text-align: center;
}

.version {
  color: rgba(255, 255, 255, 0.45);
  font-size: 12px;
  margin-bottom: 8px;
}

.status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  color: rgba(255, 255, 255, 0.65);
  font-size: 12px;
}

/* 滚动条样式 */
.el-menu::-webkit-scrollbar {
  width: 4px;
}

.el-menu::-webkit-scrollbar-track {
  background: transparent;
}

.el-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.el-menu::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
