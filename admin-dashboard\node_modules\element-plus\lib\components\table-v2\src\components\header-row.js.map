{"version": 3, "file": "header-row.js", "sources": ["../../../../../../../packages/components/table-v2/src/components/header-row.tsx"], "sourcesContent": ["import { defineComponent } from 'vue'\nimport { isArray } from '@element-plus/utils'\nimport { tableV2HeaderRowProps } from '../header-row'\n\nimport type { CSSProperties } from 'vue'\nimport type { ColumnCellsType } from '../types'\nimport type { TableV2HeaderRowProps } from '../header-row'\n\nconst TableV2HeaderRow = defineComponent({\n  name: 'ElTableV2HeaderRow',\n  props: tableV2HeaderRowProps,\n  setup(props, { slots }) {\n    return () => {\n      const { columns, columnsStyles, headerIndex, style } = props\n      let Cells: ColumnCellsType = columns.map((column, columnIndex) => {\n        return slots.cell!({\n          columns,\n          column,\n          columnIndex,\n          headerIndex,\n          style: columnsStyles[column.key!],\n        })\n      })\n\n      if (slots.header) {\n        Cells = slots.header({\n          cells: Cells.map((node) => {\n            if (isArray(node) && node.length === 1) {\n              return node[0]\n            }\n            return node\n          }),\n          columns,\n          headerIndex,\n        })\n      }\n\n      return (\n        <div class={props.class} style={style} role=\"row\">\n          {Cells}\n        </div>\n      )\n    }\n  },\n})\n\nexport default TableV2HeaderRow\n\nexport type TableV2HeaderRowCellRendererParams = {\n  columns: TableV2HeaderRowProps['columns']\n  column: TableV2HeaderRowProps['columns'][number]\n  columnIndex: number\n  headerIndex: number\n  style: CSSProperties\n}\n\nexport type TableV2HeaderRowRendererParams = {\n  cells: ColumnCellsType\n  columns: TableV2HeaderRowProps['columns']\n  headerIndex: number\n}\n"], "names": ["TableV2HeaderRow", "defineComponent", "name", "props", "tableV2HeaderRowProps", "slots", "columns", "columnsStyles", "headerIndex", "style", "Cells", "map", "column", "columnIndex", "header", "cells", "node", "isArray"], "mappings": ";;;;;;;;AAQA,MAAMA,gBAAgB,GAAGC,mBAAe,CAAC;AACvCC,EAAAA,IAAI,EAAE,oBADiC;AAEvCC,EAAAA,KAAK,EAAEC,+BAFgC;;IAGlC;AAAUC,GAAAA,EAAAA;AAAF,IAAW,OAAA,MAAA;AACtB,MAAA,MAAa;QACL,OAAA;QAAEC,aAAF;QAAWC,WAAX;QAA0BC,KAA1B;AAAuCC,OAAAA,GAAAA,KAAAA,CAAAA;AAAvC,MAAA,IAAiDN,KAAvD,GAAA,OAAA,CAAA,GAAA,CAAA,CAAA,MAAA,EAAA,WAAA,KAAA;QACIO,OAAsB,KAAGJ,CAAAA,IAAO,CAACK;UAC5BN,OAAAA;UACLC,MADiB;UAEjBM,WAFiB;UAGjBC,WAHiB;UAIjBL,KAJiB,EAAA,aAAA,CAAA,MAAA,CAAA,GAAA,CAAA;AAKjBC,SAAAA,CAAAA,CAAAA;AALiB,OAAA,CAAA,CAAZ;AAOR,MAAA,IARD,KAAA,CAAA,MAAA,EAAA;;UAUIJ,KAAK,EAACS,KAAV,CAAkB,GAAA,CAAA,CAAA,IAAA,KAAA;AAChBJ,YAAAA,IAAQL,cAAMS,CAAAA,IAAN,CAAa,IAAA,IAAA,CAAA,MAAA,KAAA,CAAA,EAAA;AACnBC,cAAAA,OAAY,IAAL,CAAA,CAAWC;aACZC;mBACKD,IAAAA,CAAAA;AACR,WAAA,CAAA;;AACD,UAAA,WAAA;AACD,SAAA,CAAA,CALM;;AAOPR,MAAAA,OAAAA,eAAAA,CAAAA,KAAAA,EAAAA;AARmB,QAAA,OAArB,EAAA,KAAA,CAAA,KAAA;AAUD,QAAA,OAAA,EAAA,KAAA;;AAED,OAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA;;AAAA,GAAA;;AAAA,gBAAA,gBAAA;;;;"}