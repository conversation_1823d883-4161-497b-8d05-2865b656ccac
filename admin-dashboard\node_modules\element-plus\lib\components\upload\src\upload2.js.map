{"version": 3, "file": "upload2.js", "sources": ["../../../../../../packages/components/upload/src/upload.vue"], "sourcesContent": ["<template>\n  <div>\n    <upload-list\n      v-if=\"isPictureCard && showFileList\"\n      :disabled=\"disabled\"\n      :list-type=\"listType\"\n      :files=\"uploadFiles\"\n      :crossorigin=\"crossorigin\"\n      :handle-preview=\"onPreview\"\n      @remove=\"handleRemove\"\n    >\n      <template v-if=\"$slots.file\" #default=\"{ file, index }\">\n        <slot name=\"file\" :file=\"file\" :index=\"index\" />\n      </template>\n      <template #append>\n        <upload-content ref=\"uploadRef\" v-bind=\"uploadContentProps\">\n          <slot v-if=\"$slots.trigger\" name=\"trigger\" />\n          <slot v-if=\"!$slots.trigger && $slots.default\" />\n        </upload-content>\n      </template>\n    </upload-list>\n\n    <upload-content\n      v-if=\"!isPictureCard || (isPictureCard && !showFileList)\"\n      ref=\"uploadRef\"\n      v-bind=\"uploadContentProps\"\n    >\n      <slot v-if=\"$slots.trigger\" name=\"trigger\" />\n      <slot v-if=\"!$slots.trigger && $slots.default\" />\n    </upload-content>\n\n    <slot v-if=\"$slots.trigger\" />\n    <slot name=\"tip\" />\n    <upload-list\n      v-if=\"!isPictureCard && showFileList\"\n      :disabled=\"disabled\"\n      :list-type=\"listType\"\n      :files=\"uploadFiles\"\n      :crossorigin=\"crossorigin\"\n      :handle-preview=\"onPreview\"\n      @remove=\"handleRemove\"\n    >\n      <template v-if=\"$slots.file\" #default=\"{ file, index }\">\n        <slot name=\"file\" :file=\"file\" :index=\"index\" />\n      </template>\n    </upload-list>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onBeforeUnmount, provide, shallowRef, toRef } from 'vue'\nimport { useFormDisabled } from '@element-plus/components/form'\nimport { uploadContextKey } from './constants'\nimport UploadList from './upload-list.vue'\nimport UploadContent from './upload-content.vue'\nimport { useHandlers } from './use-handlers'\nimport { uploadProps } from './upload'\n\nimport type {\n  UploadContentInstance,\n  UploadContentProps,\n} from './upload-content'\n\ndefineOptions({\n  name: 'ElUpload',\n})\n\nconst props = defineProps(uploadProps)\n\nconst disabled = useFormDisabled()\n\nconst uploadRef = shallowRef<UploadContentInstance>()\nconst {\n  abort,\n  submit,\n  clearFiles,\n  uploadFiles,\n  handleStart,\n  handleError,\n  handleRemove,\n  handleSuccess,\n  handleProgress,\n  revokeFileObjectURL,\n} = useHandlers(props, uploadRef)\n\nconst isPictureCard = computed(() => props.listType === 'picture-card')\n\nconst uploadContentProps = computed<UploadContentProps>(() => ({\n  ...props,\n  fileList: uploadFiles.value,\n  onStart: handleStart,\n  onProgress: handleProgress,\n  onSuccess: handleSuccess,\n  onError: handleError,\n  onRemove: handleRemove,\n}))\n\nonBeforeUnmount(() => {\n  uploadFiles.value.forEach(revokeFileObjectURL)\n})\n\nprovide(uploadContextKey, {\n  accept: toRef(props, 'accept'),\n})\n\ndefineExpose({\n  /** @description cancel upload request */\n  abort,\n  /** @description upload the file list manually */\n  submit,\n  /** @description clear the file list  */\n  clearFiles,\n  /** @description select the file manually */\n  handleStart,\n  /** @description remove the file manually */\n  handleRemove,\n})\n</script>\n"], "names": ["useFormDisabled", "shallowRef", "useHandlers", "computed", "onBeforeUnmount", "provide", "uploadContextKey", "toRef", "_openBlock", "_createElementBlock", "_unref", "_createBlock", "UploadList"], "mappings": ";;;;;;;;;;;;;uCA+Dc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,WAAWA,kCAAgB,EAAA,CAAA;AAEjC,IAAA,MAAM,YAAYC,cAAkC,EAAA,CAAA;AACpD,IAAM,MAAA;AAAA,MACJ,KAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,WAAA;AAAA,MACA,WAAA;AAAA,MACA,WAAA;AAAA,MACA,YAAA;AAAA,MACA,aAAA;AAAA,MACA,cAAA;AAAA,MACA,mBAAA;AAAA,KACF,GAAIC,uBAAY,CAAA,KAAA,EAAO,SAAS,CAAA,CAAA;AAEhC,IAAA,MAAM,aAAgB,GAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,aAAa,cAAc,CAAA,CAAA;AAEtE,IAAM,MAAA,kBAAA,GAAqBA,aAA6B,OAAO;AAAA,MAC7D,GAAG,KAAA;AAAA,MACH,UAAU,WAAY,CAAA,KAAA;AAAA,MACtB,OAAS,EAAA,WAAA;AAAA,MACT,UAAY,EAAA,cAAA;AAAA,MACZ,SAAW,EAAA,aAAA;AAAA,MACX,OAAS,EAAA,WAAA;AAAA,MACT,QAAU,EAAA,YAAA;AAAA,KACV,CAAA,CAAA,CAAA;AAEF,IAAAC,mBAAA,CAAgB,MAAM;AACpB,MAAY,WAAA,CAAA,KAAA,CAAM,QAAQ,mBAAmB,CAAA,CAAA;AAAA,KAC9C,CAAA,CAAA;AAED,IAAAC,WAAA,CAAQC,0BAAkB,EAAA;AAAA,MACxB,MAAA,EAAQC,SAAM,CAAA,KAAA,EAAO,QAAQ,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MAAA,KAAA;AAAA,MAEX,MAAA;AAAA,MAAA,UAAA;AAAA,MAEA,WAAA;AAAA,MAAA,YAAA;AAAA,KAEA,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAEA,OAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA,IAAA,EAAA;AAAA,QAAAC,SAAA,CAAA,aAAA,CAAA,IAAA,IAAA,CAAA,YAAA,IAAAF,aAAA,EAAA,EAAAG,eAAA,CAAAC,qBAAA,EAAA;AAAA,UAEA,GAAA,EAAA,CAAA;AAAA,UACD,QAAA,EAAAF,SAAA,CAAA,QAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}