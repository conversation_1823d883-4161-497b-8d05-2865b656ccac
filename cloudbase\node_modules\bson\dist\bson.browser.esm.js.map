{"version": 3, "file": "bson.browser.esm.js", "sources": ["../node_modules/base64-js/index.js", "../node_modules/ieee754/index.js", "../node_modules/buffer/index.js", "../node_modules/tslib/tslib.es6.js", "../src/error.ts", "../src/utils/global.ts", "../src/parser/utils.ts", "../src/ensure_buffer.ts", "../src/uuid_utils.ts", "../src/constants.ts", "../src/binary.ts", "../src/code.ts", "../src/db_ref.ts", "../src/long.ts", "../src/decimal128.ts", "../src/double.ts", "../src/int_32.ts", "../src/max_key.ts", "../src/min_key.ts", "../src/objectid.ts", "../src/regexp.ts", "../src/symbol.ts", "../src/timestamp.ts", "../src/extended_json.ts", "../src/map.ts", "../src/parser/calculate_size.ts", "../src/validate_utf8.ts", "../src/parser/deserializer.ts", "../src/parser/serializer.ts", "../src/bson.ts"], "sourcesContent": ["'use strict'\n\nexports.byteLength = byteLength\nexports.toByteArray = toByteArray\nexports.fromByteArray = fromByteArray\n\nvar lookup = []\nvar revLookup = []\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array\n\nvar code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'\nfor (var i = 0, len = code.length; i < len; ++i) {\n  lookup[i] = code[i]\n  revLookup[code.charCodeAt(i)] = i\n}\n\n// Support decoding URL-safe base64 strings, as Node.js does.\n// See: https://en.wikipedia.org/wiki/Base64#URL_applications\nrevLookup['-'.charCodeAt(0)] = 62\nrevLookup['_'.charCodeAt(0)] = 63\n\nfunction getLens (b64) {\n  var len = b64.length\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // Trim off extra bytes after placeholder bytes are found\n  // See: https://github.com/beatgammit/base64-js/issues/42\n  var validLen = b64.indexOf('=')\n  if (validLen === -1) validLen = len\n\n  var placeHoldersLen = validLen === len\n    ? 0\n    : 4 - (validLen % 4)\n\n  return [validLen, placeHoldersLen]\n}\n\n// base64 is 4/3 + up to two characters of the original data\nfunction byteLength (b64) {\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction _byteLength (b64, validLen, placeHoldersLen) {\n  return ((validLen + placeHoldersLen) * 3 / 4) - placeHoldersLen\n}\n\nfunction toByteArray (b64) {\n  var tmp\n  var lens = getLens(b64)\n  var validLen = lens[0]\n  var placeHoldersLen = lens[1]\n\n  var arr = new Arr(_byteLength(b64, validLen, placeHoldersLen))\n\n  var curByte = 0\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  var len = placeHoldersLen > 0\n    ? validLen - 4\n    : validLen\n\n  var i\n  for (i = 0; i < len; i += 4) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 18) |\n      (revLookup[b64.charCodeAt(i + 1)] << 12) |\n      (revLookup[b64.charCodeAt(i + 2)] << 6) |\n      revLookup[b64.charCodeAt(i + 3)]\n    arr[curByte++] = (tmp >> 16) & 0xFF\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 2) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 2) |\n      (revLookup[b64.charCodeAt(i + 1)] >> 4)\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  if (placeHoldersLen === 1) {\n    tmp =\n      (revLookup[b64.charCodeAt(i)] << 10) |\n      (revLookup[b64.charCodeAt(i + 1)] << 4) |\n      (revLookup[b64.charCodeAt(i + 2)] >> 2)\n    arr[curByte++] = (tmp >> 8) & 0xFF\n    arr[curByte++] = tmp & 0xFF\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] +\n    lookup[num >> 12 & 0x3F] +\n    lookup[num >> 6 & 0x3F] +\n    lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp\n  var output = []\n  for (var i = start; i < end; i += 3) {\n    tmp =\n      ((uint8[i] << 16) & 0xFF0000) +\n      ((uint8[i + 1] << 8) & 0xFF00) +\n      (uint8[i + 2] & 0xFF)\n    output.push(tripletToBase64(tmp))\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  var tmp\n  var len = uint8.length\n  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes\n  var parts = []\n  var maxChunkLength = 16383 // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 2] +\n      lookup[(tmp << 4) & 0x3F] +\n      '=='\n    )\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + uint8[len - 1]\n    parts.push(\n      lookup[tmp >> 10] +\n      lookup[(tmp >> 4) & 0x3F] +\n      lookup[(tmp << 2) & 0x3F] +\n      '='\n    )\n  }\n\n  return parts.join('')\n}\n", "/*! ieee754. BSD-3-Clause License. Feross A<PERSON> <https://feross.org/opensource> */\nexports.read = function (buffer, offset, isLE, mLen, nBytes) {\n  var e, m\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var nBits = -7\n  var i = isLE ? (nBytes - 1) : 0\n  var d = isLE ? -1 : 1\n  var s = buffer[offset + i]\n\n  i += d\n\n  e = s & ((1 << (-nBits)) - 1)\n  s >>= (-nBits)\n  nBits += eLen\n  for (; nBits > 0; e = (e * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1)\n  e >>= (-nBits)\n  nBits += mLen\n  for (; nBits > 0; m = (m * 256) + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen)\n    e = e - eBias\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nexports.write = function (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c\n  var eLen = (nBytes * 8) - mLen - 1\n  var eMax = (1 << eLen) - 1\n  var eBias = eMax >> 1\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)\n  var i = isLE ? 0 : (nBytes - 1)\n  var d = isLE ? 1 : -1\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0\n\n  value = Math.abs(value)\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0\n    e = eMax\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2)\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--\n      c *= 2\n    }\n    if (e + eBias >= 1) {\n      value += rt / c\n    } else {\n      value += rt * Math.pow(2, 1 - eBias)\n    }\n    if (value * c >= 2) {\n      e++\n      c /= 2\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0\n      e = eMax\n    } else if (e + eBias >= 1) {\n      m = ((value * c) - 1) * Math.pow(2, mLen)\n      e = e + eBias\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)\n      e = 0\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m\n  eLen += mLen\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128\n}\n", "/*!\n * The buffer module from node.js, for the browser.\n *\n * <AUTHOR> <https://feross.org>\n * @license  MIT\n */\n/* eslint-disable no-proto */\n\n'use strict'\n\nvar base64 = require('base64-js')\nvar ieee754 = require('ieee754')\nvar customInspectSymbol =\n  (typeof Symbol === 'function' && typeof Symbol['for'] === 'function') // eslint-disable-line dot-notation\n    ? Symbol['for']('nodejs.util.inspect.custom') // eslint-disable-line dot-notation\n    : null\n\nexports.Buffer = Buffer\nexports.SlowBuffer = SlowBuffer\nexports.INSPECT_MAX_BYTES = 50\n\nvar K_MAX_LENGTH = 0x7fffffff\nexports.kMaxLength = K_MAX_LENGTH\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Print warning and recommend using `buffer` v4.x which has an Object\n *               implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * We report that the browser does not support typed arrays if the are not subclassable\n * using __proto__. Firefox 4-29 lacks support for adding new properties to `Uint8Array`\n * (See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438). IE 10 lacks support\n * for __proto__ and has a buggy typed array implementation.\n */\nBuffer.TYPED_ARRAY_SUPPORT = typedArraySupport()\n\nif (!Buffer.TYPED_ARRAY_SUPPORT && typeof console !== 'undefined' &&\n    typeof console.error === 'function') {\n  console.error(\n    'This browser lacks typed array (Uint8Array) support which is required by ' +\n    '`buffer` v5.x. Use `buffer` v4.x if you require old browser support.'\n  )\n}\n\nfunction typedArraySupport () {\n  // Can typed array instances can be augmented?\n  try {\n    var arr = new Uint8Array(1)\n    var proto = { foo: function () { return 42 } }\n    Object.setPrototypeOf(proto, Uint8Array.prototype)\n    Object.setPrototypeOf(arr, proto)\n    return arr.foo() === 42\n  } catch (e) {\n    return false\n  }\n}\n\nObject.defineProperty(Buffer.prototype, 'parent', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.buffer\n  }\n})\n\nObject.defineProperty(Buffer.prototype, 'offset', {\n  enumerable: true,\n  get: function () {\n    if (!Buffer.isBuffer(this)) return undefined\n    return this.byteOffset\n  }\n})\n\nfunction createBuffer (length) {\n  if (length > K_MAX_LENGTH) {\n    throw new RangeError('The value \"' + length + '\" is invalid for option \"size\"')\n  }\n  // Return an augmented `Uint8Array` instance\n  var buf = new Uint8Array(length)\n  Object.setPrototypeOf(buf, Buffer.prototype)\n  return buf\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new TypeError(\n        'The \"string\" argument must be of type string. Received type number'\n      )\n    }\n    return allocUnsafe(arg)\n  }\n  return from(arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192 // not used by this implementation\n\nfunction from (value, encodingOrOffset, length) {\n  if (typeof value === 'string') {\n    return fromString(value, encodingOrOffset)\n  }\n\n  if (ArrayBuffer.isView(value)) {\n    return fromArrayView(value)\n  }\n\n  if (value == null) {\n    throw new TypeError(\n      'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n      'or Array-like Object. Received type ' + (typeof value)\n    )\n  }\n\n  if (isInstance(value, ArrayBuffer) ||\n      (value && isInstance(value.buffer, ArrayBuffer))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof SharedArrayBuffer !== 'undefined' &&\n      (isInstance(value, SharedArrayBuffer) ||\n      (value && isInstance(value.buffer, SharedArrayBuffer)))) {\n    return fromArrayBuffer(value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'number') {\n    throw new TypeError(\n      'The \"value\" argument must not be of type number. Received type number'\n    )\n  }\n\n  var valueOf = value.valueOf && value.valueOf()\n  if (valueOf != null && valueOf !== value) {\n    return Buffer.from(valueOf, encodingOrOffset, length)\n  }\n\n  var b = fromObject(value)\n  if (b) return b\n\n  if (typeof Symbol !== 'undefined' && Symbol.toPrimitive != null &&\n      typeof value[Symbol.toPrimitive] === 'function') {\n    return Buffer.from(\n      value[Symbol.toPrimitive]('string'), encodingOrOffset, length\n    )\n  }\n\n  throw new TypeError(\n    'The first argument must be one of type string, Buffer, ArrayBuffer, Array, ' +\n    'or Array-like Object. Received type ' + (typeof value)\n  )\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(value, encodingOrOffset, length)\n}\n\n// Note: Change prototype *after* Buffer.from is defined to workaround Chrome bug:\n// https://github.com/feross/buffer/pull/148\nObject.setPrototypeOf(Buffer.prototype, Uint8Array.prototype)\nObject.setPrototypeOf(Buffer, Uint8Array)\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be of type number')\n  } else if (size < 0) {\n    throw new RangeError('The value \"' + size + '\" is invalid for option \"size\"')\n  }\n}\n\nfunction alloc (size, fill, encoding) {\n  assertSize(size)\n  if (size <= 0) {\n    return createBuffer(size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpreted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(size).fill(fill, encoding)\n      : createBuffer(size).fill(fill)\n  }\n  return createBuffer(size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(size, fill, encoding)\n}\n\nfunction allocUnsafe (size) {\n  assertSize(size)\n  return createBuffer(size < 0 ? 0 : checked(size) | 0)\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(size)\n}\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(size)\n}\n\nfunction fromString (string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8'\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('Unknown encoding: ' + encoding)\n  }\n\n  var length = byteLength(string, encoding) | 0\n  var buf = createBuffer(length)\n\n  var actual = buf.write(string, encoding)\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    buf = buf.slice(0, actual)\n  }\n\n  return buf\n}\n\nfunction fromArrayLike (array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0\n  var buf = createBuffer(length)\n  for (var i = 0; i < length; i += 1) {\n    buf[i] = array[i] & 255\n  }\n  return buf\n}\n\nfunction fromArrayView (arrayView) {\n  if (isInstance(arrayView, Uint8Array)) {\n    var copy = new Uint8Array(arrayView)\n    return fromArrayBuffer(copy.buffer, copy.byteOffset, copy.byteLength)\n  }\n  return fromArrayLike(arrayView)\n}\n\nfunction fromArrayBuffer (array, byteOffset, length) {\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\"offset\" is outside of buffer bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\"length\" is outside of buffer bounds')\n  }\n\n  var buf\n  if (byteOffset === undefined && length === undefined) {\n    buf = new Uint8Array(array)\n  } else if (length === undefined) {\n    buf = new Uint8Array(array, byteOffset)\n  } else {\n    buf = new Uint8Array(array, byteOffset, length)\n  }\n\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(buf, Buffer.prototype)\n\n  return buf\n}\n\nfunction fromObject (obj) {\n  if (Buffer.isBuffer(obj)) {\n    var len = checked(obj.length) | 0\n    var buf = createBuffer(len)\n\n    if (buf.length === 0) {\n      return buf\n    }\n\n    obj.copy(buf, 0, 0, len)\n    return buf\n  }\n\n  if (obj.length !== undefined) {\n    if (typeof obj.length !== 'number' || numberIsNaN(obj.length)) {\n      return createBuffer(0)\n    }\n    return fromArrayLike(obj)\n  }\n\n  if (obj.type === 'Buffer' && Array.isArray(obj.data)) {\n    return fromArrayLike(obj.data)\n  }\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < K_MAX_LENGTH` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= K_MAX_LENGTH) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + K_MAX_LENGTH.toString(16) + ' bytes')\n  }\n  return length | 0\n}\n\nfunction SlowBuffer (length) {\n  if (+length != length) { // eslint-disable-line eqeqeq\n    length = 0\n  }\n  return Buffer.alloc(+length)\n}\n\nBuffer.isBuffer = function isBuffer (b) {\n  return b != null && b._isBuffer === true &&\n    b !== Buffer.prototype // so Buffer.isBuffer(Buffer.prototype) will be false\n}\n\nBuffer.compare = function compare (a, b) {\n  if (isInstance(a, Uint8Array)) a = Buffer.from(a, a.offset, a.byteLength)\n  if (isInstance(b, Uint8Array)) b = Buffer.from(b, b.offset, b.byteLength)\n  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {\n    throw new TypeError(\n      'The \"buf1\", \"buf2\" arguments must be one of type Buffer or Uint8Array'\n    )\n  }\n\n  if (a === b) return 0\n\n  var x = a.length\n  var y = b.length\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i]\n      y = b[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n}\n\nBuffer.concat = function concat (list, length) {\n  if (!Array.isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i\n  if (length === undefined) {\n    length = 0\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length)\n  var pos = 0\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i]\n    if (isInstance(buf, Uint8Array)) {\n      if (pos + buf.length > buffer.length) {\n        Buffer.from(buf).copy(buffer, pos)\n      } else {\n        Uint8Array.prototype.set.call(\n          buffer,\n          buf,\n          pos\n        )\n      }\n    } else if (!Buffer.isBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    } else {\n      buf.copy(buffer, pos)\n    }\n    pos += buf.length\n  }\n  return buffer\n}\n\nfunction byteLength (string, encoding) {\n  if (Buffer.isBuffer(string)) {\n    return string.length\n  }\n  if (ArrayBuffer.isView(string) || isInstance(string, ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    throw new TypeError(\n      'The \"string\" argument must be one of type string, Buffer, or ArrayBuffer. ' +\n      'Received type ' + typeof string\n    )\n  }\n\n  var len = string.length\n  var mustMatch = (arguments.length > 2 && arguments[2] === true)\n  if (!mustMatch && len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) {\n          return mustMatch ? -1 : utf8ToBytes(string).length // assume utf8\n        }\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\nBuffer.byteLength = byteLength\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coercion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0\n  start >>>= 0\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\n// This property is used by `Buffer.isBuffer` (and the `is-buffer` npm package)\n// to detect a Buffer instance. It's not possible to use `instanceof Buffer`\n// reliably in a browserify context because there could be multiple different\n// copies of the 'buffer' package in use. This method works even for Buffer\n// instances that were created from another copy of the `buffer` package.\n// See: https://github.com/feross/buffer/issues/154\nBuffer.prototype._isBuffer = true\n\nfunction swap (b, n, m) {\n  var i = b[n]\n  b[n] = b[m]\n  b[m] = i\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1)\n  }\n  return this\n}\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3)\n    swap(this, i + 1, i + 2)\n  }\n  return this\n}\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7)\n    swap(this, i + 1, i + 6)\n    swap(this, i + 2, i + 5)\n    swap(this, i + 3, i + 4)\n  }\n  return this\n}\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n}\n\nBuffer.prototype.toLocaleString = Buffer.prototype.toString\n\nBuffer.prototype.equals = function equals (b) {\n  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n}\n\nBuffer.prototype.inspect = function inspect () {\n  var str = ''\n  var max = exports.INSPECT_MAX_BYTES\n  str = this.toString('hex', 0, max).replace(/(.{2})/g, '$1 ').trim()\n  if (this.length > max) str += ' ... '\n  return '<Buffer ' + str + '>'\n}\nif (customInspectSymbol) {\n  Buffer.prototype[customInspectSymbol] = Buffer.prototype.inspect\n}\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (isInstance(target, Uint8Array)) {\n    target = Buffer.from(target, target.offset, target.byteLength)\n  }\n  if (!Buffer.isBuffer(target)) {\n    throw new TypeError(\n      'The \"target\" argument must be one of type Buffer or Uint8Array. ' +\n      'Received type ' + (typeof target)\n    )\n  }\n\n  if (start === undefined) {\n    start = 0\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0\n  }\n  if (thisStart === undefined) {\n    thisStart = 0\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0\n  end >>>= 0\n  thisStart >>>= 0\n  thisEnd >>>= 0\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart\n  var y = end - start\n  var len = Math.min(x, y)\n\n  var thisCopy = this.slice(thisStart, thisEnd)\n  var targetCopy = target.slice(start, end)\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i]\n      y = targetCopy[i]\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n}\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset\n    byteOffset = 0\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000\n  }\n  byteOffset = +byteOffset // Coerce to Number.\n  if (numberIsNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1)\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding)\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (Buffer.isBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF // Search for a byte value [0-255]\n    if (typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [val], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1\n  var arrLength = arr.length\n  var valLength = val.length\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase()\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2\n      arrLength /= 2\n      valLength /= 2\n      byteOffset /= 2\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i\n  if (dir) {\n    var foundIndex = -1\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex\n        foundIndex = -1\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n}\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n}\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n}\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0\n  var remaining = buf.length - offset\n  if (!length) {\n    length = remaining\n  } else {\n    length = Number(length)\n    if (length > remaining) {\n      length = remaining\n    }\n  }\n\n  var strLen = string.length\n\n  if (length > strLen / 2) {\n    length = strLen / 2\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16)\n    if (numberIsNaN(parsed)) return i\n    buf[offset + i] = parsed\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8'\n    length = this.length\n    offset = 0\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset\n    length = this.length\n    offset = 0\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset >>> 0\n    if (isFinite(length)) {\n      length = length >>> 0\n      if (encoding === undefined) encoding = 'utf8'\n    } else {\n      encoding = length\n      length = undefined\n    }\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset\n  if (length === undefined || length > remaining) length = remaining\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8'\n\n  var loweredCase = false\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return asciiWrite(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase()\n        loweredCase = true\n    }\n  }\n}\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n}\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return base64.fromByteArray(buf)\n  } else {\n    return base64.fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end)\n  var res = []\n\n  var i = start\n  while (i < end) {\n    var firstByte = buf[i]\n    var codePoint = null\n    var bytesPerSequence = (firstByte > 0xEF)\n      ? 4\n      : (firstByte > 0xDF)\n          ? 3\n          : (firstByte > 0xBF)\n              ? 2\n              : 1\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1]\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1]\n          thirdByte = buf[i + 2]\n          fourthByte = buf[i + 3]\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD\n      bytesPerSequence = 1\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800)\n      codePoint = 0xDC00 | codePoint & 0x3FF\n    }\n\n    res.push(codePoint)\n    i += bytesPerSequence\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = ''\n  var i = 0\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    )\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F)\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = ''\n  end = Math.min(buf.length, end)\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i])\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length\n\n  if (!start || start < 0) start = 0\n  if (!end || end < 0 || end > len) end = len\n\n  var out = ''\n  for (var i = start; i < end; ++i) {\n    out += hexSliceLookupTable[buf[i]]\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end)\n  var res = ''\n  // If bytes.length is odd, the last 8 bits must be ignored (same as node.js)\n  for (var i = 0; i < bytes.length - 1; i += 2) {\n    res += String.fromCharCode(bytes[i] + (bytes[i + 1] * 256))\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length\n  start = ~~start\n  end = end === undefined ? len : ~~end\n\n  if (start < 0) {\n    start += len\n    if (start < 0) start = 0\n  } else if (start > len) {\n    start = len\n  }\n\n  if (end < 0) {\n    end += len\n    if (end < 0) end = 0\n  } else if (end > len) {\n    end = len\n  }\n\n  if (end < start) end = start\n\n  var newBuf = this.subarray(start, end)\n  // Return an augmented `Uint8Array` instance\n  Object.setPrototypeOf(newBuf, Buffer.prototype)\n\n  return newBuf\n}\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUintLE =\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUintBE =\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length)\n  }\n\n  var val = this[offset + --byteLength]\n  var mul = 1\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul\n  }\n\n  return val\n}\n\nBuffer.prototype.readUint8 =\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  return this[offset]\n}\n\nBuffer.prototype.readUint16LE =\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return this[offset] | (this[offset + 1] << 8)\n}\n\nBuffer.prototype.readUint16BE =\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  return (this[offset] << 8) | this[offset + 1]\n}\n\nBuffer.prototype.readUint32LE =\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n}\n\nBuffer.prototype.readUint32BE =\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n}\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var val = this[offset]\n  var mul = 1\n  var i = 0\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) checkOffset(offset, byteLength, this.length)\n\n  var i = byteLength\n  var mul = 1\n  var val = this[offset + --i]\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul\n  }\n  mul *= 0x80\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength)\n\n  return val\n}\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 1, this.length)\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n}\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset] | (this[offset + 1] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 2, this.length)\n  var val = this[offset + 1] | (this[offset] << 8)\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n}\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n}\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n}\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, true, 23, 4)\n}\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 4, this.length)\n  return ieee754.read(this, offset, false, 23, 4)\n}\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, true, 52, 8)\n}\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  offset = offset >>> 0\n  if (!noAssert) checkOffset(offset, 8, this.length)\n  return ieee754.read(this, offset, false, 52, 8)\n}\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!Buffer.isBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUintLE =\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var mul = 1\n  var i = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUintBE =\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  byteLength = byteLength >>> 0\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1\n    checkInt(this, value, offset, byteLength, maxBytes, 0)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeUint8 =\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeUint16LE =\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint16BE =\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeUint32LE =\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset + 3] = (value >>> 24)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 1] = (value >>> 8)\n  this[offset] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeUint32BE =\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    var limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = 0\n  var mul = 1\n  var sub = 0\n  this[offset] = value & 0xFF\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    var limit = Math.pow(2, (8 * byteLength) - 1)\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit)\n  }\n\n  var i = byteLength - 1\n  var mul = 1\n  var sub = 0\n  this[offset + i] = value & 0xFF\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF\n  }\n\n  return offset + byteLength\n}\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)\n  if (value < 0) value = 0xff + value + 1\n  this[offset] = (value & 0xff)\n  return offset + 1\n}\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)\n  this[offset] = (value >>> 8)\n  this[offset + 1] = (value & 0xff)\n  return offset + 2\n}\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  this[offset] = (value & 0xff)\n  this[offset + 1] = (value >>> 8)\n  this[offset + 2] = (value >>> 16)\n  this[offset + 3] = (value >>> 24)\n  return offset + 4\n}\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)\n  if (value < 0) value = 0xffffffff + value + 1\n  this[offset] = (value >>> 24)\n  this[offset + 1] = (value >>> 16)\n  this[offset + 2] = (value >>> 8)\n  this[offset + 3] = (value & 0xff)\n  return offset + 4\n}\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 23, 4)\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n}\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  value = +value\n  offset = offset >>> 0\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)\n  }\n  ieee754.write(buf, value, offset, littleEndian, 52, 8)\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n}\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n}\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!Buffer.isBuffer(target)) throw new TypeError('argument should be a Buffer')\n  if (!start) start = 0\n  if (!end && end !== 0) end = this.length\n  if (targetStart >= target.length) targetStart = target.length\n  if (!targetStart) targetStart = 0\n  if (end > 0 && end < start) end = start\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('Index out of range')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start\n  }\n\n  var len = end - start\n\n  if (this === target && typeof Uint8Array.prototype.copyWithin === 'function') {\n    // Use built-in when available, missing from IE11\n    this.copyWithin(targetStart, start, end)\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, end),\n      targetStart\n    )\n  }\n\n  return len\n}\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start\n      start = 0\n      end = this.length\n    } else if (typeof end === 'string') {\n      encoding = end\n      end = this.length\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0)\n      if ((encoding === 'utf8' && code < 128) ||\n          encoding === 'latin1') {\n        // Fast path: If `val` fits into a single byte, use that numeric value.\n        val = code\n      }\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255\n  } else if (typeof val === 'boolean') {\n    val = Number(val)\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0\n  end = end === undefined ? this.length : end >>> 0\n\n  if (!val) val = 0\n\n  var i\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val\n    }\n  } else {\n    var bytes = Buffer.isBuffer(val)\n      ? val\n      : Buffer.from(val, encoding)\n    var len = bytes.length\n    if (len === 0) {\n      throw new TypeError('The value \"' + val +\n        '\" is invalid for argument \"value\"')\n    }\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len]\n    }\n  }\n\n  return this\n}\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+/0-9A-Za-z-_]/g\n\nfunction base64clean (str) {\n  // Node takes equal signs as end of the Base64 encoding\n  str = str.split('=')[0]\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = str.trim().replace(INVALID_BASE64_RE, '')\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '='\n  }\n  return str\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity\n  var codePoint\n  var length = string.length\n  var leadSurrogate = null\n  var bytes = []\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i)\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n        leadSurrogate = codePoint\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)\n    }\n\n    leadSurrogate = null\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint)\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      )\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF)\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo\n  var byteArray = []\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i)\n    hi = c >> 8\n    lo = c % 256\n    byteArray.push(lo)\n    byteArray.push(hi)\n  }\n\n  return byteArray\n}\n\nfunction base64ToBytes (str) {\n  return base64.toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i]\n  }\n  return i\n}\n\n// ArrayBuffer or Uint8Array objects from other contexts (i.e. iframes) do not pass\n// the `instanceof` check but they should be treated as of that type.\n// See: https://github.com/feross/buffer/issues/166\nfunction isInstance (obj, type) {\n  return obj instanceof type ||\n    (obj != null && obj.constructor != null && obj.constructor.name != null &&\n      obj.constructor.name === type.name)\n}\nfunction numberIsNaN (obj) {\n  // For IE11 support\n  return obj !== obj // eslint-disable-line no-self-compare\n}\n\n// Create lookup table for `toString('hex')`\n// See: https://github.com/feross/buffer/issues/219\nvar hexSliceLookupTable = (function () {\n  var alphabet = '0123456789abcdef'\n  var table = new Array(256)\n  for (var i = 0; i < 16; ++i) {\n    var i16 = i * 16\n    for (var j = 0; j < 16; ++j) {\n      table[i16 + j] = alphabet[i] + alphabet[j]\n    }\n  }\n  return table\n})()\n", "/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport function __createBinding(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}\r\n\r\nexport function __exportStar(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !exports.hasOwnProperty(p)) exports[p] = m[p];\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (Object.hasOwnProperty.call(mod, k)) result[k] = mod[k];\r\n    result.default = mod;\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null], "names": ["byteLength", "toByteArray", "fromByteArray", "lookup", "revLookup", "Arr", "Uint8Array", "Array", "code", "i", "len", "length", "charCodeAt", "getLens", "b64", "Error", "validLen", "indexOf", "placeHoldersLen", "lens", "_byteLength", "tmp", "arr", "curByte", "tripletToBase64", "num", "encodeChunk", "uint8", "start", "end", "output", "push", "join", "extraBytes", "parts", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "len2", "buffer", "offset", "isLE", "mLen", "nBytes", "e", "m", "eLen", "eMax", "eBias", "nBits", "d", "s", "NaN", "Infinity", "Math", "pow", "value", "c", "rt", "abs", "isNaN", "floor", "log", "LN2", "customInspectSymbol", "Symbol", "exports", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "K_MAX_LENGTH", "TYPED_ARRAY_SUPPORT", "typedArraySupport", "console", "error", "proto", "foo", "Object", "setPrototypeOf", "prototype", "defineProperty", "enumerable", "get", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "byteOffset", "createBuffer", "RangeError", "buf", "arg", "encodingOrOffset", "TypeError", "allocUnsafe", "from", "poolSize", "fromString", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "fromArrayView", "isInstance", "fromArrayBuffer", "SharedArrayBuffer", "valueOf", "b", "fromObject", "toPrimitive", "assertSize", "size", "alloc", "fill", "encoding", "checked", "allocUnsafeSlow", "string", "isEncoding", "actual", "write", "slice", "fromArrayLike", "array", "arrayView", "copy", "obj", "numberIsNaN", "type", "isArray", "data", "toString", "_isBuffer", "compare", "a", "x", "y", "min", "String", "toLowerCase", "concat", "list", "pos", "set", "call", "mustMatch", "arguments", "loweredCase", "utf8ToBytes", "base64ToBytes", "slowToString", "hexSlice", "utf8Slice", "asciiSlice", "latin1Slice", "base64Slice", "utf16leSlice", "swap", "n", "swap16", "swap32", "swap64", "apply", "toLocaleString", "equals", "inspect", "str", "max", "INSPECT_MAX_BYTES", "replace", "trim", "target", "thisStart", "thisEnd", "thisCopy", "targetCopy", "bidirectionalIndexOf", "val", "dir", "arrayIndexOf", "lastIndexOf", "indexSize", "arr<PERSON><PERSON><PERSON>", "v<PERSON><PERSON><PERSON><PERSON>", "read", "readUInt16BE", "foundIndex", "found", "j", "includes", "hexWrite", "Number", "remaining", "strLen", "parsed", "parseInt", "substr", "utf8Write", "blit<PERSON><PERSON>er", "asciiWrite", "asciiToBytes", "base64Write", "ucs2Write", "utf16leToBytes", "isFinite", "toJSON", "_arr", "base64", "res", "firstByte", "codePoint", "bytesPerSequence", "secondByte", "thirdByte", "fourthByte", "tempCodePoint", "decodeCodePointsArray", "MAX_ARGUMENTS_LENGTH", "codePoints", "fromCharCode", "ret", "out", "hexSliceLookupTable", "bytes", "newBuf", "subarray", "checkOffset", "ext", "readUintLE", "readUIntLE", "noAssert", "mul", "readUintBE", "readUIntBE", "readUint8", "readUInt8", "readUint16LE", "readUInt16LE", "readUint16BE", "readUint32LE", "readUInt32LE", "readUint32BE", "readUInt32BE", "readIntLE", "readIntBE", "readInt8", "readInt16LE", "readInt16BE", "readInt32LE", "readInt32BE", "readFloatLE", "ieee754", "readFloatBE", "readDoubleLE", "readDoubleBE", "checkInt", "writeUintLE", "writeUIntLE", "maxBytes", "writeUintBE", "writeUIntBE", "writeUint8", "writeUInt8", "writeUint16LE", "writeUInt16LE", "writeUint16BE", "writeUInt16BE", "writeUint32LE", "writeUInt32LE", "writeUint32BE", "writeUInt32BE", "writeIntLE", "limit", "sub", "writeIntBE", "writeInt8", "writeInt16LE", "writeInt16BE", "writeInt32LE", "writeInt32BE", "checkIEEE754", "writeFloat", "littleEndian", "writeFloatLE", "writeFloatBE", "writeDouble", "writeDoubleLE", "writeDoubleBE", "targetStart", "copyWithin", "INVALID_BASE64_RE", "base64clean", "split", "units", "leadSurrogate", "byteArray", "hi", "lo", "src", "dst", "constructor", "name", "alphabet", "table", "i16", "extendStatics", "__proto__", "p", "hasOwnProperty", "__extends", "__", "create", "__assign", "assign", "t", "BSON_INT32_MAX", "BSON_INT32_MIN", "BSON_INT64_MAX", "BSON_INT64_MIN", "calculateObjectSize", "constants.JS_INT_MIN", "constants.JS_INT_MAX", "constants.BSON_INT32_MIN", "constants.BSON_INT32_MAX", "deserialize", "constants.BSON_DATA_STRING", "constants.BSON_DATA_OID", "constants.BSON_DATA_INT", "constants.BSON_DATA_NUMBER", "constants.BSON_DATA_DATE", "constants.BSON_DATA_BOOLEAN", "constants.BSON_DATA_OBJECT", "constants.BSON_DATA_ARRAY", "constants.BSON_DATA_UNDEFINED", "constants.BSON_DATA_NULL", "constants.BSON_DATA_LONG", "constants.BSON_DATA_DECIMAL128", "constants.BSON_DATA_BINARY", "constants.BSON_BINARY_SUBTYPE_UUID_NEW", "constants.BSON_DATA_REGEXP", "constants.BSON_DATA_SYMBOL", "constants.BSON_DATA_TIMESTAMP", "constants.BSON_DATA_MIN_KEY", "constants.BSON_DATA_MAX_KEY", "constants.BSON_DATA_CODE", "constants.BSON_DATA_CODE_W_SCOPE", "constants.BSON_DATA_DBPOINTER", "constants.BSON_BINARY_SUBTYPE_DEFAULT", "Map", "internalSerialize", "internalDeserialize", "internalCalculateObjectSize"], "mappings": ";;;;AAEA,gBAAkB,GAAGA,UAArB;AACA,iBAAmB,GAAGC,WAAtB;AACA,mBAAqB,GAAGC,aAAxB;AAEA,IAAIC,MAAM,GAAG,EAAb;AACA,IAAIC,SAAS,GAAG,EAAhB;AACA,IAAIC,GAAG,GAAG,OAAOC,UAAP,KAAsB,WAAtB,GAAoCA,UAApC,GAAiDC,KAA3D;AAEA,IAAIC,IAAI,GAAG,kEAAX;;AACA,KAAK,IAAIC,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAGF,IAAI,CAACG,MAA3B,EAAmCF,CAAC,GAAGC,GAAvC,EAA4C,EAAED,CAA9C,EAAiD;AAC/CN,EAAAA,MAAM,CAACM,CAAD,CAAN,GAAYD,IAAI,CAACC,CAAD,CAAhB;AACAL,EAAAA,SAAS,CAACI,IAAI,CAACI,UAAL,CAAgBH,CAAhB,CAAD,CAAT,GAAgCA,CAAhC;AACD;AAGD;;;AACAL,SAAS,CAAC,IAAIQ,UAAJ,CAAe,CAAf,CAAD,CAAT,GAA+B,EAA/B;AACAR,SAAS,CAAC,IAAIQ,UAAJ,CAAe,CAAf,CAAD,CAAT,GAA+B,EAA/B;;AAEA,SAASC,OAAT,CAAkBC,GAAlB,EAAuB;AACrB,MAAIJ,GAAG,GAAGI,GAAG,CAACH,MAAd;;AAEA,MAAID,GAAG,GAAG,CAAN,GAAU,CAAd,EAAiB;AACf,UAAM,IAAIK,KAAJ,CAAU,gDAAV,CAAN;AACD,GALoB;;;;AASrB,MAAIC,QAAQ,GAAGF,GAAG,CAACG,OAAJ,CAAY,GAAZ,CAAf;AACA,MAAID,QAAQ,KAAK,CAAC,CAAlB,EAAqBA,QAAQ,GAAGN,GAAX;AAErB,MAAIQ,eAAe,GAAGF,QAAQ,KAAKN,GAAb,GAClB,CADkB,GAElB,IAAKM,QAAQ,GAAG,CAFpB;AAIA,SAAO,CAACA,QAAD,EAAWE,eAAX,CAAP;AACD;;;AAGD,SAASlB,UAAT,CAAqBc,GAArB,EAA0B;AACxB,MAAIK,IAAI,GAAGN,OAAO,CAACC,GAAD,CAAlB;AACA,MAAIE,QAAQ,GAAGG,IAAI,CAAC,CAAD,CAAnB;AACA,MAAID,eAAe,GAAGC,IAAI,CAAC,CAAD,CAA1B;AACA,SAAQ,CAACH,QAAQ,GAAGE,eAAZ,IAA+B,CAA/B,GAAmC,CAApC,GAAyCA,eAAhD;AACD;;AAED,SAASE,WAAT,CAAsBN,GAAtB,EAA2BE,QAA3B,EAAqCE,eAArC,EAAsD;AACpD,SAAQ,CAACF,QAAQ,GAAGE,eAAZ,IAA+B,CAA/B,GAAmC,CAApC,GAAyCA,eAAhD;AACD;;AAED,SAASjB,WAAT,CAAsBa,GAAtB,EAA2B;AACzB,MAAIO,GAAJ;AACA,MAAIF,IAAI,GAAGN,OAAO,CAACC,GAAD,CAAlB;AACA,MAAIE,QAAQ,GAAGG,IAAI,CAAC,CAAD,CAAnB;AACA,MAAID,eAAe,GAAGC,IAAI,CAAC,CAAD,CAA1B;AAEA,MAAIG,GAAG,GAAG,IAAIjB,GAAJ,CAAQe,WAAW,CAACN,GAAD,EAAME,QAAN,EAAgBE,eAAhB,CAAnB,CAAV;AAEA,MAAIK,OAAO,GAAG,CAAd,CARyB;;AAWzB,MAAIb,GAAG,GAAGQ,eAAe,GAAG,CAAlB,GACNF,QAAQ,GAAG,CADL,GAENA,QAFJ;AAIA,MAAIP,CAAJ;;AACA,OAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGC,GAAhB,EAAqBD,CAAC,IAAI,CAA1B,EAA6B;AAC3BY,IAAAA,GAAG,GACAjB,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAf,CAAD,CAAT,IAAgC,EAAjC,GACCL,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAC,GAAG,CAAnB,CAAD,CAAT,IAAoC,EADrC,GAECL,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAC,GAAG,CAAnB,CAAD,CAAT,IAAoC,CAFrC,GAGAL,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAC,GAAG,CAAnB,CAAD,CAJX;AAKAa,IAAAA,GAAG,CAACC,OAAO,EAAR,CAAH,GAAkBF,GAAG,IAAI,EAAR,GAAc,IAA/B;AACAC,IAAAA,GAAG,CAACC,OAAO,EAAR,CAAH,GAAkBF,GAAG,IAAI,CAAR,GAAa,IAA9B;AACAC,IAAAA,GAAG,CAACC,OAAO,EAAR,CAAH,GAAiBF,GAAG,GAAG,IAAvB;AACD;;AAED,MAAIH,eAAe,KAAK,CAAxB,EAA2B;AACzBG,IAAAA,GAAG,GACAjB,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAf,CAAD,CAAT,IAAgC,CAAjC,GACCL,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAC,GAAG,CAAnB,CAAD,CAAT,IAAoC,CAFvC;AAGAa,IAAAA,GAAG,CAACC,OAAO,EAAR,CAAH,GAAiBF,GAAG,GAAG,IAAvB;AACD;;AAED,MAAIH,eAAe,KAAK,CAAxB,EAA2B;AACzBG,IAAAA,GAAG,GACAjB,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAf,CAAD,CAAT,IAAgC,EAAjC,GACCL,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAC,GAAG,CAAnB,CAAD,CAAT,IAAoC,CADrC,GAECL,SAAS,CAACU,GAAG,CAACF,UAAJ,CAAeH,CAAC,GAAG,CAAnB,CAAD,CAAT,IAAoC,CAHvC;AAIAa,IAAAA,GAAG,CAACC,OAAO,EAAR,CAAH,GAAkBF,GAAG,IAAI,CAAR,GAAa,IAA9B;AACAC,IAAAA,GAAG,CAACC,OAAO,EAAR,CAAH,GAAiBF,GAAG,GAAG,IAAvB;AACD;;AAED,SAAOC,GAAP;AACD;;AAED,SAASE,eAAT,CAA0BC,GAA1B,EAA+B;AAC7B,SAAOtB,MAAM,CAACsB,GAAG,IAAI,EAAP,GAAY,IAAb,CAAN,GACLtB,MAAM,CAACsB,GAAG,IAAI,EAAP,GAAY,IAAb,CADD,GAELtB,MAAM,CAACsB,GAAG,IAAI,CAAP,GAAW,IAAZ,CAFD,GAGLtB,MAAM,CAACsB,GAAG,GAAG,IAAP,CAHR;AAID;;AAED,SAASC,WAAT,CAAsBC,KAAtB,EAA6BC,KAA7B,EAAoCC,GAApC,EAAyC;AACvC,MAAIR,GAAJ;AACA,MAAIS,MAAM,GAAG,EAAb;;AACA,OAAK,IAAIrB,CAAC,GAAGmB,KAAb,EAAoBnB,CAAC,GAAGoB,GAAxB,EAA6BpB,CAAC,IAAI,CAAlC,EAAqC;AACnCY,IAAAA,GAAG,GACD,CAAEM,KAAK,CAAClB,CAAD,CAAL,IAAY,EAAb,GAAmB,QAApB,KACEkB,KAAK,CAAClB,CAAC,GAAG,CAAL,CAAL,IAAgB,CAAjB,GAAsB,MADvB,KAECkB,KAAK,CAAClB,CAAC,GAAG,CAAL,CAAL,GAAe,IAFhB,CADF;AAIAqB,IAAAA,MAAM,CAACC,IAAP,CAAYP,eAAe,CAACH,GAAD,CAA3B;AACD;;AACD,SAAOS,MAAM,CAACE,IAAP,CAAY,EAAZ,CAAP;AACD;;AAED,SAAS9B,aAAT,CAAwByB,KAAxB,EAA+B;AAC7B,MAAIN,GAAJ;AACA,MAAIX,GAAG,GAAGiB,KAAK,CAAChB,MAAhB;AACA,MAAIsB,UAAU,GAAGvB,GAAG,GAAG,CAAvB,CAH6B;;AAI7B,MAAIwB,KAAK,GAAG,EAAZ;AACA,MAAIC,cAAc,GAAG,KAArB,CAL6B;;;AAQ7B,OAAK,IAAI1B,CAAC,GAAG,CAAR,EAAW2B,IAAI,GAAG1B,GAAG,GAAGuB,UAA7B,EAAyCxB,CAAC,GAAG2B,IAA7C,EAAmD3B,CAAC,IAAI0B,cAAxD,EAAwE;AACtED,IAAAA,KAAK,CAACH,IAAN,CAAWL,WAAW,CAACC,KAAD,EAAQlB,CAAR,EAAYA,CAAC,GAAG0B,cAAL,GAAuBC,IAAvB,GAA8BA,IAA9B,GAAsC3B,CAAC,GAAG0B,cAArD,CAAtB;AACD,GAV4B;;;AAa7B,MAAIF,UAAU,KAAK,CAAnB,EAAsB;AACpBZ,IAAAA,GAAG,GAAGM,KAAK,CAACjB,GAAG,GAAG,CAAP,CAAX;AACAwB,IAAAA,KAAK,CAACH,IAAN,CACE5B,MAAM,CAACkB,GAAG,IAAI,CAAR,CAAN,GACAlB,MAAM,CAAEkB,GAAG,IAAI,CAAR,GAAa,IAAd,CADN,GAEA,IAHF;AAKD,GAPD,MAOO,IAAIY,UAAU,KAAK,CAAnB,EAAsB;AAC3BZ,IAAAA,GAAG,GAAG,CAACM,KAAK,CAACjB,GAAG,GAAG,CAAP,CAAL,IAAkB,CAAnB,IAAwBiB,KAAK,CAACjB,GAAG,GAAG,CAAP,CAAnC;AACAwB,IAAAA,KAAK,CAACH,IAAN,CACE5B,MAAM,CAACkB,GAAG,IAAI,EAAR,CAAN,GACAlB,MAAM,CAAEkB,GAAG,IAAI,CAAR,GAAa,IAAd,CADN,GAEAlB,MAAM,CAAEkB,GAAG,IAAI,CAAR,GAAa,IAAd,CAFN,GAGA,GAJF;AAMD;;AAED,SAAOa,KAAK,CAACF,IAAN,CAAW,EAAX,CAAP;;;;;;;;;ACpJF;AACA,QAAY,GAAG,aAAA,CAAUK,MAAV,EAAkBC,MAAlB,EAA0BC,IAA1B,EAAgCC,IAAhC,EAAsCC,MAAtC,EAA8C;AAC3D,MAAIC,CAAJ,EAAOC,CAAP;AACA,MAAIC,IAAI,GAAIH,MAAM,GAAG,CAAV,GAAeD,IAAf,GAAsB,CAAjC;AACA,MAAIK,IAAI,GAAG,CAAC,KAAKD,IAAN,IAAc,CAAzB;AACA,MAAIE,KAAK,GAAGD,IAAI,IAAI,CAApB;AACA,MAAIE,KAAK,GAAG,CAAC,CAAb;AACA,MAAItC,CAAC,GAAG8B,IAAI,GAAIE,MAAM,GAAG,CAAb,GAAkB,CAA9B;AACA,MAAIO,CAAC,GAAGT,IAAI,GAAG,CAAC,CAAJ,GAAQ,CAApB;AACA,MAAIU,CAAC,GAAGZ,MAAM,CAACC,MAAM,GAAG7B,CAAV,CAAd;AAEAA,EAAAA,CAAC,IAAIuC,CAAL;AAEAN,EAAAA,CAAC,GAAGO,CAAC,GAAI,CAAC,KAAM,CAACF,KAAR,IAAkB,CAA3B;AACAE,EAAAA,CAAC,KAAM,CAACF,KAAR;AACAA,EAAAA,KAAK,IAAIH,IAAT;;AACA,SAAOG,KAAK,GAAG,CAAf,EAAkBL,CAAC,GAAIA,CAAC,GAAG,GAAL,GAAYL,MAAM,CAACC,MAAM,GAAG7B,CAAV,CAAtB,EAAoCA,CAAC,IAAIuC,CAAzC,EAA4CD,KAAK,IAAI,CAAvE,EAA0E;;AAE1EJ,EAAAA,CAAC,GAAGD,CAAC,GAAI,CAAC,KAAM,CAACK,KAAR,IAAkB,CAA3B;AACAL,EAAAA,CAAC,KAAM,CAACK,KAAR;AACAA,EAAAA,KAAK,IAAIP,IAAT;;AACA,SAAOO,KAAK,GAAG,CAAf,EAAkBJ,CAAC,GAAIA,CAAC,GAAG,GAAL,GAAYN,MAAM,CAACC,MAAM,GAAG7B,CAAV,CAAtB,EAAoCA,CAAC,IAAIuC,CAAzC,EAA4CD,KAAK,IAAI,CAAvE,EAA0E;;AAE1E,MAAIL,CAAC,KAAK,CAAV,EAAa;AACXA,IAAAA,CAAC,GAAG,IAAII,KAAR;AACD,GAFD,MAEO,IAAIJ,CAAC,KAAKG,IAAV,EAAgB;AACrB,WAAOF,CAAC,GAAGO,GAAH,GAAU,CAACD,CAAC,GAAG,CAAC,CAAJ,GAAQ,CAAV,IAAeE,QAAjC;AACD,GAFM,MAEA;AACLR,IAAAA,CAAC,GAAGA,CAAC,GAAGS,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYb,IAAZ,CAAR;AACAE,IAAAA,CAAC,GAAGA,CAAC,GAAGI,KAAR;AACD;;AACD,SAAO,CAACG,CAAC,GAAG,CAAC,CAAJ,GAAQ,CAAV,IAAeN,CAAf,GAAmBS,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYX,CAAC,GAAGF,IAAhB,CAA1B;AACD,CA/BD;;AAiCA,SAAa,GAAG,cAAA,CAAUH,MAAV,EAAkBiB,KAAlB,EAAyBhB,MAAzB,EAAiCC,IAAjC,EAAuCC,IAAvC,EAA6CC,MAA7C,EAAqD;AACnE,MAAIC,CAAJ,EAAOC,CAAP,EAAUY,CAAV;AACA,MAAIX,IAAI,GAAIH,MAAM,GAAG,CAAV,GAAeD,IAAf,GAAsB,CAAjC;AACA,MAAIK,IAAI,GAAG,CAAC,KAAKD,IAAN,IAAc,CAAzB;AACA,MAAIE,KAAK,GAAGD,IAAI,IAAI,CAApB;AACA,MAAIW,EAAE,GAAIhB,IAAI,KAAK,EAAT,GAAcY,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,CAAC,EAAb,IAAmBD,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,CAAC,EAAb,CAAjC,GAAoD,CAA9D;AACA,MAAI5C,CAAC,GAAG8B,IAAI,GAAG,CAAH,GAAQE,MAAM,GAAG,CAA7B;AACA,MAAIO,CAAC,GAAGT,IAAI,GAAG,CAAH,GAAO,CAAC,CAApB;AACA,MAAIU,CAAC,GAAGK,KAAK,GAAG,CAAR,IAAcA,KAAK,KAAK,CAAV,IAAe,IAAIA,KAAJ,GAAY,CAAzC,GAA8C,CAA9C,GAAkD,CAA1D;AAEAA,EAAAA,KAAK,GAAGF,IAAI,CAACK,GAAL,CAASH,KAAT,CAAR;;AAEA,MAAII,KAAK,CAACJ,KAAD,CAAL,IAAgBA,KAAK,KAAKH,QAA9B,EAAwC;AACtCR,IAAAA,CAAC,GAAGe,KAAK,CAACJ,KAAD,CAAL,GAAe,CAAf,GAAmB,CAAvB;AACAZ,IAAAA,CAAC,GAAGG,IAAJ;AACD,GAHD,MAGO;AACLH,IAAAA,CAAC,GAAGU,IAAI,CAACO,KAAL,CAAWP,IAAI,CAACQ,GAAL,CAASN,KAAT,IAAkBF,IAAI,CAACS,GAAlC,CAAJ;;AACA,QAAIP,KAAK,IAAIC,CAAC,GAAGH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,CAACX,CAAb,CAAR,CAAL,GAAgC,CAApC,EAAuC;AACrCA,MAAAA,CAAC;AACDa,MAAAA,CAAC,IAAI,CAAL;AACD;;AACD,QAAIb,CAAC,GAAGI,KAAJ,IAAa,CAAjB,EAAoB;AAClBQ,MAAAA,KAAK,IAAIE,EAAE,GAAGD,CAAd;AACD,KAFD,MAEO;AACLD,MAAAA,KAAK,IAAIE,EAAE,GAAGJ,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,IAAIP,KAAhB,CAAd;AACD;;AACD,QAAIQ,KAAK,GAAGC,CAAR,IAAa,CAAjB,EAAoB;AAClBb,MAAAA,CAAC;AACDa,MAAAA,CAAC,IAAI,CAAL;AACD;;AAED,QAAIb,CAAC,GAAGI,KAAJ,IAAaD,IAAjB,EAAuB;AACrBF,MAAAA,CAAC,GAAG,CAAJ;AACAD,MAAAA,CAAC,GAAGG,IAAJ;AACD,KAHD,MAGO,IAAIH,CAAC,GAAGI,KAAJ,IAAa,CAAjB,EAAoB;AACzBH,MAAAA,CAAC,GAAG,CAAEW,KAAK,GAAGC,CAAT,GAAc,CAAf,IAAoBH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYb,IAAZ,CAAxB;AACAE,MAAAA,CAAC,GAAGA,CAAC,GAAGI,KAAR;AACD,KAHM,MAGA;AACLH,MAAAA,CAAC,GAAGW,KAAK,GAAGF,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYP,KAAK,GAAG,CAApB,CAAR,GAAiCM,IAAI,CAACC,GAAL,CAAS,CAAT,EAAYb,IAAZ,CAArC;AACAE,MAAAA,CAAC,GAAG,CAAJ;AACD;AACF;;AAED,SAAOF,IAAI,IAAI,CAAf,EAAkBH,MAAM,CAACC,MAAM,GAAG7B,CAAV,CAAN,GAAqBkC,CAAC,GAAG,IAAzB,EAA+BlC,CAAC,IAAIuC,CAApC,EAAuCL,CAAC,IAAI,GAA5C,EAAiDH,IAAI,IAAI,CAA3E,EAA8E;;AAE9EE,EAAAA,CAAC,GAAIA,CAAC,IAAIF,IAAN,GAAcG,CAAlB;AACAC,EAAAA,IAAI,IAAIJ,IAAR;;AACA,SAAOI,IAAI,GAAG,CAAd,EAAiBP,MAAM,CAACC,MAAM,GAAG7B,CAAV,CAAN,GAAqBiC,CAAC,GAAG,IAAzB,EAA+BjC,CAAC,IAAIuC,CAApC,EAAuCN,CAAC,IAAI,GAA5C,EAAiDE,IAAI,IAAI,CAA1E,EAA6E;;AAE7EP,EAAAA,MAAM,CAACC,MAAM,GAAG7B,CAAT,GAAauC,CAAd,CAAN,IAA0BC,CAAC,GAAG,GAA9B;CAjDF;;;;;;;;;ACtBA,MAAIa,mBAAmB,GACpB,OAAOC,MAAP,KAAkB,UAAlB,IAAgC,OAAOA,MAAM,CAAC,KAAD,CAAb,KAAyB,UAA1D;AACIA,EAAAA,MAAM,CAAC,KAAD,CAAN,CAAc,4BAAd,CADJ;AAAA,IAEI,IAHN;AAKAC,EAAAA,cAAA,GAAiBC,MAAjB;AACAD,EAAAA,kBAAA,GAAqBE,UAArB;AACAF,EAAAA,yBAAA,GAA4B,EAA5B;AAEA,MAAIG,YAAY,GAAG,UAAnB;AACAH,EAAAA,kBAAA,GAAqBG,YAArB;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACAF,EAAAA,MAAM,CAACG,mBAAP,GAA6BC,iBAAiB,EAA9C;;AAEA,MAAI,CAACJ,MAAM,CAACG,mBAAR,IAA+B,OAAOE,OAAP,KAAmB,WAAlD,IACA,OAAOA,OAAO,CAACC,KAAf,KAAyB,UAD7B,EACyC;AACvCD,IAAAA,OAAO,CAACC,KAAR,CACE,8EACA,sEAFF;AAID;;AAED,WAASF,iBAAT,GAA8B;;AAE5B,QAAI;AACF,UAAI/C,GAAG,GAAG,IAAIhB,UAAJ,CAAe,CAAf,CAAV;AACA,UAAIkE,KAAK,GAAG;AAAEC,QAAAA,GAAG,EAAE,eAAY;AAAE,iBAAO,EAAP;AAAW;AAAhC,OAAZ;AACAC,MAAAA,MAAM,CAACC,cAAP,CAAsBH,KAAtB,EAA6BlE,UAAU,CAACsE,SAAxC;AACAF,MAAAA,MAAM,CAACC,cAAP,CAAsBrD,GAAtB,EAA2BkD,KAA3B;AACA,aAAOlD,GAAG,CAACmD,GAAJ,OAAc,EAArB;AACD,KAND,CAME,OAAO/B,CAAP,EAAU;AACV,aAAO,KAAP;AACD;AACF;;AAEDgC,EAAAA,MAAM,CAACG,cAAP,CAAsBZ,MAAM,CAACW,SAA7B,EAAwC,QAAxC,EAAkD;AAChDE,IAAAA,UAAU,EAAE,IADoC;AAEhDC,IAAAA,GAAG,EAAE,eAAY;AACf,UAAI,CAACd,MAAM,CAACe,QAAP,CAAgB,IAAhB,CAAL,EAA4B,OAAOC,SAAP;AAC5B,aAAO,KAAK5C,MAAZ;AACD;AAL+C,GAAlD;AAQAqC,EAAAA,MAAM,CAACG,cAAP,CAAsBZ,MAAM,CAACW,SAA7B,EAAwC,QAAxC,EAAkD;AAChDE,IAAAA,UAAU,EAAE,IADoC;AAEhDC,IAAAA,GAAG,EAAE,eAAY;AACf,UAAI,CAACd,MAAM,CAACe,QAAP,CAAgB,IAAhB,CAAL,EAA4B,OAAOC,SAAP;AAC5B,aAAO,KAAKC,UAAZ;AACD;AAL+C,GAAlD;;AAQA,WAASC,YAAT,CAAuBxE,MAAvB,EAA+B;AAC7B,QAAIA,MAAM,GAAGwD,YAAb,EAA2B;AACzB,YAAM,IAAIiB,UAAJ,CAAe,gBAAgBzE,MAAhB,GAAyB,gCAAxC,CAAN;AACD,KAH4B;;;AAK7B,QAAI0E,GAAG,GAAG,IAAI/E,UAAJ,CAAeK,MAAf,CAAV;AACA+D,IAAAA,MAAM,CAACC,cAAP,CAAsBU,GAAtB,EAA2BpB,MAAM,CAACW,SAAlC;AACA,WAAOS,GAAP;AACD;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAEA,WAASpB,MAAT,CAAiBqB,GAAjB,EAAsBC,gBAAtB,EAAwC5E,MAAxC,EAAgD;;AAE9C,QAAI,OAAO2E,GAAP,KAAe,QAAnB,EAA6B;AAC3B,UAAI,OAAOC,gBAAP,KAA4B,QAAhC,EAA0C;AACxC,cAAM,IAAIC,SAAJ,CACJ,oEADI,CAAN;AAGD;;AACD,aAAOC,WAAW,CAACH,GAAD,CAAlB;AACD;;AACD,WAAOI,IAAI,CAACJ,GAAD,EAAMC,gBAAN,EAAwB5E,MAAxB,CAAX;AACD;;AAEDsD,EAAAA,MAAM,CAAC0B,QAAP,GAAkB,IAAlB;;AAEA,WAASD,IAAT,CAAepC,KAAf,EAAsBiC,gBAAtB,EAAwC5E,MAAxC,EAAgD;AAC9C,QAAI,OAAO2C,KAAP,KAAiB,QAArB,EAA+B;AAC7B,aAAOsC,UAAU,CAACtC,KAAD,EAAQiC,gBAAR,CAAjB;AACD;;AAED,QAAIM,WAAW,CAACC,MAAZ,CAAmBxC,KAAnB,CAAJ,EAA+B;AAC7B,aAAOyC,aAAa,CAACzC,KAAD,CAApB;AACD;;AAED,QAAIA,KAAK,IAAI,IAAb,EAAmB;AACjB,YAAM,IAAIkC,SAAJ,CACJ,gFACA,sCADA,0BACiDlC,KADjD,CADI,CAAN;AAID;;AAED,QAAI0C,UAAU,CAAC1C,KAAD,EAAQuC,WAAR,CAAV,IACCvC,KAAK,IAAI0C,UAAU,CAAC1C,KAAK,CAACjB,MAAP,EAAewD,WAAf,CADxB,EACsD;AACpD,aAAOI,eAAe,CAAC3C,KAAD,EAAQiC,gBAAR,EAA0B5E,MAA1B,CAAtB;AACD;;AAED,QAAI,OAAOuF,iBAAP,KAA6B,WAA7B,KACCF,UAAU,CAAC1C,KAAD,EAAQ4C,iBAAR,CAAV,IACA5C,KAAK,IAAI0C,UAAU,CAAC1C,KAAK,CAACjB,MAAP,EAAe6D,iBAAf,CAFpB,CAAJ,EAE6D;AAC3D,aAAOD,eAAe,CAAC3C,KAAD,EAAQiC,gBAAR,EAA0B5E,MAA1B,CAAtB;AACD;;AAED,QAAI,OAAO2C,KAAP,KAAiB,QAArB,EAA+B;AAC7B,YAAM,IAAIkC,SAAJ,CACJ,uEADI,CAAN;AAGD;;AAED,QAAIW,OAAO,GAAG7C,KAAK,CAAC6C,OAAN,IAAiB7C,KAAK,CAAC6C,OAAN,EAA/B;;AACA,QAAIA,OAAO,IAAI,IAAX,IAAmBA,OAAO,KAAK7C,KAAnC,EAA0C;AACxC,aAAOW,MAAM,CAACyB,IAAP,CAAYS,OAAZ,EAAqBZ,gBAArB,EAAuC5E,MAAvC,CAAP;AACD;;AAED,QAAIyF,CAAC,GAAGC,UAAU,CAAC/C,KAAD,CAAlB;AACA,QAAI8C,CAAJ,EAAO,OAAOA,CAAP;;AAEP,QAAI,OAAOrC,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACuC,WAAP,IAAsB,IAAvD,IACA,OAAOhD,KAAK,CAACS,MAAM,CAACuC,WAAR,CAAZ,KAAqC,UADzC,EACqD;AACnD,aAAOrC,MAAM,CAACyB,IAAP,CACLpC,KAAK,CAACS,MAAM,CAACuC,WAAR,CAAL,CAA0B,QAA1B,CADK,EACgCf,gBADhC,EACkD5E,MADlD,CAAP;AAGD;;AAED,UAAM,IAAI6E,SAAJ,CACJ,gFACA,sCADA,0BACiDlC,KADjD,CADI,CAAN;AAID;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACAW,EAAAA,MAAM,CAACyB,IAAP,GAAc,UAAUpC,KAAV,EAAiBiC,gBAAjB,EAAmC5E,MAAnC,EAA2C;AACvD,WAAO+E,IAAI,CAACpC,KAAD,EAAQiC,gBAAR,EAA0B5E,MAA1B,CAAX;AACD,GAFD;AAKA;;;AACA+D,EAAAA,MAAM,CAACC,cAAP,CAAsBV,MAAM,CAACW,SAA7B,EAAwCtE,UAAU,CAACsE,SAAnD;AACAF,EAAAA,MAAM,CAACC,cAAP,CAAsBV,MAAtB,EAA8B3D,UAA9B;;AAEA,WAASiG,UAAT,CAAqBC,IAArB,EAA2B;AACzB,QAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;AAC5B,YAAM,IAAIhB,SAAJ,CAAc,wCAAd,CAAN;AACD,KAFD,MAEO,IAAIgB,IAAI,GAAG,CAAX,EAAc;AACnB,YAAM,IAAIpB,UAAJ,CAAe,gBAAgBoB,IAAhB,GAAuB,gCAAtC,CAAN;AACD;AACF;;AAED,WAASC,KAAT,CAAgBD,IAAhB,EAAsBE,IAAtB,EAA4BC,QAA5B,EAAsC;AACpCJ,IAAAA,UAAU,CAACC,IAAD,CAAV;;AACA,QAAIA,IAAI,IAAI,CAAZ,EAAe;AACb,aAAOrB,YAAY,CAACqB,IAAD,CAAnB;AACD;;AACD,QAAIE,IAAI,KAAKzB,SAAb,EAAwB;;;;AAItB,aAAO,OAAO0B,QAAP,KAAoB,QAApB,GACHxB,YAAY,CAACqB,IAAD,CAAZ,CAAmBE,IAAnB,CAAwBA,IAAxB,EAA8BC,QAA9B,CADG,GAEHxB,YAAY,CAACqB,IAAD,CAAZ,CAAmBE,IAAnB,CAAwBA,IAAxB,CAFJ;AAGD;;AACD,WAAOvB,YAAY,CAACqB,IAAD,CAAnB;AACD;AAED;AACA;AACA;AACA;;;AACAvC,EAAAA,MAAM,CAACwC,KAAP,GAAe,UAAUD,IAAV,EAAgBE,IAAhB,EAAsBC,QAAtB,EAAgC;AAC7C,WAAOF,KAAK,CAACD,IAAD,EAAOE,IAAP,EAAaC,QAAb,CAAZ;AACD,GAFD;;AAIA,WAASlB,WAAT,CAAsBe,IAAtB,EAA4B;AAC1BD,IAAAA,UAAU,CAACC,IAAD,CAAV;AACA,WAAOrB,YAAY,CAACqB,IAAI,GAAG,CAAP,GAAW,CAAX,GAAeI,OAAO,CAACJ,IAAD,CAAP,GAAgB,CAAhC,CAAnB;AACD;AAED;AACA;AACA;;;AACAvC,EAAAA,MAAM,CAACwB,WAAP,GAAqB,UAAUe,IAAV,EAAgB;AACnC,WAAOf,WAAW,CAACe,IAAD,CAAlB;AACD,GAFD;AAGA;AACA;AACA;;;AACAvC,EAAAA,MAAM,CAAC4C,eAAP,GAAyB,UAAUL,IAAV,EAAgB;AACvC,WAAOf,WAAW,CAACe,IAAD,CAAlB;AACD,GAFD;;AAIA,WAASZ,UAAT,CAAqBkB,MAArB,EAA6BH,QAA7B,EAAuC;AACrC,QAAI,OAAOA,QAAP,KAAoB,QAApB,IAAgCA,QAAQ,KAAK,EAAjD,EAAqD;AACnDA,MAAAA,QAAQ,GAAG,MAAX;AACD;;AAED,QAAI,CAAC1C,MAAM,CAAC8C,UAAP,CAAkBJ,QAAlB,CAAL,EAAkC;AAChC,YAAM,IAAInB,SAAJ,CAAc,uBAAuBmB,QAArC,CAAN;AACD;;AAED,QAAIhG,MAAM,GAAGX,UAAU,CAAC8G,MAAD,EAASH,QAAT,CAAV,GAA+B,CAA5C;AACA,QAAItB,GAAG,GAAGF,YAAY,CAACxE,MAAD,CAAtB;AAEA,QAAIqG,MAAM,GAAG3B,GAAG,CAAC4B,KAAJ,CAAUH,MAAV,EAAkBH,QAAlB,CAAb;;AAEA,QAAIK,MAAM,KAAKrG,MAAf,EAAuB;;;;AAIrB0E,MAAAA,GAAG,GAAGA,GAAG,CAAC6B,KAAJ,CAAU,CAAV,EAAaF,MAAb,CAAN;AACD;;AAED,WAAO3B,GAAP;AACD;;AAED,WAAS8B,aAAT,CAAwBC,KAAxB,EAA+B;AAC7B,QAAIzG,MAAM,GAAGyG,KAAK,CAACzG,MAAN,GAAe,CAAf,GAAmB,CAAnB,GAAuBiG,OAAO,CAACQ,KAAK,CAACzG,MAAP,CAAP,GAAwB,CAA5D;AACA,QAAI0E,GAAG,GAAGF,YAAY,CAACxE,MAAD,CAAtB;;AACA,SAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGE,MAApB,EAA4BF,CAAC,IAAI,CAAjC,EAAoC;AAClC4E,MAAAA,GAAG,CAAC5E,CAAD,CAAH,GAAS2G,KAAK,CAAC3G,CAAD,CAAL,GAAW,GAApB;AACD;;AACD,WAAO4E,GAAP;AACD;;AAED,WAASU,aAAT,CAAwBsB,SAAxB,EAAmC;AACjC,QAAIrB,UAAU,CAACqB,SAAD,EAAY/G,UAAZ,CAAd,EAAuC;AACrC,UAAIgH,IAAI,GAAG,IAAIhH,UAAJ,CAAe+G,SAAf,CAAX;AACA,aAAOpB,eAAe,CAACqB,IAAI,CAACjF,MAAN,EAAciF,IAAI,CAACpC,UAAnB,EAA+BoC,IAAI,CAACtH,UAApC,CAAtB;AACD;;AACD,WAAOmH,aAAa,CAACE,SAAD,CAApB;AACD;;AAED,WAASpB,eAAT,CAA0BmB,KAA1B,EAAiClC,UAAjC,EAA6CvE,MAA7C,EAAqD;AACnD,QAAIuE,UAAU,GAAG,CAAb,IAAkBkC,KAAK,CAACpH,UAAN,GAAmBkF,UAAzC,EAAqD;AACnD,YAAM,IAAIE,UAAJ,CAAe,sCAAf,CAAN;AACD;;AAED,QAAIgC,KAAK,CAACpH,UAAN,GAAmBkF,UAAU,IAAIvE,MAAM,IAAI,CAAd,CAAjC,EAAmD;AACjD,YAAM,IAAIyE,UAAJ,CAAe,sCAAf,CAAN;AACD;;AAED,QAAIC,GAAJ;;AACA,QAAIH,UAAU,KAAKD,SAAf,IAA4BtE,MAAM,KAAKsE,SAA3C,EAAsD;AACpDI,MAAAA,GAAG,GAAG,IAAI/E,UAAJ,CAAe8G,KAAf,CAAN;AACD,KAFD,MAEO,IAAIzG,MAAM,KAAKsE,SAAf,EAA0B;AAC/BI,MAAAA,GAAG,GAAG,IAAI/E,UAAJ,CAAe8G,KAAf,EAAsBlC,UAAtB,CAAN;AACD,KAFM,MAEA;AACLG,MAAAA,GAAG,GAAG,IAAI/E,UAAJ,CAAe8G,KAAf,EAAsBlC,UAAtB,EAAkCvE,MAAlC,CAAN;AACD,KAhBkD;;;AAmBnD+D,IAAAA,MAAM,CAACC,cAAP,CAAsBU,GAAtB,EAA2BpB,MAAM,CAACW,SAAlC;AAEA,WAAOS,GAAP;AACD;;AAED,WAASgB,UAAT,CAAqBkB,GAArB,EAA0B;AACxB,QAAItD,MAAM,CAACe,QAAP,CAAgBuC,GAAhB,CAAJ,EAA0B;AACxB,UAAI7G,GAAG,GAAGkG,OAAO,CAACW,GAAG,CAAC5G,MAAL,CAAP,GAAsB,CAAhC;AACA,UAAI0E,GAAG,GAAGF,YAAY,CAACzE,GAAD,CAAtB;;AAEA,UAAI2E,GAAG,CAAC1E,MAAJ,KAAe,CAAnB,EAAsB;AACpB,eAAO0E,GAAP;AACD;;AAEDkC,MAAAA,GAAG,CAACD,IAAJ,CAASjC,GAAT,EAAc,CAAd,EAAiB,CAAjB,EAAoB3E,GAApB;AACA,aAAO2E,GAAP;AACD;;AAED,QAAIkC,GAAG,CAAC5G,MAAJ,KAAesE,SAAnB,EAA8B;AAC5B,UAAI,OAAOsC,GAAG,CAAC5G,MAAX,KAAsB,QAAtB,IAAkC6G,WAAW,CAACD,GAAG,CAAC5G,MAAL,CAAjD,EAA+D;AAC7D,eAAOwE,YAAY,CAAC,CAAD,CAAnB;AACD;;AACD,aAAOgC,aAAa,CAACI,GAAD,CAApB;AACD;;AAED,QAAIA,GAAG,CAACE,IAAJ,KAAa,QAAb,IAAyBlH,KAAK,CAACmH,OAAN,CAAcH,GAAG,CAACI,IAAlB,CAA7B,EAAsD;AACpD,aAAOR,aAAa,CAACI,GAAG,CAACI,IAAL,CAApB;AACD;AACF;;AAED,WAASf,OAAT,CAAkBjG,MAAlB,EAA0B;;;AAGxB,QAAIA,MAAM,IAAIwD,YAAd,EAA4B;AAC1B,YAAM,IAAIiB,UAAJ,CAAe,oDACA,UADA,GACajB,YAAY,CAACyD,QAAb,CAAsB,EAAtB,CADb,GACyC,QADxD,CAAN;AAED;;AACD,WAAOjH,MAAM,GAAG,CAAhB;AACD;;AAED,WAASuD,UAAT,CAAqBvD,MAArB,EAA6B;AAC3B,QAAI,CAACA,MAAD,IAAWA,MAAf,EAAuB;;AACrBA,MAAAA,MAAM,GAAG,CAAT;AACD;;AACD,WAAOsD,MAAM,CAACwC,KAAP,CAAa,CAAC9F,MAAd,CAAP;AACD;;AAEDsD,EAAAA,MAAM,CAACe,QAAP,GAAkB,SAASA,QAAT,CAAmBoB,CAAnB,EAAsB;AACtC,WAAOA,CAAC,IAAI,IAAL,IAAaA,CAAC,CAACyB,SAAF,KAAgB,IAA7B,IACLzB,CAAC,KAAKnC,MAAM,CAACW,SADf,CADsC;AAGvC,GAHD;;AAKAX,EAAAA,MAAM,CAAC6D,OAAP,GAAiB,SAASA,OAAT,CAAkBC,CAAlB,EAAqB3B,CAArB,EAAwB;AACvC,QAAIJ,UAAU,CAAC+B,CAAD,EAAIzH,UAAJ,CAAd,EAA+ByH,CAAC,GAAG9D,MAAM,CAACyB,IAAP,CAAYqC,CAAZ,EAAeA,CAAC,CAACzF,MAAjB,EAAyByF,CAAC,CAAC/H,UAA3B,CAAJ;AAC/B,QAAIgG,UAAU,CAACI,CAAD,EAAI9F,UAAJ,CAAd,EAA+B8F,CAAC,GAAGnC,MAAM,CAACyB,IAAP,CAAYU,CAAZ,EAAeA,CAAC,CAAC9D,MAAjB,EAAyB8D,CAAC,CAACpG,UAA3B,CAAJ;;AAC/B,QAAI,CAACiE,MAAM,CAACe,QAAP,CAAgB+C,CAAhB,CAAD,IAAuB,CAAC9D,MAAM,CAACe,QAAP,CAAgBoB,CAAhB,CAA5B,EAAgD;AAC9C,YAAM,IAAIZ,SAAJ,CACJ,uEADI,CAAN;AAGD;;AAED,QAAIuC,CAAC,KAAK3B,CAAV,EAAa,OAAO,CAAP;AAEb,QAAI4B,CAAC,GAAGD,CAAC,CAACpH,MAAV;AACA,QAAIsH,CAAC,GAAG7B,CAAC,CAACzF,MAAV;;AAEA,SAAK,IAAIF,CAAC,GAAG,CAAR,EAAWC,GAAG,GAAG0C,IAAI,CAAC8E,GAAL,CAASF,CAAT,EAAYC,CAAZ,CAAtB,EAAsCxH,CAAC,GAAGC,GAA1C,EAA+C,EAAED,CAAjD,EAAoD;AAClD,UAAIsH,CAAC,CAACtH,CAAD,CAAD,KAAS2F,CAAC,CAAC3F,CAAD,CAAd,EAAmB;AACjBuH,QAAAA,CAAC,GAAGD,CAAC,CAACtH,CAAD,CAAL;AACAwH,QAAAA,CAAC,GAAG7B,CAAC,CAAC3F,CAAD,CAAL;AACA;AACD;AACF;;AAED,QAAIuH,CAAC,GAAGC,CAAR,EAAW,OAAO,CAAC,CAAR;AACX,QAAIA,CAAC,GAAGD,CAAR,EAAW,OAAO,CAAP;AACX,WAAO,CAAP;AACD,GAzBD;;AA2BA/D,EAAAA,MAAM,CAAC8C,UAAP,GAAoB,SAASA,UAAT,CAAqBJ,QAArB,EAA+B;AACjD,YAAQwB,MAAM,CAACxB,QAAD,CAAN,CAAiByB,WAAjB,EAAR;AACE,WAAK,KAAL;AACA,WAAK,MAAL;AACA,WAAK,OAAL;AACA,WAAK,OAAL;AACA,WAAK,QAAL;AACA,WAAK,QAAL;AACA,WAAK,QAAL;AACA,WAAK,MAAL;AACA,WAAK,OAAL;AACA,WAAK,SAAL;AACA,WAAK,UAAL;AACE,eAAO,IAAP;;AACF;AACE,eAAO,KAAP;AAdJ;AAgBD,GAjBD;;AAmBAnE,EAAAA,MAAM,CAACoE,MAAP,GAAgB,SAASA,MAAT,CAAiBC,IAAjB,EAAuB3H,MAAvB,EAA+B;AAC7C,QAAI,CAACJ,KAAK,CAACmH,OAAN,CAAcY,IAAd,CAAL,EAA0B;AACxB,YAAM,IAAI9C,SAAJ,CAAc,6CAAd,CAAN;AACD;;AAED,QAAI8C,IAAI,CAAC3H,MAAL,KAAgB,CAApB,EAAuB;AACrB,aAAOsD,MAAM,CAACwC,KAAP,CAAa,CAAb,CAAP;AACD;;AAED,QAAIhG,CAAJ;;AACA,QAAIE,MAAM,KAAKsE,SAAf,EAA0B;AACxBtE,MAAAA,MAAM,GAAG,CAAT;;AACA,WAAKF,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG6H,IAAI,CAAC3H,MAArB,EAA6B,EAAEF,CAA/B,EAAkC;AAChCE,QAAAA,MAAM,IAAI2H,IAAI,CAAC7H,CAAD,CAAJ,CAAQE,MAAlB;AACD;AACF;;AAED,QAAI0B,MAAM,GAAG4B,MAAM,CAACwB,WAAP,CAAmB9E,MAAnB,CAAb;AACA,QAAI4H,GAAG,GAAG,CAAV;;AACA,SAAK9H,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG6H,IAAI,CAAC3H,MAArB,EAA6B,EAAEF,CAA/B,EAAkC;AAChC,UAAI4E,GAAG,GAAGiD,IAAI,CAAC7H,CAAD,CAAd;;AACA,UAAIuF,UAAU,CAACX,GAAD,EAAM/E,UAAN,CAAd,EAAiC;AAC/B,YAAIiI,GAAG,GAAGlD,GAAG,CAAC1E,MAAV,GAAmB0B,MAAM,CAAC1B,MAA9B,EAAsC;AACpCsD,UAAAA,MAAM,CAACyB,IAAP,CAAYL,GAAZ,EAAiBiC,IAAjB,CAAsBjF,MAAtB,EAA8BkG,GAA9B;AACD,SAFD,MAEO;AACLjI,UAAAA,UAAU,CAACsE,SAAX,CAAqB4D,GAArB,CAAyBC,IAAzB,CACEpG,MADF,EAEEgD,GAFF,EAGEkD,GAHF;AAKD;AACF,OAVD,MAUO,IAAI,CAACtE,MAAM,CAACe,QAAP,CAAgBK,GAAhB,CAAL,EAA2B;AAChC,cAAM,IAAIG,SAAJ,CAAc,6CAAd,CAAN;AACD,OAFM,MAEA;AACLH,QAAAA,GAAG,CAACiC,IAAJ,CAASjF,MAAT,EAAiBkG,GAAjB;AACD;;AACDA,MAAAA,GAAG,IAAIlD,GAAG,CAAC1E,MAAX;AACD;;AACD,WAAO0B,MAAP;AACD,GAvCD;;AAyCA,WAASrC,UAAT,CAAqB8G,MAArB,EAA6BH,QAA7B,EAAuC;AACrC,QAAI1C,MAAM,CAACe,QAAP,CAAgB8B,MAAhB,CAAJ,EAA6B;AAC3B,aAAOA,MAAM,CAACnG,MAAd;AACD;;AACD,QAAIkF,WAAW,CAACC,MAAZ,CAAmBgB,MAAnB,KAA8Bd,UAAU,CAACc,MAAD,EAASjB,WAAT,CAA5C,EAAmE;AACjE,aAAOiB,MAAM,CAAC9G,UAAd;AACD;;AACD,QAAI,OAAO8G,MAAP,KAAkB,QAAtB,EAAgC;AAC9B,YAAM,IAAItB,SAAJ,CACJ,+EACA,gBADA,0BAC0BsB,MAD1B,CADI,CAAN;AAID;;AAED,QAAIpG,GAAG,GAAGoG,MAAM,CAACnG,MAAjB;AACA,QAAI+H,SAAS,GAAIC,SAAS,CAAChI,MAAV,GAAmB,CAAnB,IAAwBgI,SAAS,CAAC,CAAD,CAAT,KAAiB,IAA1D;AACA,QAAI,CAACD,SAAD,IAAchI,GAAG,KAAK,CAA1B,EAA6B,OAAO,CAAP,CAhBQ;;AAmBrC,QAAIkI,WAAW,GAAG,KAAlB;;AACA,aAAS;AACP,cAAQjC,QAAR;AACE,aAAK,OAAL;AACA,aAAK,QAAL;AACA,aAAK,QAAL;AACE,iBAAOjG,GAAP;;AACF,aAAK,MAAL;AACA,aAAK,OAAL;AACE,iBAAOmI,WAAW,CAAC/B,MAAD,CAAX,CAAoBnG,MAA3B;;AACF,aAAK,MAAL;AACA,aAAK,OAAL;AACA,aAAK,SAAL;AACA,aAAK,UAAL;AACE,iBAAOD,GAAG,GAAG,CAAb;;AACF,aAAK,KAAL;AACE,iBAAOA,GAAG,KAAK,CAAf;;AACF,aAAK,QAAL;AACE,iBAAOoI,aAAa,CAAChC,MAAD,CAAb,CAAsBnG,MAA7B;;AACF;AACE,cAAIiI,WAAJ,EAAiB;AACf,mBAAOF,SAAS,GAAG,CAAC,CAAJ,GAAQG,WAAW,CAAC/B,MAAD,CAAX,CAAoBnG,MAA5C,CADe;AAEhB;;AACDgG,UAAAA,QAAQ,GAAG,CAAC,KAAKA,QAAN,EAAgByB,WAAhB,EAAX;AACAQ,UAAAA,WAAW,GAAG,IAAd;AAtBJ;AAwBD;AACF;;AACD3E,EAAAA,MAAM,CAACjE,UAAP,GAAoBA,UAApB;;AAEA,WAAS+I,YAAT,CAAuBpC,QAAvB,EAAiC/E,KAAjC,EAAwCC,GAAxC,EAA6C;AAC3C,QAAI+G,WAAW,GAAG,KAAlB,CAD2C;;;;;;;AAU3C,QAAIhH,KAAK,KAAKqD,SAAV,IAAuBrD,KAAK,GAAG,CAAnC,EAAsC;AACpCA,MAAAA,KAAK,GAAG,CAAR;AACD,KAZ0C;;;;AAe3C,QAAIA,KAAK,GAAG,KAAKjB,MAAjB,EAAyB;AACvB,aAAO,EAAP;AACD;;AAED,QAAIkB,GAAG,KAAKoD,SAAR,IAAqBpD,GAAG,GAAG,KAAKlB,MAApC,EAA4C;AAC1CkB,MAAAA,GAAG,GAAG,KAAKlB,MAAX;AACD;;AAED,QAAIkB,GAAG,IAAI,CAAX,EAAc;AACZ,aAAO,EAAP;AACD,KAzB0C;;;AA4B3CA,IAAAA,GAAG,MAAM,CAAT;AACAD,IAAAA,KAAK,MAAM,CAAX;;AAEA,QAAIC,GAAG,IAAID,KAAX,EAAkB;AAChB,aAAO,EAAP;AACD;;AAED,QAAI,CAAC+E,QAAL,EAAeA,QAAQ,GAAG,MAAX;;AAEf,WAAO,IAAP,EAAa;AACX,cAAQA,QAAR;AACE,aAAK,KAAL;AACE,iBAAOqC,QAAQ,CAAC,IAAD,EAAOpH,KAAP,EAAcC,GAAd,CAAf;;AAEF,aAAK,MAAL;AACA,aAAK,OAAL;AACE,iBAAOoH,SAAS,CAAC,IAAD,EAAOrH,KAAP,EAAcC,GAAd,CAAhB;;AAEF,aAAK,OAAL;AACE,iBAAOqH,UAAU,CAAC,IAAD,EAAOtH,KAAP,EAAcC,GAAd,CAAjB;;AAEF,aAAK,QAAL;AACA,aAAK,QAAL;AACE,iBAAOsH,WAAW,CAAC,IAAD,EAAOvH,KAAP,EAAcC,GAAd,CAAlB;;AAEF,aAAK,QAAL;AACE,iBAAOuH,WAAW,CAAC,IAAD,EAAOxH,KAAP,EAAcC,GAAd,CAAlB;;AAEF,aAAK,MAAL;AACA,aAAK,OAAL;AACA,aAAK,SAAL;AACA,aAAK,UAAL;AACE,iBAAOwH,YAAY,CAAC,IAAD,EAAOzH,KAAP,EAAcC,GAAd,CAAnB;;AAEF;AACE,cAAI+G,WAAJ,EAAiB,MAAM,IAAIpD,SAAJ,CAAc,uBAAuBmB,QAArC,CAAN;AACjBA,UAAAA,QAAQ,GAAG,CAACA,QAAQ,GAAG,EAAZ,EAAgByB,WAAhB,EAAX;AACAQ,UAAAA,WAAW,GAAG,IAAd;AA3BJ;AA6BD;AACF;AAGD;AACA;AACA;AACA;AACA;;;AACA3E,EAAAA,MAAM,CAACW,SAAP,CAAiBiD,SAAjB,GAA6B,IAA7B;;AAEA,WAASyB,IAAT,CAAelD,CAAf,EAAkBmD,CAAlB,EAAqB5G,CAArB,EAAwB;AACtB,QAAIlC,CAAC,GAAG2F,CAAC,CAACmD,CAAD,CAAT;AACAnD,IAAAA,CAAC,CAACmD,CAAD,CAAD,GAAOnD,CAAC,CAACzD,CAAD,CAAR;AACAyD,IAAAA,CAAC,CAACzD,CAAD,CAAD,GAAOlC,CAAP;AACD;;AAEDwD,EAAAA,MAAM,CAACW,SAAP,CAAiB4E,MAAjB,GAA0B,SAASA,MAAT,GAAmB;AAC3C,QAAI9I,GAAG,GAAG,KAAKC,MAAf;;AACA,QAAID,GAAG,GAAG,CAAN,KAAY,CAAhB,EAAmB;AACjB,YAAM,IAAI0E,UAAJ,CAAe,2CAAf,CAAN;AACD;;AACD,SAAK,IAAI3E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,GAApB,EAAyBD,CAAC,IAAI,CAA9B,EAAiC;AAC/B6I,MAAAA,IAAI,CAAC,IAAD,EAAO7I,CAAP,EAAUA,CAAC,GAAG,CAAd,CAAJ;AACD;;AACD,WAAO,IAAP;AACD,GATD;;AAWAwD,EAAAA,MAAM,CAACW,SAAP,CAAiB6E,MAAjB,GAA0B,SAASA,MAAT,GAAmB;AAC3C,QAAI/I,GAAG,GAAG,KAAKC,MAAf;;AACA,QAAID,GAAG,GAAG,CAAN,KAAY,CAAhB,EAAmB;AACjB,YAAM,IAAI0E,UAAJ,CAAe,2CAAf,CAAN;AACD;;AACD,SAAK,IAAI3E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,GAApB,EAAyBD,CAAC,IAAI,CAA9B,EAAiC;AAC/B6I,MAAAA,IAAI,CAAC,IAAD,EAAO7I,CAAP,EAAUA,CAAC,GAAG,CAAd,CAAJ;AACA6I,MAAAA,IAAI,CAAC,IAAD,EAAO7I,CAAC,GAAG,CAAX,EAAcA,CAAC,GAAG,CAAlB,CAAJ;AACD;;AACD,WAAO,IAAP;AACD,GAVD;;AAYAwD,EAAAA,MAAM,CAACW,SAAP,CAAiB8E,MAAjB,GAA0B,SAASA,MAAT,GAAmB;AAC3C,QAAIhJ,GAAG,GAAG,KAAKC,MAAf;;AACA,QAAID,GAAG,GAAG,CAAN,KAAY,CAAhB,EAAmB;AACjB,YAAM,IAAI0E,UAAJ,CAAe,2CAAf,CAAN;AACD;;AACD,SAAK,IAAI3E,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,GAApB,EAAyBD,CAAC,IAAI,CAA9B,EAAiC;AAC/B6I,MAAAA,IAAI,CAAC,IAAD,EAAO7I,CAAP,EAAUA,CAAC,GAAG,CAAd,CAAJ;AACA6I,MAAAA,IAAI,CAAC,IAAD,EAAO7I,CAAC,GAAG,CAAX,EAAcA,CAAC,GAAG,CAAlB,CAAJ;AACA6I,MAAAA,IAAI,CAAC,IAAD,EAAO7I,CAAC,GAAG,CAAX,EAAcA,CAAC,GAAG,CAAlB,CAAJ;AACA6I,MAAAA,IAAI,CAAC,IAAD,EAAO7I,CAAC,GAAG,CAAX,EAAcA,CAAC,GAAG,CAAlB,CAAJ;AACD;;AACD,WAAO,IAAP;AACD,GAZD;;AAcAwD,EAAAA,MAAM,CAACW,SAAP,CAAiBgD,QAAjB,GAA4B,SAASA,QAAT,GAAqB;AAC/C,QAAIjH,MAAM,GAAG,KAAKA,MAAlB;AACA,QAAIA,MAAM,KAAK,CAAf,EAAkB,OAAO,EAAP;AAClB,QAAIgI,SAAS,CAAChI,MAAV,KAAqB,CAAzB,EAA4B,OAAOsI,SAAS,CAAC,IAAD,EAAO,CAAP,EAAUtI,MAAV,CAAhB;AAC5B,WAAOoI,YAAY,CAACY,KAAb,CAAmB,IAAnB,EAAyBhB,SAAzB,CAAP;AACD,GALD;;AAOA1E,EAAAA,MAAM,CAACW,SAAP,CAAiBgF,cAAjB,GAAkC3F,MAAM,CAACW,SAAP,CAAiBgD,QAAnD;;AAEA3D,EAAAA,MAAM,CAACW,SAAP,CAAiBiF,MAAjB,GAA0B,SAASA,MAAT,CAAiBzD,CAAjB,EAAoB;AAC5C,QAAI,CAACnC,MAAM,CAACe,QAAP,CAAgBoB,CAAhB,CAAL,EAAyB,MAAM,IAAIZ,SAAJ,CAAc,2BAAd,CAAN;AACzB,QAAI,SAASY,CAAb,EAAgB,OAAO,IAAP;AAChB,WAAOnC,MAAM,CAAC6D,OAAP,CAAe,IAAf,EAAqB1B,CAArB,MAA4B,CAAnC;AACD,GAJD;;AAMAnC,EAAAA,MAAM,CAACW,SAAP,CAAiBkF,OAAjB,GAA2B,SAASA,OAAT,GAAoB;AAC7C,QAAIC,GAAG,GAAG,EAAV;AACA,QAAIC,GAAG,GAAGhG,OAAO,CAACiG,iBAAlB;AACAF,IAAAA,GAAG,GAAG,KAAKnC,QAAL,CAAc,KAAd,EAAqB,CAArB,EAAwBoC,GAAxB,EAA6BE,OAA7B,CAAqC,SAArC,EAAgD,KAAhD,EAAuDC,IAAvD,EAAN;AACA,QAAI,KAAKxJ,MAAL,GAAcqJ,GAAlB,EAAuBD,GAAG,IAAI,OAAP;AACvB,WAAO,aAAaA,GAAb,GAAmB,GAA1B;AACD,GAND;;AAOA,MAAIjG,mBAAJ,EAAyB;AACvBG,IAAAA,MAAM,CAACW,SAAP,CAAiBd,mBAAjB,IAAwCG,MAAM,CAACW,SAAP,CAAiBkF,OAAzD;AACD;;AAED7F,EAAAA,MAAM,CAACW,SAAP,CAAiBkD,OAAjB,GAA2B,SAASA,OAAT,CAAkBsC,MAAlB,EAA0BxI,KAA1B,EAAiCC,GAAjC,EAAsCwI,SAAtC,EAAiDC,OAAjD,EAA0D;AACnF,QAAItE,UAAU,CAACoE,MAAD,EAAS9J,UAAT,CAAd,EAAoC;AAClC8J,MAAAA,MAAM,GAAGnG,MAAM,CAACyB,IAAP,CAAY0E,MAAZ,EAAoBA,MAAM,CAAC9H,MAA3B,EAAmC8H,MAAM,CAACpK,UAA1C,CAAT;AACD;;AACD,QAAI,CAACiE,MAAM,CAACe,QAAP,CAAgBoF,MAAhB,CAAL,EAA8B;AAC5B,YAAM,IAAI5E,SAAJ,CACJ,qEACA,gBADA,0BAC2B4E,MAD3B,CADI,CAAN;AAID;;AAED,QAAIxI,KAAK,KAAKqD,SAAd,EAAyB;AACvBrD,MAAAA,KAAK,GAAG,CAAR;AACD;;AACD,QAAIC,GAAG,KAAKoD,SAAZ,EAAuB;AACrBpD,MAAAA,GAAG,GAAGuI,MAAM,GAAGA,MAAM,CAACzJ,MAAV,GAAmB,CAA/B;AACD;;AACD,QAAI0J,SAAS,KAAKpF,SAAlB,EAA6B;AAC3BoF,MAAAA,SAAS,GAAG,CAAZ;AACD;;AACD,QAAIC,OAAO,KAAKrF,SAAhB,EAA2B;AACzBqF,MAAAA,OAAO,GAAG,KAAK3J,MAAf;AACD;;AAED,QAAIiB,KAAK,GAAG,CAAR,IAAaC,GAAG,GAAGuI,MAAM,CAACzJ,MAA1B,IAAoC0J,SAAS,GAAG,CAAhD,IAAqDC,OAAO,GAAG,KAAK3J,MAAxE,EAAgF;AAC9E,YAAM,IAAIyE,UAAJ,CAAe,oBAAf,CAAN;AACD;;AAED,QAAIiF,SAAS,IAAIC,OAAb,IAAwB1I,KAAK,IAAIC,GAArC,EAA0C;AACxC,aAAO,CAAP;AACD;;AACD,QAAIwI,SAAS,IAAIC,OAAjB,EAA0B;AACxB,aAAO,CAAC,CAAR;AACD;;AACD,QAAI1I,KAAK,IAAIC,GAAb,EAAkB;AAChB,aAAO,CAAP;AACD;;AAEDD,IAAAA,KAAK,MAAM,CAAX;AACAC,IAAAA,GAAG,MAAM,CAAT;AACAwI,IAAAA,SAAS,MAAM,CAAf;AACAC,IAAAA,OAAO,MAAM,CAAb;AAEA,QAAI,SAASF,MAAb,EAAqB,OAAO,CAAP;AAErB,QAAIpC,CAAC,GAAGsC,OAAO,GAAGD,SAAlB;AACA,QAAIpC,CAAC,GAAGpG,GAAG,GAAGD,KAAd;AACA,QAAIlB,GAAG,GAAG0C,IAAI,CAAC8E,GAAL,CAASF,CAAT,EAAYC,CAAZ,CAAV;AAEA,QAAIsC,QAAQ,GAAG,KAAKrD,KAAL,CAAWmD,SAAX,EAAsBC,OAAtB,CAAf;AACA,QAAIE,UAAU,GAAGJ,MAAM,CAAClD,KAAP,CAAatF,KAAb,EAAoBC,GAApB,CAAjB;;AAEA,SAAK,IAAIpB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,GAApB,EAAyB,EAAED,CAA3B,EAA8B;AAC5B,UAAI8J,QAAQ,CAAC9J,CAAD,CAAR,KAAgB+J,UAAU,CAAC/J,CAAD,CAA9B,EAAmC;AACjCuH,QAAAA,CAAC,GAAGuC,QAAQ,CAAC9J,CAAD,CAAZ;AACAwH,QAAAA,CAAC,GAAGuC,UAAU,CAAC/J,CAAD,CAAd;AACA;AACD;AACF;;AAED,QAAIuH,CAAC,GAAGC,CAAR,EAAW,OAAO,CAAC,CAAR;AACX,QAAIA,CAAC,GAAGD,CAAR,EAAW,OAAO,CAAP;AACX,WAAO,CAAP;AACD,GA/DD;AAkEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AACA,WAASyC,oBAAT,CAA+BpI,MAA/B,EAAuCqI,GAAvC,EAA4CxF,UAA5C,EAAwDyB,QAAxD,EAAkEgE,GAAlE,EAAuE;;AAErE,QAAItI,MAAM,CAAC1B,MAAP,KAAkB,CAAtB,EAAyB,OAAO,CAAC,CAAR,CAF4C;;AAKrE,QAAI,OAAOuE,UAAP,KAAsB,QAA1B,EAAoC;AAClCyB,MAAAA,QAAQ,GAAGzB,UAAX;AACAA,MAAAA,UAAU,GAAG,CAAb;AACD,KAHD,MAGO,IAAIA,UAAU,GAAG,UAAjB,EAA6B;AAClCA,MAAAA,UAAU,GAAG,UAAb;AACD,KAFM,MAEA,IAAIA,UAAU,GAAG,CAAC,UAAlB,EAA8B;AACnCA,MAAAA,UAAU,GAAG,CAAC,UAAd;AACD;;AACDA,IAAAA,UAAU,GAAG,CAACA,UAAd,CAbqE;;AAcrE,QAAIsC,WAAW,CAACtC,UAAD,CAAf,EAA6B;;AAE3BA,MAAAA,UAAU,GAAGyF,GAAG,GAAG,CAAH,GAAQtI,MAAM,CAAC1B,MAAP,GAAgB,CAAxC;AACD,KAjBoE;;;AAoBrE,QAAIuE,UAAU,GAAG,CAAjB,EAAoBA,UAAU,GAAG7C,MAAM,CAAC1B,MAAP,GAAgBuE,UAA7B;;AACpB,QAAIA,UAAU,IAAI7C,MAAM,CAAC1B,MAAzB,EAAiC;AAC/B,UAAIgK,GAAJ,EAAS,OAAO,CAAC,CAAR,CAAT,KACKzF,UAAU,GAAG7C,MAAM,CAAC1B,MAAP,GAAgB,CAA7B;AACN,KAHD,MAGO,IAAIuE,UAAU,GAAG,CAAjB,EAAoB;AACzB,UAAIyF,GAAJ,EAASzF,UAAU,GAAG,CAAb,CAAT,KACK,OAAO,CAAC,CAAR;AACN,KA3BoE;;;AA8BrE,QAAI,OAAOwF,GAAP,KAAe,QAAnB,EAA6B;AAC3BA,MAAAA,GAAG,GAAGzG,MAAM,CAACyB,IAAP,CAAYgF,GAAZ,EAAiB/D,QAAjB,CAAN;AACD,KAhCoE;;;AAmCrE,QAAI1C,MAAM,CAACe,QAAP,CAAgB0F,GAAhB,CAAJ,EAA0B;;AAExB,UAAIA,GAAG,CAAC/J,MAAJ,KAAe,CAAnB,EAAsB;AACpB,eAAO,CAAC,CAAR;AACD;;AACD,aAAOiK,YAAY,CAACvI,MAAD,EAASqI,GAAT,EAAcxF,UAAd,EAA0ByB,QAA1B,EAAoCgE,GAApC,CAAnB;AACD,KAND,MAMO,IAAI,OAAOD,GAAP,KAAe,QAAnB,EAA6B;AAClCA,MAAAA,GAAG,GAAGA,GAAG,GAAG,IAAZ,CADkC;;AAElC,UAAI,OAAOpK,UAAU,CAACsE,SAAX,CAAqB3D,OAA5B,KAAwC,UAA5C,EAAwD;AACtD,YAAI0J,GAAJ,EAAS;AACP,iBAAOrK,UAAU,CAACsE,SAAX,CAAqB3D,OAArB,CAA6BwH,IAA7B,CAAkCpG,MAAlC,EAA0CqI,GAA1C,EAA+CxF,UAA/C,CAAP;AACD,SAFD,MAEO;AACL,iBAAO5E,UAAU,CAACsE,SAAX,CAAqBiG,WAArB,CAAiCpC,IAAjC,CAAsCpG,MAAtC,EAA8CqI,GAA9C,EAAmDxF,UAAnD,CAAP;AACD;AACF;;AACD,aAAO0F,YAAY,CAACvI,MAAD,EAAS,CAACqI,GAAD,CAAT,EAAgBxF,UAAhB,EAA4ByB,QAA5B,EAAsCgE,GAAtC,CAAnB;AACD;;AAED,UAAM,IAAInF,SAAJ,CAAc,sCAAd,CAAN;AACD;;AAED,WAASoF,YAAT,CAAuBtJ,GAAvB,EAA4BoJ,GAA5B,EAAiCxF,UAAjC,EAA6CyB,QAA7C,EAAuDgE,GAAvD,EAA4D;AAC1D,QAAIG,SAAS,GAAG,CAAhB;AACA,QAAIC,SAAS,GAAGzJ,GAAG,CAACX,MAApB;AACA,QAAIqK,SAAS,GAAGN,GAAG,CAAC/J,MAApB;;AAEA,QAAIgG,QAAQ,KAAK1B,SAAjB,EAA4B;AAC1B0B,MAAAA,QAAQ,GAAGwB,MAAM,CAACxB,QAAD,CAAN,CAAiByB,WAAjB,EAAX;;AACA,UAAIzB,QAAQ,KAAK,MAAb,IAAuBA,QAAQ,KAAK,OAApC,IACAA,QAAQ,KAAK,SADb,IAC0BA,QAAQ,KAAK,UAD3C,EACuD;AACrD,YAAIrF,GAAG,CAACX,MAAJ,GAAa,CAAb,IAAkB+J,GAAG,CAAC/J,MAAJ,GAAa,CAAnC,EAAsC;AACpC,iBAAO,CAAC,CAAR;AACD;;AACDmK,QAAAA,SAAS,GAAG,CAAZ;AACAC,QAAAA,SAAS,IAAI,CAAb;AACAC,QAAAA,SAAS,IAAI,CAAb;AACA9F,QAAAA,UAAU,IAAI,CAAd;AACD;AACF;;AAED,aAAS+F,IAAT,CAAe5F,GAAf,EAAoB5E,CAApB,EAAuB;AACrB,UAAIqK,SAAS,KAAK,CAAlB,EAAqB;AACnB,eAAOzF,GAAG,CAAC5E,CAAD,CAAV;AACD,OAFD,MAEO;AACL,eAAO4E,GAAG,CAAC6F,YAAJ,CAAiBzK,CAAC,GAAGqK,SAArB,CAAP;AACD;AACF;;AAED,QAAIrK,CAAJ;;AACA,QAAIkK,GAAJ,EAAS;AACP,UAAIQ,UAAU,GAAG,CAAC,CAAlB;;AACA,WAAK1K,CAAC,GAAGyE,UAAT,EAAqBzE,CAAC,GAAGsK,SAAzB,EAAoCtK,CAAC,EAArC,EAAyC;AACvC,YAAIwK,IAAI,CAAC3J,GAAD,EAAMb,CAAN,CAAJ,KAAiBwK,IAAI,CAACP,GAAD,EAAMS,UAAU,KAAK,CAAC,CAAhB,GAAoB,CAApB,GAAwB1K,CAAC,GAAG0K,UAAlC,CAAzB,EAAwE;AACtE,cAAIA,UAAU,KAAK,CAAC,CAApB,EAAuBA,UAAU,GAAG1K,CAAb;AACvB,cAAIA,CAAC,GAAG0K,UAAJ,GAAiB,CAAjB,KAAuBH,SAA3B,EAAsC,OAAOG,UAAU,GAAGL,SAApB;AACvC,SAHD,MAGO;AACL,cAAIK,UAAU,KAAK,CAAC,CAApB,EAAuB1K,CAAC,IAAIA,CAAC,GAAG0K,UAAT;AACvBA,UAAAA,UAAU,GAAG,CAAC,CAAd;AACD;AACF;AACF,KAXD,MAWO;AACL,UAAIjG,UAAU,GAAG8F,SAAb,GAAyBD,SAA7B,EAAwC7F,UAAU,GAAG6F,SAAS,GAAGC,SAAzB;;AACxC,WAAKvK,CAAC,GAAGyE,UAAT,EAAqBzE,CAAC,IAAI,CAA1B,EAA6BA,CAAC,EAA9B,EAAkC;AAChC,YAAI2K,KAAK,GAAG,IAAZ;;AACA,aAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,SAApB,EAA+BK,CAAC,EAAhC,EAAoC;AAClC,cAAIJ,IAAI,CAAC3J,GAAD,EAAMb,CAAC,GAAG4K,CAAV,CAAJ,KAAqBJ,IAAI,CAACP,GAAD,EAAMW,CAAN,CAA7B,EAAuC;AACrCD,YAAAA,KAAK,GAAG,KAAR;AACA;AACD;AACF;;AACD,YAAIA,KAAJ,EAAW,OAAO3K,CAAP;AACZ;AACF;;AAED,WAAO,CAAC,CAAR;AACD;;AAEDwD,EAAAA,MAAM,CAACW,SAAP,CAAiB0G,QAAjB,GAA4B,SAASA,QAAT,CAAmBZ,GAAnB,EAAwBxF,UAAxB,EAAoCyB,QAApC,EAA8C;AACxE,WAAO,KAAK1F,OAAL,CAAayJ,GAAb,EAAkBxF,UAAlB,EAA8ByB,QAA9B,MAA4C,CAAC,CAApD;AACD,GAFD;;AAIA1C,EAAAA,MAAM,CAACW,SAAP,CAAiB3D,OAAjB,GAA2B,SAASA,OAAT,CAAkByJ,GAAlB,EAAuBxF,UAAvB,EAAmCyB,QAAnC,EAA6C;AACtE,WAAO8D,oBAAoB,CAAC,IAAD,EAAOC,GAAP,EAAYxF,UAAZ,EAAwByB,QAAxB,EAAkC,IAAlC,CAA3B;AACD,GAFD;;AAIA1C,EAAAA,MAAM,CAACW,SAAP,CAAiBiG,WAAjB,GAA+B,SAASA,WAAT,CAAsBH,GAAtB,EAA2BxF,UAA3B,EAAuCyB,QAAvC,EAAiD;AAC9E,WAAO8D,oBAAoB,CAAC,IAAD,EAAOC,GAAP,EAAYxF,UAAZ,EAAwByB,QAAxB,EAAkC,KAAlC,CAA3B;AACD,GAFD;;AAIA,WAAS4E,QAAT,CAAmBlG,GAAnB,EAAwByB,MAAxB,EAAgCxE,MAAhC,EAAwC3B,MAAxC,EAAgD;AAC9C2B,IAAAA,MAAM,GAAGkJ,MAAM,CAAClJ,MAAD,CAAN,IAAkB,CAA3B;AACA,QAAImJ,SAAS,GAAGpG,GAAG,CAAC1E,MAAJ,GAAa2B,MAA7B;;AACA,QAAI,CAAC3B,MAAL,EAAa;AACXA,MAAAA,MAAM,GAAG8K,SAAT;AACD,KAFD,MAEO;AACL9K,MAAAA,MAAM,GAAG6K,MAAM,CAAC7K,MAAD,CAAf;;AACA,UAAIA,MAAM,GAAG8K,SAAb,EAAwB;AACtB9K,QAAAA,MAAM,GAAG8K,SAAT;AACD;AACF;;AAED,QAAIC,MAAM,GAAG5E,MAAM,CAACnG,MAApB;;AAEA,QAAIA,MAAM,GAAG+K,MAAM,GAAG,CAAtB,EAAyB;AACvB/K,MAAAA,MAAM,GAAG+K,MAAM,GAAG,CAAlB;AACD;;AACD,SAAK,IAAIjL,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGE,MAApB,EAA4B,EAAEF,CAA9B,EAAiC;AAC/B,UAAIkL,MAAM,GAAGC,QAAQ,CAAC9E,MAAM,CAAC+E,MAAP,CAAcpL,CAAC,GAAG,CAAlB,EAAqB,CAArB,CAAD,EAA0B,EAA1B,CAArB;AACA,UAAI+G,WAAW,CAACmE,MAAD,CAAf,EAAyB,OAAOlL,CAAP;AACzB4E,MAAAA,GAAG,CAAC/C,MAAM,GAAG7B,CAAV,CAAH,GAAkBkL,MAAlB;AACD;;AACD,WAAOlL,CAAP;AACD;;AAED,WAASqL,SAAT,CAAoBzG,GAApB,EAAyByB,MAAzB,EAAiCxE,MAAjC,EAAyC3B,MAAzC,EAAiD;AAC/C,WAAOoL,UAAU,CAAClD,WAAW,CAAC/B,MAAD,EAASzB,GAAG,CAAC1E,MAAJ,GAAa2B,MAAtB,CAAZ,EAA2C+C,GAA3C,EAAgD/C,MAAhD,EAAwD3B,MAAxD,CAAjB;AACD;;AAED,WAASqL,UAAT,CAAqB3G,GAArB,EAA0ByB,MAA1B,EAAkCxE,MAAlC,EAA0C3B,MAA1C,EAAkD;AAChD,WAAOoL,UAAU,CAACE,YAAY,CAACnF,MAAD,CAAb,EAAuBzB,GAAvB,EAA4B/C,MAA5B,EAAoC3B,MAApC,CAAjB;AACD;;AAED,WAASuL,WAAT,CAAsB7G,GAAtB,EAA2ByB,MAA3B,EAAmCxE,MAAnC,EAA2C3B,MAA3C,EAAmD;AACjD,WAAOoL,UAAU,CAACjD,aAAa,CAAChC,MAAD,CAAd,EAAwBzB,GAAxB,EAA6B/C,MAA7B,EAAqC3B,MAArC,CAAjB;AACD;;AAED,WAASwL,SAAT,CAAoB9G,GAApB,EAAyByB,MAAzB,EAAiCxE,MAAjC,EAAyC3B,MAAzC,EAAiD;AAC/C,WAAOoL,UAAU,CAACK,cAAc,CAACtF,MAAD,EAASzB,GAAG,CAAC1E,MAAJ,GAAa2B,MAAtB,CAAf,EAA8C+C,GAA9C,EAAmD/C,MAAnD,EAA2D3B,MAA3D,CAAjB;AACD;;AAEDsD,EAAAA,MAAM,CAACW,SAAP,CAAiBqC,KAAjB,GAAyB,SAASA,KAAT,CAAgBH,MAAhB,EAAwBxE,MAAxB,EAAgC3B,MAAhC,EAAwCgG,QAAxC,EAAkD;;AAEzE,QAAIrE,MAAM,KAAK2C,SAAf,EAA0B;AACxB0B,MAAAA,QAAQ,GAAG,MAAX;AACAhG,MAAAA,MAAM,GAAG,KAAKA,MAAd;AACA2B,MAAAA,MAAM,GAAG,CAAT,CAHwB;AAKzB,KALD,MAKO,IAAI3B,MAAM,KAAKsE,SAAX,IAAwB,OAAO3C,MAAP,KAAkB,QAA9C,EAAwD;AAC7DqE,MAAAA,QAAQ,GAAGrE,MAAX;AACA3B,MAAAA,MAAM,GAAG,KAAKA,MAAd;AACA2B,MAAAA,MAAM,GAAG,CAAT,CAH6D;AAK9D,KALM,MAKA,IAAI+J,QAAQ,CAAC/J,MAAD,CAAZ,EAAsB;AAC3BA,MAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;;AACA,UAAI+J,QAAQ,CAAC1L,MAAD,CAAZ,EAAsB;AACpBA,QAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,YAAIgG,QAAQ,KAAK1B,SAAjB,EAA4B0B,QAAQ,GAAG,MAAX;AAC7B,OAHD,MAGO;AACLA,QAAAA,QAAQ,GAAGhG,MAAX;AACAA,QAAAA,MAAM,GAAGsE,SAAT;AACD;AACF,KATM,MASA;AACL,YAAM,IAAIlE,KAAJ,CACJ,yEADI,CAAN;AAGD;;AAED,QAAI0K,SAAS,GAAG,KAAK9K,MAAL,GAAc2B,MAA9B;AACA,QAAI3B,MAAM,KAAKsE,SAAX,IAAwBtE,MAAM,GAAG8K,SAArC,EAAgD9K,MAAM,GAAG8K,SAAT;;AAEhD,QAAK3E,MAAM,CAACnG,MAAP,GAAgB,CAAhB,KAAsBA,MAAM,GAAG,CAAT,IAAc2B,MAAM,GAAG,CAA7C,CAAD,IAAqDA,MAAM,GAAG,KAAK3B,MAAvE,EAA+E;AAC7E,YAAM,IAAIyE,UAAJ,CAAe,wCAAf,CAAN;AACD;;AAED,QAAI,CAACuB,QAAL,EAAeA,QAAQ,GAAG,MAAX;AAEf,QAAIiC,WAAW,GAAG,KAAlB;;AACA,aAAS;AACP,cAAQjC,QAAR;AACE,aAAK,KAAL;AACE,iBAAO4E,QAAQ,CAAC,IAAD,EAAOzE,MAAP,EAAexE,MAAf,EAAuB3B,MAAvB,CAAf;;AAEF,aAAK,MAAL;AACA,aAAK,OAAL;AACE,iBAAOmL,SAAS,CAAC,IAAD,EAAOhF,MAAP,EAAexE,MAAf,EAAuB3B,MAAvB,CAAhB;;AAEF,aAAK,OAAL;AACA,aAAK,QAAL;AACA,aAAK,QAAL;AACE,iBAAOqL,UAAU,CAAC,IAAD,EAAOlF,MAAP,EAAexE,MAAf,EAAuB3B,MAAvB,CAAjB;;AAEF,aAAK,QAAL;;AAEE,iBAAOuL,WAAW,CAAC,IAAD,EAAOpF,MAAP,EAAexE,MAAf,EAAuB3B,MAAvB,CAAlB;;AAEF,aAAK,MAAL;AACA,aAAK,OAAL;AACA,aAAK,SAAL;AACA,aAAK,UAAL;AACE,iBAAOwL,SAAS,CAAC,IAAD,EAAOrF,MAAP,EAAexE,MAAf,EAAuB3B,MAAvB,CAAhB;;AAEF;AACE,cAAIiI,WAAJ,EAAiB,MAAM,IAAIpD,SAAJ,CAAc,uBAAuBmB,QAArC,CAAN;AACjBA,UAAAA,QAAQ,GAAG,CAAC,KAAKA,QAAN,EAAgByB,WAAhB,EAAX;AACAQ,UAAAA,WAAW,GAAG,IAAd;AA1BJ;AA4BD;AACF,GAnED;;AAqEA3E,EAAAA,MAAM,CAACW,SAAP,CAAiB0H,MAAjB,GAA0B,SAASA,MAAT,GAAmB;AAC3C,WAAO;AACL7E,MAAAA,IAAI,EAAE,QADD;AAELE,MAAAA,IAAI,EAAEpH,KAAK,CAACqE,SAAN,CAAgBsC,KAAhB,CAAsBuB,IAAtB,CAA2B,KAAK8D,IAAL,IAAa,IAAxC,EAA8C,CAA9C;AAFD,KAAP;AAID,GALD;;AAOA,WAASnD,WAAT,CAAsB/D,GAAtB,EAA2BzD,KAA3B,EAAkCC,GAAlC,EAAuC;AACrC,QAAID,KAAK,KAAK,CAAV,IAAeC,GAAG,KAAKwD,GAAG,CAAC1E,MAA/B,EAAuC;AACrC,aAAO6L,QAAM,CAACtM,aAAP,CAAqBmF,GAArB,CAAP;AACD,KAFD,MAEO;AACL,aAAOmH,QAAM,CAACtM,aAAP,CAAqBmF,GAAG,CAAC6B,KAAJ,CAAUtF,KAAV,EAAiBC,GAAjB,CAArB,CAAP;AACD;AACF;;AAED,WAASoH,SAAT,CAAoB5D,GAApB,EAAyBzD,KAAzB,EAAgCC,GAAhC,EAAqC;AACnCA,IAAAA,GAAG,GAAGuB,IAAI,CAAC8E,GAAL,CAAS7C,GAAG,CAAC1E,MAAb,EAAqBkB,GAArB,CAAN;AACA,QAAI4K,GAAG,GAAG,EAAV;AAEA,QAAIhM,CAAC,GAAGmB,KAAR;;AACA,WAAOnB,CAAC,GAAGoB,GAAX,EAAgB;AACd,UAAI6K,SAAS,GAAGrH,GAAG,CAAC5E,CAAD,CAAnB;AACA,UAAIkM,SAAS,GAAG,IAAhB;AACA,UAAIC,gBAAgB,GAAIF,SAAS,GAAG,IAAb,GACnB,CADmB,GAElBA,SAAS,GAAG,IAAb,GACI,CADJ,GAEKA,SAAS,GAAG,IAAb,GACI,CADJ,GAEI,CANZ;;AAQA,UAAIjM,CAAC,GAAGmM,gBAAJ,IAAwB/K,GAA5B,EAAiC;AAC/B,YAAIgL,UAAJ,EAAgBC,SAAhB,EAA2BC,UAA3B,EAAuCC,aAAvC;;AAEA,gBAAQJ,gBAAR;AACE,eAAK,CAAL;AACE,gBAAIF,SAAS,GAAG,IAAhB,EAAsB;AACpBC,cAAAA,SAAS,GAAGD,SAAZ;AACD;;AACD;;AACF,eAAK,CAAL;AACEG,YAAAA,UAAU,GAAGxH,GAAG,CAAC5E,CAAC,GAAG,CAAL,CAAhB;;AACA,gBAAI,CAACoM,UAAU,GAAG,IAAd,MAAwB,IAA5B,EAAkC;AAChCG,cAAAA,aAAa,GAAG,CAACN,SAAS,GAAG,IAAb,KAAsB,GAAtB,GAA6BG,UAAU,GAAG,IAA1D;;AACA,kBAAIG,aAAa,GAAG,IAApB,EAA0B;AACxBL,gBAAAA,SAAS,GAAGK,aAAZ;AACD;AACF;;AACD;;AACF,eAAK,CAAL;AACEH,YAAAA,UAAU,GAAGxH,GAAG,CAAC5E,CAAC,GAAG,CAAL,CAAhB;AACAqM,YAAAA,SAAS,GAAGzH,GAAG,CAAC5E,CAAC,GAAG,CAAL,CAAf;;AACA,gBAAI,CAACoM,UAAU,GAAG,IAAd,MAAwB,IAAxB,IAAgC,CAACC,SAAS,GAAG,IAAb,MAAuB,IAA3D,EAAiE;AAC/DE,cAAAA,aAAa,GAAG,CAACN,SAAS,GAAG,GAAb,KAAqB,GAArB,GAA2B,CAACG,UAAU,GAAG,IAAd,KAAuB,GAAlD,GAAyDC,SAAS,GAAG,IAArF;;AACA,kBAAIE,aAAa,GAAG,KAAhB,KAA0BA,aAAa,GAAG,MAAhB,IAA0BA,aAAa,GAAG,MAApE,CAAJ,EAAiF;AAC/EL,gBAAAA,SAAS,GAAGK,aAAZ;AACD;AACF;;AACD;;AACF,eAAK,CAAL;AACEH,YAAAA,UAAU,GAAGxH,GAAG,CAAC5E,CAAC,GAAG,CAAL,CAAhB;AACAqM,YAAAA,SAAS,GAAGzH,GAAG,CAAC5E,CAAC,GAAG,CAAL,CAAf;AACAsM,YAAAA,UAAU,GAAG1H,GAAG,CAAC5E,CAAC,GAAG,CAAL,CAAhB;;AACA,gBAAI,CAACoM,UAAU,GAAG,IAAd,MAAwB,IAAxB,IAAgC,CAACC,SAAS,GAAG,IAAb,MAAuB,IAAvD,IAA+D,CAACC,UAAU,GAAG,IAAd,MAAwB,IAA3F,EAAiG;AAC/FC,cAAAA,aAAa,GAAG,CAACN,SAAS,GAAG,GAAb,KAAqB,IAArB,GAA4B,CAACG,UAAU,GAAG,IAAd,KAAuB,GAAnD,GAAyD,CAACC,SAAS,GAAG,IAAb,KAAsB,GAA/E,GAAsFC,UAAU,GAAG,IAAnH;;AACA,kBAAIC,aAAa,GAAG,MAAhB,IAA0BA,aAAa,GAAG,QAA9C,EAAwD;AACtDL,gBAAAA,SAAS,GAAGK,aAAZ;AACD;AACF;;AAlCL;AAoCD;;AAED,UAAIL,SAAS,KAAK,IAAlB,EAAwB;;;AAGtBA,QAAAA,SAAS,GAAG,MAAZ;AACAC,QAAAA,gBAAgB,GAAG,CAAnB;AACD,OALD,MAKO,IAAID,SAAS,GAAG,MAAhB,EAAwB;;AAE7BA,QAAAA,SAAS,IAAI,OAAb;AACAF,QAAAA,GAAG,CAAC1K,IAAJ,CAAS4K,SAAS,KAAK,EAAd,GAAmB,KAAnB,GAA2B,MAApC;AACAA,QAAAA,SAAS,GAAG,SAASA,SAAS,GAAG,KAAjC;AACD;;AAEDF,MAAAA,GAAG,CAAC1K,IAAJ,CAAS4K,SAAT;AACAlM,MAAAA,CAAC,IAAImM,gBAAL;AACD;;AAED,WAAOK,qBAAqB,CAACR,GAAD,CAA5B;AACD;AAGD;AACA;;;AACA,MAAIS,oBAAoB,GAAG,MAA3B;;AAEA,WAASD,qBAAT,CAAgCE,UAAhC,EAA4C;AAC1C,QAAIzM,GAAG,GAAGyM,UAAU,CAACxM,MAArB;;AACA,QAAID,GAAG,IAAIwM,oBAAX,EAAiC;AAC/B,aAAO/E,MAAM,CAACiF,YAAP,CAAoBzD,KAApB,CAA0BxB,MAA1B,EAAkCgF,UAAlC,CAAP,CAD+B;AAEhC,KAJyC;;;AAO1C,QAAIV,GAAG,GAAG,EAAV;AACA,QAAIhM,CAAC,GAAG,CAAR;;AACA,WAAOA,CAAC,GAAGC,GAAX,EAAgB;AACd+L,MAAAA,GAAG,IAAItE,MAAM,CAACiF,YAAP,CAAoBzD,KAApB,CACLxB,MADK,EAELgF,UAAU,CAACjG,KAAX,CAAiBzG,CAAjB,EAAoBA,CAAC,IAAIyM,oBAAzB,CAFK,CAAP;AAID;;AACD,WAAOT,GAAP;AACD;;AAED,WAASvD,UAAT,CAAqB7D,GAArB,EAA0BzD,KAA1B,EAAiCC,GAAjC,EAAsC;AACpC,QAAIwL,GAAG,GAAG,EAAV;AACAxL,IAAAA,GAAG,GAAGuB,IAAI,CAAC8E,GAAL,CAAS7C,GAAG,CAAC1E,MAAb,EAAqBkB,GAArB,CAAN;;AAEA,SAAK,IAAIpB,CAAC,GAAGmB,KAAb,EAAoBnB,CAAC,GAAGoB,GAAxB,EAA6B,EAAEpB,CAA/B,EAAkC;AAChC4M,MAAAA,GAAG,IAAIlF,MAAM,CAACiF,YAAP,CAAoB/H,GAAG,CAAC5E,CAAD,CAAH,GAAS,IAA7B,CAAP;AACD;;AACD,WAAO4M,GAAP;AACD;;AAED,WAASlE,WAAT,CAAsB9D,GAAtB,EAA2BzD,KAA3B,EAAkCC,GAAlC,EAAuC;AACrC,QAAIwL,GAAG,GAAG,EAAV;AACAxL,IAAAA,GAAG,GAAGuB,IAAI,CAAC8E,GAAL,CAAS7C,GAAG,CAAC1E,MAAb,EAAqBkB,GAArB,CAAN;;AAEA,SAAK,IAAIpB,CAAC,GAAGmB,KAAb,EAAoBnB,CAAC,GAAGoB,GAAxB,EAA6B,EAAEpB,CAA/B,EAAkC;AAChC4M,MAAAA,GAAG,IAAIlF,MAAM,CAACiF,YAAP,CAAoB/H,GAAG,CAAC5E,CAAD,CAAvB,CAAP;AACD;;AACD,WAAO4M,GAAP;AACD;;AAED,WAASrE,QAAT,CAAmB3D,GAAnB,EAAwBzD,KAAxB,EAA+BC,GAA/B,EAAoC;AAClC,QAAInB,GAAG,GAAG2E,GAAG,CAAC1E,MAAd;AAEA,QAAI,CAACiB,KAAD,IAAUA,KAAK,GAAG,CAAtB,EAAyBA,KAAK,GAAG,CAAR;AACzB,QAAI,CAACC,GAAD,IAAQA,GAAG,GAAG,CAAd,IAAmBA,GAAG,GAAGnB,GAA7B,EAAkCmB,GAAG,GAAGnB,GAAN;AAElC,QAAI4M,GAAG,GAAG,EAAV;;AACA,SAAK,IAAI7M,CAAC,GAAGmB,KAAb,EAAoBnB,CAAC,GAAGoB,GAAxB,EAA6B,EAAEpB,CAA/B,EAAkC;AAChC6M,MAAAA,GAAG,IAAIC,mBAAmB,CAAClI,GAAG,CAAC5E,CAAD,CAAJ,CAA1B;AACD;;AACD,WAAO6M,GAAP;AACD;;AAED,WAASjE,YAAT,CAAuBhE,GAAvB,EAA4BzD,KAA5B,EAAmCC,GAAnC,EAAwC;AACtC,QAAI2L,KAAK,GAAGnI,GAAG,CAAC6B,KAAJ,CAAUtF,KAAV,EAAiBC,GAAjB,CAAZ;AACA,QAAI4K,GAAG,GAAG,EAAV,CAFsC;;AAItC,SAAK,IAAIhM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG+M,KAAK,CAAC7M,MAAN,GAAe,CAAnC,EAAsCF,CAAC,IAAI,CAA3C,EAA8C;AAC5CgM,MAAAA,GAAG,IAAItE,MAAM,CAACiF,YAAP,CAAoBI,KAAK,CAAC/M,CAAD,CAAL,GAAY+M,KAAK,CAAC/M,CAAC,GAAG,CAAL,CAAL,GAAe,GAA/C,CAAP;AACD;;AACD,WAAOgM,GAAP;AACD;;AAEDxI,EAAAA,MAAM,CAACW,SAAP,CAAiBsC,KAAjB,GAAyB,SAASA,KAAT,CAAgBtF,KAAhB,EAAuBC,GAAvB,EAA4B;AACnD,QAAInB,GAAG,GAAG,KAAKC,MAAf;AACAiB,IAAAA,KAAK,GAAG,CAAC,CAACA,KAAV;AACAC,IAAAA,GAAG,GAAGA,GAAG,KAAKoD,SAAR,GAAoBvE,GAApB,GAA0B,CAAC,CAACmB,GAAlC;;AAEA,QAAID,KAAK,GAAG,CAAZ,EAAe;AACbA,MAAAA,KAAK,IAAIlB,GAAT;AACA,UAAIkB,KAAK,GAAG,CAAZ,EAAeA,KAAK,GAAG,CAAR;AAChB,KAHD,MAGO,IAAIA,KAAK,GAAGlB,GAAZ,EAAiB;AACtBkB,MAAAA,KAAK,GAAGlB,GAAR;AACD;;AAED,QAAImB,GAAG,GAAG,CAAV,EAAa;AACXA,MAAAA,GAAG,IAAInB,GAAP;AACA,UAAImB,GAAG,GAAG,CAAV,EAAaA,GAAG,GAAG,CAAN;AACd,KAHD,MAGO,IAAIA,GAAG,GAAGnB,GAAV,EAAe;AACpBmB,MAAAA,GAAG,GAAGnB,GAAN;AACD;;AAED,QAAImB,GAAG,GAAGD,KAAV,EAAiBC,GAAG,GAAGD,KAAN;AAEjB,QAAI6L,MAAM,GAAG,KAAKC,QAAL,CAAc9L,KAAd,EAAqBC,GAArB,CAAb,CArBmD;;AAuBnD6C,IAAAA,MAAM,CAACC,cAAP,CAAsB8I,MAAtB,EAA8BxJ,MAAM,CAACW,SAArC;AAEA,WAAO6I,MAAP;AACD,GA1BD;AA4BA;AACA;AACA;;;AACA,WAASE,WAAT,CAAsBrL,MAAtB,EAA8BsL,GAA9B,EAAmCjN,MAAnC,EAA2C;AACzC,QAAK2B,MAAM,GAAG,CAAV,KAAiB,CAAjB,IAAsBA,MAAM,GAAG,CAAnC,EAAsC,MAAM,IAAI8C,UAAJ,CAAe,oBAAf,CAAN;AACtC,QAAI9C,MAAM,GAAGsL,GAAT,GAAejN,MAAnB,EAA2B,MAAM,IAAIyE,UAAJ,CAAe,uCAAf,CAAN;AAC5B;;AAEDnB,EAAAA,MAAM,CAACW,SAAP,CAAiBiJ,UAAjB,GACA5J,MAAM,CAACW,SAAP,CAAiBkJ,UAAjB,GAA8B,SAASA,UAAT,CAAqBxL,MAArB,EAA6BtC,UAA7B,EAAyC+N,QAAzC,EAAmD;AAC/EzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACAtC,IAAAA,UAAU,GAAGA,UAAU,KAAK,CAA5B;AACA,QAAI,CAAC+N,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAStC,UAAT,EAAqB,KAAKW,MAA1B,CAAX;AAEf,QAAI+J,GAAG,GAAG,KAAKpI,MAAL,CAAV;AACA,QAAI0L,GAAG,GAAG,CAAV;AACA,QAAIvN,CAAC,GAAG,CAAR;;AACA,WAAO,EAAEA,CAAF,GAAMT,UAAN,KAAqBgO,GAAG,IAAI,KAA5B,CAAP,EAA2C;AACzCtD,MAAAA,GAAG,IAAI,KAAKpI,MAAM,GAAG7B,CAAd,IAAmBuN,GAA1B;AACD;;AAED,WAAOtD,GAAP;AACD,GAdD;;AAgBAzG,EAAAA,MAAM,CAACW,SAAP,CAAiBqJ,UAAjB,GACAhK,MAAM,CAACW,SAAP,CAAiBsJ,UAAjB,GAA8B,SAASA,UAAT,CAAqB5L,MAArB,EAA6BtC,UAA7B,EAAyC+N,QAAzC,EAAmD;AAC/EzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACAtC,IAAAA,UAAU,GAAGA,UAAU,KAAK,CAA5B;;AACA,QAAI,CAAC+N,QAAL,EAAe;AACbJ,MAAAA,WAAW,CAACrL,MAAD,EAAStC,UAAT,EAAqB,KAAKW,MAA1B,CAAX;AACD;;AAED,QAAI+J,GAAG,GAAG,KAAKpI,MAAM,GAAG,EAAEtC,UAAhB,CAAV;AACA,QAAIgO,GAAG,GAAG,CAAV;;AACA,WAAOhO,UAAU,GAAG,CAAb,KAAmBgO,GAAG,IAAI,KAA1B,CAAP,EAAyC;AACvCtD,MAAAA,GAAG,IAAI,KAAKpI,MAAM,GAAG,EAAEtC,UAAhB,IAA8BgO,GAArC;AACD;;AAED,WAAOtD,GAAP;AACD,GAfD;;AAiBAzG,EAAAA,MAAM,CAACW,SAAP,CAAiBuJ,SAAjB,GACAlK,MAAM,CAACW,SAAP,CAAiBwJ,SAAjB,GAA6B,SAASA,SAAT,CAAoB9L,MAApB,EAA4ByL,QAA5B,EAAsC;AACjEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,WAAO,KAAK2B,MAAL,CAAP;AACD,GALD;;AAOA2B,EAAAA,MAAM,CAACW,SAAP,CAAiByJ,YAAjB,GACApK,MAAM,CAACW,SAAP,CAAiB0J,YAAjB,GAAgC,SAASA,YAAT,CAAuBhM,MAAvB,EAA+ByL,QAA/B,EAAyC;AACvEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,WAAO,KAAK2B,MAAL,IAAgB,KAAKA,MAAM,GAAG,CAAd,KAAoB,CAA3C;AACD,GALD;;AAOA2B,EAAAA,MAAM,CAACW,SAAP,CAAiB2J,YAAjB,GACAtK,MAAM,CAACW,SAAP,CAAiBsG,YAAjB,GAAgC,SAASA,YAAT,CAAuB5I,MAAvB,EAA+ByL,QAA/B,EAAyC;AACvEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,WAAQ,KAAK2B,MAAL,KAAgB,CAAjB,GAAsB,KAAKA,MAAM,GAAG,CAAd,CAA7B;AACD,GALD;;AAOA2B,EAAAA,MAAM,CAACW,SAAP,CAAiB4J,YAAjB,GACAvK,MAAM,CAACW,SAAP,CAAiB6J,YAAjB,GAAgC,SAASA,YAAT,CAAuBnM,MAAvB,EAA+ByL,QAA/B,EAAyC;AACvEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AAEf,WAAO,CAAE,KAAK2B,MAAL,CAAD,GACH,KAAKA,MAAM,GAAG,CAAd,KAAoB,CADjB,GAEH,KAAKA,MAAM,GAAG,CAAd,KAAoB,EAFlB,IAGF,KAAKA,MAAM,GAAG,CAAd,IAAmB,SAHxB;AAID,GATD;;AAWA2B,EAAAA,MAAM,CAACW,SAAP,CAAiB8J,YAAjB,GACAzK,MAAM,CAACW,SAAP,CAAiB+J,YAAjB,GAAgC,SAASA,YAAT,CAAuBrM,MAAvB,EAA+ByL,QAA/B,EAAyC;AACvEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AAEf,WAAQ,KAAK2B,MAAL,IAAe,SAAhB,IACH,KAAKA,MAAM,GAAG,CAAd,KAAoB,EAArB,GACA,KAAKA,MAAM,GAAG,CAAd,KAAoB,CADpB,GAED,KAAKA,MAAM,GAAG,CAAd,CAHK,CAAP;AAID,GATD;;AAWA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBgK,SAAjB,GAA6B,SAASA,SAAT,CAAoBtM,MAApB,EAA4BtC,UAA5B,EAAwC+N,QAAxC,EAAkD;AAC7EzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACAtC,IAAAA,UAAU,GAAGA,UAAU,KAAK,CAA5B;AACA,QAAI,CAAC+N,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAStC,UAAT,EAAqB,KAAKW,MAA1B,CAAX;AAEf,QAAI+J,GAAG,GAAG,KAAKpI,MAAL,CAAV;AACA,QAAI0L,GAAG,GAAG,CAAV;AACA,QAAIvN,CAAC,GAAG,CAAR;;AACA,WAAO,EAAEA,CAAF,GAAMT,UAAN,KAAqBgO,GAAG,IAAI,KAA5B,CAAP,EAA2C;AACzCtD,MAAAA,GAAG,IAAI,KAAKpI,MAAM,GAAG7B,CAAd,IAAmBuN,GAA1B;AACD;;AACDA,IAAAA,GAAG,IAAI,IAAP;AAEA,QAAItD,GAAG,IAAIsD,GAAX,EAAgBtD,GAAG,IAAItH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,IAAIrD,UAAhB,CAAP;AAEhB,WAAO0K,GAAP;AACD,GAhBD;;AAkBAzG,EAAAA,MAAM,CAACW,SAAP,CAAiBiK,SAAjB,GAA6B,SAASA,SAAT,CAAoBvM,MAApB,EAA4BtC,UAA5B,EAAwC+N,QAAxC,EAAkD;AAC7EzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACAtC,IAAAA,UAAU,GAAGA,UAAU,KAAK,CAA5B;AACA,QAAI,CAAC+N,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAStC,UAAT,EAAqB,KAAKW,MAA1B,CAAX;AAEf,QAAIF,CAAC,GAAGT,UAAR;AACA,QAAIgO,GAAG,GAAG,CAAV;AACA,QAAItD,GAAG,GAAG,KAAKpI,MAAM,GAAG,EAAE7B,CAAhB,CAAV;;AACA,WAAOA,CAAC,GAAG,CAAJ,KAAUuN,GAAG,IAAI,KAAjB,CAAP,EAAgC;AAC9BtD,MAAAA,GAAG,IAAI,KAAKpI,MAAM,GAAG,EAAE7B,CAAhB,IAAqBuN,GAA5B;AACD;;AACDA,IAAAA,GAAG,IAAI,IAAP;AAEA,QAAItD,GAAG,IAAIsD,GAAX,EAAgBtD,GAAG,IAAItH,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,IAAIrD,UAAhB,CAAP;AAEhB,WAAO0K,GAAP;AACD,GAhBD;;AAkBAzG,EAAAA,MAAM,CAACW,SAAP,CAAiBkK,QAAjB,GAA4B,SAASA,QAAT,CAAmBxM,MAAnB,EAA2ByL,QAA3B,EAAqC;AAC/DzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,QAAI,EAAE,KAAK2B,MAAL,IAAe,IAAjB,CAAJ,EAA4B,OAAQ,KAAKA,MAAL,CAAR;AAC5B,WAAQ,CAAC,OAAO,KAAKA,MAAL,CAAP,GAAsB,CAAvB,IAA4B,CAAC,CAArC;AACD,GALD;;AAOA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBmK,WAAjB,GAA+B,SAASA,WAAT,CAAsBzM,MAAtB,EAA8ByL,QAA9B,EAAwC;AACrEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,QAAI+J,GAAG,GAAG,KAAKpI,MAAL,IAAgB,KAAKA,MAAM,GAAG,CAAd,KAAoB,CAA9C;AACA,WAAQoI,GAAG,GAAG,MAAP,GAAiBA,GAAG,GAAG,UAAvB,GAAoCA,GAA3C;AACD,GALD;;AAOAzG,EAAAA,MAAM,CAACW,SAAP,CAAiBoK,WAAjB,GAA+B,SAASA,WAAT,CAAsB1M,MAAtB,EAA8ByL,QAA9B,EAAwC;AACrEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,QAAI+J,GAAG,GAAG,KAAKpI,MAAM,GAAG,CAAd,IAAoB,KAAKA,MAAL,KAAgB,CAA9C;AACA,WAAQoI,GAAG,GAAG,MAAP,GAAiBA,GAAG,GAAG,UAAvB,GAAoCA,GAA3C;AACD,GALD;;AAOAzG,EAAAA,MAAM,CAACW,SAAP,CAAiBqK,WAAjB,GAA+B,SAASA,WAAT,CAAsB3M,MAAtB,EAA8ByL,QAA9B,EAAwC;AACrEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AAEf,WAAQ,KAAK2B,MAAL,CAAD,GACJ,KAAKA,MAAM,GAAG,CAAd,KAAoB,CADhB,GAEJ,KAAKA,MAAM,GAAG,CAAd,KAAoB,EAFhB,GAGJ,KAAKA,MAAM,GAAG,CAAd,KAAoB,EAHvB;AAID,GARD;;AAUA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBsK,WAAjB,GAA+B,SAASA,WAAT,CAAsB5M,MAAtB,EAA8ByL,QAA9B,EAAwC;AACrEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AAEf,WAAQ,KAAK2B,MAAL,KAAgB,EAAjB,GACJ,KAAKA,MAAM,GAAG,CAAd,KAAoB,EADhB,GAEJ,KAAKA,MAAM,GAAG,CAAd,KAAoB,CAFhB,GAGJ,KAAKA,MAAM,GAAG,CAAd,CAHH;AAID,GARD;;AAUA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBuK,WAAjB,GAA+B,SAASA,WAAT,CAAsB7M,MAAtB,EAA8ByL,QAA9B,EAAwC;AACrEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,WAAOyO,OAAO,CAACnE,IAAR,CAAa,IAAb,EAAmB3I,MAAnB,EAA2B,IAA3B,EAAiC,EAAjC,EAAqC,CAArC,CAAP;AACD,GAJD;;AAMA2B,EAAAA,MAAM,CAACW,SAAP,CAAiByK,WAAjB,GAA+B,SAASA,WAAT,CAAsB/M,MAAtB,EAA8ByL,QAA9B,EAAwC;AACrEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,WAAOyO,OAAO,CAACnE,IAAR,CAAa,IAAb,EAAmB3I,MAAnB,EAA2B,KAA3B,EAAkC,EAAlC,EAAsC,CAAtC,CAAP;AACD,GAJD;;AAMA2B,EAAAA,MAAM,CAACW,SAAP,CAAiB0K,YAAjB,GAAgC,SAASA,YAAT,CAAuBhN,MAAvB,EAA+ByL,QAA/B,EAAyC;AACvEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,WAAOyO,OAAO,CAACnE,IAAR,CAAa,IAAb,EAAmB3I,MAAnB,EAA2B,IAA3B,EAAiC,EAAjC,EAAqC,CAArC,CAAP;AACD,GAJD;;AAMA2B,EAAAA,MAAM,CAACW,SAAP,CAAiB2K,YAAjB,GAAgC,SAASA,YAAT,CAAuBjN,MAAvB,EAA+ByL,QAA/B,EAAyC;AACvEzL,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeJ,WAAW,CAACrL,MAAD,EAAS,CAAT,EAAY,KAAK3B,MAAjB,CAAX;AACf,WAAOyO,OAAO,CAACnE,IAAR,CAAa,IAAb,EAAmB3I,MAAnB,EAA2B,KAA3B,EAAkC,EAAlC,EAAsC,CAAtC,CAAP;AACD,GAJD;;AAMA,WAASkN,QAAT,CAAmBnK,GAAnB,EAAwB/B,KAAxB,EAA+BhB,MAA/B,EAAuCsL,GAAvC,EAA4C5D,GAA5C,EAAiD9B,GAAjD,EAAsD;AACpD,QAAI,CAACjE,MAAM,CAACe,QAAP,CAAgBK,GAAhB,CAAL,EAA2B,MAAM,IAAIG,SAAJ,CAAc,6CAAd,CAAN;AAC3B,QAAIlC,KAAK,GAAG0G,GAAR,IAAe1G,KAAK,GAAG4E,GAA3B,EAAgC,MAAM,IAAI9C,UAAJ,CAAe,mCAAf,CAAN;AAChC,QAAI9C,MAAM,GAAGsL,GAAT,GAAevI,GAAG,CAAC1E,MAAvB,EAA+B,MAAM,IAAIyE,UAAJ,CAAe,oBAAf,CAAN;AAChC;;AAEDnB,EAAAA,MAAM,CAACW,SAAP,CAAiB6K,WAAjB,GACAxL,MAAM,CAACW,SAAP,CAAiB8K,WAAjB,GAA+B,SAASA,WAAT,CAAsBpM,KAAtB,EAA6BhB,MAA7B,EAAqCtC,UAArC,EAAiD+N,QAAjD,EAA2D;AACxFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACAtC,IAAAA,UAAU,GAAGA,UAAU,KAAK,CAA5B;;AACA,QAAI,CAAC+N,QAAL,EAAe;AACb,UAAI4B,QAAQ,GAAGvM,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,IAAIrD,UAAhB,IAA8B,CAA7C;AACAwP,MAAAA,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsBtC,UAAtB,EAAkC2P,QAAlC,EAA4C,CAA5C,CAAR;AACD;;AAED,QAAI3B,GAAG,GAAG,CAAV;AACA,QAAIvN,CAAC,GAAG,CAAR;AACA,SAAK6B,MAAL,IAAegB,KAAK,GAAG,IAAvB;;AACA,WAAO,EAAE7C,CAAF,GAAMT,UAAN,KAAqBgO,GAAG,IAAI,KAA5B,CAAP,EAA2C;AACzC,WAAK1L,MAAM,GAAG7B,CAAd,IAAoB6C,KAAK,GAAG0K,GAAT,GAAgB,IAAnC;AACD;;AAED,WAAO1L,MAAM,GAAGtC,UAAhB;AACD,GAlBD;;AAoBAiE,EAAAA,MAAM,CAACW,SAAP,CAAiBgL,WAAjB,GACA3L,MAAM,CAACW,SAAP,CAAiBiL,WAAjB,GAA+B,SAASA,WAAT,CAAsBvM,KAAtB,EAA6BhB,MAA7B,EAAqCtC,UAArC,EAAiD+N,QAAjD,EAA2D;AACxFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACAtC,IAAAA,UAAU,GAAGA,UAAU,KAAK,CAA5B;;AACA,QAAI,CAAC+N,QAAL,EAAe;AACb,UAAI4B,QAAQ,GAAGvM,IAAI,CAACC,GAAL,CAAS,CAAT,EAAY,IAAIrD,UAAhB,IAA8B,CAA7C;AACAwP,MAAAA,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsBtC,UAAtB,EAAkC2P,QAAlC,EAA4C,CAA5C,CAAR;AACD;;AAED,QAAIlP,CAAC,GAAGT,UAAU,GAAG,CAArB;AACA,QAAIgO,GAAG,GAAG,CAAV;AACA,SAAK1L,MAAM,GAAG7B,CAAd,IAAmB6C,KAAK,GAAG,IAA3B;;AACA,WAAO,EAAE7C,CAAF,IAAO,CAAP,KAAauN,GAAG,IAAI,KAApB,CAAP,EAAmC;AACjC,WAAK1L,MAAM,GAAG7B,CAAd,IAAoB6C,KAAK,GAAG0K,GAAT,GAAgB,IAAnC;AACD;;AAED,WAAO1L,MAAM,GAAGtC,UAAhB;AACD,GAlBD;;AAoBAiE,EAAAA,MAAM,CAACW,SAAP,CAAiBkL,UAAjB,GACA7L,MAAM,CAACW,SAAP,CAAiBmL,UAAjB,GAA8B,SAASA,UAAT,CAAqBzM,KAArB,EAA4BhB,MAA5B,EAAoCyL,QAApC,EAA8C;AAC1EzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,IAAzB,EAA+B,CAA/B,CAAR;AACf,SAAKA,MAAL,IAAgBgB,KAAK,GAAG,IAAxB;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GAPD;;AASA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBoL,aAAjB,GACA/L,MAAM,CAACW,SAAP,CAAiBqL,aAAjB,GAAiC,SAASA,aAAT,CAAwB3M,KAAxB,EAA+BhB,MAA/B,EAAuCyL,QAAvC,EAAiD;AAChFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,MAAzB,EAAiC,CAAjC,CAAR;AACf,SAAKA,MAAL,IAAgBgB,KAAK,GAAG,IAAxB;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,CAA9B;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GARD;;AAUA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBsL,aAAjB,GACAjM,MAAM,CAACW,SAAP,CAAiBuL,aAAjB,GAAiC,SAASA,aAAT,CAAwB7M,KAAxB,EAA+BhB,MAA/B,EAAuCyL,QAAvC,EAAiD;AAChFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,MAAzB,EAAiC,CAAjC,CAAR;AACf,SAAKA,MAAL,IAAgBgB,KAAK,KAAK,CAA1B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,GAAG,IAA5B;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GARD;;AAUA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBwL,aAAjB,GACAnM,MAAM,CAACW,SAAP,CAAiByL,aAAjB,GAAiC,SAASA,aAAT,CAAwB/M,KAAxB,EAA+BhB,MAA/B,EAAuCyL,QAAvC,EAAiD;AAChFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,UAAzB,EAAqC,CAArC,CAAR;AACf,SAAKA,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,EAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,EAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,CAA9B;AACA,SAAKhB,MAAL,IAAgBgB,KAAK,GAAG,IAAxB;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GAVD;;AAYA2B,EAAAA,MAAM,CAACW,SAAP,CAAiB0L,aAAjB,GACArM,MAAM,CAACW,SAAP,CAAiB2L,aAAjB,GAAiC,SAASA,aAAT,CAAwBjN,KAAxB,EAA+BhB,MAA/B,EAAuCyL,QAAvC,EAAiD;AAChFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,UAAzB,EAAqC,CAArC,CAAR;AACf,SAAKA,MAAL,IAAgBgB,KAAK,KAAK,EAA1B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,EAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,CAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,GAAG,IAA5B;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GAVD;;AAYA2B,EAAAA,MAAM,CAACW,SAAP,CAAiB4L,UAAjB,GAA8B,SAASA,UAAT,CAAqBlN,KAArB,EAA4BhB,MAA5B,EAAoCtC,UAApC,EAAgD+N,QAAhD,EAA0D;AACtFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;;AACA,QAAI,CAACyL,QAAL,EAAe;AACb,UAAI0C,KAAK,GAAGrN,IAAI,CAACC,GAAL,CAAS,CAAT,EAAa,IAAIrD,UAAL,GAAmB,CAA/B,CAAZ;AAEAwP,MAAAA,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsBtC,UAAtB,EAAkCyQ,KAAK,GAAG,CAA1C,EAA6C,CAACA,KAA9C,CAAR;AACD;;AAED,QAAIhQ,CAAC,GAAG,CAAR;AACA,QAAIuN,GAAG,GAAG,CAAV;AACA,QAAI0C,GAAG,GAAG,CAAV;AACA,SAAKpO,MAAL,IAAegB,KAAK,GAAG,IAAvB;;AACA,WAAO,EAAE7C,CAAF,GAAMT,UAAN,KAAqBgO,GAAG,IAAI,KAA5B,CAAP,EAA2C;AACzC,UAAI1K,KAAK,GAAG,CAAR,IAAaoN,GAAG,KAAK,CAArB,IAA0B,KAAKpO,MAAM,GAAG7B,CAAT,GAAa,CAAlB,MAAyB,CAAvD,EAA0D;AACxDiQ,QAAAA,GAAG,GAAG,CAAN;AACD;;AACD,WAAKpO,MAAM,GAAG7B,CAAd,IAAmB,CAAE6C,KAAK,GAAG0K,GAAT,IAAiB,CAAlB,IAAuB0C,GAAvB,GAA6B,IAAhD;AACD;;AAED,WAAOpO,MAAM,GAAGtC,UAAhB;AACD,GArBD;;AAuBAiE,EAAAA,MAAM,CAACW,SAAP,CAAiB+L,UAAjB,GAA8B,SAASA,UAAT,CAAqBrN,KAArB,EAA4BhB,MAA5B,EAAoCtC,UAApC,EAAgD+N,QAAhD,EAA0D;AACtFzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;;AACA,QAAI,CAACyL,QAAL,EAAe;AACb,UAAI0C,KAAK,GAAGrN,IAAI,CAACC,GAAL,CAAS,CAAT,EAAa,IAAIrD,UAAL,GAAmB,CAA/B,CAAZ;AAEAwP,MAAAA,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsBtC,UAAtB,EAAkCyQ,KAAK,GAAG,CAA1C,EAA6C,CAACA,KAA9C,CAAR;AACD;;AAED,QAAIhQ,CAAC,GAAGT,UAAU,GAAG,CAArB;AACA,QAAIgO,GAAG,GAAG,CAAV;AACA,QAAI0C,GAAG,GAAG,CAAV;AACA,SAAKpO,MAAM,GAAG7B,CAAd,IAAmB6C,KAAK,GAAG,IAA3B;;AACA,WAAO,EAAE7C,CAAF,IAAO,CAAP,KAAauN,GAAG,IAAI,KAApB,CAAP,EAAmC;AACjC,UAAI1K,KAAK,GAAG,CAAR,IAAaoN,GAAG,KAAK,CAArB,IAA0B,KAAKpO,MAAM,GAAG7B,CAAT,GAAa,CAAlB,MAAyB,CAAvD,EAA0D;AACxDiQ,QAAAA,GAAG,GAAG,CAAN;AACD;;AACD,WAAKpO,MAAM,GAAG7B,CAAd,IAAmB,CAAE6C,KAAK,GAAG0K,GAAT,IAAiB,CAAlB,IAAuB0C,GAAvB,GAA6B,IAAhD;AACD;;AAED,WAAOpO,MAAM,GAAGtC,UAAhB;AACD,GArBD;;AAuBAiE,EAAAA,MAAM,CAACW,SAAP,CAAiBgM,SAAjB,GAA6B,SAASA,SAAT,CAAoBtN,KAApB,EAA2BhB,MAA3B,EAAmCyL,QAAnC,EAA6C;AACxEzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,IAAzB,EAA+B,CAAC,IAAhC,CAAR;AACf,QAAIgB,KAAK,GAAG,CAAZ,EAAeA,KAAK,GAAG,OAAOA,KAAP,GAAe,CAAvB;AACf,SAAKhB,MAAL,IAAgBgB,KAAK,GAAG,IAAxB;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GAPD;;AASA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBiM,YAAjB,GAAgC,SAASA,YAAT,CAAuBvN,KAAvB,EAA8BhB,MAA9B,EAAsCyL,QAAtC,EAAgD;AAC9EzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,MAAzB,EAAiC,CAAC,MAAlC,CAAR;AACf,SAAKA,MAAL,IAAgBgB,KAAK,GAAG,IAAxB;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,CAA9B;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GAPD;;AASA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBkM,YAAjB,GAAgC,SAASA,YAAT,CAAuBxN,KAAvB,EAA8BhB,MAA9B,EAAsCyL,QAAtC,EAAgD;AAC9EzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,MAAzB,EAAiC,CAAC,MAAlC,CAAR;AACf,SAAKA,MAAL,IAAgBgB,KAAK,KAAK,CAA1B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,GAAG,IAA5B;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GAPD;;AASA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBmM,YAAjB,GAAgC,SAASA,YAAT,CAAuBzN,KAAvB,EAA8BhB,MAA9B,EAAsCyL,QAAtC,EAAgD;AAC9EzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,UAAzB,EAAqC,CAAC,UAAtC,CAAR;AACf,SAAKA,MAAL,IAAgBgB,KAAK,GAAG,IAAxB;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,CAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,EAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,EAA9B;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GATD;;AAWA2B,EAAAA,MAAM,CAACW,SAAP,CAAiBoM,YAAjB,GAAgC,SAASA,YAAT,CAAuB1N,KAAvB,EAA8BhB,MAA9B,EAAsCyL,QAAtC,EAAgD;AAC9EzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;AACA,QAAI,CAACyL,QAAL,EAAeyB,QAAQ,CAAC,IAAD,EAAOlM,KAAP,EAAchB,MAAd,EAAsB,CAAtB,EAAyB,UAAzB,EAAqC,CAAC,UAAtC,CAAR;AACf,QAAIgB,KAAK,GAAG,CAAZ,EAAeA,KAAK,GAAG,aAAaA,KAAb,GAAqB,CAA7B;AACf,SAAKhB,MAAL,IAAgBgB,KAAK,KAAK,EAA1B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,EAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,KAAK,CAA9B;AACA,SAAKhB,MAAM,GAAG,CAAd,IAAoBgB,KAAK,GAAG,IAA5B;AACA,WAAOhB,MAAM,GAAG,CAAhB;AACD,GAVD;;AAYA,WAAS2O,YAAT,CAAuB5L,GAAvB,EAA4B/B,KAA5B,EAAmChB,MAAnC,EAA2CsL,GAA3C,EAAgD5D,GAAhD,EAAqD9B,GAArD,EAA0D;AACxD,QAAI5F,MAAM,GAAGsL,GAAT,GAAevI,GAAG,CAAC1E,MAAvB,EAA+B,MAAM,IAAIyE,UAAJ,CAAe,oBAAf,CAAN;AAC/B,QAAI9C,MAAM,GAAG,CAAb,EAAgB,MAAM,IAAI8C,UAAJ,CAAe,oBAAf,CAAN;AACjB;;AAED,WAAS8L,UAAT,CAAqB7L,GAArB,EAA0B/B,KAA1B,EAAiChB,MAAjC,EAAyC6O,YAAzC,EAAuDpD,QAAvD,EAAiE;AAC/DzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;;AACA,QAAI,CAACyL,QAAL,EAAe;AACbkD,MAAAA,YAAY,CAAC5L,GAAD,EAAM/B,KAAN,EAAahB,MAAb,EAAqB,CAArB,CAAZ;AACD;;AACD8M,IAAAA,OAAO,CAACnI,KAAR,CAAc5B,GAAd,EAAmB/B,KAAnB,EAA0BhB,MAA1B,EAAkC6O,YAAlC,EAAgD,EAAhD,EAAoD,CAApD;AACA,WAAO7O,MAAM,GAAG,CAAhB;AACD;;AAED2B,EAAAA,MAAM,CAACW,SAAP,CAAiBwM,YAAjB,GAAgC,SAASA,YAAT,CAAuB9N,KAAvB,EAA8BhB,MAA9B,EAAsCyL,QAAtC,EAAgD;AAC9E,WAAOmD,UAAU,CAAC,IAAD,EAAO5N,KAAP,EAAchB,MAAd,EAAsB,IAAtB,EAA4ByL,QAA5B,CAAjB;AACD,GAFD;;AAIA9J,EAAAA,MAAM,CAACW,SAAP,CAAiByM,YAAjB,GAAgC,SAASA,YAAT,CAAuB/N,KAAvB,EAA8BhB,MAA9B,EAAsCyL,QAAtC,EAAgD;AAC9E,WAAOmD,UAAU,CAAC,IAAD,EAAO5N,KAAP,EAAchB,MAAd,EAAsB,KAAtB,EAA6ByL,QAA7B,CAAjB;AACD,GAFD;;AAIA,WAASuD,WAAT,CAAsBjM,GAAtB,EAA2B/B,KAA3B,EAAkChB,MAAlC,EAA0C6O,YAA1C,EAAwDpD,QAAxD,EAAkE;AAChEzK,IAAAA,KAAK,GAAG,CAACA,KAAT;AACAhB,IAAAA,MAAM,GAAGA,MAAM,KAAK,CAApB;;AACA,QAAI,CAACyL,QAAL,EAAe;AACbkD,MAAAA,YAAY,CAAC5L,GAAD,EAAM/B,KAAN,EAAahB,MAAb,EAAqB,CAArB,CAAZ;AACD;;AACD8M,IAAAA,OAAO,CAACnI,KAAR,CAAc5B,GAAd,EAAmB/B,KAAnB,EAA0BhB,MAA1B,EAAkC6O,YAAlC,EAAgD,EAAhD,EAAoD,CAApD;AACA,WAAO7O,MAAM,GAAG,CAAhB;AACD;;AAED2B,EAAAA,MAAM,CAACW,SAAP,CAAiB2M,aAAjB,GAAiC,SAASA,aAAT,CAAwBjO,KAAxB,EAA+BhB,MAA/B,EAAuCyL,QAAvC,EAAiD;AAChF,WAAOuD,WAAW,CAAC,IAAD,EAAOhO,KAAP,EAAchB,MAAd,EAAsB,IAAtB,EAA4ByL,QAA5B,CAAlB;AACD,GAFD;;AAIA9J,EAAAA,MAAM,CAACW,SAAP,CAAiB4M,aAAjB,GAAiC,SAASA,aAAT,CAAwBlO,KAAxB,EAA+BhB,MAA/B,EAAuCyL,QAAvC,EAAiD;AAChF,WAAOuD,WAAW,CAAC,IAAD,EAAOhO,KAAP,EAAchB,MAAd,EAAsB,KAAtB,EAA6ByL,QAA7B,CAAlB;AACD,GAFD;;;AAKA9J,EAAAA,MAAM,CAACW,SAAP,CAAiB0C,IAAjB,GAAwB,SAASA,IAAT,CAAe8C,MAAf,EAAuBqH,WAAvB,EAAoC7P,KAApC,EAA2CC,GAA3C,EAAgD;AACtE,QAAI,CAACoC,MAAM,CAACe,QAAP,CAAgBoF,MAAhB,CAAL,EAA8B,MAAM,IAAI5E,SAAJ,CAAc,6BAAd,CAAN;AAC9B,QAAI,CAAC5D,KAAL,EAAYA,KAAK,GAAG,CAAR;AACZ,QAAI,CAACC,GAAD,IAAQA,GAAG,KAAK,CAApB,EAAuBA,GAAG,GAAG,KAAKlB,MAAX;AACvB,QAAI8Q,WAAW,IAAIrH,MAAM,CAACzJ,MAA1B,EAAkC8Q,WAAW,GAAGrH,MAAM,CAACzJ,MAArB;AAClC,QAAI,CAAC8Q,WAAL,EAAkBA,WAAW,GAAG,CAAd;AAClB,QAAI5P,GAAG,GAAG,CAAN,IAAWA,GAAG,GAAGD,KAArB,EAA4BC,GAAG,GAAGD,KAAN,CAN0C;;AAStE,QAAIC,GAAG,KAAKD,KAAZ,EAAmB,OAAO,CAAP;AACnB,QAAIwI,MAAM,CAACzJ,MAAP,KAAkB,CAAlB,IAAuB,KAAKA,MAAL,KAAgB,CAA3C,EAA8C,OAAO,CAAP,CAVwB;;AAatE,QAAI8Q,WAAW,GAAG,CAAlB,EAAqB;AACnB,YAAM,IAAIrM,UAAJ,CAAe,2BAAf,CAAN;AACD;;AACD,QAAIxD,KAAK,GAAG,CAAR,IAAaA,KAAK,IAAI,KAAKjB,MAA/B,EAAuC,MAAM,IAAIyE,UAAJ,CAAe,oBAAf,CAAN;AACvC,QAAIvD,GAAG,GAAG,CAAV,EAAa,MAAM,IAAIuD,UAAJ,CAAe,yBAAf,CAAN,CAjByD;;AAoBtE,QAAIvD,GAAG,GAAG,KAAKlB,MAAf,EAAuBkB,GAAG,GAAG,KAAKlB,MAAX;;AACvB,QAAIyJ,MAAM,CAACzJ,MAAP,GAAgB8Q,WAAhB,GAA8B5P,GAAG,GAAGD,KAAxC,EAA+C;AAC7CC,MAAAA,GAAG,GAAGuI,MAAM,CAACzJ,MAAP,GAAgB8Q,WAAhB,GAA8B7P,KAApC;AACD;;AAED,QAAIlB,GAAG,GAAGmB,GAAG,GAAGD,KAAhB;;AAEA,QAAI,SAASwI,MAAT,IAAmB,OAAO9J,UAAU,CAACsE,SAAX,CAAqB8M,UAA5B,KAA2C,UAAlE,EAA8E;;AAE5E,WAAKA,UAAL,CAAgBD,WAAhB,EAA6B7P,KAA7B,EAAoCC,GAApC;AACD,KAHD,MAGO;AACLvB,MAAAA,UAAU,CAACsE,SAAX,CAAqB4D,GAArB,CAAyBC,IAAzB,CACE2B,MADF,EAEE,KAAKsD,QAAL,CAAc9L,KAAd,EAAqBC,GAArB,CAFF,EAGE4P,WAHF;AAKD;;AAED,WAAO/Q,GAAP;AACD,GAvCD;AA0CA;AACA;AACA;;;AACAuD,EAAAA,MAAM,CAACW,SAAP,CAAiB8B,IAAjB,GAAwB,SAASA,IAAT,CAAegE,GAAf,EAAoB9I,KAApB,EAA2BC,GAA3B,EAAgC8E,QAAhC,EAA0C;;AAEhE,QAAI,OAAO+D,GAAP,KAAe,QAAnB,EAA6B;AAC3B,UAAI,OAAO9I,KAAP,KAAiB,QAArB,EAA+B;AAC7B+E,QAAAA,QAAQ,GAAG/E,KAAX;AACAA,QAAAA,KAAK,GAAG,CAAR;AACAC,QAAAA,GAAG,GAAG,KAAKlB,MAAX;AACD,OAJD,MAIO,IAAI,OAAOkB,GAAP,KAAe,QAAnB,EAA6B;AAClC8E,QAAAA,QAAQ,GAAG9E,GAAX;AACAA,QAAAA,GAAG,GAAG,KAAKlB,MAAX;AACD;;AACD,UAAIgG,QAAQ,KAAK1B,SAAb,IAA0B,OAAO0B,QAAP,KAAoB,QAAlD,EAA4D;AAC1D,cAAM,IAAInB,SAAJ,CAAc,2BAAd,CAAN;AACD;;AACD,UAAI,OAAOmB,QAAP,KAAoB,QAApB,IAAgC,CAAC1C,MAAM,CAAC8C,UAAP,CAAkBJ,QAAlB,CAArC,EAAkE;AAChE,cAAM,IAAInB,SAAJ,CAAc,uBAAuBmB,QAArC,CAAN;AACD;;AACD,UAAI+D,GAAG,CAAC/J,MAAJ,KAAe,CAAnB,EAAsB;AACpB,YAAIH,IAAI,GAAGkK,GAAG,CAAC9J,UAAJ,CAAe,CAAf,CAAX;;AACA,YAAK+F,QAAQ,KAAK,MAAb,IAAuBnG,IAAI,GAAG,GAA/B,IACAmG,QAAQ,KAAK,QADjB,EAC2B;;AAEzB+D,UAAAA,GAAG,GAAGlK,IAAN;AACD;AACF;AACF,KAvBD,MAuBO,IAAI,OAAOkK,GAAP,KAAe,QAAnB,EAA6B;AAClCA,MAAAA,GAAG,GAAGA,GAAG,GAAG,GAAZ;AACD,KAFM,MAEA,IAAI,OAAOA,GAAP,KAAe,SAAnB,EAA8B;AACnCA,MAAAA,GAAG,GAAGc,MAAM,CAACd,GAAD,CAAZ;AACD,KA7B+D;;;AAgChE,QAAI9I,KAAK,GAAG,CAAR,IAAa,KAAKjB,MAAL,GAAciB,KAA3B,IAAoC,KAAKjB,MAAL,GAAckB,GAAtD,EAA2D;AACzD,YAAM,IAAIuD,UAAJ,CAAe,oBAAf,CAAN;AACD;;AAED,QAAIvD,GAAG,IAAID,KAAX,EAAkB;AAChB,aAAO,IAAP;AACD;;AAEDA,IAAAA,KAAK,GAAGA,KAAK,KAAK,CAAlB;AACAC,IAAAA,GAAG,GAAGA,GAAG,KAAKoD,SAAR,GAAoB,KAAKtE,MAAzB,GAAkCkB,GAAG,KAAK,CAAhD;AAEA,QAAI,CAAC6I,GAAL,EAAUA,GAAG,GAAG,CAAN;AAEV,QAAIjK,CAAJ;;AACA,QAAI,OAAOiK,GAAP,KAAe,QAAnB,EAA6B;AAC3B,WAAKjK,CAAC,GAAGmB,KAAT,EAAgBnB,CAAC,GAAGoB,GAApB,EAAyB,EAAEpB,CAA3B,EAA8B;AAC5B,aAAKA,CAAL,IAAUiK,GAAV;AACD;AACF,KAJD,MAIO;AACL,UAAI8C,KAAK,GAAGvJ,MAAM,CAACe,QAAP,CAAgB0F,GAAhB,IACRA,GADQ,GAERzG,MAAM,CAACyB,IAAP,CAAYgF,GAAZ,EAAiB/D,QAAjB,CAFJ;AAGA,UAAIjG,GAAG,GAAG8M,KAAK,CAAC7M,MAAhB;;AACA,UAAID,GAAG,KAAK,CAAZ,EAAe;AACb,cAAM,IAAI8E,SAAJ,CAAc,gBAAgBkF,GAAhB,GAClB,mCADI,CAAN;AAED;;AACD,WAAKjK,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAGoB,GAAG,GAAGD,KAAtB,EAA6B,EAAEnB,CAA/B,EAAkC;AAChC,aAAKA,CAAC,GAAGmB,KAAT,IAAkB4L,KAAK,CAAC/M,CAAC,GAAGC,GAAL,CAAvB;AACD;AACF;;AAED,WAAO,IAAP;AACD,GAjED;AAoEA;;;AAEA,MAAIiR,iBAAiB,GAAG,mBAAxB;;AAEA,WAASC,WAAT,CAAsB7H,GAAtB,EAA2B;;AAEzBA,IAAAA,GAAG,GAAGA,GAAG,CAAC8H,KAAJ,CAAU,GAAV,EAAe,CAAf,CAAN,CAFyB;;AAIzB9H,IAAAA,GAAG,GAAGA,GAAG,CAACI,IAAJ,GAAWD,OAAX,CAAmByH,iBAAnB,EAAsC,EAAtC,CAAN,CAJyB;;AAMzB,QAAI5H,GAAG,CAACpJ,MAAJ,GAAa,CAAjB,EAAoB,OAAO,EAAP,CANK;;AAQzB,WAAOoJ,GAAG,CAACpJ,MAAJ,GAAa,CAAb,KAAmB,CAA1B,EAA6B;AAC3BoJ,MAAAA,GAAG,GAAGA,GAAG,GAAG,GAAZ;AACD;;AACD,WAAOA,GAAP;AACD;;AAED,WAASlB,WAAT,CAAsB/B,MAAtB,EAA8BgL,KAA9B,EAAqC;AACnCA,IAAAA,KAAK,GAAGA,KAAK,IAAI3O,QAAjB;AACA,QAAIwJ,SAAJ;AACA,QAAIhM,MAAM,GAAGmG,MAAM,CAACnG,MAApB;AACA,QAAIoR,aAAa,GAAG,IAApB;AACA,QAAIvE,KAAK,GAAG,EAAZ;;AAEA,SAAK,IAAI/M,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGE,MAApB,EAA4B,EAAEF,CAA9B,EAAiC;AAC/BkM,MAAAA,SAAS,GAAG7F,MAAM,CAAClG,UAAP,CAAkBH,CAAlB,CAAZ,CAD+B;;AAI/B,UAAIkM,SAAS,GAAG,MAAZ,IAAsBA,SAAS,GAAG,MAAtC,EAA8C;;AAE5C,YAAI,CAACoF,aAAL,EAAoB;;AAElB,cAAIpF,SAAS,GAAG,MAAhB,EAAwB;;AAEtB,gBAAI,CAACmF,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBtE,KAAK,CAACzL,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;AACvB;AACD,WAJD,MAIO,IAAItB,CAAC,GAAG,CAAJ,KAAUE,MAAd,EAAsB;;AAE3B,gBAAI,CAACmR,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBtE,KAAK,CAACzL,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;AACvB;AACD,WAViB;;;AAalBgQ,UAAAA,aAAa,GAAGpF,SAAhB;AAEA;AACD,SAlB2C;;;AAqB5C,YAAIA,SAAS,GAAG,MAAhB,EAAwB;AACtB,cAAI,CAACmF,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBtE,KAAK,CAACzL,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;AACvBgQ,UAAAA,aAAa,GAAGpF,SAAhB;AACA;AACD,SAzB2C;;;AA4B5CA,QAAAA,SAAS,GAAG,CAACoF,aAAa,GAAG,MAAhB,IAA0B,EAA1B,GAA+BpF,SAAS,GAAG,MAA5C,IAAsD,OAAlE;AACD,OA7BD,MA6BO,IAAIoF,aAAJ,EAAmB;;AAExB,YAAI,CAACD,KAAK,IAAI,CAAV,IAAe,CAAC,CAApB,EAAuBtE,KAAK,CAACzL,IAAN,CAAW,IAAX,EAAiB,IAAjB,EAAuB,IAAvB;AACxB;;AAEDgQ,MAAAA,aAAa,GAAG,IAAhB,CAtC+B;;AAyC/B,UAAIpF,SAAS,GAAG,IAAhB,EAAsB;AACpB,YAAI,CAACmF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;AACtBtE,QAAAA,KAAK,CAACzL,IAAN,CAAW4K,SAAX;AACD,OAHD,MAGO,IAAIA,SAAS,GAAG,KAAhB,EAAuB;AAC5B,YAAI,CAACmF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;AACtBtE,QAAAA,KAAK,CAACzL,IAAN,CACE4K,SAAS,IAAI,GAAb,GAAmB,IADrB,EAEEA,SAAS,GAAG,IAAZ,GAAmB,IAFrB;AAID,OANM,MAMA,IAAIA,SAAS,GAAG,OAAhB,EAAyB;AAC9B,YAAI,CAACmF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;AACtBtE,QAAAA,KAAK,CAACzL,IAAN,CACE4K,SAAS,IAAI,GAAb,GAAmB,IADrB,EAEEA,SAAS,IAAI,GAAb,GAAmB,IAAnB,GAA0B,IAF5B,EAGEA,SAAS,GAAG,IAAZ,GAAmB,IAHrB;AAKD,OAPM,MAOA,IAAIA,SAAS,GAAG,QAAhB,EAA0B;AAC/B,YAAI,CAACmF,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;AACtBtE,QAAAA,KAAK,CAACzL,IAAN,CACE4K,SAAS,IAAI,IAAb,GAAoB,IADtB,EAEEA,SAAS,IAAI,GAAb,GAAmB,IAAnB,GAA0B,IAF5B,EAGEA,SAAS,IAAI,GAAb,GAAmB,IAAnB,GAA0B,IAH5B,EAIEA,SAAS,GAAG,IAAZ,GAAmB,IAJrB;AAMD,OARM,MAQA;AACL,cAAM,IAAI5L,KAAJ,CAAU,oBAAV,CAAN;AACD;AACF;;AAED,WAAOyM,KAAP;AACD;;AAED,WAASvB,YAAT,CAAuBlC,GAAvB,EAA4B;AAC1B,QAAIiI,SAAS,GAAG,EAAhB;;AACA,SAAK,IAAIvR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsJ,GAAG,CAACpJ,MAAxB,EAAgC,EAAEF,CAAlC,EAAqC;;AAEnCuR,MAAAA,SAAS,CAACjQ,IAAV,CAAegI,GAAG,CAACnJ,UAAJ,CAAeH,CAAf,IAAoB,IAAnC;AACD;;AACD,WAAOuR,SAAP;AACD;;AAED,WAAS5F,cAAT,CAAyBrC,GAAzB,EAA8B+H,KAA9B,EAAqC;AACnC,QAAIvO,CAAJ,EAAO0O,EAAP,EAAWC,EAAX;AACA,QAAIF,SAAS,GAAG,EAAhB;;AACA,SAAK,IAAIvR,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGsJ,GAAG,CAACpJ,MAAxB,EAAgC,EAAEF,CAAlC,EAAqC;AACnC,UAAI,CAACqR,KAAK,IAAI,CAAV,IAAe,CAAnB,EAAsB;AAEtBvO,MAAAA,CAAC,GAAGwG,GAAG,CAACnJ,UAAJ,CAAeH,CAAf,CAAJ;AACAwR,MAAAA,EAAE,GAAG1O,CAAC,IAAI,CAAV;AACA2O,MAAAA,EAAE,GAAG3O,CAAC,GAAG,GAAT;AACAyO,MAAAA,SAAS,CAACjQ,IAAV,CAAemQ,EAAf;AACAF,MAAAA,SAAS,CAACjQ,IAAV,CAAekQ,EAAf;AACD;;AAED,WAAOD,SAAP;AACD;;AAED,WAASlJ,aAAT,CAAwBiB,GAAxB,EAA6B;AAC3B,WAAOyC,QAAM,CAACvM,WAAP,CAAmB2R,WAAW,CAAC7H,GAAD,CAA9B,CAAP;AACD;;AAED,WAASgC,UAAT,CAAqBoG,GAArB,EAA0BC,GAA1B,EAA+B9P,MAA/B,EAAuC3B,MAAvC,EAA+C;AAC7C,SAAK,IAAIF,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGE,MAApB,EAA4B,EAAEF,CAA9B,EAAiC;AAC/B,UAAKA,CAAC,GAAG6B,MAAJ,IAAc8P,GAAG,CAACzR,MAAnB,IAA+BF,CAAC,IAAI0R,GAAG,CAACxR,MAA5C,EAAqD;AACrDyR,MAAAA,GAAG,CAAC3R,CAAC,GAAG6B,MAAL,CAAH,GAAkB6P,GAAG,CAAC1R,CAAD,CAArB;AACD;;AACD,WAAOA,CAAP;AACD;AAGD;AACA;;;AACA,WAASuF,UAAT,CAAqBuB,GAArB,EAA0BE,IAA1B,EAAgC;AAC9B,WAAOF,GAAG,YAAYE,IAAf,IACJF,GAAG,IAAI,IAAP,IAAeA,GAAG,CAAC8K,WAAJ,IAAmB,IAAlC,IAA0C9K,GAAG,CAAC8K,WAAJ,CAAgBC,IAAhB,IAAwB,IAAlE,IACC/K,GAAG,CAAC8K,WAAJ,CAAgBC,IAAhB,KAAyB7K,IAAI,CAAC6K,IAFlC;AAGD;;AACD,WAAS9K,WAAT,CAAsBD,GAAtB,EAA2B;;AAEzB,WAAOA,GAAG,KAAKA,GAAf,CAFyB;AAG1B;AAGD;;;AACA,MAAIgG,mBAAmB,GAAI,YAAY;AACrC,QAAIgF,QAAQ,GAAG,kBAAf;AACA,QAAIC,KAAK,GAAG,IAAIjS,KAAJ,CAAU,GAAV,CAAZ;;AACA,SAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwB,EAAEA,CAA1B,EAA6B;AAC3B,UAAIgS,GAAG,GAAGhS,CAAC,GAAG,EAAd;;AACA,WAAK,IAAI4K,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,EAApB,EAAwB,EAAEA,CAA1B,EAA6B;AAC3BmH,QAAAA,KAAK,CAACC,GAAG,GAAGpH,CAAP,CAAL,GAAiBkH,QAAQ,CAAC9R,CAAD,CAAR,GAAc8R,QAAQ,CAAClH,CAAD,CAAvC;AACD;AACF;;AACD,WAAOmH,KAAP;AACD,GAVyB,EAA1B;;;;;;;AC9wDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA;AAEA,IAAIE,cAAa,GAAG,uBAAS1P,CAAT,EAAYoD,CAAZ,EAAe;AAC/BsM,EAAAA,cAAa,GAAGhO,MAAM,CAACC,cAAP,IACX;AAAEgO,IAAAA,SAAS,EAAE;AAAb,eAA6BpS,KAA7B,IAAsC,UAAUyC,CAAV,EAAaoD,CAAb,EAAgB;AAAEpD,IAAAA,CAAC,CAAC2P,SAAF,GAAcvM,CAAd;AAAkB,GAD/D,IAEZ,UAAUpD,CAAV,EAAaoD,CAAb,EAAgB;AAAE,SAAK,IAAIwM,CAAT,IAAcxM,CAAd;AAAiB,UAAIA,CAAC,CAACyM,cAAF,CAAiBD,CAAjB,CAAJ,EAAyB5P,CAAC,CAAC4P,CAAD,CAAD,GAAOxM,CAAC,CAACwM,CAAD,CAAR;AAA1C;AAAwD,GAF9E;;AAGA,SAAOF,cAAa,CAAC1P,CAAD,EAAIoD,CAAJ,CAApB;AACH,CALD;;AAOO,SAAS0M,SAAT,CAAmB9P,CAAnB,EAAsBoD,CAAtB,EAAyB;AAC5BsM,EAAAA,cAAa,CAAC1P,CAAD,EAAIoD,CAAJ,CAAb;;AACA,WAAS2M,EAAT,GAAc;AAAE,SAAKV,WAAL,GAAmBrP,CAAnB;AAAuB;;AACvCA,EAAAA,CAAC,CAAC4B,SAAF,GAAcwB,CAAC,KAAK,IAAN,GAAa1B,MAAM,CAACsO,MAAP,CAAc5M,CAAd,CAAb,IAAiC2M,EAAE,CAACnO,SAAH,GAAewB,CAAC,CAACxB,SAAjB,EAA4B,IAAImO,EAAJ,EAA7D,CAAd;AACH;;AAEM,IAAIE,OAAQ,GAAG,oBAAW;AAC7BA,EAAAA,OAAQ,GAAGvO,MAAM,CAACwO,MAAP,IAAiB,SAASD,QAAT,CAAkBE,CAAlB,EAAqB;AAC7C,SAAK,IAAIlQ,CAAJ,EAAOxC,CAAC,GAAG,CAAX,EAAc8I,CAAC,GAAGZ,SAAS,CAAChI,MAAjC,EAAyCF,CAAC,GAAG8I,CAA7C,EAAgD9I,CAAC,EAAjD,EAAqD;AACjDwC,MAAAA,CAAC,GAAG0F,SAAS,CAAClI,CAAD,CAAb;;AACA,WAAK,IAAImS,CAAT,IAAc3P,CAAd;AAAiB,YAAIyB,MAAM,CAACE,SAAP,CAAiBiO,cAAjB,CAAgCpK,IAAhC,CAAqCxF,CAArC,EAAwC2P,CAAxC,CAAJ,EAAgDO,CAAC,CAACP,CAAD,CAAD,GAAO3P,CAAC,CAAC2P,CAAD,CAAR;AAAjE;AACH;;AACD,WAAOO,CAAP;AACH,GAND;;AAOA,SAAOF,OAAQ,CAACtJ,KAAT,CAAe,IAAf,EAAqBhB,SAArB,CAAP;AACH,CATM;;AC7BP;;IAC+B,6BAAK;IAClC,mBAAY,OAAe;QAA3B,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;;KAClD;IAED,sBAAI,2BAAI;aAAR;YACE,OAAO,WAAW,CAAC;SACpB;;;OAAA;IACH,gBAAC;AAAD,CATA,CAA+B,KAAK,GASnC;AAED;;IACmC,iCAAS;IAC1C,uBAAY,OAAe;QAA3B,YACE,kBAAM,OAAO,CAAC,SAEf;QADC,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;;KACtD;IAED,sBAAI,+BAAI;aAAR;YACE,OAAO,eAAe,CAAC;SACxB;;;OAAA;IACH,oBAAC;AAAD,CATA,CAAmC,SAAS;;ACP5C,SAAS,YAAY,CAAC,eAAoB;;IAExC,OAAO,eAAe,IAAI,eAAe,CAAC,IAAI,IAAI,IAAI,IAAI,eAAe,CAAC;AAC5E,CAAC;AAED;SACgB,SAAS;IACvB,QACE,YAAY,CAAC,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC;QAC1D,YAAY,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC;QAClD,YAAY,CAAC,OAAO,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC;QAC9C,YAAY,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,MAAM,CAAC;;QAElD,QAAQ,CAAC,aAAa,CAAC,EAAE,EACzB;AACJ;;AChBA;;;;SAIgB,wBAAwB,CAAC,EAAY;IACnD,OAAO,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,YAAY,CAAC,CAAC;AAC1D,CAAC;AAED,SAAS,aAAa;IACpB,IAAM,CAAC,GAAG,SAAS,EAAwC,CAAC;IAC5D,OAAO,OAAO,CAAC,CAAC,SAAS,KAAK,QAAQ,IAAI,CAAC,CAAC,SAAS,CAAC,OAAO,KAAK,aAAa,CAAC;AAClF,CAAC;AAED,IAAM,mBAAmB,GAAwB,SAAS,mBAAmB,CAAC,IAAY;IACxF,IAAM,eAAe,GAAG,aAAa,EAAE;UACnC,0IAA0I;UAC1I,+GAA+G,CAAC;IACpH,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;IAE9B,IAAM,MAAM,GAAG1E,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,EAAE,CAAC;QAAE,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC;IAC3E,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AAWF,IAAM,iBAAiB,GAAG;IACH;QACnB,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;;YAEjC,IAAM,QAAM,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC;YAChD,IAAI,QAAM,IAAI,QAAM,CAAC,eAAe,EAAE;gBACpC,OAAO,UAAA,IAAI,IAAI,OAAA,QAAM,CAAC,eAAe,CAACA,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAA,CAAC;aAC3D;SACF;QAED,IAAI,OAAO,MAAM,KAAK,WAAW,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,CAAC,eAAe,EAAE;;YAEnF,OAAO,UAAA,IAAI,IAAI,OAAA,MAAM,CAAC,MAAM,CAAC,eAAe,CAACA,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,GAAA,CAAC;SAClE;QAED,OAAO,mBAAmB,CAAC;KAY5B;AACH,CAAC,CAAC;AAEK,IAAM,WAAW,GAAG,iBAAiB,EAAE,CAAC;SAE/B,gBAAgB,CAAC,KAAc;IAC7C,OAAO,CAAC,sBAAsB,EAAE,4BAA4B,CAAC,CAAC,QAAQ,CACpE,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CACtC,CAAC;AACJ,CAAC;SAEe,YAAY,CAAC,KAAc;IACzC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,qBAAqB,CAAC;AACzE,CAAC;SAEe,eAAe,CAAC,KAAc;IAC5C,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,wBAAwB,CAAC;AAC5E,CAAC;SAEe,gBAAgB,CAAC,KAAc;IAC7C,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,yBAAyB,CAAC;AAC7E,CAAC;SAEe,QAAQ,CAAC,CAAU;IACjC,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,iBAAiB,CAAC;AACjE,CAAC;SAEe,KAAK,CAAC,CAAU;IAC9B,OAAO,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,cAAc,CAAC;AAC9D,CAAC;AAOD;SACgB,MAAM,CAAC,CAAU;IAC/B,OAAO,YAAY,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,eAAe,CAAC;AAClF,CAAC;AAED;;;;;SAKgB,YAAY,CAAC,SAAkB;IAC7C,OAAO,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,KAAK,IAAI,CAAC;AAC7D,CAAC;SAGe,SAAS,CAAqB,EAAK,EAAE,OAAe;IAClE,IAAI,MAAM,GAAG,KAAK,CAAC;IACnB,SAAS,UAAU;QAAgB,cAAkB;aAAlB,UAAkB,EAAlB,qBAAkB,EAAlB,IAAkB;YAAlB,yBAAkB;;QACnD,IAAI,CAAC,MAAM,EAAE;YACX,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACtB,MAAM,GAAG,IAAI,CAAC;SACf;QACD,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KAC7B;IACD,OAAO,UAA0B,CAAC;AACpC;;AC1HA;;;;;;;;SAQgB,YAAY,CAAC,eAAuD;IAClF,IAAI,WAAW,CAAC,MAAM,CAAC,eAAe,CAAC,EAAE;QACvC,OAAOA,QAAM,CAAC,IAAI,CAChB,eAAe,CAAC,MAAM,EACtB,eAAe,CAAC,UAAU,EAC1B,eAAe,CAAC,UAAU,CAC3B,CAAC;KACH;IAED,IAAI,gBAAgB,CAAC,eAAe,CAAC,EAAE;QACrC,OAAOA,QAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;KACrC;IAED,MAAM,IAAI,aAAa,CAAC,sCAAsC,CAAC,CAAC;AAClE;;ACvBA;AACA,IAAM,gBAAgB,GACpB,uHAAuH,CAAC;AAEnH,IAAM,kBAAkB,GAAG,UAAC,GAAW;IAC5C,OAAA,OAAO,GAAG,KAAK,QAAQ,IAAI,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC;AAArD,CAAqD,CAAC;AAEjD,IAAM,qBAAqB,GAAG,UAAC,SAAiB;IACrD,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE;QAClC,MAAM,IAAI,aAAa,CACrB,uLAAuL,CACxL,CAAC;KACH;IAED,IAAM,kBAAkB,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IACvD,OAAOA,QAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAChD,CAAC,CAAC;AAEK,IAAM,qBAAqB,GAAG,UAAC,MAAc,EAAE,aAAoB;IAApB,8BAAA,EAAA,oBAAoB;IACxE,OAAA,aAAa;UACT,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B,GAAG;YACH,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B,GAAG;YACH,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;YAC5B,GAAG;YACH,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,GAAG;YACH,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,CAAC;UAC9B,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;AAV1B,CAU0B;;AChC5B;IACamP,gBAAc,GAAG,WAAW;AACzC;IACaC,gBAAc,GAAG,CAAC,WAAW;AAC1C;IACaC,gBAAc,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE;AAClD;IACaC,gBAAc,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE;AAE/C;;;;AAIO,IAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAE1C;;;;AAIO,IAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAE3C;IACa,gBAAgB,GAAG,EAAE;AAElC;IACa,gBAAgB,GAAG,EAAE;AAElC;IACa,gBAAgB,GAAG,EAAE;AAElC;IACa,eAAe,GAAG,EAAE;AAEjC;IACa,gBAAgB,GAAG,EAAE;AAElC;IACa,mBAAmB,GAAG,EAAE;AAErC;IACa,aAAa,GAAG,EAAE;AAE/B;IACa,iBAAiB,GAAG,EAAE;AAEnC;IACa,cAAc,GAAG,EAAE;AAEhC;IACa,cAAc,GAAG,GAAG;AAEjC;IACa,gBAAgB,GAAG,GAAG;AAEnC;IACa,mBAAmB,GAAG,GAAG;AAEtC;IACa,cAAc,GAAG,GAAG;AAEjC;IACa,gBAAgB,GAAG,GAAG;AAEnC;IACa,sBAAsB,GAAG,GAAG;AAEzC;IACa,aAAa,GAAG,GAAG;AAEhC;IACa,mBAAmB,GAAG,GAAG;AAEtC;IACa,cAAc,GAAG,GAAG;AAEjC;IACa,oBAAoB,GAAG,GAAG;AAEvC;IACa,iBAAiB,GAAG,KAAK;AAEtC;IACa,iBAAiB,GAAG,KAAK;AAEtC;IACa,2BAA2B,GAAG,EAAE;AAE7C;IACa,4BAA4B,GAAG,EAAE;AAE9C;IACa,8BAA8B,GAAG,EAAE;AAEhD;IACa,wBAAwB,GAAG,EAAE;AAE1C;IACa,4BAA4B,GAAG,EAAE;AAE9C;IACa,uBAAuB,GAAG,EAAE;AAEzC;IACa,6BAA6B,GAAG,EAAE;AAE/C;IACa,0BAA0B,GAAG,EAAE;AAE5C;IACa,gCAAgC,GAAG;;ACpFhD;;;;;;;;;;;;;;;;;IAkDE,gBAAY,MAAgC,EAAE,OAAgB;QAC5D,IAAI,EAAE,IAAI,YAAY,MAAM,CAAC;YAAE,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAElE,IACE,EAAE,MAAM,IAAI,IAAI,CAAC;YACjB,EAAE,OAAO,MAAM,KAAK,QAAQ,CAAC;YAC7B,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;YAC3B,EAAE,MAAM,YAAY,WAAW,CAAC;YAChC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EACtB;YACA,MAAM,IAAI,aAAa,CACrB,kFAAkF,CACnF,CAAC;SACH;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,MAAM,CAAC,2BAA2B,CAAC;QAE9D,IAAI,MAAM,IAAI,IAAI,EAAE;;YAElB,IAAI,CAAC,MAAM,GAAGtP,QAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;SACnB;aAAM;YACL,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;;gBAE9B,IAAI,CAAC,MAAM,GAAGA,QAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;aAC7C;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;gBAEhC,IAAI,CAAC,MAAM,GAAGA,QAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACnC;iBAAM;;gBAEL,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;aACpC;YAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;SACxC;KACF;;;;;;IAOD,oBAAG,GAAH,UAAI,SAA2D;;QAE7D,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3D,MAAM,IAAI,aAAa,CAAC,sCAAsC,CAAC,CAAC;SACjE;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAChE,MAAM,IAAI,aAAa,CAAC,mDAAmD,CAAC,CAAC;;QAG/E,IAAI,WAAmB,CAAC;QACxB,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACvC;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACxC,WAAW,GAAG,SAAS,CAAC;SACzB;aAAM;YACL,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SAC5B;QAED,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,GAAG,EAAE;YACxC,MAAM,IAAI,aAAa,CAAC,0DAA0D,CAAC,CAAC;SACrF;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,WAAW,CAAC;SAC5C;aAAM;YACL,IAAM,MAAM,GAAGA,QAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;;YAErE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,WAAW,CAAC;SAC5C;KACF;;;;;;;IAQD,sBAAK,GAAL,UAAM,QAAiC,EAAE,MAAc;QACrD,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;;QAG7D,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE;YACjD,IAAM,MAAM,GAAGA,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;YAClE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;;YAGnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;QAED,IAAI,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ;gBACX,MAAM,GAAG,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;SAC3F;aAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ;gBACX,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;SACvF;KACF;;;;;;;IAQD,qBAAI,GAAJ,UAAK,QAAgB,EAAE,MAAc;QACnC,MAAM,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;;QAGvD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC;KACvD;;;;;;;IAQD,sBAAK,GAAL,UAAM,KAAe;QACnB,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;;QAGhB,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;YACjD,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;;QAGD,IAAI,KAAK,EAAE;YACT,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC5C;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACzD;;IAGD,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;KACtB;IAED,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;KACvC;IAED,yBAAQ,GAAR,UAAS,MAAe;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;KACrC;;IAGD,+BAAc,GAAd,UAAe,OAAsB;QACnC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpD,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO;gBACL,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,OAAO;aACtD,CAAC;SACH;QACD,OAAO;YACL,OAAO,EAAE;gBACP,MAAM,EAAE,YAAY;gBACpB,OAAO,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC,GAAG,GAAG,GAAG,OAAO,GAAG,OAAO;aACxD;SACF,CAAC;KACH;IAED,uBAAM,GAAN;QACE,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,YAAY,EAAE;YACzC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;SACtD;QAED,MAAM,IAAI,SAAS,CACjB,4BAAoB,IAAI,CAAC,QAAQ,gEAAoD,MAAM,CAAC,YAAY,+BAA2B,CACpI,CAAC;KACH;;IAGM,uBAAgB,GAAvB,UACE,GAAyD,EACzD,OAAsB;QAEtB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,IAAwB,CAAC;QAC7B,IAAI,IAAI,CAAC;QACT,IAAI,SAAS,IAAI,GAAG,EAAE;YACpB,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAI,GAAG,EAAE;gBACvE,IAAI,GAAG,GAAG,CAAC,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;gBAC/C,IAAI,GAAGA,QAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;aAC3C;iBAAM;gBACL,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,EAAE;oBACnC,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC;oBACnE,IAAI,GAAGA,QAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;iBAClD;aACF;SACF;aAAM,IAAI,OAAO,IAAI,GAAG,EAAE;YACzB,IAAI,GAAG,CAAC,CAAC;YACT,IAAI,GAAG,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,aAAa,CAAC,iDAA0C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;SAC1F;QACD,OAAO,IAAI,KAAK,4BAA4B,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACxF;;IAGD,iBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,wBAAO,GAAP;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,mCAA2B,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAAc,IAAI,CAAC,QAAQ,MAAG,CAAC;KAC1F;;;;;IA9PuB,kCAA2B,GAAG,CAAC,CAAC;;IAGxC,kBAAW,GAAG,GAAG,CAAC;;IAElB,sBAAe,GAAG,CAAC,CAAC;;IAEpB,uBAAgB,GAAG,CAAC,CAAC;;IAErB,yBAAkB,GAAG,CAAC,CAAC;;IAEvB,uBAAgB,GAAG,CAAC,CAAC;;IAErB,mBAAY,GAAG,CAAC,CAAC;;IAEjB,kBAAW,GAAG,CAAC,CAAC;;IAEhB,wBAAiB,GAAG,CAAC,CAAC;;IAEtB,qBAAc,GAAG,CAAC,CAAC;;IAEnB,2BAAoB,GAAG,GAAG,CAAC;IA0O7C,aAAC;CAtQD,IAsQC;AAED,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAM1E,IAAM,gBAAgB,GAAG,EAAE,CAAC;AAE5B;;;;;IAI0B,wBAAM;;;;;;IAW9B,cAAY,KAA8B;QAA1C,iBAmBC;QAlBC,IAAI,KAAK,CAAC;QACV,IAAI,MAAM,CAAC;QACX,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;SACzB;aAAM,IAAI,KAAK,YAAY,IAAI,EAAE;YAChC,KAAK,GAAGA,QAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;SACrB;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,KAAK,gBAAgB,EAAE;YAC7E,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;SAC7B;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,KAAK,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;SACtC;aAAM;YACL,MAAM,IAAI,aAAa,CACrB,gLAAgL,CACjL,CAAC;SACH;gBACD,kBAAM,KAAK,EAAE,4BAA4B,CAAC;QAC1C,KAAI,CAAC,IAAI,GAAG,MAAM,CAAC;;KACpB;IAMD,sBAAI,oBAAE;;;;;aAAN;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;aAED,UAAO,KAAa;YAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YAEpB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC,KAAK,CAAC,CAAC;aAC1C;SACF;;;OARA;;;;;IAcD,0BAAW,GAAX,UAAY,aAAoB;QAApB,8BAAA,EAAA,oBAAoB;QAC9B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE;YACpC,OAAO,IAAI,CAAC,IAAI,CAAC;SAClB;QAED,IAAM,aAAa,GAAG,qBAAqB,CAAC,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QAEpE,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;SAC3B;QAED,OAAO,aAAa,CAAC;KACtB;;;;IAKD,uBAAQ,GAAR,UAAS,QAAiB;QACxB,OAAO,QAAQ,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;KACnE;;;;;IAMD,qBAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;KAC3B;;;;;;IAOD,qBAAM,GAAN,UAAO,OAA+B;QACpC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,YAAY,IAAI,EAAE;YAC3B,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACnC;QAED,IAAI;YACF,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7C;QAAC,WAAM;YACN,OAAO,KAAK,CAAC;SACd;KACF;;;;IAKD,uBAAQ,GAAR;QACE,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;KACjD;;;;IAKM,aAAQ,GAAf;QACE,IAAM,KAAK,GAAG,WAAW,CAAC,gBAAgB,CAAC,CAAC;;;QAI5C,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC;QACpC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC;QAEpC,OAAOA,QAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC3B;;;;;IAMM,YAAO,GAAd,UAAe,KAA6B;QAC1C,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,KAAK,CAAC;SACd;QAED,IAAI,KAAK,YAAY,IAAI,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAClC;QAED,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;;YAEvB,IAAI,KAAK,CAAC,MAAM,KAAK,gBAAgB,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,IAAI,CAAC;SACjE;QAED,OAAO,KAAK,CAAC;KACd;;;;;IAMM,wBAAmB,GAA1B,UAA2B,SAAiB;QAC1C,IAAM,MAAM,GAAG,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAChD,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;KACzB;;;;;;;IAQD,eAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,sBAAO,GAAP;QACE,OAAO,qBAAa,IAAI,CAAC,WAAW,EAAE,QAAI,CAAC;KAC5C;IACH,WAAC;AAAD,CA9KA,CAA0B,MAAM;;AC1ShC;;;;;;;;;;IAcE,cAAY,IAAuB,EAAE,KAAgB;QACnD,IAAI,EAAE,IAAI,YAAY,IAAI,CAAC;YAAE,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;QAE1D,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACpB;IAED,qBAAM,GAAN;QACE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;KAC/C;;IAGD,6BAAc,GAAd;QACE,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;SACjD;QAED,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;KAC7B;;IAGM,qBAAgB,GAAvB,UAAwB,GAAiB;QACvC,OAAO,IAAI,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;KACxC;;IAGD,eAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,sBAAO,GAAP;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/B,OAAO,qBAAa,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,eACvC,QAAQ,CAAC,KAAK,GAAG,YAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAE,GAAG,EAAE,MAC1D,CAAC;KACL;IACH,WAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;;AChDrE;SACgB,WAAW,CAAC,KAAc;IACxC,QACE,YAAY,CAAC,KAAK,CAAC;QACnB,KAAK,CAAC,GAAG,IAAI,IAAI;QACjB,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;SAC7B,KAAK,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,KAAK,CAAC,GAAG,KAAK,QAAQ,CAAC,EACpD;AACJ,CAAC;AAED;;;;;;;;;;;IAkBE,eAAY,UAAkB,EAAE,GAAa,EAAE,EAAW,EAAE,MAAiB;QAC3E,IAAI,EAAE,IAAI,YAAY,KAAK,CAAC;YAAE,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,CAAC,CAAC;;QAG5E,IAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;YACtB,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;;YAEnB,UAAU,GAAG,KAAK,CAAC,KAAK,EAAG,CAAC;SAC7B;QAED,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;QACb,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;KAC5B;IAMD,sBAAI,4BAAS;;;;aAAb;YACE,OAAO,IAAI,CAAC,UAAU,CAAC;SACxB;aAED,UAAc,KAAa;YACzB,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC;SACzB;;;OAJA;IAMD,sBAAM,GAAN;QACE,IAAM,CAAC,GAAG,MAAM,CAAC,MAAM,CACrB;YACE,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,EACD,IAAI,CAAC,MAAM,CACZ,CAAC;QAEF,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI;YAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QACrC,OAAO,CAAC,CAAC;KACV;;IAGD,8BAAc,GAAd,UAAe,OAAsB;QACnC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,GAAc;YACjB,IAAI,EAAE,IAAI,CAAC,UAAU;YACrB,GAAG,EAAE,IAAI,CAAC,GAAG;SACd,CAAC;QAEF,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,CAAC,CAAC;SACV;QAED,IAAI,IAAI,CAAC,EAAE;YAAE,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,CAAC;QAC7B,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAClC,OAAO,CAAC,CAAC;KACV;;IAGM,sBAAgB,GAAvB,UAAwB,GAAc;QACpC,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAuB,CAAC;QAC1D,OAAO,IAAI,CAAC,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO,IAAI,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KACpD;;IAGD,gBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,uBAAO,GAAP;;QAEE,IAAM,GAAG,GACP,IAAI,CAAC,GAAG,KAAK,SAAS,IAAI,IAAI,CAAC,GAAG,CAAC,QAAQ,KAAK,SAAS,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;QAC7F,OAAO,sBAAc,IAAI,CAAC,SAAS,gCAAoB,MAAM,CAAC,GAAG,CAAC,gBAChE,IAAI,CAAC,EAAE,GAAG,cAAM,IAAI,CAAC,EAAE,OAAG,GAAG,EAAE,MAC9B,CAAC;KACL;IACH,YAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;;AC/EvE;;;AAGA,IAAI,IAAI,GAAgC,SAAS,CAAC;AAMlD,IAAI;IACF,IAAI,GAAG,IAAI,WAAW,CAAC,QAAQ,CAC7B,IAAI,WAAW,CAAC,MAAM;;IAEpB,IAAI,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,CAC/oC,EACD,EAAE,CACH,CAAC,OAAqC,CAAC;CACzC;AAAC,WAAM;;CAEP;AAED,IAAM,cAAc,GAAG,CAAC,IAAI,EAAE,CAAC;AAC/B,IAAM,cAAc,GAAG,CAAC,IAAI,EAAE,CAAC;AAC/B,IAAM,cAAc,GAAG,cAAc,GAAG,cAAc,CAAC;AACvD,IAAM,cAAc,GAAG,cAAc,GAAG,cAAc,CAAC;AACvD,IAAM,cAAc,GAAG,cAAc,GAAG,CAAC,CAAC;AAE1C;AACA,IAAM,SAAS,GAA4B,EAAE,CAAC;AAE9C;AACA,IAAM,UAAU,GAA4B,EAAE,CAAC;AAO/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAqDE,cAAY,GAAiC,EAAE,IAAuB,EAAE,QAAkB;QAA9E,oBAAA,EAAA,OAAiC;QAC3C,IAAI,EAAE,IAAI,YAAY,IAAI,CAAC;YAAE,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QAElE,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SACnD;aAAM,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAClC,MAAM,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;SACnD;aAAM;YACL,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC,CAAC;YACnB,IAAI,CAAC,IAAI,GAAI,IAAe,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;SAC5B;QAED,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE;YACxC,KAAK,EAAE,IAAI;YACX,YAAY,EAAE,KAAK;YACnB,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;KACJ;;;;;;;;;IA6BM,aAAQ,GAAf,UAAgB,OAAe,EAAE,QAAgB,EAAE,QAAkB;QACnE,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;KAC9C;;;;;;;IAQM,YAAO,GAAd,UAAe,KAAa,EAAE,QAAkB;QAC9C,IAAI,GAAG,EAAE,SAAS,EAAE,KAAK,CAAC;QAC1B,IAAI,QAAQ,EAAE;YACZ,KAAK,MAAM,CAAC,CAAC;YACb,KAAK,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;gBACvC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC;gBAC9B,IAAI,SAAS;oBAAE,OAAO,SAAS,CAAC;aACjC;YACD,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;YAC3D,IAAI,KAAK;gBAAE,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YACnC,OAAO,GAAG,CAAC;SACZ;aAAM;YACL,KAAK,IAAI,CAAC,CAAC;YACX,KAAK,KAAK,GAAG,CAAC,GAAG,IAAI,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;gBAC1C,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;gBAC7B,IAAI,SAAS;oBAAE,OAAO,SAAS,CAAC;aACjC;YACD,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;YACtD,IAAI,KAAK;gBAAE,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC;YAClC,OAAO,GAAG,CAAC;SACZ;KACF;;;;;;;IAQM,eAAU,GAAjB,UAAkB,KAAa,EAAE,QAAkB;QACjD,IAAI,KAAK,CAAC,KAAK,CAAC;YAAE,OAAO,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QAC3D,IAAI,QAAQ,EAAE;YACZ,IAAI,KAAK,GAAG,CAAC;gBAAE,OAAO,IAAI,CAAC,KAAK,CAAC;YACjC,IAAI,KAAK,IAAI,cAAc;gBAAE,OAAO,IAAI,CAAC,kBAAkB,CAAC;SAC7D;aAAM;YACL,IAAI,KAAK,IAAI,CAAC,cAAc;gBAAE,OAAO,IAAI,CAAC,SAAS,CAAC;YACpD,IAAI,KAAK,GAAG,CAAC,IAAI,cAAc;gBAAE,OAAO,IAAI,CAAC,SAAS,CAAC;SACxD;QACD,IAAI,KAAK,GAAG,CAAC;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,GAAG,EAAE,CAAC;QAC9D,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,cAAc,GAAG,CAAC,EAAE,CAAC,KAAK,GAAG,cAAc,IAAI,CAAC,EAAE,QAAQ,CAAC,CAAC;KAC1F;;;;;;;IAQM,eAAU,GAAjB,UAAkB,KAAa,EAAE,QAAkB;QACjD,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,QAAQ,CAAC,CAAC;KACpD;;;;;;;;IASM,eAAU,GAAjB,UAAkB,GAAW,EAAE,QAAkB,EAAE,KAAc;QAC/D,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;YAAE,MAAM,KAAK,CAAC,cAAc,CAAC,CAAC;QAClD,IAAI,GAAG,KAAK,KAAK,IAAI,GAAG,KAAK,UAAU,IAAI,GAAG,KAAK,WAAW,IAAI,GAAG,KAAK,WAAW;YACnF,OAAO,IAAI,CAAC,IAAI,CAAC;QACnB,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;;YAEhC,CAAC,KAAK,GAAG,QAAQ,IAAI,QAAQ,GAAG,KAAK,CAAC,CAAC;SACxC;aAAM;YACL,QAAQ,GAAG,CAAC,CAAC,QAAQ,CAAC;SACvB;QACD,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;QACpB,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK;YAAE,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC;QAEvD,IAAI,CAAC,CAAC;QACN,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,MAAM,KAAK,CAAC,iBAAiB,CAAC,CAAC;aAC1D,IAAI,CAAC,KAAK,CAAC,EAAE;YAChB,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC;SACjE;;;QAID,IAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;QAEzD,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;QACvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;YACtC,IAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,EACtC,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;YACtD,IAAI,IAAI,GAAG,CAAC,EAAE;gBACZ,IAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;gBACrD,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;aACxD;iBAAM;gBACL,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAClC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;aAC7C;SACF;QACD,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC3B,OAAO,MAAM,CAAC;KACf;;;;;;;;IASM,cAAS,GAAhB,UAAiB,KAAe,EAAE,QAAkB,EAAE,EAAY;QAChE,OAAO,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;KACnF;;;;;;;IAQM,gBAAW,GAAlB,UAAmB,KAAe,EAAE,QAAkB;QACpD,OAAO,IAAI,IAAI,CACb,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAChE,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,EAChE,QAAQ,CACT,CAAC;KACH;;;;;;;IAQM,gBAAW,GAAlB,UAAmB,KAAe,EAAE,QAAkB;QACpD,OAAO,IAAI,IAAI,CACb,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAChE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAChE,QAAQ,CACT,CAAC;KACH;;;;IAKM,WAAM,GAAb,UAAc,KAAc;QAC1B,OAAO,YAAY,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,IAAI,CAAC;KAC5D;;;;;IAMM,cAAS,GAAhB,UACE,GAAwE,EACxE,QAAkB;QAElB,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QACnE,IAAI,OAAO,GAAG,KAAK,QAAQ;YAAE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;;QAEnE,OAAO,IAAI,CAAC,QAAQ,CAClB,GAAG,CAAC,GAAG,EACP,GAAG,CAAC,IAAI,EACR,OAAO,QAAQ,KAAK,SAAS,GAAG,QAAQ,GAAG,GAAG,CAAC,QAAQ,CACxD,CAAC;KACH;;IAGD,kBAAG,GAAH,UAAI,MAA0C;QAC5C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAAE,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;;QAI1D,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC;QAC7B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC;QAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;QAE9B,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,KAAK,EAAE,CAAC;QAC/B,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC;QACjC,IAAM,GAAG,GAAG,MAAM,CAAC,GAAG,KAAK,EAAE,CAAC;QAC9B,IAAM,GAAG,GAAG,MAAM,CAAC,GAAG,GAAG,MAAM,CAAC;QAEhC,IAAI,GAAG,GAAG,CAAC,EACT,GAAG,GAAG,CAAC,EACP,GAAG,GAAG,CAAC,EACP,GAAG,GAAG,CAAC,CAAC;QACV,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,MAAM,CAAC;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC3E;;;;;IAMD,kBAAG,GAAH,UAAI,KAAyC;QAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACnF;;;;;IAMD,sBAAO,GAAP,UAAQ,KAAyC;QAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAC7B,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,EAAE,EAC/B,QAAQ,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;QAChC,IAAI,OAAO,IAAI,CAAC,QAAQ;YAAE,OAAO,CAAC,CAAC,CAAC;QACpC,IAAI,CAAC,OAAO,IAAI,QAAQ;YAAE,OAAO,CAAC,CAAC;;QAEnC,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;QAEjE,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC;aACtC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,IAAI,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;cAC5D,CAAC,CAAC;cACF,CAAC,CAAC;KACP;;IAGD,mBAAI,GAAJ,UAAK,KAAyC;QAC5C,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;KAC5B;;;;;IAMD,qBAAM,GAAN,UAAO,OAA2C;QAChD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC7D,IAAI,OAAO,CAAC,MAAM,EAAE;YAAE,MAAM,KAAK,CAAC,kBAAkB,CAAC,CAAC;;QAGtD,IAAI,IAAI,EAAE;;;;YAIR,IACE,CAAC,IAAI,CAAC,QAAQ;gBACd,IAAI,CAAC,IAAI,KAAK,CAAC,UAAU;gBACzB,OAAO,CAAC,GAAG,KAAK,CAAC,CAAC;gBAClB,OAAO,CAAC,IAAI,KAAK,CAAC,CAAC,EACnB;;gBAEA,OAAO,IAAI,CAAC;aACb;YACD,IAAM,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAClD,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,IAAI,EACT,OAAO,CAAC,GAAG,EACX,OAAO,CAAC,IAAI,CACb,CAAC;YACF,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3D;QAED,IAAI,IAAI,CAAC,MAAM,EAAE;YAAE,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;QACjE,IAAI,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QACrB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;;;YAGlB,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBAC3B,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;oBAAE,OAAO,IAAI,CAAC,SAAS,CAAC;;qBAEvE,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;oBAAE,OAAO,IAAI,CAAC,GAAG,CAAC;qBAChD;;oBAEH,IAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBAC7B,MAAM,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;oBACtC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;wBACxB,OAAO,OAAO,CAAC,UAAU,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC;qBACvD;yBAAM;wBACL,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;wBACpC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;wBACnC,OAAO,GAAG,CAAC;qBACZ;iBACF;aACF;iBAAM,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;gBAAE,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC;YACrF,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;gBACrB,IAAI,OAAO,CAAC,UAAU,EAAE;oBAAE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/D,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;aACtC;iBAAM,IAAI,OAAO,CAAC,UAAU,EAAE;gBAAE,OAAO,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;YACtE,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC;SACjB;aAAM;;;YAGL,IAAI,CAAC,OAAO,CAAC,QAAQ;gBAAE,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;YACtD,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC;gBAAE,OAAO,IAAI,CAAC,KAAK,CAAC;YACxC,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;;gBAE1B,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;SAClB;;;;;;;QAQD,GAAG,GAAG,IAAI,CAAC;QACX,OAAO,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;;;YAGvB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;;;YAItE,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;YACpD,IAAM,KAAK,GAAG,IAAI,IAAI,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;;;YAGtD,IAAI,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;YACxC,IAAI,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACvC,OAAO,SAAS,CAAC,UAAU,EAAE,IAAI,SAAS,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE;gBAClD,MAAM,IAAI,KAAK,CAAC;gBAChB,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACnD,SAAS,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;aACpC;;;YAID,IAAI,SAAS,CAAC,MAAM,EAAE;gBAAE,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC;YAE7C,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACzB,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;SAC1B;QACD,OAAO,GAAG,CAAC;KACZ;;IAGD,kBAAG,GAAH,UAAI,OAA2C;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC7B;;;;;IAMD,qBAAM,GAAN,UAAO,KAAyC;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,EAAE,KAAK,CAAC;YACvF,OAAO,KAAK,CAAC;QACf,OAAO,IAAI,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC;KAC3D;;IAGD,iBAAE,GAAF,UAAG,KAAyC;QAC1C,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC3B;;IAGD,0BAAW,GAAX;QACE,OAAO,IAAI,CAAC,IAAI,CAAC;KAClB;;IAGD,kCAAmB,GAAnB;QACE,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC;KACxB;;IAGD,yBAAU,GAAV;QACE,OAAO,IAAI,CAAC,GAAG,CAAC;KACjB;;IAGD,iCAAkB,GAAlB;QACE,OAAO,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;KACvB;;IAGD,4BAAa,GAAb;QACE,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;YAErB,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,aAAa,EAAE,CAAC;SAClE;QACD,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;QACnD,IAAI,GAAW,CAAC;QAChB,KAAK,GAAG,GAAG,EAAE,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,EAAE;YAAE,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,CAAC;gBAAE,MAAM;QACnE,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,GAAG,GAAG,CAAC,CAAC;KAC7C;;IAGD,0BAAW,GAAX,UAAY,KAAyC;QACnD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC7B;;IAGD,iBAAE,GAAF,UAAG,KAAyC;QAC1C,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;KAChC;;IAGD,iCAAkB,GAAlB,UAAmB,KAAyC;QAC1D,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC9B;;IAGD,kBAAG,GAAH,UAAI,KAAyC;QAC3C,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;KACvC;;IAED,iBAAE,GAAF,UAAG,KAAyC;QAC1C,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;KACvC;;IAGD,qBAAM,GAAN;QACE,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;KAC7B;;IAGD,yBAAU,GAAV;QACE,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;KACxC;;IAGD,oBAAK,GAAL;QACE,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,CAAC;KAC7B;;IAGD,yBAAU,GAAV;QACE,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;KACxC;;IAGD,qBAAM,GAAN;QACE,OAAO,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;KAC1C;;IAGD,uBAAQ,GAAR,UAAS,KAAyC;QAChD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC7B;;IAGD,iBAAE,GAAF,UAAG,KAAyC;QAC1C,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KAC7B;;IAGD,8BAAe,GAAf,UAAgB,KAAyC;QACvD,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC9B;;IAGD,kBAAG,GAAH,UAAI,KAAyC;QAC3C,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;KACpC;;IAGD,qBAAM,GAAN,UAAO,OAA2C;QAChD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;;QAG7D,IAAI,IAAI,EAAE;YACR,IAAM,GAAG,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,EAClD,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,IAAI,EACT,OAAO,CAAC,GAAG,EACX,OAAO,CAAC,IAAI,CACb,CAAC;YACF,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3D;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC;KACjD;;IAGD,kBAAG,GAAH,UAAI,OAA2C;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC7B;;IAED,kBAAG,GAAH,UAAI,OAA2C;QAC7C,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC7B;;;;;;IAOD,uBAAQ,GAAR,UAAS,UAA8C;QACrD,IAAI,IAAI,CAAC,MAAM,EAAE;YAAE,OAAO,IAAI,CAAC,IAAI,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YAAE,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;;QAGtE,IAAI,IAAI,EAAE;YACR,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;YAC3E,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC3D;QAED,IAAI,UAAU,CAAC,MAAM,EAAE;YAAE,OAAO,IAAI,CAAC,IAAI,CAAC;QAC1C,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,UAAU,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;QACpF,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,IAAI,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC;QAEpF,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,IAAI,UAAU,CAAC,UAAU,EAAE;gBAAE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;;gBAChE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,EAAE,CAAC;SAC9C;aAAM,IAAI,UAAU,CAAC,UAAU,EAAE;YAAE,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC;;QAG5E,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC;YAC5D,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,UAAU,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;;QAKjF,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC;QAC7B,IAAM,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,MAAM,CAAC;QAC/B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC;QAC5B,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;QAE9B,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,KAAK,EAAE,CAAC;QACnC,IAAM,GAAG,GAAG,UAAU,CAAC,IAAI,GAAG,MAAM,CAAC;QACrC,IAAM,GAAG,GAAG,UAAU,CAAC,GAAG,KAAK,EAAE,CAAC;QAClC,IAAM,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,MAAM,CAAC;QAEpC,IAAI,GAAG,GAAG,CAAC,EACT,GAAG,GAAG,CAAC,EACP,GAAG,GAAG,CAAC,EACP,GAAG,GAAG,CAAC,CAAC;QACV,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;QACjB,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;QAClB,GAAG,IAAI,MAAM,CAAC;QACd,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;QACrD,GAAG,IAAI,MAAM,CAAC;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,GAAG,IAAI,EAAE,IAAI,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC3E;;IAGD,kBAAG,GAAH,UAAI,UAA8C;QAChD,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAClC;;IAGD,qBAAM,GAAN;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC;YAAE,OAAO,IAAI,CAAC,SAAS,CAAC;QACrE,OAAO,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;KACjC;;IAGD,kBAAG,GAAH;QACE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;KACtB;;IAGD,kBAAG,GAAH;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KAC5D;;IAGD,wBAAS,GAAT,UAAU,KAAyC;QACjD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KAC5B;;IAGD,kBAAG,GAAH,UAAI,KAAyC;QAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KAC9B;;IAED,iBAAE,GAAF,UAAG,KAAyC;QAC1C,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;KAC9B;;;;IAKD,iBAAE,GAAF,UAAG,KAA6B;QAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACnF;;;;;;IAOD,wBAAS,GAAT,UAAU,OAAsB;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QACpD,IAAI,CAAC,OAAO,IAAI,EAAE,MAAM,CAAC;YAAE,OAAO,IAAI,CAAC;aAClC,IAAI,OAAO,GAAG,EAAE;YACnB,OAAO,IAAI,CAAC,QAAQ,CAClB,IAAI,CAAC,GAAG,IAAI,OAAO,EACnB,CAAC,IAAI,CAAC,IAAI,IAAI,OAAO,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,EACtD,IAAI,CAAC,QAAQ,CACd,CAAC;;YACC,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,OAAO,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACzE;;IAGD,kBAAG,GAAH,UAAI,OAAsB;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;KAChC;;;;;;IAOD,yBAAU,GAAV,UAAW,OAAsB;QAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QACpD,IAAI,CAAC,OAAO,IAAI,EAAE,MAAM,CAAC;YAAE,OAAO,IAAI,CAAC;aAClC,IAAI,OAAO,GAAG,EAAE;YACnB,OAAO,IAAI,CAAC,QAAQ,CAClB,CAAC,IAAI,CAAC,GAAG,KAAK,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,EAAE,GAAG,OAAO,CAAC,CAAC,EACtD,IAAI,CAAC,IAAI,IAAI,OAAO,EACpB,IAAI,CAAC,QAAQ,CACd,CAAC;;YACC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KAChG;;IAGD,kBAAG,GAAH,UAAI,OAAsB;QACxB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;KACjC;;;;;;IAOD,iCAAkB,GAAlB,UAAmB,OAAsB;QACvC,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;YAAE,OAAO,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;QACpD,OAAO,IAAI,EAAE,CAAC;QACd,IAAI,OAAO,KAAK,CAAC;YAAE,OAAO,IAAI,CAAC;aAC1B;YACH,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;YACvB,IAAI,OAAO,GAAG,EAAE,EAAE;gBAChB,IAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;gBACrB,OAAO,IAAI,CAAC,QAAQ,CAClB,CAAC,GAAG,KAAK,OAAO,KAAK,IAAI,KAAK,EAAE,GAAG,OAAO,CAAC,CAAC,EAC5C,IAAI,KAAK,OAAO,EAChB,IAAI,CAAC,QAAQ,CACd,CAAC;aACH;iBAAM,IAAI,OAAO,KAAK,EAAE;gBAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;gBACnE,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACtE;KACF;;IAGD,oBAAK,GAAL,UAAM,OAAsB;QAC1B,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;KACzC;;IAED,mBAAI,GAAJ,UAAK,OAAsB;QACzB,OAAO,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC;KACzC;;;;;;IAOD,uBAAQ,GAAR,UAAS,UAA8C;QACrD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;YAAE,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;QACtE,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC;KACnC;;IAGD,kBAAG,GAAH,UAAI,UAA8C;QAChD,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;KAClC;;IAGD,oBAAK,GAAL;QACE,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC;KAClD;;IAGD,uBAAQ,GAAR;QACE,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,cAAc,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAChF,OAAO,IAAI,CAAC,IAAI,GAAG,cAAc,IAAI,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;KACtD;;IAGD,uBAAQ,GAAR;QACE,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;KAChC;;;;;;IAOD,sBAAO,GAAP,UAAQ,EAAY;QAClB,OAAO,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;KACjD;;;;;IAMD,wBAAS,GAAT;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,EAClB,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO;YACL,EAAE,GAAG,IAAI;YACT,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI;YACjB,CAAC,EAAE,KAAK,EAAE,IAAI,IAAI;YAClB,EAAE,KAAK,EAAE;YACT,EAAE,GAAG,IAAI;YACT,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI;YACjB,CAAC,EAAE,KAAK,EAAE,IAAI,IAAI;YAClB,EAAE,KAAK,EAAE;SACV,CAAC;KACH;;;;;IAMD,wBAAS,GAAT;QACE,IAAM,EAAE,GAAG,IAAI,CAAC,IAAI,EAClB,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO;YACL,EAAE,KAAK,EAAE;YACT,CAAC,EAAE,KAAK,EAAE,IAAI,IAAI;YAClB,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI;YACjB,EAAE,GAAG,IAAI;YACT,EAAE,KAAK,EAAE;YACT,CAAC,EAAE,KAAK,EAAE,IAAI,IAAI;YAClB,CAAC,EAAE,KAAK,CAAC,IAAI,IAAI;YACjB,EAAE,GAAG,IAAI;SACV,CAAC;KACH;;;;IAKD,uBAAQ,GAAR;QACE,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAChC,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;KAClD;;;;;;IAOD,uBAAQ,GAAR,UAAS,KAAc;QACrB,KAAK,GAAG,KAAK,IAAI,EAAE,CAAC;QACpB,IAAI,KAAK,GAAG,CAAC,IAAI,EAAE,GAAG,KAAK;YAAE,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC;QACvD,IAAI,IAAI,CAAC,MAAM,EAAE;YAAE,OAAO,GAAG,CAAC;QAC9B,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;;YAErB,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;;;gBAG3B,IAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EACtC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,EACzB,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBACtC,OAAO,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aAC3D;;gBAAM,OAAO,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SAChD;;;QAID,IAAM,YAAY,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;;QAExE,IAAI,GAAG,GAAS,IAAI,CAAC;QACrB,IAAI,MAAM,GAAG,EAAE,CAAC;;QAEhB,OAAO,IAAI,EAAE;YACX,IAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YACrC,IAAM,MAAM,GAAG,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACpC,GAAG,GAAG,MAAM,CAAC;YACb,IAAI,GAAG,CAAC,MAAM,EAAE,EAAE;gBAChB,OAAO,MAAM,GAAG,MAAM,CAAC;aACxB;iBAAM;gBACL,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC;oBAAE,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;gBAChD,MAAM,GAAG,EAAE,GAAG,MAAM,GAAG,MAAM,CAAC;aAC/B;SACF;KACF;;IAGD,yBAAU,GAAV;QACE,IAAI,IAAI,CAAC,QAAQ;YAAE,OAAO,IAAI,CAAC;QAC/B,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;KACjD;;IAGD,kBAAG,GAAH,UAAI,KAA6B;QAC/B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;YAAE,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;QACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;KACnF;;IAGD,kBAAG,GAAH;QACE,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC;KACtB;;IAGD,iBAAE,GAAF,UAAG,KAAyC;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;KACpC;;;;;;IAOD,6BAAc,GAAd,UAAe,OAAsB;QACnC,IAAI,OAAO,IAAI,OAAO,CAAC,OAAO;YAAE,OAAO,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvD,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;KACzC;IACM,qBAAgB,GAAvB,UAAwB,GAA4B,EAAE,OAAsB;QAC1E,IAAM,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;QAChD,OAAO,OAAO,IAAI,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC;KAChE;;IAGD,eAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,sBAAO,GAAP;QACE,OAAO,qBAAa,IAAI,CAAC,QAAQ,EAAE,eAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG,EAAE,MAAG,CAAC;KACzE;IA/2BM,eAAU,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;;IAG1C,uBAAkB,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC,CAAC;;IAEzE,SAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;IAEvB,UAAK,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;IAE9B,QAAG,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;;IAEtB,SAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;;IAE7B,YAAO,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE3B,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;;IAEjE,cAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,UAAU,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC;IA+1B7D,WAAC;CAv6BD,IAu6BC;AAED,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AACrE,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC;;AC1gCrE,IAAM,mBAAmB,GAAG,+CAA+C,CAAC;AAC5E,IAAM,gBAAgB,GAAG,0BAA0B,CAAC;AACpD,IAAM,gBAAgB,GAAG,eAAe,CAAC;AAEzC,IAAM,YAAY,GAAG,IAAI,CAAC;AAC1B,IAAM,YAAY,GAAG,CAAC,IAAI,CAAC;AAC3B,IAAM,aAAa,GAAG,IAAI,CAAC;AAC3B,IAAM,UAAU,GAAG,EAAE,CAAC;AAEtB;AACA,IAAM,UAAU,GAAG;IACjB,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;CAC/F,CAAC,OAAO,EAAE,CAAC;AACZ;AACA,IAAM,mBAAmB,GAAG;IAC1B,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;CAC/F,CAAC,OAAO,EAAE,CAAC;AACZ,IAAM,mBAAmB,GAAG;IAC1B,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;CAC/F,CAAC,OAAO,EAAE,CAAC;AAEZ,IAAM,cAAc,GAAG,iBAAiB,CAAC;AAEzC;AACA,IAAM,gBAAgB,GAAG,IAAI,CAAC;AAC9B;AACA,IAAM,aAAa,GAAG,MAAM,CAAC;AAC7B;AACA,IAAM,oBAAoB,GAAG,EAAE,CAAC;AAChC;AACA,IAAM,eAAe,GAAG,EAAE,CAAC;AAE3B;AACA,SAAS,OAAO,CAAC,KAAa;IAC5B,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;AACrC,CAAC;AAED;AACA,SAAS,UAAU,CAAC,KAAkD;IACpE,IAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC;IACpD,IAAI,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;IAE9B,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;QAC5E,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;KACvC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;;QAE3B,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC;;QAE1B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC7C,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC;QACvC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;KAC7B;IAED,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;AACxC,CAAC;AAED;AACA,SAAS,YAAY,CAAC,IAAU,EAAE,KAAW;IAC3C,IAAI,CAAC,IAAI,IAAI,CAAC,KAAK,EAAE;QACnB,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;KAC9D;IAED,IAAM,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;IAC7C,IAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;IAC/C,IAAM,SAAS,GAAG,KAAK,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC;IAC/C,IAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC;IAEjD,IAAI,WAAW,GAAG,QAAQ,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC/C,IAAI,UAAU,GAAG,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAC7C,IAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;IAChD,IAAI,UAAU,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IAE5C,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,UAAU,GAAG,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;SAC9C,GAAG,CAAC,WAAW,CAAC;SAChB,GAAG,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IAE1C,WAAW,GAAG,WAAW,CAAC,GAAG,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,CAAC;IACjE,UAAU,GAAG,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;;IAGhF,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,CAAC;AAChD,CAAC;AAED,SAAS,QAAQ,CAAC,IAAU,EAAE,KAAW;;IAEvC,IAAM,MAAM,GAAG,IAAI,CAAC,IAAI,KAAK,CAAC,CAAC;IAC/B,IAAM,OAAO,GAAG,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC;;IAGjC,IAAI,MAAM,GAAG,OAAO,EAAE;QACpB,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,MAAM,KAAK,OAAO,EAAE;QAC7B,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAC9B,IAAM,OAAO,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,CAAC;QAChC,IAAI,MAAM,GAAG,OAAO;YAAE,OAAO,IAAI,CAAC;KACnC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,UAAU,CAAC,MAAc,EAAE,OAAe;IACjD,MAAM,IAAI,aAAa,CAAC,YAAI,MAAM,mDAAwC,OAAO,CAAE,CAAC,CAAC;AACvF,CAAC;AAOD;;;;;;;;;;IAcE,oBAAY,KAAsB;QAChC,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;YAAE,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;QAEhE,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC;SACjD;aAAM,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;YAC9B,IAAI,KAAK,CAAC,UAAU,KAAK,EAAE,EAAE;gBAC3B,MAAM,IAAI,aAAa,CAAC,2CAA2C,CAAC,CAAC;aACtE;YACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;SACpB;aAAM;YACL,MAAM,IAAI,aAAa,CAAC,yCAAyC,CAAC,CAAC;SACpE;KACF;;;;;;IAOM,qBAAU,GAAjB,UAAkB,cAAsB;;QAEtC,IAAI,UAAU,GAAG,KAAK,CAAC;QACvB,IAAI,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAI,YAAY,GAAG,KAAK,CAAC;;QAGzB,IAAI,iBAAiB,GAAG,CAAC,CAAC;;QAE1B,IAAI,WAAW,GAAG,CAAC,CAAC;;QAEpB,IAAI,OAAO,GAAG,CAAC,CAAC;;QAEhB,IAAI,aAAa,GAAG,CAAC,CAAC;;QAEtB,IAAI,YAAY,GAAG,CAAC,CAAC;;QAGrB,IAAM,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;;QAEnB,IAAI,aAAa,GAAG,CAAC,CAAC;;QAEtB,IAAI,YAAY,GAAG,CAAC,CAAC;;QAErB,IAAI,UAAU,GAAG,CAAC,CAAC;;QAEnB,IAAI,SAAS,GAAG,CAAC,CAAC;;QAGlB,IAAI,QAAQ,GAAG,CAAC,CAAC;;QAEjB,IAAI,CAAC,GAAG,CAAC,CAAC;;QAEV,IAAI,eAAe,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAErC,IAAI,cAAc,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;QAEpC,IAAI,cAAc,GAAG,CAAC,CAAC;;QAGvB,IAAI,KAAK,GAAG,CAAC,CAAC;;;;QAKd,IAAI,cAAc,CAAC,MAAM,IAAI,IAAI,EAAE;YACjC,MAAM,IAAI,aAAa,CAAC,EAAE,GAAG,cAAc,GAAG,gCAAgC,CAAC,CAAC;SACjF;;QAGD,IAAM,WAAW,GAAG,cAAc,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;QAC9D,IAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;QACxD,IAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;;QAGxD,IAAI,CAAC,CAAC,WAAW,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,KAAK,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3E,MAAM,IAAI,aAAa,CAAC,EAAE,GAAG,cAAc,GAAG,gCAAgC,CAAC,CAAC;SACjF;QAED,IAAI,WAAW,EAAE;;;YAIf,IAAM,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;;;YAItC,IAAM,CAAC,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YACzB,IAAM,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;YAC/B,IAAM,SAAS,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;;YAGjC,IAAI,CAAC,IAAI,SAAS,KAAK,SAAS;gBAAE,UAAU,CAAC,cAAc,EAAE,wBAAwB,CAAC,CAAC;;YAGvF,IAAI,CAAC,IAAI,cAAc,KAAK,SAAS;gBAAE,UAAU,CAAC,cAAc,EAAE,uBAAuB,CAAC,CAAC;YAE3F,IAAI,CAAC,KAAK,SAAS,KAAK,OAAO,IAAI,SAAS,CAAC,EAAE;gBAC7C,UAAU,CAAC,cAAc,EAAE,2BAA2B,CAAC,CAAC;aACzD;SACF;;QAGD,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;YAClE,UAAU,GAAG,cAAc,CAAC,KAAK,EAAE,CAAC,KAAK,GAAG,CAAC;SAC9C;;QAGD,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;YACpE,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;gBAClE,OAAO,IAAI,UAAU,CAACA,QAAM,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,mBAAmB,CAAC,CAAC,CAAC;aAC5F;iBAAM,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;gBACxC,OAAO,IAAI,UAAU,CAACA,QAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aAChD;SACF;;QAGD,OAAO,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;YACtE,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;gBACjC,IAAI,QAAQ;oBAAE,UAAU,CAAC,cAAc,EAAE,2BAA2B,CAAC,CAAC;gBAEtE,QAAQ,GAAG,IAAI,CAAC;gBAChB,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;gBAClB,SAAS;aACV;YAED,IAAI,aAAa,GAAG,EAAE,EAAE;gBACtB,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE;oBACjD,IAAI,CAAC,YAAY,EAAE;wBACjB,YAAY,GAAG,WAAW,CAAC;qBAC5B;oBAED,YAAY,GAAG,IAAI,CAAC;;oBAGpB,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;oBAC7D,aAAa,GAAG,aAAa,GAAG,CAAC,CAAC;iBACnC;aACF;YAED,IAAI,YAAY;gBAAE,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;YACxC,IAAI,QAAQ;gBAAE,aAAa,GAAG,aAAa,GAAG,CAAC,CAAC;YAEhD,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;YAC9B,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;SACnB;QAED,IAAI,QAAQ,IAAI,CAAC,WAAW;YAC1B,MAAM,IAAI,aAAa,CAAC,EAAE,GAAG,cAAc,GAAG,gCAAgC,CAAC,CAAC;;QAGlF,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,cAAc,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;;YAElE,IAAM,KAAK,GAAG,cAAc,CAAC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;;YAGnE,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBAAE,OAAO,IAAI,UAAU,CAACA,QAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;;YAGxE,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;;YAGlC,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;SACjC;;QAGD,IAAI,cAAc,CAAC,KAAK,CAAC;YAAE,OAAO,IAAI,UAAU,CAACA,QAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;;;QAI1E,UAAU,GAAG,CAAC,CAAC;QAEf,IAAI,CAAC,aAAa,EAAE;YAClB,UAAU,GAAG,CAAC,CAAC;YACf,SAAS,GAAG,CAAC,CAAC;YACd,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;YACd,OAAO,GAAG,CAAC,CAAC;YACZ,aAAa,GAAG,CAAC,CAAC;YAClB,iBAAiB,GAAG,CAAC,CAAC;SACvB;aAAM;YACL,SAAS,GAAG,aAAa,GAAG,CAAC,CAAC;YAC9B,iBAAiB,GAAG,OAAO,CAAC;YAC5B,IAAI,iBAAiB,KAAK,CAAC,EAAE;gBAC3B,OAAO,MAAM,CAAC,YAAY,GAAG,iBAAiB,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;oBACzD,iBAAiB,GAAG,iBAAiB,GAAG,CAAC,CAAC;iBAC3C;aACF;SACF;;;;;QAOD,IAAI,QAAQ,IAAI,aAAa,IAAI,aAAa,GAAG,QAAQ,GAAG,CAAC,IAAI,EAAE,EAAE;YACnE,QAAQ,GAAG,YAAY,CAAC;SACzB;aAAM;YACL,QAAQ,GAAG,QAAQ,GAAG,aAAa,CAAC;SACrC;;QAGD,OAAO,QAAQ,GAAG,YAAY,EAAE;;YAE9B,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;YAE1B,IAAI,SAAS,GAAG,UAAU,GAAG,UAAU,EAAE;;gBAEvC,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrC,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;oBAC9B,QAAQ,GAAG,YAAY,CAAC;oBACxB,MAAM;iBACP;gBAED,UAAU,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;aACxC;YACD,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;SACzB;QAED,OAAO,QAAQ,GAAG,YAAY,IAAI,aAAa,GAAG,OAAO,EAAE;;YAEzD,IAAI,SAAS,KAAK,CAAC,IAAI,iBAAiB,GAAG,aAAa,EAAE;gBACxD,QAAQ,GAAG,YAAY,CAAC;gBACxB,iBAAiB,GAAG,CAAC,CAAC;gBACtB,MAAM;aACP;YAED,IAAI,aAAa,GAAG,OAAO,EAAE;;gBAE3B,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC;aACvB;iBAAM;;gBAEL,SAAS,GAAG,SAAS,GAAG,CAAC,CAAC;aAC3B;YAED,IAAI,QAAQ,GAAG,YAAY,EAAE;gBAC3B,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;aACzB;iBAAM;;gBAEL,IAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACrC,IAAI,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,EAAE;oBAC9B,QAAQ,GAAG,YAAY,CAAC;oBACxB,MAAM;iBACP;gBACD,UAAU,CAAC,cAAc,EAAE,UAAU,CAAC,CAAC;aACxC;SACF;;;QAID,IAAI,SAAS,GAAG,UAAU,GAAG,CAAC,GAAG,iBAAiB,EAAE;YAClD,IAAI,WAAW,GAAG,WAAW,CAAC;;;;YAK9B,IAAI,QAAQ,EAAE;gBACZ,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC;gBAChC,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;aAC/B;;YAED,IAAI,UAAU,EAAE;gBACd,YAAY,GAAG,YAAY,GAAG,CAAC,CAAC;gBAChC,WAAW,GAAG,WAAW,GAAG,CAAC,CAAC;aAC/B;YAED,IAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAC9E,IAAI,QAAQ,GAAG,CAAC,CAAC;YAEjB,IAAI,UAAU,IAAI,CAAC,EAAE;gBACnB,QAAQ,GAAG,CAAC,CAAC;gBACb,IAAI,UAAU,KAAK,CAAC,EAAE;oBACpB,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;oBAC/C,KAAK,CAAC,GAAG,YAAY,GAAG,SAAS,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC,EAAE,EAAE;wBAC3D,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;4BACnC,QAAQ,GAAG,CAAC,CAAC;4BACb,MAAM;yBACP;qBACF;iBACF;aACF;YAED,IAAI,QAAQ,EAAE;gBACZ,IAAI,IAAI,GAAG,SAAS,CAAC;gBAErB,OAAO,IAAI,IAAI,CAAC,EAAE,IAAI,EAAE,EAAE;oBACxB,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;wBACtB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;wBAGjB,IAAI,IAAI,KAAK,CAAC,EAAE;4BACd,IAAI,QAAQ,GAAG,YAAY,EAAE;gCAC3B,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC;gCACxB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;6BAClB;iCAAM;gCACL,OAAO,IAAI,UAAU,CACnBA,QAAM,CAAC,IAAI,CAAC,UAAU,GAAG,mBAAmB,GAAG,mBAAmB,CAAC,CACpE,CAAC;6BACH;yBACF;qBACF;iBACF;aACF;SACF;;;QAID,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;QAErC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;;QAGpC,IAAI,iBAAiB,KAAK,CAAC,EAAE;YAC3B,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACrC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACrC;aAAM,IAAI,SAAS,GAAG,UAAU,GAAG,EAAE,EAAE;YACtC,IAAI,IAAI,GAAG,UAAU,CAAC;YACtB,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YACjD,eAAe,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAEjC,OAAO,IAAI,IAAI,SAAS,EAAE,IAAI,EAAE,EAAE;gBAChC,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9D,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACpE;SACF;aAAM;YACL,IAAI,IAAI,GAAG,UAAU,CAAC;YACtB,eAAe,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAElD,OAAO,IAAI,IAAI,SAAS,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE;gBACrC,eAAe,GAAG,eAAe,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;gBAChE,eAAe,GAAG,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACtE;YAED,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;YAEjD,OAAO,IAAI,IAAI,SAAS,EAAE,IAAI,EAAE,EAAE;gBAChC,cAAc,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC9D,cAAc,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;aACpE;SACF;QAED,IAAM,WAAW,GAAG,YAAY,CAAC,eAAe,EAAE,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC,CAAC,CAAC;QACzF,WAAW,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QAEtD,IAAI,QAAQ,CAAC,WAAW,CAAC,GAAG,EAAE,cAAc,CAAC,EAAE;YAC7C,WAAW,CAAC,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7D;;QAGD,cAAc,GAAG,QAAQ,GAAG,aAAa,CAAC;QAC1C,IAAM,GAAG,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC;;QAGlE,IACE,WAAW,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,EAC1F;;YAEA,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3D,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CACpB,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAC3E,CAAC;YACF,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;SAC/E;aAAM;YACL,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/E,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;SAChF;QAED,GAAG,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC;;QAG1B,IAAI,UAAU,EAAE;YACd,GAAG,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,CAAC,CAAC;SAChE;;QAGD,IAAM,MAAM,GAAGA,QAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAChC,KAAK,GAAG,CAAC,CAAC;;;QAIV,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;QACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;QAC5C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC;QAC7C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC;;QAE7C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;QACtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;QAC7C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;QAC9C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;;QAI9C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QACtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;QAC7C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC;QAC9C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC;;QAE9C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACvC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;QAC9C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;QAC/C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;QAG/C,OAAO,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;KAC/B;;IAGD,6BAAQ,GAAR;;;;QAKE,IAAI,eAAe,CAAC;;QAEpB,IAAI,kBAAkB,GAAG,CAAC,CAAC;;QAE3B,IAAM,WAAW,GAAG,IAAI,KAAK,CAAS,EAAE,CAAC,CAAC;QAC1C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,EAAE;YAAE,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;QAEhE,IAAI,KAAK,GAAG,CAAC,CAAC;;QAGd,IAAI,OAAO,GAAG,KAAK,CAAC;;QAGpB,IAAI,eAAe,CAAC;;QAEpB,IAAI,cAAc,GAAgD,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;;QAE1F,IAAI,CAAC,EAAE,CAAC,CAAC;;QAGT,IAAM,MAAM,GAAa,EAAE,CAAC;;QAG5B,KAAK,GAAG,CAAC,CAAC;;QAGV,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC;;;QAI1B,IAAM,GAAG,GACP,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;QAE/F,IAAM,IAAI,GACR,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;;QAI/F,IAAM,IAAI,GACR,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;QAE/F,IAAM,IAAI,GACR,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;QAG/F,KAAK,GAAG,CAAC,CAAC;;QAGV,IAAM,GAAG,GAAG;YACV,GAAG,EAAE,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;YACxB,IAAI,EAAE,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;SAC3B,CAAC;QAEF,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YAChC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;SAClB;;;QAID,IAAM,WAAW,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,gBAAgB,CAAC;QAEpD,IAAI,WAAW,IAAI,CAAC,KAAK,CAAC,EAAE;;YAE1B,IAAI,WAAW,KAAK,oBAAoB,EAAE;gBACxC,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC;aACrC;iBAAM,IAAI,WAAW,KAAK,eAAe,EAAE;gBAC1C,OAAO,KAAK,CAAC;aACd;iBAAM;gBACL,eAAe,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,aAAa,CAAC;gBAC/C,eAAe,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC;aAChD;SACF;aAAM;YACL,eAAe,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;YACtC,eAAe,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,aAAa,CAAC;SAChD;;QAGD,IAAM,QAAQ,GAAG,eAAe,GAAG,aAAa,CAAC;;;;;QAOjD,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,eAAe,GAAG,GAAG,KAAK,EAAE,CAAC,CAAC;QAC5E,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAC/B,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;QAC/B,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;QAE9B,IACE,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;YAC7B,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;YAC7B,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC;YAC7B,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAC7B;YACA,OAAO,GAAG,IAAI,CAAC;SAChB;aAAM;YACL,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACvB,IAAI,YAAY,GAAG,CAAC,CAAC;;gBAErB,IAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;gBAC1C,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC;gBACjC,YAAY,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;;;gBAI9B,IAAI,CAAC,YAAY;oBAAE,SAAS;gBAE5B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;;oBAEvB,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY,GAAG,EAAE,CAAC;;oBAE3C,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,EAAE,CAAC,CAAC;iBAC9C;aACF;SACF;;;;QAMD,IAAI,OAAO,EAAE;YACX,kBAAkB,GAAG,CAAC,CAAC;YACvB,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACxB;aAAM;YACL,kBAAkB,GAAG,EAAE,CAAC;YACxB,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;gBAC1B,kBAAkB,GAAG,kBAAkB,GAAG,CAAC,CAAC;gBAC5C,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;aACnB;SACF;;QAGD,IAAM,mBAAmB,GAAG,kBAAkB,GAAG,CAAC,GAAG,QAAQ,CAAC;;;;;;;;QAS9D,IAAI,mBAAmB,IAAI,EAAE,IAAI,mBAAmB,IAAI,CAAC,CAAC,IAAI,QAAQ,GAAG,CAAC,EAAE;;;;;YAM1E,IAAI,kBAAkB,GAAG,EAAE,EAAE;gBAC3B,MAAM,CAAC,IAAI,CAAC,UAAG,CAAC,CAAE,CAAC,CAAC;gBACpB,IAAI,QAAQ,GAAG,CAAC;oBAAE,MAAM,CAAC,IAAI,CAAC,YAAK,QAAQ,CAAE,CAAC,CAAC;qBAC1C,IAAI,QAAQ,GAAG,CAAC;oBAAE,MAAM,CAAC,IAAI,CAAC,WAAI,QAAQ,CAAE,CAAC,CAAC;gBACnD,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;aACxB;YAED,MAAM,CAAC,IAAI,CAAC,UAAG,WAAW,CAAC,KAAK,EAAE,CAAC,CAAE,CAAC,CAAC;YACvC,kBAAkB,GAAG,kBAAkB,GAAG,CAAC,CAAC;YAE5C,IAAI,kBAAkB,EAAE;gBACtB,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aAClB;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,CAAC,EAAE,EAAE;gBAC3C,MAAM,CAAC,IAAI,CAAC,UAAG,WAAW,CAAC,KAAK,EAAE,CAAC,CAAE,CAAC,CAAC;aACxC;;YAGD,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjB,IAAI,mBAAmB,GAAG,CAAC,EAAE;gBAC3B,MAAM,CAAC,IAAI,CAAC,WAAI,mBAAmB,CAAE,CAAC,CAAC;aACxC;iBAAM;gBACL,MAAM,CAAC,IAAI,CAAC,UAAG,mBAAmB,CAAE,CAAC,CAAC;aACvC;SACF;aAAM;;YAEL,IAAI,QAAQ,IAAI,CAAC,EAAE;gBACjB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,EAAE,CAAC,EAAE,EAAE;oBAC3C,MAAM,CAAC,IAAI,CAAC,UAAG,WAAW,CAAC,KAAK,EAAE,CAAC,CAAE,CAAC,CAAC;iBACxC;aACF;iBAAM;gBACL,IAAI,cAAc,GAAG,kBAAkB,GAAG,QAAQ,CAAC;;gBAGnD,IAAI,cAAc,GAAG,CAAC,EAAE;oBACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,EAAE,CAAC,EAAE,EAAE;wBACvC,MAAM,CAAC,IAAI,CAAC,UAAG,WAAW,CAAC,KAAK,EAAE,CAAC,CAAE,CAAC,CAAC;qBACxC;iBACF;qBAAM;oBACL,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAClB;gBAED,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;gBAEjB,OAAO,cAAc,EAAE,GAAG,CAAC,EAAE;oBAC3B,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAClB;gBAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;oBAC7E,MAAM,CAAC,IAAI,CAAC,UAAG,WAAW,CAAC,KAAK,EAAE,CAAC,CAAE,CAAC,CAAC;iBACxC;aACF;SACF;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;KACxB;IAED,2BAAM,GAAN;QACE,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;KAC5C;;IAGD,mCAAc,GAAd;QACE,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC;KAC5C;;IAGM,2BAAgB,GAAvB,UAAwB,GAAuB;QAC7C,OAAO,UAAU,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;KAClD;;IAGD,qBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,4BAAO,GAAP;QACE,OAAO,2BAAmB,IAAI,CAAC,QAAQ,EAAE,QAAI,CAAC;KAC/C;IACH,iBAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;;AC7vBjF;;;;;;;;;;;IAcE,gBAAY,KAAa;QACvB,IAAI,EAAE,IAAI,YAAY,MAAM,CAAC;YAAE,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;QAExD,IAAK,KAAiB,YAAY,MAAM,EAAE;YACxC,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,CAAC;KACrB;;;;;;IAOD,wBAAO,GAAP;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,yBAAQ,GAAR,UAAS,KAAc;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KACnC;;IAGD,+BAAc,GAAd,UAAe,OAAsB;QACnC,IAAI,OAAO,KAAK,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;YAC5E,OAAO,IAAI,CAAC,KAAK,CAAC;SACnB;QAED,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;;;YAGxC,OAAO,EAAE,aAAa,EAAE,WAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAE,EAAE,CAAC;SACvD;QAED,OAAO;YACL,aAAa,EAAE,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;SAC5F,CAAC;KACH;;IAGM,uBAAgB,GAAvB,UAAwB,GAAmB,EAAE,OAAsB;QACjE,IAAM,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAClD,OAAO,OAAO,IAAI,OAAO,CAAC,OAAO,GAAG,WAAW,GAAG,IAAI,MAAM,CAAC,WAAW,CAAC,CAAC;KAC3E;;IAGD,iBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,wBAAO,GAAP;QACE,IAAM,KAAK,GAAG,IAAI,CAAC,cAAc,EAAoB,CAAC;QACtD,OAAO,qBAAc,KAAK,CAAC,aAAa,MAAG,CAAC;KAC7C;IACH,aAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;;AC3EzE;;;;;;;;;;;IAcE,eAAY,KAAsB;QAChC,IAAI,EAAE,IAAI,YAAY,KAAK,CAAC;YAAE,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;QAEtD,IAAK,KAAiB,YAAY,MAAM,EAAE;YACxC,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;SACzB;QAED,IAAI,CAAC,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;KACzB;;;;;;IAOD,uBAAO,GAAP;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,wBAAQ,GAAR,UAAS,KAAc;QACrB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;KACnC;IAED,sBAAM,GAAN;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;IAGD,8BAAc,GAAd,UAAe,OAAsB;QACnC,IAAI,OAAO,KAAK,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;YAAE,OAAO,IAAI,CAAC,KAAK,CAAC;QACtE,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;KAC9C;;IAGM,sBAAgB,GAAvB,UAAwB,GAAkB,EAAE,OAAsB;QAChE,OAAO,OAAO,IAAI,OAAO,CAAC,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KAC9F;;IAGD,gBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,uBAAO,GAAP;QACE,OAAO,oBAAa,IAAI,CAAC,OAAO,EAAE,MAAG,CAAC;KACvC;IACH,YAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;;AChEvE;;;;;;IAQE;QACE,IAAI,EAAE,IAAI,YAAY,MAAM,CAAC;YAAE,OAAO,IAAI,MAAM,EAAE,CAAC;KACpD;;IAGD,+BAAc,GAAd;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;KACvB;;IAGM,uBAAgB,GAAvB;QACE,OAAO,IAAI,MAAM,EAAE,CAAC;KACrB;;IAGD,iBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,wBAAO,GAAP;QACE,OAAO,cAAc,CAAC;KACvB;IACH,aAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;;AChCzE;;;;;;IAQE;QACE,IAAI,EAAE,IAAI,YAAY,MAAM,CAAC;YAAE,OAAO,IAAI,MAAM,EAAE,CAAC;KACpD;;IAGD,+BAAc,GAAd;QACE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;KACvB;;IAGM,uBAAgB,GAAvB;QACE,OAAO,IAAI,MAAM,EAAE,CAAC;KACrB;;IAGD,iBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,wBAAO,GAAP;QACE,OAAO,cAAc,CAAC;KACvB;IACH,aAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;;AChCzE;AACA,IAAM,iBAAiB,GAAG,IAAI,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAE1D;AACA,IAAI,cAAc,GAAsB,IAAI,CAAC;AAc7C,IAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC;AAEzB;;;;;;;;;;;IAuBE,kBAAY,OAAyE;QACnF,IAAI,EAAE,IAAI,YAAY,QAAQ,CAAC;YAAE,OAAO,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC;;QAG9D,IAAI,SAAS,CAAC;QACd,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,EAAE;YAC7D,IAAI,OAAO,OAAO,CAAC,EAAE,KAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;gBACrE,MAAM,IAAI,aAAa,CACrB,qEAAqE,CACtE,CAAC;aACH;YACD,IAAI,aAAa,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,EAAE;gBACzE,SAAS,GAAGA,QAAM,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,KAAK,CAAC,CAAC;aACvD;iBAAM;gBACL,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC;aACxB;SACF;aAAM;YACL,SAAS,GAAG,OAAO,CAAC;SACrB;;QAGD,IAAI,SAAS,IAAI,IAAI,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;;;YAGtD,IAAI,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,SAAS,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,CAAC;SACtF;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,UAAU,KAAK,EAAE,EAAE;;YAEvE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS,YAAYA,QAAM,GAAG,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;SAC/E;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACxC,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,EAAE;gBAC3B,IAAM,KAAK,GAAGA,QAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACrC,IAAI,KAAK,CAAC,UAAU,KAAK,EAAE,EAAE;oBAC3B,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;iBACnB;qBAAM;oBACL,MAAM,IAAI,aAAa,CAAC,iDAAiD,CAAC,CAAC;iBAC5E;aACF;iBAAM,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACvE,IAAI,CAAC,GAAG,CAAC,GAAGA,QAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;aAC3C;iBAAM;gBACL,MAAM,IAAI,aAAa,CACrB,gGAAgG,CACjG,CAAC;aACH;SACF;aAAM;YACL,MAAM,IAAI,aAAa,CAAC,sDAAsD,CAAC,CAAC;SACjF;;QAED,IAAI,QAAQ,CAAC,cAAc,EAAE;YAC3B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACrC;KACF;IAMD,sBAAI,wBAAE;;;;;aAAN;YACE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;SAClB;aAED,UAAO,KAAa;YAClB,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;YAClB,IAAI,QAAQ,CAAC,cAAc,EAAE;gBAC3B,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;aACnC;SACF;;;OAPA;IAaD,sBAAI,oCAAc;;;;;aAAlB;YACE,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;SAC/B;aAED,UAAmB,KAAa;;YAE9B,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;SACjC;;;OALA;;IAQD,8BAAW,GAAX;QACE,IAAI,QAAQ,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE;YACxC,OAAO,IAAI,CAAC,IAAI,CAAC;SAClB;QAED,IAAM,SAAS,GAAG,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAE1C,IAAI,QAAQ,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YACzC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC;SACvB;QAED,OAAO,SAAS,CAAC;KAClB;;;;;;;IAQM,eAAM,GAAb;QACE,QAAQ,QAAQ,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,KAAK,GAAG,CAAC,IAAI,QAAQ,EAAE;KAC3D;;;;;;IAOM,iBAAQ,GAAf,UAAgB,IAAa;QAC3B,IAAI,QAAQ,KAAK,OAAO,IAAI,EAAE;YAC5B,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;SACtC;QAED,IAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC9B,IAAM,MAAM,GAAGA,QAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;;QAGhC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;;QAG9B,IAAI,cAAc,KAAK,IAAI,EAAE;YAC3B,cAAc,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;SACjC;;QAGD,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;QAC9B,MAAM,CAAC,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;;QAG9B,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,GAAG,IAAI,CAAC;QACxB,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;QAC/B,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,IAAI,IAAI,CAAC;QAE/B,OAAO,MAAM,CAAC;KACf;;;;;;IAOD,2BAAQ,GAAR,UAAS,MAAe;;QAEtB,IAAI,MAAM;YAAE,OAAO,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAC5C,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;KAC3B;;IAGD,yBAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;KAC3B;;;;;;IAOD,yBAAM,GAAN,UAAO,OAAyC;QAC9C,IAAI,OAAO,KAAK,SAAS,IAAI,OAAO,KAAK,IAAI,EAAE;YAC7C,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,YAAY,QAAQ,EAAE;YAC/B,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;SAC7E;QAED,IACE,OAAO,OAAO,KAAK,QAAQ;YAC3B,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC;YACzB,OAAO,CAAC,MAAM,KAAK,EAAE;YACrB,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EACrB;YACA,OAAO,OAAO,KAAKA,QAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;SACtE;QAED,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACrF,OAAO,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,CAAC;SACrD;QAED,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,MAAM,KAAK,EAAE,EAAE;YACrF,OAAOA,QAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7C;QAED,IACE,OAAO,OAAO,KAAK,QAAQ;YAC3B,aAAa,IAAI,OAAO;YACxB,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,EACzC;YACA,IAAM,aAAa,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YAC5C,IAAM,YAAY,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,WAAW,EAAE,CAAC;YACtD,OAAO,OAAO,aAAa,KAAK,QAAQ,IAAI,aAAa,CAAC,WAAW,EAAE,KAAK,YAAY,CAAC;SAC1F;QAED,OAAO,KAAK,CAAC;KACd;;IAGD,+BAAY,GAAZ;QACE,IAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,IAAM,IAAI,GAAG,IAAI,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACrC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;QAC3C,OAAO,SAAS,CAAC;KAClB;;IAGM,iBAAQ,GAAf;QACE,OAAO,IAAI,QAAQ,EAAE,CAAC;KACvB;;;;;;IAOM,uBAAc,GAArB,UAAsB,IAAY;QAChC,IAAM,MAAM,GAAGA,QAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;QAEjE,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;;QAE9B,OAAO,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;KAC7B;;;;;;IAOM,4BAAmB,GAA1B,UAA2B,SAAiB;;QAE1C,IAAI,OAAO,SAAS,KAAK,WAAW,KAAK,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,EAAE,CAAC,EAAE;YACtF,MAAM,IAAI,aAAa,CACrB,yFAAyF,CAC1F,CAAC;SACH;QAED,OAAO,IAAI,QAAQ,CAACA,QAAM,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC,CAAC;KACpD;;;;;;IAOM,gBAAO,GAAd,UAAe,EAAmE;QAChF,IAAI,EAAE,IAAI,IAAI;YAAE,OAAO,KAAK,CAAC;QAE7B,IAAI;YACF,IAAI,QAAQ,CAAC,EAAE,CAAC,CAAC;YACjB,OAAO,IAAI,CAAC;SACb;QAAC,WAAM;YACN,OAAO,KAAK,CAAC;SACd;KACF;;IAGD,iCAAc,GAAd;QACE,IAAI,IAAI,CAAC,WAAW;YAAE,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;QAC1D,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;KACvC;;IAGM,yBAAgB,GAAvB,UAAwB,GAAqB;QAC3C,OAAO,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC/B;;;;;;;IAQD,mBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,0BAAO,GAAP;QACE,OAAO,yBAAiB,IAAI,CAAC,WAAW,EAAE,QAAI,CAAC;KAChD;;IAxSM,cAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,QAAQ,CAAC,CAAC;IAyStD,eAAC;CA7SD,IA6SC;AAED;AACA,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,EAAE;IACpD,KAAK,EAAE,SAAS,CACd,UAAC,IAAY,IAAK,OAAA,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAA,EACzC,yDAAyD,CAC1D;CACF,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE;IAClD,KAAK,EAAE,SAAS,CAAC,cAAM,OAAA,QAAQ,CAAC,MAAM,EAAE,GAAA,EAAE,mDAAmD,CAAC;CAC/F,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,EAAE;IACnD,KAAK,EAAE,SAAS,CAAC,cAAM,OAAA,QAAQ,CAAC,MAAM,EAAE,GAAA,EAAE,mDAAmD,CAAC;CAC/F,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,EAAE,SAAS,EAAE;IACzC,KAAK,EAAE,SAAS,CAAC,cAAM,OAAA,QAAQ,CAAC,MAAM,EAAE,GAAA,EAAE,mDAAmD,CAAC;CAC/F,CAAC,CAAC;AAEH,MAAM,CAAC,cAAc,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;;AC9V7E,SAAS,WAAW,CAAC,GAAW;IAC9B,OAAO,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACvC,CAAC;AAgBD;;;;;;;;;;IAcE,oBAAY,OAAe,EAAE,OAAgB;QAC3C,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;YAAE,OAAO,IAAI,UAAU,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE3E,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC,CAAC;QAE1C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YACvC,MAAM,IAAI,SAAS,CACjB,gEAAyD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAE,CACxF,CAAC;SACH;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;YACvC,MAAM,IAAI,SAAS,CACjB,+DAAwD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAE,CACvF,CAAC;SACH;;QAGD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC5C,IACE,EACE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG;gBACvB,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,CACxB,EACD;gBACA,MAAM,IAAI,SAAS,CAAC,yCAAkC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,uBAAoB,CAAC,CAAC;aAC5F;SACF;KACF;IAEM,uBAAY,GAAnB,UAAoB,OAAgB;QAClC,OAAO,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;KACzD;;IAGD,mCAAc,GAAd,UAAe,OAAsB;QACnC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,CAAC;SACzD;QACD,OAAO,EAAE,kBAAkB,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;KACjF;;IAGM,2BAAgB,GAAvB,UAAwB,GAAkD;QACxE,IAAI,QAAQ,IAAI,GAAG,EAAE;YACnB,IAAI,OAAO,GAAG,CAAC,MAAM,KAAK,QAAQ,EAAE;;gBAElC,IAAI,GAAG,CAAC,MAAM,CAAC,SAAS,KAAK,YAAY,EAAE;oBACzC,OAAO,GAA4B,CAAC;iBACrC;aACF;iBAAM;gBACL,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;aAC1E;SACF;QACD,IAAI,oBAAoB,IAAI,GAAG,EAAE;YAC/B,OAAO,IAAI,UAAU,CACnB,GAAG,CAAC,kBAAkB,CAAC,OAAO,EAC9B,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,kBAAkB,CAAC,OAAO,CAAC,CACxD,CAAC;SACH;QACD,MAAM,IAAI,aAAa,CAAC,mDAA4C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;KAC5F;IACH,iBAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;;ACnGjF;;;;;;;;;IAYE,oBAAY,KAAa;QACvB,IAAI,EAAE,IAAI,YAAY,UAAU,CAAC;YAAE,OAAO,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC;QAEhE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;KACpB;;IAGD,4BAAO,GAAP;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;IAED,6BAAQ,GAAR;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;IAGD,4BAAO,GAAP;QACE,OAAO,2BAAmB,IAAI,CAAC,KAAK,QAAI,CAAC;KAC1C;IAED,2BAAM,GAAN;QACE,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;IAGD,mCAAc,GAAd;QACE,OAAO,EAAE,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,CAAC;KAChC;;IAGM,2BAAgB,GAAvB,UAAwB,GAAuB;QAC7C,OAAO,IAAI,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KACpC;;IAGD,qBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IACH,iBAAC;AAAD,CAAC,IAAA;AAED,MAAM,CAAC,cAAc,CAAC,UAAU,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC;;AChD7E;IACa,yBAAyB,GACpC,KAAwC;AAU1C;;;;;IAI+B,6BAAyB;IAmBtD,mBAAY,GAA6C,EAAE,IAAa;QAAxE,iBAkBC;;;QAfC,IAAI,EAAE,KAAI,YAAY,SAAS,CAAC;YAAE,OAAO,IAAI,SAAS,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;QAElE,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACpB,QAAA,kBAAM,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,SAAC;SAChC;aAAM,IAAI,YAAY,CAAC,GAAG,CAAC,IAAI,OAAO,GAAG,CAAC,CAAC,KAAK,WAAW,IAAI,OAAO,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;YAC5F,QAAA,kBAAM,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,SAAC;SAC3B;aAAM;YACL,QAAA,kBAAM,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,SAAC;SACxB;QACD,MAAM,CAAC,cAAc,CAAC,KAAI,EAAE,WAAW,EAAE;YACvC,KAAK,EAAE,WAAW;YAClB,QAAQ,EAAE,KAAK;YACf,YAAY,EAAE,KAAK;YACnB,UAAU,EAAE,KAAK;SAClB,CAAC,CAAC;;KACJ;IAED,0BAAM,GAAN;QACE,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,QAAQ,EAAE;SAC5B,CAAC;KACH;;IAGM,iBAAO,GAAd,UAAe,KAAa;QAC1B,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;KACjD;;IAGM,oBAAU,GAAjB,UAAkB,KAAa;QAC7B,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;KACpD;;;;;;;IAQM,kBAAQ,GAAf,UAAgB,OAAe,EAAE,QAAgB;QAC/C,OAAO,IAAI,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KACzC;;;;;;;IAQM,oBAAU,GAAjB,UAAkB,GAAW,EAAE,QAAgB;QAC7C,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;KAC5D;;IAGD,kCAAc,GAAd;QACE,OAAO,EAAE,UAAU,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC;KAClE;;IAGM,0BAAgB,GAAvB,UAAwB,GAAsB;QAC5C,OAAO,IAAI,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;KACtC;;IAGD,oBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;KACvB;IAED,2BAAO,GAAP;QACE,OAAO,6BAAsB,IAAI,CAAC,WAAW,EAAE,kBAAQ,IAAI,CAAC,UAAU,EAAE,QAAK,CAAC;KAC/E;IAzFe,mBAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC;IA0FtD,gBAAC;CAAA,CA7F8B,yBAAyB;;SCWxC,UAAU,CAAC,KAAc;IACvC,QACE,YAAY,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,WAAW,CAAC,IAAI,OAAO,KAAK,CAAC,SAAS,KAAK,QAAQ,EAC7F;AACJ,CAAC;AAED;AACA,IAAM,cAAc,GAAG,UAAU,CAAC;AAClC,IAAM,cAAc,GAAG,CAAC,UAAU,CAAC;AACnC;AACA;AACA,IAAM,cAAc,GAAG,kBAAkB,CAAC;AAC1C,IAAM,cAAc,GAAG,CAAC,kBAAkB,CAAC;AAE3C;AACA;AACA,IAAM,YAAY,GAAG;IACnB,IAAI,EAAE,QAAQ;IACd,OAAO,EAAE,MAAM;IACf,KAAK,EAAE,MAAM;IACb,OAAO,EAAE,UAAU;IACnB,UAAU,EAAE,KAAK;IACjB,cAAc,EAAE,UAAU;IAC1B,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,IAAI;IACjB,OAAO,EAAE,MAAM;IACf,OAAO,EAAE,MAAM;IACf,MAAM,EAAE,UAAU;IAClB,kBAAkB,EAAE,UAAU;IAC9B,UAAU,EAAE,SAAS;CACb,CAAC;AAEX;AACA,SAAS,gBAAgB,CAAC,KAAU,EAAE,OAA2B;IAA3B,wBAAA,EAAA,YAA2B;IAC/D,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAI,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE;YACrC,OAAO,KAAK,CAAC;SACd;;;QAID,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,EAAE;YAC/B,IAAI,KAAK,IAAI,cAAc,IAAI,KAAK,IAAI,cAAc;gBAAE,OAAO,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;YAChF,IAAI,KAAK,IAAI,cAAc,IAAI,KAAK,IAAI,cAAc;gBAAE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;SACvF;;QAGD,OAAO,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;KAC1B;;IAGD,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,KAAK,CAAC;;IAG7D,IAAI,KAAK,CAAC,UAAU;QAAE,OAAO,IAAI,CAAC;IAElC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,CACpC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI,GAAA,CACV,CAAC;IACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAM,CAAC,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,IAAI,CAAC;YAAE,OAAO,CAAC,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;KAClD;IAED,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;QACvB,IAAM,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC;QACtB,IAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAExB,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,IAAI,OAAO,CAAC,KAAK,QAAQ;gBAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;iBACtC,IAAI,OAAO,CAAC,KAAK,QAAQ;gBAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SAC7D;aAAM;YACL,IAAI,OAAO,CAAC,KAAK,QAAQ;gBAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;iBAClD,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;gBAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;iBAC/C,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,OAAO;gBAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;SACpE;QACD,OAAO,IAAI,CAAC;KACb;IAED,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;QACvB,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC;QACtC,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SAC9C;QAED,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,CAAC;KACrC;IAED,IAAI,WAAW,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,EAAE;QAC1C,IAAM,CAAC,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,GAAG,KAAK,CAAC,UAAU,CAAC;;;QAIhD,IAAI,CAAC,YAAY,KAAK;YAAE,OAAO,CAAC,CAAC;QAEjC,IAAM,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,GAAA,CAAC,CAAC;QACjE,IAAI,OAAK,GAAG,IAAI,CAAC;QACjB,UAAU,CAAC,OAAO,CAAC,UAAA,CAAC;YAClB,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;gBAAE,OAAK,GAAG,KAAK,CAAC;SAC7D,CAAC,CAAC;;QAGH,IAAI,OAAK;YAAE,OAAO,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC;KAC7C;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAMD;AACA,SAAS,cAAc,CAAC,KAAY,EAAE,OAA8B;IAClE,OAAO,KAAK,CAAC,GAAG,CAAC,UAAC,CAAU,EAAE,KAAa;QACzC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,gBAAS,KAAK,CAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;QACxE,IAAI;YACF,OAAO,cAAc,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;SACnC;gBAAS;YACR,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;SAC3B;KACF,CAAC,CAAC;AACL,CAAC;AAED,SAAS,YAAY,CAAC,IAAU;IAC9B,IAAM,MAAM,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;;IAElC,OAAO,IAAI,CAAC,kBAAkB,EAAE,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AAC9E,CAAC;AAED;AACA,SAAS,cAAc,CAAC,KAAU,EAAE,OAA8B;IAChE,IAAI,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,IAAI,EAAE;QAChF,IAAM,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,GAAG,KAAK,KAAK,GAAA,CAAC,CAAC;QAC1E,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;YAChB,IAAM,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,UAAA,KAAK,IAAI,OAAA,KAAK,CAAC,YAAY,GAAA,CAAC,CAAC;YACnE,IAAM,WAAW,GAAG,KAAK;iBACtB,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC;iBACf,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,UAAG,IAAI,SAAM,GAAA,CAAC;iBAC1B,IAAI,CAAC,EAAE,CAAC,CAAC;YACZ,IAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC;YACjC,IAAM,YAAY,GAChB,MAAM;gBACN,KAAK;qBACF,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;qBAClC,GAAG,CAAC,UAAA,IAAI,IAAI,OAAA,UAAG,IAAI,SAAM,GAAA,CAAC;qBAC1B,IAAI,CAAC,EAAE,CAAC,CAAC;YACd,IAAM,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACxC,IAAM,YAAY,GAAG,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAC7E,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CACvB,YAAY,CAAC,MAAM,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,CACpE,CAAC;YAEF,MAAM,IAAI,aAAa,CACrB,2CAA2C;gBACzC,cAAO,WAAW,SAAG,WAAW,SAAG,YAAY,SAAG,OAAO,OAAI;gBAC7D,cAAO,YAAY,eAAK,MAAM,MAAG,CACpC,CAAC;SACH;QACD,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC;KACjE;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;QAAE,OAAO,cAAc,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAEhE,IAAI,KAAK,KAAK,SAAS;QAAE,OAAO,IAAI,CAAC;IAErC,IAAI,KAAK,YAAY,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;QAC1C,IAAM,OAAO,GAAG,KAAK,CAAC,OAAO,EAAE;;QAE7B,OAAO,GAAG,OAAO,GAAG,CAAC,CAAC,IAAI,OAAO,GAAG,eAAe,CAAC;QAEtD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO;kBAC7B,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,EAAE;kBAC1B,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;SACpC;QACD,OAAO,OAAO,CAAC,OAAO,IAAI,OAAO;cAC7B,EAAE,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE;cAC9B,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC;KAC5D;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,KAAK,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;;QAEvE,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,EAAE;YAC/B,IAAM,UAAU,GAAG,KAAK,IAAI,cAAc,IAAI,KAAK,IAAI,cAAc,EACnE,UAAU,GAAG,KAAK,IAAI,cAAc,IAAI,KAAK,IAAI,cAAc,CAAC;;YAGlE,IAAI,UAAU;gBAAE,OAAO,EAAE,UAAU,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;YACxD,IAAI,UAAU;gBAAE,OAAO,EAAE,WAAW,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;SAC1D;QACD,OAAO,EAAE,aAAa,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,CAAC;KAC5C;IAED,IAAI,KAAK,YAAY,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;QAC9C,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;QACxB,IAAI,KAAK,KAAK,SAAS,EAAE;YACvB,IAAM,KAAK,GAAG,KAAK,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;YAClD,IAAI,KAAK,EAAE;gBACT,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;aAClB;SACF;QAED,IAAM,EAAE,GAAG,IAAI,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;QAC/C,OAAO,EAAE,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;KACnC;IAED,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ;QAAE,OAAO,iBAAiB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACzF,OAAO,KAAK,CAAC;AACf,CAAC;AAED,IAAM,kBAAkB,GAAG;IACzB,MAAM,EAAE,UAAC,CAAS,IAAK,OAAA,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAA;IACxD,IAAI,EAAE,UAAC,CAAO,IAAK,OAAA,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,GAAA;IAC5C,KAAK,EAAE,UAAC,CAAQ,IAAK,OAAA,IAAI,KAAK,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,CAAC,GAAA;IAClF,UAAU,EAAE,UAAC,CAAa,IAAK,OAAA,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAA;IACtD,MAAM,EAAE,UAAC,CAAS,IAAK,OAAA,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAA;IAC1C,KAAK,EAAE,UAAC,CAAQ,IAAK,OAAA,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAA;IACvC,IAAI,EAAE,UACJ,CAIC;QAED,OAAA,IAAI,CAAC,QAAQ;;QAEX,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,EAC9B,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,EAChC,CAAC,CAAC,GAAG,IAAI,IAAI,GAAG,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,SAAS,CACzC;KAAA;IACH,MAAM,EAAE,cAAM,OAAA,IAAI,MAAM,EAAE,GAAA;IAC1B,MAAM,EAAE,cAAM,OAAA,IAAI,MAAM,EAAE,GAAA;IAC1B,QAAQ,EAAE,UAAC,CAAW,IAAK,OAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAA;IAC1C,QAAQ,EAAE,UAAC,CAAW,IAAK,OAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,GAAA;IAC1C,UAAU,EAAE,UAAC,CAAa,IAAK,OAAA,IAAI,UAAU,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,GAAA;IACnE,MAAM,EAAE,UAAC,CAAa,IAAK,OAAA,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,GAAA;IAClD,SAAS,EAAE,UAAC,CAAY,IAAK,OAAA,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,GAAA;CACtD,CAAC;AAEX;AACA,SAAS,iBAAiB,CAAC,GAAQ,EAAE,OAA8B;IACjE,IAAI,GAAG,IAAI,IAAI,IAAI,OAAO,GAAG,KAAK,QAAQ;QAAE,MAAM,IAAI,SAAS,CAAC,wBAAwB,CAAC,CAAC;IAE1F,IAAM,QAAQ,GAA0B,GAAG,CAAC,SAAS,CAAC;IACtD,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;;QAEnC,IAAM,IAAI,GAAa,EAAE,CAAC;QAC1B,KAAK,IAAM,IAAI,IAAI,GAAG,EAAE;YACtB,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;YAC5D,IAAI;gBACF,IAAM,KAAK,GAAG,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,CAAC,CAAC;gBACjD,IAAI,IAAI,KAAK,WAAW,EAAE;oBACxB,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE;wBAChC,KAAK,OAAA;wBACL,QAAQ,EAAE,IAAI;wBACd,UAAU,EAAE,IAAI;wBAChB,YAAY,EAAE,IAAI;qBACnB,CAAC,CAAC;iBACJ;qBAAM;oBACL,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;iBACpB;aACF;oBAAS;gBACR,OAAO,CAAC,WAAW,CAAC,GAAG,EAAE,CAAC;aAC3B;SACF;QACD,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;;;QAG1B,IAAI,MAAM,GAAQ,GAAG,CAAC;QACtB,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,UAAU,EAAE;;;;;YAK/C,IAAM,MAAM,GAAG,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACjD,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,IAAI,aAAa,CAAC,qCAAqC,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;aAChF;YACD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;SACzB;;QAGD,IAAI,QAAQ,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE;YACvC,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;SACvE;aAAM,IAAI,QAAQ,KAAK,OAAO,IAAI,MAAM,CAAC,GAAG,EAAE;YAC7C,MAAM,GAAG,IAAI,KAAK,CAChB,cAAc,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,EAC1C,cAAc,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,EACnC,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,OAAO,CAAC,EAClC,cAAc,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CACvC,CAAC;SACH;QAED,OAAO,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;KACvC;SAAM;QACL,MAAM,IAAI,SAAS,CAAC,uCAAuC,GAAG,OAAO,QAAQ,CAAC,CAAC;KAChF;AACH,CAAC;AAED;;;;AAIA;AACA;AACA;IACiB,MAqHhB;AArHD,WAAiB,KAAK;;;;;;;;;;;;;;;;;IA6BpB,SAAgB,KAAK,CAAC,IAAY,EAAE,OAAuB;QACzD,IAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;;QAGlF,IAAI,OAAO,YAAY,CAAC,OAAO,KAAK,SAAS;YAAE,YAAY,CAAC,MAAM,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC;QAC3F,IAAI,OAAO,YAAY,CAAC,MAAM,KAAK,SAAS;YAAE,YAAY,CAAC,OAAO,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;QAE1F,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,UAAC,GAAG,EAAE,KAAK;YACjC,IAAI,GAAG,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBAC9B,MAAM,IAAI,SAAS,CACjB,sEAA+D,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAE,CACrF,CAAC;aACH;YACD,OAAO,gBAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;SAC9C,CAAC,CAAC;KACJ;IAfe,WAAK,QAepB,CAAA;;;;;;;;;;;;;;;;;;;;;;;;IA4BD,SAAgB,SAAS,CACvB,KAAwB;;IAExB,QAA8F,EAC9F,KAAuB,EACvB,OAAuB;QAEvB,IAAI,KAAK,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC9C,OAAO,GAAG,KAAK,CAAC;YAChB,KAAK,GAAG,CAAC,CAAC;SACX;QACD,IAAI,QAAQ,IAAI,IAAI,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAChF,OAAO,GAAG,QAAQ,CAAC;YACnB,QAAQ,GAAG,SAAS,CAAC;YACrB,KAAK,GAAG,CAAC,CAAC;SACX;QACD,IAAM,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,OAAO,EAAE;YAChF,WAAW,EAAE,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;SACrD,CAAC,CAAC;QAEH,IAAM,GAAG,GAAG,cAAc,CAAC,KAAK,EAAE,gBAAgB,CAAC,CAAC;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,QAA4C,EAAE,KAAK,CAAC,CAAC;KACjF;IAtBe,eAAS,YAsBxB,CAAA;;;;;;;IAQD,SAAgB,SAAS,CAAC,KAAwB,EAAE,OAAuB;QACzE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;KAC9C;IAHe,eAAS,YAGxB,CAAA;;;;;;;IAQD,SAAgB,WAAW,CAAC,KAAe,EAAE,OAAuB;QAClE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;KAC9C;IAHe,iBAAW,cAG1B,CAAA;AACH,CAAC,EArHgB,KAAK,KAAL,KAAK;;ACxVtB;AAKA;IACI,QAAwB;AAE5B,IAAM,UAAU,GAAG,SAAS,EAA4B,CAAC;AACzD,IAAI,UAAU,CAAC,GAAG,EAAE;IAClB,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;CAC1B;KAAM;;IAEL,OAAO;QAGL,aAAY,KAA2B;YAA3B,sBAAA,EAAA,UAA2B;YACrC,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;YAElB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,IAAI;oBAAE,SAAS;gBAC/B,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACvB,IAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACrB,IAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;gBAEvB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;gBAGrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;aAC5D;SACF;QACD,mBAAK,GAAL;YACE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;YAChB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACnB;QACD,oBAAM,GAAN,UAAO,GAAW;YAChB,IAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;YAChC,IAAI,KAAK,IAAI,IAAI;gBAAE,OAAO,KAAK,CAAC;;YAEhC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;YAEzB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC;SACb;QACD,qBAAO,GAAP;YAAA,iBAYC;YAXC,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,OAAO;gBACL,IAAI,EAAE;oBACJ,IAAM,GAAG,GAAG,KAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;oBAChC,OAAO;wBACL,KAAK,EAAE,GAAG,KAAK,SAAS,GAAG,CAAC,GAAG,EAAE,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS;wBACjE,IAAI,EAAE,GAAG,KAAK,SAAS,GAAG,KAAK,GAAG,IAAI;qBACvC,CAAC;iBACH;aACF,CAAC;SACH;QACD,qBAAO,GAAP,UAAQ,QAAmE,EAAE,IAAW;YACtF,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC;YAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBAC1C,IAAM,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;gBAE1B,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;aACrD;SACF;QACD,iBAAG,GAAH,UAAI,GAAW;YACb,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;SAC5D;QACD,iBAAG,GAAH,UAAI,GAAW;YACb,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;SAClC;QACD,kBAAI,GAAJ;YAAA,iBAYC;YAXC,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,OAAO;gBACL,IAAI,EAAE;oBACJ,IAAM,GAAG,GAAG,KAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;oBAChC,OAAO;wBACL,KAAK,EAAE,GAAG,KAAK,SAAS,GAAG,GAAG,GAAG,SAAS;wBAC1C,IAAI,EAAE,GAAG,KAAK,SAAS,GAAG,KAAK,GAAG,IAAI;qBACvC,CAAC;iBACH;aACF,CAAC;SACH;QACD,iBAAG,GAAH,UAAI,GAAW,EAAE,KAAU;YACzB,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBACrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;gBAC5B,OAAO,IAAI,CAAC;aACb;;YAGD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;;YAGrB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3D,OAAO,IAAI,CAAC;SACb;QACD,oBAAM,GAAN;YAAA,iBAYC;YAXC,IAAI,KAAK,GAAG,CAAC,CAAC;YAEd,OAAO;gBACL,IAAI,EAAE;oBACJ,IAAM,GAAG,GAAG,KAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,CAAC;oBAChC,OAAO;wBACL,KAAK,EAAE,GAAG,KAAK,SAAS,GAAG,KAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,SAAS;wBAC1D,IAAI,EAAE,GAAG,KAAK,SAAS,GAAG,KAAK,GAAG,IAAI;qBACvC,CAAC;iBACH;aACF,CAAC;SACH;QACD,sBAAI,qBAAI;iBAAR;gBACE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;aAC1B;;;WAAA;QACH,UAAC;KAtGS,GAsGoB,CAAC;;;SC7GjBuP,qBAAmB,CACjC,MAAgB,EAChB,kBAA4B,EAC5B,eAAyB;IAEzB,IAAI,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IAExB,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,WAAW,IAAI,gBAAgB,CAC7B,CAAC,CAAC,QAAQ,EAAE,EACZ,MAAM,CAAC,CAAC,CAAC,EACT,kBAAkB,EAClB,IAAI,EACJ,eAAe,CAChB,CAAC;SACH;KACF;SAAM;;QAGL,IAAI,QAAO,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,CAAA,KAAK,UAAU,EAAE;YACxC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;SAC1B;;QAGD,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;YACxB,WAAW,IAAI,gBAAgB,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,eAAe,CAAC,CAAC;SAC/F;KACF;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;AACA,SAAS,gBAAgB,CACvB,IAAY;AACZ;AACA,KAAU,EACV,kBAA0B,EAC1B,OAAe,EACf,eAAuB;IAFvB,mCAAA,EAAA,0BAA0B;IAC1B,wBAAA,EAAA,eAAe;IACf,gCAAA,EAAA,uBAAuB;;IAGvB,IAAI,QAAO,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAA,KAAK,UAAU,EAAE;QACvC,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;KACxB;IAED,QAAQ,OAAO,KAAK;QAClB,KAAK,QAAQ;YACX,OAAO,CAAC,GAAGvP,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAM,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAC5F,KAAK,QAAQ;YACX,IACE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK;gBAC3B,KAAK,IAAIwP,UAAoB;gBAC7B,KAAK,IAAIC,UAAoB,EAC7B;gBACA,IAAI,KAAK,IAAIC,gBAAwB,IAAI,KAAK,IAAIC,gBAAwB,EAAE;;oBAE1E,OAAO,CAAC,IAAI,IAAI,IAAI,GAAG3P,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC3E;qBAAM;oBACL,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;iBAC3E;aACF;iBAAM;;gBAEL,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3E;QACH,KAAK,WAAW;YACd,IAAI,OAAO,IAAI,CAAC,eAAe;gBAC7B,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACtE,OAAO,CAAC,CAAC;QACX,KAAK,SAAS;YACZ,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5E,KAAK,QAAQ;YACX,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBACvF,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACrE;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,EAAE;gBACjF,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;aAC5E;iBAAM,IAAI,KAAK,YAAY,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;gBACjD,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3E;iBAAM,IACL,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC;gBACzB,KAAK,YAAY,WAAW;gBAC5B,gBAAgB,CAAC,KAAK,CAAC,EACvB;gBACA,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,UAAU,EACzF;aACH;iBAAM,IACL,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM;gBAC7B,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ;gBAC/B,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAClC;gBACA,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;aAC3E;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBAC9C,OAAO,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;aAC5E;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,EAAE;;gBAExC,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC9D,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;wBACvD,CAAC;wBACD,CAAC;wBACD,CAAC;wBACDA,QAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;wBAChD,CAAC;wBACDuP,qBAAmB,CAAC,KAAK,CAAC,KAAK,EAAE,kBAAkB,EAAE,eAAe,CAAC,EACrE;iBACH;qBAAM;oBACL,QACE,CAAC,IAAI,IAAI,IAAI,GAAGvP,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;wBACvD,CAAC;wBACD,CAAC;wBACDA,QAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,MAAM,CAAC;wBAChD,CAAC,EACD;iBACH;aACF;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,IAAM,MAAM,GAAW,KAAK,CAAC;;gBAE7B,IAAI,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC,kBAAkB,EAAE;oBACjD,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;yBACtD,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EACjC;iBACH;qBAAM;oBACL,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EACxF;iBACH;aACF;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;oBACvDA,QAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC;oBACtC,CAAC;oBACD,CAAC;oBACD,CAAC,EACD;aACH;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;;gBAEzC,IAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAClC;oBACE,IAAI,EAAE,KAAK,CAAC,UAAU;oBACtB,GAAG,EAAE,KAAK,CAAC,GAAG;iBACf,EACD,KAAK,CAAC,MAAM,CACb,CAAC;;gBAGF,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE;oBACpB,cAAc,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;iBAClC;gBAED,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;oBACvD,CAAC;oBACDuP,qBAAmB,CAAC,cAAc,EAAE,kBAAkB,EAAE,eAAe,CAAC,EACxE;aACH;iBAAM,IAAI,KAAK,YAAY,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACrD,QACE,CAAC,IAAI,IAAI,IAAI,GAAGvP,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;oBACvD,CAAC;oBACDA,QAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC;oBACvC,CAAC;qBACA,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;qBACrB,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;qBACzB,KAAK,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;oBACzB,CAAC,EACD;aACH;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBAC9C,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;oBACvD,CAAC;oBACDA,QAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;oBACxC,CAAC;oBACDA,QAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC;oBACxC,CAAC,EACD;aACH;iBAAM;gBACL,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;oBACvDuP,qBAAmB,CAAC,KAAK,EAAE,kBAAkB,EAAE,eAAe,CAAC;oBAC/D,CAAC,EACD;aACH;QACH,KAAK,UAAU;;YAEb,IAAI,KAAK,YAAY,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,EAAE;gBAC1F,QACE,CAAC,IAAI,IAAI,IAAI,GAAGvP,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;oBACvD,CAAC;oBACDA,QAAM,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC;oBACvC,CAAC;qBACA,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;qBACrB,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;qBACzB,KAAK,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC;oBACzB,CAAC,EACD;aACH;iBAAM;gBACL,IAAI,kBAAkB,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;oBACpF,QACE,CAAC,IAAI,IAAI,IAAI,GAAGA,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;wBACvD,CAAC;wBACD,CAAC;wBACD,CAAC;wBACDA,QAAM,CAAC,UAAU,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC;wBAC1D,CAAC;wBACDuP,qBAAmB,CAAC,KAAK,CAAC,KAAK,EAAE,kBAAkB,EAAE,eAAe,CAAC,EACrE;iBACH;qBAAM,IAAI,kBAAkB,EAAE;oBAC7B,QACE,CAAC,IAAI,IAAI,IAAI,GAAGvP,QAAM,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;wBACvD,CAAC;wBACD,CAAC;wBACDA,QAAM,CAAC,UAAU,CAAC,wBAAwB,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC;wBAC1D,CAAC,EACD;iBACH;aACF;KACJ;IAED,OAAO,CAAC,CAAC;AACX;;ACnOA,IAAM,SAAS,GAAG,IAAI,CAAC;AACvB,IAAM,cAAc,GAAG,IAAI,CAAC;AAC5B,IAAM,gBAAgB,GAAG,IAAI,CAAC;AAC9B,IAAM,eAAe,GAAG,IAAI,CAAC;AAC7B,IAAM,eAAe,GAAG,IAAI,CAAC;AAE7B,IAAM,YAAY,GAAG,IAAI,CAAC;AAC1B,IAAM,cAAc,GAAG,IAAI,CAAC;AAC5B,IAAM,aAAa,GAAG,IAAI,CAAC;AAC3B,IAAM,eAAe,GAAG,IAAI,CAAC;AAE7B;;;;;;SAMgB,YAAY,CAC1B,KAAkC,EAClC,KAAa,EACb,GAAW;IAEX,IAAI,YAAY,GAAG,CAAC,CAAC;IAErB,KAAK,IAAI,CAAC,GAAG,KAAK,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE;QACnC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,IAAI,YAAY,EAAE;YAChB,IAAI,CAAC,IAAI,GAAG,cAAc,MAAM,eAAe,EAAE;gBAC/C,OAAO,KAAK,CAAC;aACd;YACD,YAAY,IAAI,CAAC,CAAC;SACnB;aAAM,IAAI,IAAI,GAAG,SAAS,EAAE;YAC3B,IAAI,CAAC,IAAI,GAAG,gBAAgB,MAAM,YAAY,EAAE;gBAC9C,YAAY,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,CAAC,IAAI,GAAG,eAAe,MAAM,cAAc,EAAE;gBACtD,YAAY,GAAG,CAAC,CAAC;aAClB;iBAAM,IAAI,CAAC,IAAI,GAAG,eAAe,MAAM,aAAa,EAAE;gBACrD,YAAY,GAAG,CAAC,CAAC;aAClB;iBAAM;gBACL,OAAO,KAAK,CAAC;aACd;SACF;KACF;IAED,OAAO,CAAC,YAAY,CAAC;AACvB;;ACmBA;AACA,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAACyP,UAAoB,CAAC,CAAC;AAC9D,IAAM,eAAe,GAAG,IAAI,CAAC,UAAU,CAACD,UAAoB,CAAC,CAAC;AAE9D,IAAM,aAAa,GAAiC,EAAE,CAAC;SAEvCI,aAAW,CACzB,MAAc,EACd,OAA2B,EAC3B,OAAiB;IAEjB,OAAO,GAAG,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,OAAO,CAAC;IACzC,IAAM,KAAK,GAAG,OAAO,IAAI,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;;IAE3D,IAAM,IAAI,GACR,MAAM,CAAC,KAAK,CAAC;SACZ,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;SACvB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;SACxB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;IAE5B,IAAI,IAAI,GAAG,CAAC,EAAE;QACZ,MAAM,IAAI,SAAS,CAAC,qCAA8B,IAAI,CAAE,CAAC,CAAC;KAC3D;IAED,IAAI,OAAO,CAAC,gCAAgC,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE;QACpE,MAAM,IAAI,SAAS,CAAC,wBAAiB,MAAM,CAAC,MAAM,mCAAyB,IAAI,CAAE,CAAC,CAAC;KACpF;IAED,IAAI,CAAC,OAAO,CAAC,gCAAgC,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,EAAE;QACvE,MAAM,IAAI,SAAS,CAAC,wBAAiB,MAAM,CAAC,MAAM,iCAAuB,IAAI,CAAE,CAAC,CAAC;KAClF;IAED,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC,UAAU,EAAE;QACpC,MAAM,IAAI,SAAS,CACjB,qBAAc,IAAI,8BAAoB,KAAK,uCAA6B,MAAM,CAAC,UAAU,MAAG,CAC7F,CAAC;KACH;;IAGD,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,SAAS,CACjB,6EAA6E,CAC9E,CAAC;KACH;;IAGD,OAAO,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED,IAAM,gBAAgB,GAAG,uBAAuB,CAAC;AAEjD,SAAS,iBAAiB,CACxB,MAAc,EACd,KAAa,EACb,OAA2B,EAC3B,OAAe;IAAf,wBAAA,EAAA,eAAe;IAEf,IAAM,aAAa,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;IAC1F,IAAM,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAE7F,IAAM,WAAW,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,CAAC;;IAGnF,IAAM,GAAG,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;;IAG5D,IAAM,UAAU,GAAG,OAAO,OAAO,CAAC,YAAY,CAAC,KAAK,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,GAAG,KAAK,CAAC;;IAG9F,IAAM,cAAc,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,IAAI,GAAG,KAAK,GAAG,OAAO,CAAC,gBAAgB,CAAC,CAAC;IAC7F,IAAM,YAAY,GAAG,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,CAAC;IACtF,IAAM,aAAa,GAAG,OAAO,CAAC,eAAe,CAAC,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,CAAC,eAAe,CAAC,CAAC;;IAGzF,IAAM,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,IAAI,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,OAAO,CAAC,UAAU,CAAC;;IAGpF,IAAI,mBAAmB,GAAG,IAAI,CAAC;;IAE/B,IAAI,iBAA0B,CAAC;;IAE/B,IAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;;IAG9B,IAAM,iBAAiB,GAAG,UAAU,CAAC,IAAI,CAAC;IAC1C,IAAI,OAAO,iBAAiB,KAAK,SAAS,EAAE;QAC1C,iBAAiB,GAAG,iBAAiB,CAAC;KACvC;SAAM;QACL,mBAAmB,GAAG,KAAK,CAAC;QAC5B,IAAM,oBAAoB,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG;YAC3E,OAAO,iBAAiB,CAAC,GAAG,CAAC,CAAC;SAC/B,CAAC,CAAC;QACH,IAAI,oBAAoB,CAAC,MAAM,KAAK,CAAC,EAAE;YACrC,MAAM,IAAI,SAAS,CAAC,0CAA0C,CAAC,CAAC;SACjE;QACD,IAAI,OAAO,oBAAoB,CAAC,CAAC,CAAC,KAAK,SAAS,EAAE;YAChD,MAAM,IAAI,SAAS,CAAC,8DAA8D,CAAC,CAAC;SACrF;QACD,iBAAiB,GAAG,oBAAoB,CAAC,CAAC,CAAC,CAAC;;QAE5C,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,UAAA,IAAI,IAAI,OAAA,IAAI,KAAK,iBAAiB,GAAA,CAAC,EAAE;YACnE,MAAM,IAAI,SAAS,CAAC,sEAAsE,CAAC,CAAC;SAC7F;KACF;;IAGD,IAAI,CAAC,mBAAmB,EAAE;QACxB,KAAkB,UAA8B,EAA9B,KAAA,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,EAA9B,cAA8B,EAA9B,IAA8B,EAAE;YAA7C,IAAM,GAAG,SAAA;YACZ,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACtB;KACF;;IAGD,IAAM,UAAU,GAAG,KAAK,CAAC;;IAGzB,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC;QAAE,MAAM,IAAI,SAAS,CAAC,qCAAqC,CAAC,CAAC;;IAGlF,IAAM,IAAI,GACR,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;IAG/F,IAAI,IAAI,GAAG,CAAC,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM;QAAE,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;;IAGlF,IAAM,MAAM,GAAa,OAAO,GAAG,EAAE,GAAG,EAAE,CAAC;;IAE3C,IAAI,UAAU,GAAG,CAAC,CAAC;IACnB,IAAM,IAAI,GAAG,KAAK,CAAC;IAEnB,IAAI,eAAe,GAAG,OAAO,GAAG,KAAK,GAAG,IAAI,CAAC;;IAG7C,IAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC;IACnF,OAAO,CAAC,IAAI,EAAE;;QAEZ,IAAM,WAAW,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;QAGpC,IAAI,WAAW,KAAK,CAAC;YAAE,MAAM;;QAG7B,IAAI,CAAC,GAAG,KAAK,CAAC;;QAEd,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;YAC9C,CAAC,EAAE,CAAC;SACL;;QAGD,IAAI,CAAC,IAAI,MAAM,CAAC,UAAU;YAAE,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;;QAGtF,IAAM,IAAI,GAAG,OAAO,GAAG,UAAU,EAAE,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;;QAGxE,IAAI,iBAAiB,GAAG,IAAI,CAAC;QAC7B,IAAI,mBAAmB,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YAChD,iBAAiB,GAAG,iBAAiB,CAAC;SACvC;aAAM;YACL,iBAAiB,GAAG,CAAC,iBAAiB,CAAC;SACxC;QAED,IAAI,eAAe,KAAK,KAAK,IAAK,IAAe,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;YAC5D,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC,IAAc,CAAC,CAAC;SACzD;QACD,IAAI,KAAK,SAAA,CAAC;QAEV,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QAEd,IAAI,WAAW,KAAKC,gBAA0B,EAAE;YAC9C,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IACE,UAAU,IAAI,CAAC;gBACf,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK;gBAClC,MAAM,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,EACpC;gBACA,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAC;aAClD;YACD,KAAK,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,UAAU,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC;YACrF,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;SAC5B;aAAM,IAAI,WAAW,KAAKC,aAAuB,EAAE;YAClD,IAAM,GAAG,GAAG9P,QAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC7B,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;YACvC,KAAK,GAAG,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC1B,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;SACpB;aAAM,IAAI,WAAW,KAAK+P,aAAuB,IAAI,aAAa,KAAK,KAAK,EAAE;YAC7E,KAAK,GAAG,IAAI,KAAK,CACf,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAC7F,CAAC;SACH;aAAM,IAAI,WAAW,KAAKA,aAAuB,EAAE;YAClD,KAAK;gBACH,MAAM,CAAC,KAAK,EAAE,CAAC;qBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;qBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;qBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;SAC3B;aAAM,IAAI,WAAW,KAAKC,gBAA0B,IAAI,aAAa,KAAK,KAAK,EAAE;YAChF,KAAK,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;YACrD,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;SACnB;aAAM,IAAI,WAAW,KAAKA,gBAA0B,EAAE;YACrD,KAAK,GAAG,QAAQ,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACzC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;SACnB;aAAM,IAAI,WAAW,KAAKC,cAAwB,EAAE;YACnD,IAAM,OAAO,GACX,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IAAM,QAAQ,GACZ,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,KAAK,GAAG,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;SAC1D;aAAM,IAAI,WAAW,KAAKC,iBAA2B,EAAE;YACtD,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC5C,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;YACpD,KAAK,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,CAAC;SAC/B;aAAM,IAAI,WAAW,KAAKC,gBAA0B,EAAE;YACrD,IAAM,MAAM,GAAG,KAAK,CAAC;YACrB,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,CAAC;iBACZ,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;iBACvB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;iBACxB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YAC5B,IAAI,UAAU,IAAI,CAAC,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK;gBACvD,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAC;;YAG9D,IAAI,GAAG,EAAE;gBACP,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,UAAU,CAAC,CAAC;aACjD;iBAAM;gBACL,IAAI,aAAa,GAAG,OAAO,CAAC;gBAC5B,IAAI,CAAC,mBAAmB,EAAE;oBACxB,aAAa,uBAAQ,OAAO,KAAE,UAAU,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAE,CAAC;iBACzE;gBACD,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;aACjE;YAED,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;SAC5B;aAAM,IAAI,WAAW,KAAKC,eAAyB,EAAE;YACpD,IAAM,MAAM,GAAG,KAAK,CAAC;YACrB,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,CAAC;iBACZ,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;iBACvB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;iBACxB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;YAC5B,IAAI,YAAY,GAAG,OAAO,CAAC;;YAG3B,IAAM,SAAS,GAAG,KAAK,GAAG,UAAU,CAAC;;YAGrC,IAAI,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE;gBACpC,YAAY,GAAG,EAAE,CAAC;gBAClB,KAAK,IAAM,CAAC,IAAI,OAAO,EAAE;oBAErB,YAGD,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAA6B,CAAC,CAAC;iBAC/C;gBACD,YAAY,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC;aAC5B;YACD,IAAI,CAAC,mBAAmB,EAAE;gBACxB,YAAY,uBAAQ,YAAY,KAAE,UAAU,EAAE,EAAE,IAAI,EAAE,iBAAiB,EAAE,GAAE,CAAC;aAC7E;YACD,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC;YAC9D,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;YAE3B,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;gBAAE,MAAM,IAAI,SAAS,CAAC,+BAA+B,CAAC,CAAC;YAClF,IAAI,KAAK,KAAK,SAAS;gBAAE,MAAM,IAAI,SAAS,CAAC,sBAAsB,CAAC,CAAC;SACtE;aAAM,IAAI,WAAW,KAAKC,mBAA6B,EAAE;YACxD,KAAK,GAAG,SAAS,CAAC;SACnB;aAAM,IAAI,WAAW,KAAKC,cAAwB,EAAE;YACnD,KAAK,GAAG,IAAI,CAAC;SACd;aAAM,IAAI,WAAW,KAAKC,cAAwB,EAAE;;YAEnD,IAAM,OAAO,GACX,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IAAM,QAAQ,GACZ,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IAAM,IAAI,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;;YAEzC,IAAI,YAAY,IAAI,aAAa,KAAK,IAAI,EAAE;gBAC1C,KAAK;oBACH,IAAI,CAAC,eAAe,CAAC,eAAe,CAAC,IAAI,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC;0BAC7E,IAAI,CAAC,QAAQ,EAAE;0BACf,IAAI,CAAC;aACZ;iBAAM;gBACL,KAAK,GAAG,IAAI,CAAC;aACd;SACF;aAAM,IAAI,WAAW,KAAKC,oBAA8B,EAAE;;YAEzD,IAAM,KAAK,GAAGxQ,QAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;;YAE/B,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;;YAEzC,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;;YAEnB,IAAM,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,CAAyC,CAAC;;YAEjF,IAAI,UAAU,IAAI,UAAU,IAAI,OAAO,UAAU,CAAC,QAAQ,KAAK,UAAU,EAAE;gBACzE,KAAK,GAAG,UAAU,CAAC,QAAQ,EAAE,CAAC;aAC/B;iBAAM;gBACL,KAAK,GAAG,UAAU,CAAC;aACpB;SACF;aAAM,IAAI,WAAW,KAAKyQ,gBAA0B,EAAE;YACrD,IAAI,UAAU,GACZ,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IAAM,eAAe,GAAG,UAAU,CAAC;YACnC,IAAM,OAAO,GAAG,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;;YAGhC,IAAI,UAAU,GAAG,CAAC;gBAAE,MAAM,IAAI,SAAS,CAAC,yCAAyC,CAAC,CAAC;;YAGnF,IAAI,UAAU,GAAG,MAAM,CAAC,UAAU;gBAChC,MAAM,IAAI,SAAS,CAAC,4CAA4C,CAAC,CAAC;;YAGpE,IAAI,MAAM,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE;;gBAE3B,IAAI,OAAO,KAAK,MAAM,CAAC,kBAAkB,EAAE;oBACzC,UAAU;wBACR,MAAM,CAAC,KAAK,EAAE,CAAC;6BACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;6BACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;6BACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC1B,IAAI,UAAU,GAAG,CAAC;wBAChB,MAAM,IAAI,SAAS,CAAC,0DAA0D,CAAC,CAAC;oBAClF,IAAI,UAAU,GAAG,eAAe,GAAG,CAAC;wBAClC,MAAM,IAAI,SAAS,CAAC,6DAA6D,CAAC,CAAC;oBACrF,IAAI,UAAU,GAAG,eAAe,GAAG,CAAC;wBAClC,MAAM,IAAI,SAAS,CAAC,8DAA8D,CAAC,CAAC;iBACvF;gBAED,IAAI,cAAc,IAAI,aAAa,EAAE;oBACnC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,UAAU,CAAC,CAAC;iBACjD;qBAAM;oBACL,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;oBACrE,IAAI,OAAO,KAAKC,4BAAsC,EAAE;wBACtD,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;qBACxB;iBACF;aACF;iBAAM;gBACL,IAAM,OAAO,GAAG1Q,QAAM,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;;gBAEzC,IAAI,OAAO,KAAK,MAAM,CAAC,kBAAkB,EAAE;oBACzC,UAAU;wBACR,MAAM,CAAC,KAAK,EAAE,CAAC;6BACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;6BACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;6BACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;oBAC1B,IAAI,UAAU,GAAG,CAAC;wBAChB,MAAM,IAAI,SAAS,CAAC,0DAA0D,CAAC,CAAC;oBAClF,IAAI,UAAU,GAAG,eAAe,GAAG,CAAC;wBAClC,MAAM,IAAI,SAAS,CAAC,6DAA6D,CAAC,CAAC;oBACrF,IAAI,UAAU,GAAG,eAAe,GAAG,CAAC;wBAClC,MAAM,IAAI,SAAS,CAAC,8DAA8D,CAAC,CAAC;iBACvF;;gBAGD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,EAAE,CAAC,EAAE,EAAE;oBAC/B,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;iBAChC;gBAED,IAAI,cAAc,IAAI,aAAa,EAAE;oBACnC,KAAK,GAAG,OAAO,CAAC;iBACjB;qBAAM,IAAI,OAAO,KAAK0Q,4BAAsC,EAAE;oBAC7D,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;iBAC/E;qBAAM;oBACL,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,CAAC;iBACtE;aACF;;YAGD,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;SAC5B;aAAM,IAAI,WAAW,KAAKC,gBAA0B,IAAI,UAAU,KAAK,KAAK,EAAE;;YAE7E,CAAC,GAAG,KAAK,CAAC;;YAEV,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;gBAC9C,CAAC,EAAE,CAAC;aACL;;YAED,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;;YAElF,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;;YAEjD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;;YAGd,CAAC,GAAG,KAAK,CAAC;;YAEV,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;gBAC9C,CAAC,EAAE,CAAC;aACL;;YAED,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;;YAElF,IAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACxD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;;YAGd,IAAM,YAAY,GAAG,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;;YAGrD,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACzC,QAAQ,aAAa,CAAC,CAAC,CAAC;oBACtB,KAAK,GAAG;wBACN,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;wBACtB,MAAM;oBACR,KAAK,GAAG;wBACN,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;wBACtB,MAAM;oBACR,KAAK,GAAG;wBACN,YAAY,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;wBACtB,MAAM;iBACT;aACF;YAED,KAAK,GAAG,IAAI,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;SACnD;aAAM,IAAI,WAAW,KAAKA,gBAA0B,IAAI,UAAU,KAAK,IAAI,EAAE;;YAE5E,CAAC,GAAG,KAAK,CAAC;;YAEV,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;gBAC9C,CAAC,EAAE,CAAC;aACL;;YAED,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;;YAElF,IAAM,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACjD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;;YAGd,CAAC,GAAG,KAAK,CAAC;;YAEV,OAAO,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;gBAC9C,CAAC,EAAE,CAAC;aACL;;YAED,IAAI,CAAC,IAAI,MAAM,CAAC,MAAM;gBAAE,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;;YAElF,IAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;YACxD,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;;YAGd,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC;SAC/C;aAAM,IAAI,WAAW,KAAKC,gBAA0B,EAAE;YACrD,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IACE,UAAU,IAAI,CAAC;gBACf,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK;gBAClC,MAAM,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,EACpC;gBACA,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAC;aAClD;YACD,IAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,UAAU,GAAG,CAAC,EAAE,iBAAiB,CAAC,CAAC;YAC5F,KAAK,GAAG,aAAa,GAAG,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;YACxD,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;SAC5B;aAAM,IAAI,WAAW,KAAKC,mBAA6B,EAAE;YACxD,IAAM,OAAO,GACX,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IAAM,QAAQ,GACZ,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAE1B,KAAK,GAAG,IAAI,SAAS,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;SAC1C;aAAM,IAAI,WAAW,KAAKC,iBAA2B,EAAE;YACtD,KAAK,GAAG,IAAI,MAAM,EAAE,CAAC;SACtB;aAAM,IAAI,WAAW,KAAKC,iBAA2B,EAAE;YACtD,KAAK,GAAG,IAAI,MAAM,EAAE,CAAC;SACtB;aAAM,IAAI,WAAW,KAAKC,cAAwB,EAAE;YACnD,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;YAC1B,IACE,UAAU,IAAI,CAAC;gBACf,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK;gBAClC,MAAM,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,EACpC;gBACA,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAC;aAClD;YACD,IAAM,cAAc,GAAG,kBAAkB,CACvC,MAAM,EACN,KAAK,EACL,KAAK,GAAG,UAAU,GAAG,CAAC,EACtB,iBAAiB,CAClB,CAAC;;YAGF,IAAI,aAAa,EAAE;;gBAEjB,IAAI,cAAc,EAAE;;oBAElB,KAAK,GAAG,WAAW,CAAC,cAAc,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;iBAC5D;qBAAM;oBACL,KAAK,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC;iBACrC;aACF;iBAAM;gBACL,KAAK,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC;aAClC;;YAGD,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;SAC5B;aAAM,IAAI,WAAW,KAAKC,sBAAgC,EAAE;YAC3D,IAAM,SAAS,GACb,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;YAG1B,IAAI,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBAC7B,MAAM,IAAI,SAAS,CAAC,yDAAyD,CAAC,CAAC;aAChF;;YAGD,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;YAE1B,IACE,UAAU,IAAI,CAAC;gBACf,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK;gBAClC,MAAM,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC,EACpC;gBACA,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAC;aAClD;;YAGD,IAAM,cAAc,GAAG,kBAAkB,CACvC,MAAM,EACN,KAAK,EACL,KAAK,GAAG,UAAU,GAAG,CAAC,EACtB,iBAAiB,CAClB,CAAC;;YAEF,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;;YAE3B,IAAM,MAAM,GAAG,KAAK,CAAC;;YAErB,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,CAAC;iBACZ,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;iBACvB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;iBACxB,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;;YAE5B,IAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,CAAC,CAAC;;YAEtE,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;;YAG3B,IAAI,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE;gBAC/C,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;aAC/E;;YAGD,IAAI,SAAS,GAAG,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE;gBAC/C,MAAM,IAAI,SAAS,CAAC,2DAA2D,CAAC,CAAC;aAClF;;YAGD,IAAI,aAAa,EAAE;;gBAEjB,IAAI,cAAc,EAAE;;oBAElB,KAAK,GAAG,WAAW,CAAC,cAAc,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;iBAC5D;qBAAM;oBACL,KAAK,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC;iBACrC;gBAED,KAAK,CAAC,KAAK,GAAG,WAAW,CAAC;aAC3B;iBAAM;gBACL,KAAK,GAAG,IAAI,IAAI,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;aAC/C;SACF;aAAM,IAAI,WAAW,KAAKC,mBAA6B,EAAE;;YAExD,IAAM,UAAU,GACd,MAAM,CAAC,KAAK,EAAE,CAAC;iBACd,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,CAAC;iBACrB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC;iBACtB,MAAM,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;;YAE1B,IACE,UAAU,IAAI,CAAC;gBACf,UAAU,GAAG,MAAM,CAAC,MAAM,GAAG,KAAK;gBAClC,MAAM,CAAC,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC,KAAK,CAAC;gBAEpC,MAAM,IAAI,SAAS,CAAC,2BAA2B,CAAC,CAAC;;YAEnD,IAAI,UAAU,IAAI,IAAI,IAAI,UAAU,CAAC,IAAI,EAAE;gBACzC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC,EAAE;oBACxD,MAAM,IAAI,SAAS,CAAC,uCAAuC,CAAC,CAAC;iBAC9D;aACF;YACD,IAAM,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,UAAU,GAAG,CAAC,CAAC,CAAC;;YAEzE,KAAK,GAAG,KAAK,GAAG,UAAU,CAAC;;YAG3B,IAAM,SAAS,GAAGlR,QAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACnC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC;YAC7C,IAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,SAAS,CAAC,CAAC;;YAGpC,KAAK,GAAG,KAAK,GAAG,EAAE,CAAC;;YAGnB,KAAK,GAAG,IAAI,KAAK,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC;SACnC;aAAM;YACL,MAAM,IAAI,SAAS,CACjB,qCAA8B,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC,8BAAmB,IAAI,OAAG,CACjF,CAAC;SACH;QACD,IAAI,IAAI,KAAK,WAAW,EAAE;YACxB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;gBAClC,KAAK,OAAA;gBACL,QAAQ,EAAE,IAAI;gBACd,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;SACJ;aAAM;YACL,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;SACtB;KACF;;IAGD,IAAI,IAAI,KAAK,KAAK,GAAG,UAAU,EAAE;QAC/B,IAAI,OAAO;YAAE,MAAM,IAAI,SAAS,CAAC,oBAAoB,CAAC,CAAC;QACvD,MAAM,IAAI,SAAS,CAAC,qBAAqB,CAAC,CAAC;KAC5C;;IAGD,IAAI,CAAC,eAAe;QAAE,OAAO,MAAM,CAAC;IAEpC,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;QACvB,IAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAuB,CAAC;QAC7D,OAAO,IAAI,CAAC,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC;QAChB,OAAO,IAAI,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KAC7D;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;AAKA,SAAS,WAAW,CAClB,cAAsB,EACtB,aAA4C,EAC5C,MAAiB;;IAGjB,IAAI,CAAC,aAAa;QAAE,OAAO,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC;;IAExD,IAAI,aAAa,CAAC,cAAc,CAAC,IAAI,IAAI,EAAE;;QAEzC,aAAa,CAAC,cAAc,CAAC,GAAG,IAAI,QAAQ,CAAC,cAAc,CAAC,CAAC;KAC9D;;IAGD,OAAO,aAAa,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpD,CAAC;AAED,SAAS,kBAAkB,CACzB,MAAc,EACd,KAAa,EACb,GAAW,EACX,kBAA2B;IAE3B,IAAM,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;;IAElD,IAAI,kBAAkB,EAAE;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAI,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM,EAAE;gBAClC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE;oBACrC,MAAM,IAAI,SAAS,CAAC,uCAAuC,CAAC,CAAC;iBAC9D;gBACD,MAAM;aACP;SACF;KACF;IACD,OAAO,KAAK,CAAC;AACf;;ACpuBA,IAAM,MAAM,GAAG,MAAM,CAAC;AACtB,IAAM,UAAU,GAAG,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,CAAC,CAAC,CAAC;AAEnE;;;;;AAMA,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAa,EACb,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG6P,gBAA0B,CAAC;;IAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,GAAG,CAAC,CAAC;IACzC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;IAEtB,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;;IAE/D,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC;IAC9C,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,EAAE,IAAI,IAAI,CAAC;IAC9C,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;IAC7C,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC;;IAElC,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC;;IAEzB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACpB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,IAAM,iBAAiB,GAAG,IAAI,UAAU,CAAC,CAAC,CAAC,CAAC;AAC5C,IAAM,cAAc,GAAG,IAAI,QAAQ,CACjC,iBAAiB,CAAC,MAAM,EACxB,iBAAiB,CAAC,UAAU,EAC5B,iBAAiB,CAAC,UAAU,CAC7B,CAAC;AACF,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAa,EACb,KAAa,EACb,OAAiB;;;IAIjB,IACE,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;QACvB,KAAK,IAAIH,gBAAwB;QACjC,KAAK,IAAIC,gBAAwB,EACjC;;;QAGA,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGI,aAAuB,CAAC;;QAE1C,IAAM,oBAAoB,GAAG,CAAC,OAAO;cACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;cAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;QAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;QACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;QAEpB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;QAC/B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;QACtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC;QACvC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC;KACxC;SAAM;;QAEL,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGC,gBAA0B,CAAC;;QAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;cACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;cAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;QAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;QACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;QAEpB,cAAc,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;;QAErC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;KACnB;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,aAAa,CAAC,MAAc,EAAE,GAAW,EAAE,CAAU,EAAE,KAAa,EAAE,OAAiB;;IAE9F,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGM,cAAwB,CAAC;;IAG3C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAGjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACpB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,gBAAgB,CACvB,MAAc,EACd,GAAW,EACX,KAAc,EACd,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGJ,iBAA2B,CAAC;;IAE9C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEpB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,aAAa,CAAC,MAAc,EAAE,GAAW,EAAE,KAAW,EAAE,KAAa,EAAE,OAAiB;;IAE/F,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGD,cAAwB,CAAC;;IAE3C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAGpB,IAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;IACrD,IAAM,OAAO,GAAG,WAAW,CAAC,UAAU,EAAE,CAAC;IACzC,IAAM,QAAQ,GAAG,WAAW,CAAC,WAAW,EAAE,CAAC;;IAE3C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC;IACjC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC;IACxC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,IAAI,CAAC;IACzC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,IAAI,CAAC;;IAEzC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;IAClC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC;IACzC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC;IAC1C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC;IAC1C,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAa,EACb,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGU,gBAA0B,CAAC;;IAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAGjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACpB,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;QACtD,MAAM,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,MAAM,GAAG,8BAA8B,CAAC,CAAC;KACvE;;IAED,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;;IAErE,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;;IAEvB,IAAI,KAAK,CAAC,UAAU;QAAE,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IAC7C,IAAI,KAAK,CAAC,MAAM;QAAE,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACzC,IAAI,KAAK,CAAC,SAAS;QAAE,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;;IAG5C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACvB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,mBAAmB,CAC1B,MAAc,EACd,GAAW,EACX,KAAiB,EACjB,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGA,gBAA0B,CAAC;;IAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAGpB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;;;QAGvC,MAAM,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,OAAO,GAAG,8BAA8B,CAAC,CAAC;KAC1E;;IAGD,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;;IAEtE,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;;IAEvB,KAAK,GAAG,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;;IAEhG,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACvB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAsB,EACtB,KAAa,EACb,OAAiB;;IAGjB,IAAI,KAAK,KAAK,IAAI,EAAE;QAClB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGL,cAAwB,CAAC;KAC5C;SAAM,IAAI,KAAK,CAAC,SAAS,KAAK,QAAQ,EAAE;QACvC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGQ,iBAA2B,CAAC;KAC/C;SAAM;QACL,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGC,iBAA2B,CAAC;KAC/C;;IAGD,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACpB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CACxB,MAAc,EACd,GAAW,EACX,KAAe,EACf,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGjB,aAAuB,CAAC;;IAE1C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAGjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAGpB,IAAI,OAAO,KAAK,CAAC,EAAE,KAAK,QAAQ,EAAE;QAChC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;KACpD;SAAM,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;;;QAGjC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;KAC7C;SAAM;QACL,MAAM,IAAI,aAAa,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,2BAA2B,CAAC,CAAC;KAC3F;;IAGD,OAAO,KAAK,GAAG,EAAE,CAAC;AACpB,CAAC;AAED,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAA0B,EAC1B,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGW,gBAA0B,CAAC;;IAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEpB,IAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC;;IAE1B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAC9B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;IACtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;IAEtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGU,2BAAqC,CAAC;;IAExD,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;;IAEvC,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;IACrB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAe,EACf,KAAa,EACb,SAAiB,EACjB,KAAS,EACT,kBAA0B,EAC1B,eAAsB,EACtB,OAAe,EACf,IAAqB;IALrB,0BAAA,EAAA,iBAAiB;IACjB,sBAAA,EAAA,SAAS;IACT,mCAAA,EAAA,0BAA0B;IAC1B,gCAAA,EAAA,sBAAsB;IACtB,wBAAA,EAAA,eAAe;IACf,qBAAA,EAAA,SAAqB;IAErB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACpC,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,KAAK;YAAE,MAAM,IAAI,SAAS,CAAC,4BAA4B,CAAC,CAAC;KAC1E;;IAGD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;;IAEjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,GAAGf,eAAyB,GAAGD,gBAA0B,CAAC;;IAEhG,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACpB,IAAM,QAAQ,GAAG,aAAa,CAC5B,MAAM,EACN,KAAK,EACL,SAAS,EACT,KAAK,EACL,KAAK,GAAG,CAAC,EACT,kBAAkB,EAClB,eAAe,EACf,IAAI,CACL,CAAC;;IAEF,IAAI,CAAC,GAAG,EAAE,CAAC;IACX,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED,SAAS,mBAAmB,CAC1B,MAAc,EACd,GAAW,EACX,KAAiB,EACjB,KAAa,EACb,OAAiB;IAEjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGK,oBAA8B,CAAC;;IAEjD,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;;;IAIpB,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;IAC/C,OAAO,KAAK,GAAG,EAAE,CAAC;AACpB,CAAC;AAED,SAAS,aAAa,CAAC,MAAc,EAAE,GAAW,EAAE,KAAW,EAAE,KAAa,EAAE,OAAiB;;IAE/F,MAAM,CAAC,KAAK,EAAE,CAAC;QACb,KAAK,CAAC,SAAS,KAAK,MAAM,GAAGD,cAAwB,GAAGM,mBAA6B,CAAC;;IAExF,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEpB,IAAM,OAAO,GAAG,KAAK,CAAC,UAAU,EAAE,CAAC;IACnC,IAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;;IAErC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,OAAO,GAAG,IAAI,CAAC;IACjC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,CAAC,IAAI,IAAI,CAAC;IACxC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,IAAI,CAAC;IACzC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,IAAI,CAAC;;IAEzC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;IAClC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC;IACzC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC;IAC1C,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC;IAC1C,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,cAAc,CACrB,MAAc,EACd,GAAW,EACX,KAAqB,EACrB,KAAa,EACb,OAAiB;IAEjB,KAAK,GAAG,KAAK,CAAC,OAAO,EAAE,CAAC;;IAExB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGd,aAAuB,CAAC;;IAE1C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEpB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC;IAC/B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,IAAI,CAAC;IACtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC;IACvC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE,IAAI,IAAI,CAAC;IACvC,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAa,EACb,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGC,gBAA0B,CAAC;;IAG7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAGjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAGpB,cAAc,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAChD,MAAM,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;;IAGrC,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;IAClB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,iBAAiB,CACxB,MAAc,EACd,GAAW,EACX,KAAe,EACf,KAAa,EACb,UAAkB,EAClB,MAAU,EACV,OAAiB;IAEjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGgB,cAAwB,CAAC;;IAE3C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEpB,IAAM,cAAc,GAAG,wBAAwB,CAAC,KAAK,CAAC,CAAC;;IAGvD,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;IAE5E,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAC5B,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;IACvC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;IACxC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;IAExC,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;;IAE7B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IACpB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,aAAa,CACpB,MAAc,EACd,GAAW,EACX,KAAW,EACX,KAAa,EACb,SAAiB,EACjB,KAAS,EACT,kBAA0B,EAC1B,eAAsB,EACtB,OAAe;IAJf,0BAAA,EAAA,iBAAiB;IACjB,sBAAA,EAAA,SAAS;IACT,mCAAA,EAAA,0BAA0B;IAC1B,gCAAA,EAAA,sBAAsB;IACtB,wBAAA,EAAA,eAAe;IAEf,IAAI,KAAK,CAAC,KAAK,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,EAAE;;QAElD,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGC,sBAAgC,CAAC;;QAEnD,IAAM,oBAAoB,GAAG,CAAC,OAAO;cACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;cAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;QAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;QACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;QAGpB,IAAI,UAAU,GAAG,KAAK,CAAC;;;QAIvB,IAAM,cAAc,GAAG,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,GAAG,KAAK,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;;QAE3F,KAAK,GAAG,KAAK,GAAG,CAAC,CAAC;;QAElB,IAAM,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;QAEhF,MAAM,CAAC,KAAK,CAAC,GAAG,QAAQ,GAAG,IAAI,CAAC;QAChC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC;QAC3C,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC;QAC5C,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,QAAQ,IAAI,EAAE,IAAI,IAAI,CAAC;;QAE5C,MAAM,CAAC,KAAK,GAAG,CAAC,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;;QAErC,KAAK,GAAG,KAAK,GAAG,QAAQ,GAAG,CAAC,CAAC;;;QAI7B,IAAM,QAAQ,GAAG,aAAa,CAC5B,MAAM,EACN,KAAK,CAAC,KAAK,EACX,SAAS,EACT,KAAK,EACL,KAAK,GAAG,CAAC,EACT,kBAAkB,EAClB,eAAe,CAChB,CAAC;QACF,KAAK,GAAG,QAAQ,GAAG,CAAC,CAAC;;QAGrB,IAAM,SAAS,GAAG,QAAQ,GAAG,UAAU,CAAC;;QAGxC,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,SAAS,GAAG,IAAI,CAAC;QACxC,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC;QAC/C,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,CAAC;QAChD,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,SAAS,IAAI,EAAE,IAAI,IAAI,CAAC;;QAEhD,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;KACrB;SAAM;QACL,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGD,cAAwB,CAAC;;QAE3C,IAAM,oBAAoB,GAAG,CAAC,OAAO;cACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;cAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;QAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;QACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;QAEpB,IAAM,cAAc,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;;QAE7C,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;QAE5E,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;QAC5B,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;QACvC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;QACxC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;QAExC,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;;QAE7B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;KACrB;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAa,EACb,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGP,gBAA0B,CAAC;;IAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEpB,IAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAwB,CAAC;;IAEtD,IAAI,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC;;IAE1B,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,kBAAkB;QAAE,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;;IAElE,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAC9B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;IACtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;IAEtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;;IAGjC,IAAI,KAAK,CAAC,QAAQ,KAAK,MAAM,CAAC,kBAAkB,EAAE;QAChD,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC;QAChB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;QAC9B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;QACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;QACtC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;KACvC;;IAGD,MAAM,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;;IAExB,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC/B,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,MAAc,EACd,GAAW,EACX,KAAiB,EACjB,KAAa,EACb,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGG,gBAA0B,CAAC;;IAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAEjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;;IAEpB,IAAM,IAAI,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;;IAEzE,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAC5B,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;IACvC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;IACxC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;IAExC,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;;IAE7B,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;IACvB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,cAAc,CACrB,MAAc,EACd,GAAW,EACX,KAAY,EACZ,KAAa,EACb,KAAa,EACb,kBAA2B,EAC3B,OAAiB;;IAGjB,MAAM,CAAC,KAAK,EAAE,CAAC,GAAGT,gBAA0B,CAAC;;IAE7C,IAAM,oBAAoB,GAAG,CAAC,OAAO;UACjC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;UAC3C,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;;IAGjD,KAAK,GAAG,KAAK,GAAG,oBAAoB,CAAC;IACrC,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC;IAEpB,IAAI,UAAU,GAAG,KAAK,CAAC;IACvB,IAAI,MAAM,GAAc;QACtB,IAAI,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,SAAS;QACzC,GAAG,EAAE,KAAK,CAAC,GAAG;KACf,CAAC;IAEF,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,EAAE;QACpB,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,EAAE,CAAC;KACvB;IAED,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAC7C,IAAM,QAAQ,GAAG,aAAa,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,CAAC,EAAE,kBAAkB,CAAC,CAAC;;IAG5F,IAAM,IAAI,GAAG,QAAQ,GAAG,UAAU,CAAC;;IAEnC,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IACnC,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;IAC1C,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;IAC3C,MAAM,CAAC,UAAU,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;;IAE3C,OAAO,QAAQ,CAAC;AAClB,CAAC;SAEe,aAAa,CAC3B,MAAc,EACd,MAAgB,EAChB,SAAiB,EACjB,aAAiB,EACjB,KAAS,EACT,kBAA0B,EAC1B,eAAsB,EACtB,IAAqB;IALrB,0BAAA,EAAA,iBAAiB;IACjB,8BAAA,EAAA,iBAAiB;IACjB,sBAAA,EAAA,SAAS;IACT,mCAAA,EAAA,0BAA0B;IAC1B,gCAAA,EAAA,sBAAsB;IACtB,qBAAA,EAAA,SAAqB;IAErB,aAAa,GAAG,aAAa,IAAI,CAAC,CAAC;IACnC,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;;IAGlB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;;IAGlB,IAAI,KAAK,GAAG,aAAa,GAAG,CAAC,CAAC;;IAG9B,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;;QAEzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,GAAG,GAAG,UAAG,CAAC,CAAE,CAAC;YACnB,IAAI,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;;YAGtB,IAAI,QAAO,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAA,KAAK,UAAU,EAAE;gBACvC,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;aACxB;YAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBAC7B,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBACpC,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;gBACpC,MAAM,IAAI,aAAa,CAAC,gDAAgD,CAAC,CAAC;aAC3E;iBAAM,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;gBACrC,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC3D;iBAAM,IAAI,KAAK,YAAY,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;gBACjD,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,KAAK,SAAS,EAAE;gBAC9B,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,KAAK,IAAI,EAAE;gBACzB,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,EAAE;gBACjF,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC5D;iBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;gBAC9B,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,KAAK,YAAY,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACrD,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE;gBAClE,KAAK,GAAG,eAAe,CACrB,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,eAAe,EACf,IAAI,EACJ,IAAI,CACL,CAAC;aACH;iBAAM,IACL,OAAO,KAAK,KAAK,QAAQ;gBACzB,UAAU,CAAC,KAAK,CAAC;gBACjB,KAAK,CAAC,SAAS,KAAK,YAAY,EAChC;gBACA,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC9D;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;gBAC9E,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,kBAAkB,EAAE;gBAC5D,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC9E;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,EAAE;gBACxC,KAAK,GAAG,aAAa,CACnB,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,eAAe,EACf,IAAI,CACL,CAAC;aACH;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;gBACzC,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,EAAE,IAAI,CAAC,CAAC;aACpF;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBAC9C,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC9D;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;gBACzC,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aACzD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC7E,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;aAC1D;iBAAM,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;gBACpD,MAAM,IAAI,aAAa,CAAC,6CAAsC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC,CAAC;aAC7F;SACF;KACF;SAAM,IAAI,MAAM,YAAYiB,OAAG,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;QACjD,IAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAClC,IAAI,IAAI,GAAG,KAAK,CAAC;QAEjB,OAAO,CAAC,IAAI,EAAE;;YAEZ,IAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC9B,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;;YAEpB,IAAI,IAAI;gBAAE,SAAS;;YAGnB,IAAM,GAAG,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;YAG7B,IAAM,IAAI,GAAG,OAAO,KAAK,CAAC;;YAG1B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACnD,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;;;oBAG7B,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,8BAA8B,CAAC,CAAC;iBAC5D;gBAED,IAAI,SAAS,EAAE;oBACb,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;wBAClB,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,0BAA0B,CAAC,CAAC;qBACxD;yBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;wBAC5B,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,uBAAuB,CAAC,CAAC;qBACrD;iBACF;aACF;YAED,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAC5B,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,eAAe,CAAC,KAAK,CAAC,IAAI,gBAAgB,CAAC,KAAK,CAAC,EAAE;gBACjF,MAAM,IAAI,aAAa,CAAC,gDAAgD,CAAC,CAAC;aAC3E;iBAAM,IAAI,IAAI,KAAK,SAAS,EAAE;gBAC7B,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACrD;iBAAM,IAAI,KAAK,YAAY,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;gBACjD,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAClD;iBAAM,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,SAAS,IAAI,eAAe,KAAK,KAAK,CAAC,EAAE;gBAC/E,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAClD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,EAAE;gBACjF,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACtD;iBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;gBAC9B,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,YAAY,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACrD,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE;gBAC1D,KAAK,GAAG,eAAe,CACrB,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,eAAe,EACf,KAAK,EACL,IAAI,CACL,CAAC;aACH;iBAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBACnE,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;gBAC9E,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAClD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,EAAE;gBACxC,KAAK,GAAG,aAAa,CACnB,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,eAAe,CAChB,CAAC;aACH;iBAAM,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,kBAAkB,EAAE;gBAC5D,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;aAC5F;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;gBACzC,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;aAC9E;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBAC9C,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;gBACzC,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACnD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC7E,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;gBACpD,MAAM,IAAI,aAAa,CAAC,6CAAsC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC,CAAC;aAC7F;SACF;KACF;SAAM;QACL,IAAI,QAAO,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,CAAA,KAAK,UAAU,EAAE;;YAExC,MAAM,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;YACzB,IAAI,MAAM,IAAI,IAAI,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAChD,MAAM,IAAI,aAAa,CAAC,0CAA0C,CAAC,CAAC;aACrE;SACF;;QAGD,KAAK,IAAM,GAAG,IAAI,MAAM,EAAE;YACxB,IAAI,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;;YAExB,IAAI,QAAO,KAAK,aAAL,KAAK,uBAAL,KAAK,CAAE,MAAM,CAAA,KAAK,UAAU,EAAE;gBACvC,KAAK,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;aACxB;;YAGD,IAAM,IAAI,GAAG,OAAO,KAAK,CAAC;;YAG1B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;gBACnD,IAAI,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,IAAI,EAAE;;;oBAG7B,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,8BAA8B,CAAC,CAAC;iBAC5D;gBAED,IAAI,SAAS,EAAE;oBACb,IAAI,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;wBAClB,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,0BAA0B,CAAC,CAAC;qBACxD;yBAAM,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;wBAC5B,MAAM,KAAK,CAAC,MAAM,GAAG,GAAG,GAAG,uBAAuB,CAAC,CAAC;qBACrD;iBACF;aACF;YAED,IAAI,IAAI,KAAK,QAAQ,EAAE;gBACrB,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAC5B,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,IAAI,KAAK,QAAQ,EAAE;gBAC5B,MAAM,IAAI,aAAa,CAAC,gDAAgD,CAAC,CAAC;aAC3E;iBAAM,IAAI,IAAI,KAAK,SAAS,EAAE;gBAC7B,KAAK,GAAG,gBAAgB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACrD;iBAAM,IAAI,KAAK,YAAY,IAAI,IAAI,MAAM,CAAC,KAAK,CAAC,EAAE;gBACjD,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAClD;iBAAM,IAAI,KAAK,KAAK,SAAS,EAAE;gBAC9B,IAAI,eAAe,KAAK,KAAK;oBAAE,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACjF;iBAAM,IAAI,KAAK,KAAK,IAAI,EAAE;gBACzB,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAClD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,UAAU,EAAE;gBACjF,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACtD;iBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,EAAE;gBAC9B,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,YAAY,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;gBACrD,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE;gBAC1D,KAAK,GAAG,eAAe,CACrB,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,eAAe,EACf,KAAK,EACL,IAAI,CACL,CAAC;aACH;iBAAM,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBACnE,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;gBAC9E,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aAClD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,MAAM,EAAE;gBACxC,KAAK,GAAG,aAAa,CACnB,MAAM,EACN,GAAG,EACH,KAAK,EACL,KAAK,EACL,SAAS,EACT,KAAK,EACL,kBAAkB,EAClB,eAAe,CAChB,CAAC;aACH;iBAAM,IAAI,OAAO,KAAK,KAAK,UAAU,IAAI,kBAAkB,EAAE;gBAC5D,KAAK,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;aAC5F;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC1C,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;gBACzC,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,kBAAkB,CAAC,CAAC;aAC9E;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,YAAY,EAAE;gBAC9C,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACxD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,OAAO,EAAE;gBACzC,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACnD;iBAAM,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,IAAI,KAAK,CAAC,WAAW,CAAC,KAAK,QAAQ,EAAE;gBAC7E,KAAK,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;aACpD;iBAAM,IAAI,OAAO,KAAK,CAAC,WAAW,CAAC,KAAK,WAAW,EAAE;gBACpD,MAAM,IAAI,aAAa,CAAC,6CAAsC,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAE,CAAC,CAAC;aAC7F;SACF;KACF;;IAGD,IAAI,CAAC,GAAG,EAAE,CAAC;;IAGX,MAAM,CAAC,KAAK,EAAE,CAAC,GAAG,IAAI,CAAC;;IAGvB,IAAM,IAAI,GAAG,KAAK,GAAG,aAAa,CAAC;;IAEnC,MAAM,CAAC,aAAa,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IACtC,MAAM,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC;IAC7C,MAAM,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;IAC9C,MAAM,CAAC,aAAa,EAAE,CAAC,GAAG,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,CAAC;IAC9C,OAAO,KAAK,CAAC;AACf;;AC38BA;AACA;AACA,IAAM,OAAO,GAAG,IAAI,GAAG,IAAI,GAAG,EAAE,CAAC;AAEjC;AACA,IAAI,MAAM,GAAGpR,QAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AAEnC;;;;;;SAMgB,qBAAqB,CAAC,IAAY;;IAEhD,IAAI,MAAM,CAAC,MAAM,GAAG,IAAI,EAAE;QACxB,MAAM,GAAGA,QAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;KAC7B;AACH,CAAC;AAED;;;;;;;SAOgB,SAAS,CAAC,MAAgB,EAAE,OAA8B;IAA9B,wBAAA,EAAA,YAA8B;;IAExE,IAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IACrF,IAAM,kBAAkB,GACtB,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,GAAG,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACvF,IAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,GAAG,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;IAChF,IAAM,qBAAqB,GACzB,OAAO,OAAO,CAAC,qBAAqB,KAAK,QAAQ,GAAG,OAAO,CAAC,qBAAqB,GAAG,OAAO,CAAC;;IAG9F,IAAI,MAAM,CAAC,MAAM,GAAG,qBAAqB,EAAE;QACzC,MAAM,GAAGA,QAAM,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC;KAC9C;;IAGD,IAAM,kBAAkB,GAAGqR,aAAiB,CAC1C,MAAM,EACN,MAAM,EACN,SAAS,EACT,CAAC,EACD,CAAC,EACD,kBAAkB,EAClB,eAAe,EACf,EAAE,CACH,CAAC;;IAGF,IAAM,cAAc,GAAGrR,QAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAC;;IAGxD,MAAM,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,EAAE,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;;IAGzD,OAAO,cAAc,CAAC;AACxB,CAAC;AAED;;;;;;;;;SASgB,2BAA2B,CACzC,MAAgB,EAChB,WAAmB,EACnB,OAA8B;IAA9B,wBAAA,EAAA,YAA8B;;IAG9B,IAAM,SAAS,GAAG,OAAO,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;IACrF,IAAM,kBAAkB,GACtB,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,GAAG,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACvF,IAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,GAAG,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;IAChF,IAAM,UAAU,GAAG,OAAO,OAAO,CAAC,KAAK,KAAK,QAAQ,GAAG,OAAO,CAAC,KAAK,GAAG,CAAC,CAAC;;IAGzE,IAAM,kBAAkB,GAAGqR,aAAiB,CAC1C,MAAM,EACN,MAAM,EACN,SAAS,EACT,CAAC,EACD,CAAC,EACD,kBAAkB,EAClB,eAAe,CAChB,CAAC;IACF,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,EAAE,CAAC,EAAE,kBAAkB,CAAC,CAAC;;IAG5D,OAAO,UAAU,GAAG,kBAAkB,GAAG,CAAC,CAAC;AAC7C,CAAC;AAED;;;;;;;SAOgB,WAAW,CACzB,MAA8C,EAC9C,OAAgC;IAAhC,wBAAA,EAAA,YAAgC;IAEhC,OAAOC,aAAmB,CAAC,MAAM,YAAYtR,QAAM,GAAG,MAAM,GAAG,YAAY,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,CAAC;AAChG,CAAC;AAQD;;;;;;;SAOgB,mBAAmB,CACjC,MAAgB,EAChB,OAAwC;IAAxC,wBAAA,EAAA,YAAwC;IAExC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;IAExB,IAAM,kBAAkB,GACtB,OAAO,OAAO,CAAC,kBAAkB,KAAK,SAAS,GAAG,OAAO,CAAC,kBAAkB,GAAG,KAAK,CAAC;IACvF,IAAM,eAAe,GACnB,OAAO,OAAO,CAAC,eAAe,KAAK,SAAS,GAAG,OAAO,CAAC,eAAe,GAAG,IAAI,CAAC;IAEhF,OAAOuR,qBAA2B,CAAC,MAAM,EAAE,kBAAkB,EAAE,eAAe,CAAC,CAAC;AAClF,CAAC;AAED;;;;;;;;;;;;SAYgB,iBAAiB,CAC/B,IAA4C,EAC5C,UAAkB,EAClB,iBAAyB,EACzB,SAAqB,EACrB,aAAqB,EACrB,OAA2B;IAE3B,IAAM,eAAe,GAAG,MAAM,CAAC,MAAM,CACnC,EAAE,gCAAgC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EACpD,OAAO,CACR,CAAC;IACF,IAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAEtC,IAAI,KAAK,GAAG,UAAU,CAAC;;IAEvB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,EAAE,CAAC,EAAE,EAAE;;QAE1C,IAAM,IAAI,GACR,UAAU,CAAC,KAAK,CAAC;aAChB,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;aAC3B,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;aAC5B,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC;;QAEhC,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;;QAE9B,SAAS,CAAC,aAAa,GAAG,CAAC,CAAC,GAAGD,aAAmB,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;;QAEhF,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC;KACtB;;IAGD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;;IAQM,IAAI,GAAG;IACX,MAAM,QAAA;IACN,IAAI,MAAA;IACJ,KAAK,OAAA;IACL,UAAU,YAAA;IACV,MAAM,QAAA;IACN,KAAK,OAAA;IACL,IAAI,MAAA;IACJ,IAAI,MAAA;IACJ,GAAG,SAAA;IACH,MAAM,QAAA;IACN,MAAM,QAAA;IACN,QAAQ,UAAA;IACR,QAAQ,EAAE,QAAQ;IAClB,UAAU,YAAA;IACV,UAAU,YAAA;IACV,SAAS,WAAA;IACT,KAAK,OAAA;IACL,qBAAqB,uBAAA;IACrB,SAAS,WAAA;IACT,2BAA2B,6BAAA;IAC3B,WAAW,aAAA;IACX,mBAAmB,qBAAA;IACnB,iBAAiB,mBAAA;IACjB,SAAS,WAAA;IACT,aAAa,eAAA;;;;;;"}