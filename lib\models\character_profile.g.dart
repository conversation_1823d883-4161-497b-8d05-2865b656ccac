// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'character_profile.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class CharacterProfileAdapter extends TypeAdapter<CharacterProfile> {
  @override
  final int typeId = 17;

  @override
  CharacterProfile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CharacterProfile(
      id: fields[0] as String?,
      novelId: fields[1] as String,
      characterId: fields[2] as String,
      baseInfo: fields[3] as CharacterCard,
      developments: (fields[4] as List?)?.cast<CharacterDevelopment>(),
      relationships: (fields[5] as Map?)?.cast<String, String>(),
      createdAt: fields[6] as DateTime?,
      updatedAt: fields[7] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, CharacterProfile obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.novelId)
      ..writeByte(2)
      ..write(obj.characterId)
      ..writeByte(3)
      ..write(obj.baseInfo)
      ..writeByte(4)
      ..write(obj.developments)
      ..writeByte(5)
      ..write(obj.relationships)
      ..writeByte(6)
      ..write(obj.createdAt)
      ..writeByte(7)
      ..write(obj.updatedAt);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CharacterProfileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class CharacterDevelopmentAdapter extends TypeAdapter<CharacterDevelopment> {
  @override
  final int typeId = 18;

  @override
  CharacterDevelopment read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return CharacterDevelopment(
      chapterNumber: fields[0] as int,
      development: fields[1] as String,
      timestamp: fields[2] as DateTime?,
    );
  }

  @override
  void write(BinaryWriter writer, CharacterDevelopment obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.chapterNumber)
      ..writeByte(1)
      ..write(obj.development)
      ..writeByte(2)
      ..write(obj.timestamp);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CharacterDevelopmentAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
