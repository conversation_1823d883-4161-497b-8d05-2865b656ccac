{"version": 3, "file": "anchor.js", "sources": ["../../../../../../packages/components/anchor/src/anchor.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Anchor from './anchor.vue'\n\nexport const anchorProps = buildProps({\n  /**\n   * @description scroll container\n   */\n  container: {\n    type: definePropType<string | HTMLElement | Window | null>([\n      String,\n      Object,\n    ]),\n  },\n  /**\n   * @description Set the offset of the anchor scroll\n   */\n  offset: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description The offset of the element starting to trigger the anchor\n   */\n  bound: {\n    type: Number,\n    default: 15,\n  },\n  /**\n   * @description Set the scroll duration of the container when the anchor is clicked, in milliseconds\n   */\n  duration: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description Whether to show the marker\n   */\n  marker: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Set Anchor type\n   */\n  type: {\n    type: definePropType<'default' | 'underline'>(String),\n    default: 'default',\n  },\n  /**\n   * @description Set Anchor direction\n   */\n  direction: {\n    type: definePropType<'vertical' | 'horizontal'>(String),\n    default: 'vertical',\n  },\n  /**\n   * @description Scroll whether link is selected at the top\n   */\n  selectScrollTop: Boolean,\n})\n\nexport type AnchorProps = ExtractPropTypes<typeof anchorProps>\nexport type AnchorPropsPublic = __ExtractPublicPropTypes<typeof anchorProps>\nexport type AnchorInstance = InstanceType<typeof Anchor> & unknown\n\nexport const anchorEmits = {\n  change: (href: string) => isString(href),\n  click: (e: MouseEvent, href?: string) =>\n    e instanceof MouseEvent && (isString(href) || isUndefined(href)),\n}\nexport type AnchorEmits = typeof anchorEmits\n"], "names": ["buildProps", "definePropType", "isString", "isUndefined"], "mappings": ";;;;;;;;AAMY,MAAC,WAAW,GAAGA,kBAAU,CAAC;AACtC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,sBAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,UAAU;AACvB,GAAG;AACH,EAAE,eAAe,EAAE,OAAO;AAC1B,CAAC,EAAE;AACS,MAAC,WAAW,GAAG;AAC3B,EAAE,MAAM,EAAE,CAAC,IAAI,KAAKC,eAAQ,CAAC,IAAI,CAAC;AAClC,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,KAAK,CAAC,YAAY,UAAU,KAAKA,eAAQ,CAAC,IAAI,CAAC,IAAIC,iBAAW,CAAC,IAAI,CAAC,CAAC;AACtF;;;;;"}