#!/usr/bin/env node

// 尝试从不同位置加载CloudBase SDK
let tcb;
try {
  tcb = require('@cloudbase/node-sdk');
} catch (error) {
  try {
    tcb = require('./cloudbase-deploy/node_modules/@cloudbase/node-sdk');
  } catch (error2) {
    console.error('无法找到 @cloudbase/node-sdk');
    console.error('请确保已安装 CloudBase SDK:');
    console.error('  npm install @cloudbase/node-sdk');
    console.error('或者在 cloudbase-deploy 目录中运行此脚本');
    process.exit(1);
  }
}

const readline = require('readline');

// 初始化CloudBase SDK
const app = tcb.init({
  env: process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8'
});

const db = app.database();

// 检查是否在云函数环境中运行
const isCloudFunction = !!process.env.SCF_RUNTIME;

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 生成会员码
function generateMemberCode(prefix = 'VIP', length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 检查会员码是否存在
async function codeExists(code) {
  try {
    const result = await db.collection('memberData').where({ code }).get();
    return result.data.length > 0;
  } catch (error) {
    console.error('检查会员码失败:', error);
    return true; // 出错时假设存在，避免重复
  }
}

// 创建单个会员码
async function createMemberCode(options = {}) {
  const {
    packageId = 'pkg_permanent',
    customCode = null,
    expireAt = null,
    batchId = `batch_${Date.now()}`,
    prefix = 'VIP'
  } = options;
  
  try {
    // 生成或使用自定义会员码
    let code = customCode;
    if (!code) {
      let attempts = 0;
      do {
        code = generateMemberCode(prefix);
        attempts++;
        if (attempts > 100) {
          console.error('生成唯一会员码失败，请重试');
          return null;
        }
      } while (await codeExists(code));
    } else if (await codeExists(code)) {
      console.error(`错误: 会员码 "${code}" 已存在`);
      return null;
    }
    
    const newMemberCode = {
      code,
      packageId,
      isUsed: false,
      usedBy: null,
      usedAt: null,
      expireAt: expireAt,
      batchId: batchId,
      createdAt: new Date().toISOString()
    };
    
    const result = await db.collection('memberData').add(newMemberCode);
    
    console.log('✅ 会员码创建成功:');
    console.log(`   会员码: ${code}`);
    console.log(`   套餐: ${packageId}`);
    console.log(`   批次: ${batchId}`);
    console.log(`   数据库ID: ${result.id}`);
    if (expireAt) {
      console.log(`   过期时间: ${expireAt}`);
    }
    
    return { ...newMemberCode, _id: result.id };
  } catch (error) {
    console.error('创建会员码失败:', error);
    return null;
  }
}

// 批量创建会员码
async function batchCreateMemberCodes(options = {}) {
  const {
    packageId = 'pkg_permanent',
    count = 10,
    expireAt = null,
    batchId = `batch_${Date.now()}`,
    prefix = 'VIP'
  } = options;
  
  const createdCodes = [];
  
  console.log(`开始批量创建 ${count} 个会员码...`);
  
  for (let i = 0; i < count; i++) {
    let code;
    let attempts = 0;
    
    do {
      code = generateMemberCode(prefix);
      attempts++;
      if (attempts > 100) {
        console.error(`第 ${i + 1} 个会员码生成失败`);
        break;
      }
    } while (await codeExists(code));
    
    if (attempts <= 100) {
      try {
        const newMemberCode = {
          code,
          packageId,
          isUsed: false,
          usedBy: null,
          usedAt: null,
          expireAt: expireAt,
          batchId: batchId,
          createdAt: new Date().toISOString()
        };
        
        const result = await db.collection('memberData').add(newMemberCode);
        createdCodes.push({ ...newMemberCode, _id: result.id });
        
        console.log(`   ${i + 1}. ${code} - 创建成功`);
      } catch (error) {
        console.error(`第 ${i + 1} 个会员码保存失败:`, error);
      }
    }
  }
  
  console.log(`✅ 成功创建 ${createdCodes.length} 个会员码:`);
  console.log(`   批次ID: ${batchId}`);
  console.log(`   套餐: ${packageId}`);
  if (expireAt) {
    console.log(`   过期时间: ${expireAt}`);
  }
  
  return createdCodes;
}

// 列出会员码
async function listMemberCodes(options = {}) {
  const {
    isUsed = null,
    packageId = null,
    batchId = null,
    limit = 20
  } = options;
  
  try {
    let query = db.collection('memberData');
    
    // 构建查询条件
    let conditions = {};
    if (isUsed !== null) {
      conditions.isUsed = isUsed;
    }
    if (packageId) {
      conditions.packageId = packageId;
    }
    if (batchId) {
      conditions.batchId = batchId;
    }
    
    if (Object.keys(conditions).length > 0) {
      query = query.where(conditions);
    }
    
    const result = await query
      .orderBy('createdAt', 'desc')
      .limit(limit)
      .get();
    
    const memberCodes = result.data;
    
    console.log(`找到 ${memberCodes.length} 个会员码:`);
    console.log('');
    
    memberCodes.forEach((code, index) => {
      const status = code.isUsed ? '已使用' : '未使用';
      const expireInfo = code.expireAt ? ` (过期: ${code.expireAt})` : '';
      const usedInfo = code.isUsed ? ` (使用者: ${code.usedBy}, 使用时间: ${code.usedAt})` : '';
      
      console.log(`${index + 1}. ${code.code} - ${status}${expireInfo}`);
      console.log(`   套餐: ${code.packageId} | 批次: ${code.batchId}${usedInfo}`);
      console.log(`   数据库ID: ${code._id}`);
      console.log('');
    });
    
    return memberCodes;
  } catch (error) {
    console.error('获取会员码列表失败:', error);
    return [];
  }
}

// 获取统计信息
async function getStats() {
  try {
    const result = await db.collection('memberData').get();
    const memberCodes = result.data;
    
    const stats = {
      total: memberCodes.length,
      used: memberCodes.filter(code => code.isUsed).length,
      unused: memberCodes.filter(code => !code.isUsed).length,
      expired: memberCodes.filter(code => 
        code.expireAt && new Date(code.expireAt) < new Date()
      ).length
    };
    
    console.log('📊 会员码统计信息:');
    console.log(`   总数: ${stats.total}`);
    console.log(`   已使用: ${stats.used}`);
    console.log(`   未使用: ${stats.unused}`);
    console.log(`   已过期: ${stats.expired}`);
    
    // 按套餐统计
    const byPackage = {};
    memberCodes.forEach(code => {
      if (!byPackage[code.packageId]) {
        byPackage[code.packageId] = { total: 0, used: 0, unused: 0 };
      }
      byPackage[code.packageId].total++;
      if (code.isUsed) {
        byPackage[code.packageId].used++;
      } else {
        byPackage[code.packageId].unused++;
      }
    });
    
    console.log('\n按套餐统计:');
    Object.entries(byPackage).forEach(([packageId, stats]) => {
      console.log(`   ${packageId}: 总数 ${stats.total}, 已使用 ${stats.used}, 未使用 ${stats.unused}`);
    });
    
    // 按批次统计（只显示前10个）
    const byBatch = {};
    memberCodes.forEach(code => {
      if (!byBatch[code.batchId]) {
        byBatch[code.batchId] = { total: 0, used: 0, unused: 0 };
      }
      byBatch[code.batchId].total++;
      if (code.isUsed) {
        byBatch[code.batchId].used++;
      } else {
        byBatch[code.batchId].unused++;
      }
    });
    
    console.log('\n按批次统计 (前10个):');
    Object.entries(byBatch)
      .sort(([,a], [,b]) => b.total - a.total)
      .slice(0, 10)
      .forEach(([batchId, stats]) => {
        console.log(`   ${batchId}: 总数 ${stats.total}, 已使用 ${stats.used}, 未使用 ${stats.unused}`);
      });
    
    return stats;
  } catch (error) {
    console.error('获取统计信息失败:', error);
    return null;
  }
}

// 删除会员码
async function deleteMemberCode(code) {
  try {
    const result = await db.collection('memberData').where({ code }).get();
    
    if (result.data.length === 0) {
      console.error(`错误: 会员码 "${code}" 不存在`);
      return false;
    }
    
    const memberCode = result.data[0];
    if (memberCode.isUsed) {
      console.error(`错误: 会员码 "${code}" 已被使用，不能删除`);
      return false;
    }
    
    await db.collection('memberData').doc(memberCode._id).remove();
    console.log(`✅ 会员码 "${code}" 删除成功`);
    return true;
  } catch (error) {
    console.error('删除会员码失败:', error);
    return false;
  }
}

// 交互式菜单
async function showMenu() {
  console.log('\n=== 腾讯云会员码管理工具 ===');
  console.log('1. 创建单个会员码');
  console.log('2. 批量创建会员码');
  console.log('3. 查看会员码列表');
  console.log('4. 查看统计信息');
  console.log('5. 删除会员码');
  console.log('0. 退出');
  console.log('');
  
  rl.question('请选择操作 (0-5): ', async (choice) => {
    switch (choice) {
      case '1':
        await handleCreateSingle();
        break;
      case '2':
        await handleBatchCreate();
        break;
      case '3':
        await handleList();
        break;
      case '4':
        await getStats();
        showMenu();
        break;
      case '5':
        await handleDelete();
        break;
      case '0':
        console.log('再见！');
        rl.close();
        process.exit(0);
        break;
      default:
        console.log('无效选择，请重试');
        showMenu();
        break;
    }
  });
}

// 处理创建单个会员码
async function handleCreateSingle() {
  rl.question('套餐ID (pkg_permanent/pkg_monthly, 默认: pkg_permanent): ', (packageId) => {
    rl.question('自定义会员码 (留空自动生成): ', (customCode) => {
      rl.question('过期时间 (ISO格式, 留空永不过期): ', (expireAt) => {
        rl.question('批次ID (留空自动生成): ', (batchId) => {
          rl.question('前缀 (默认: VIP): ', async (prefix) => {
            const options = {
              packageId: packageId || 'pkg_permanent',
              customCode: customCode || null,
              expireAt: expireAt || null,
              batchId: batchId || `batch_${Date.now()}`,
              prefix: prefix || 'VIP'
            };
            
            await createMemberCode(options);
            showMenu();
          });
        });
      });
    });
  });
}

// 处理批量创建
async function handleBatchCreate() {
  rl.question('创建数量 (默认: 10): ', (count) => {
    rl.question('套餐ID (pkg_permanent/pkg_monthly, 默认: pkg_permanent): ', (packageId) => {
      rl.question('过期时间 (ISO格式, 留空永不过期): ', (expireAt) => {
        rl.question('批次ID (留空自动生成): ', (batchId) => {
          rl.question('前缀 (默认: VIP): ', async (prefix) => {
            const options = {
              count: parseInt(count) || 10,
              packageId: packageId || 'pkg_permanent',
              expireAt: expireAt || null,
              batchId: batchId || `batch_${Date.now()}`,
              prefix: prefix || 'VIP'
            };
            
            await batchCreateMemberCodes(options);
            showMenu();
          });
        });
      });
    });
  });
}

// 处理列表查看
async function handleList() {
  rl.question('使用状态过滤 (true/false, 留空显示全部): ', (isUsed) => {
    rl.question('套餐ID过滤 (留空显示全部): ', (packageId) => {
      rl.question('批次ID过滤 (留空显示全部): ', (batchId) => {
        rl.question('显示数量 (默认: 20): ', async (limit) => {
          const options = {
            isUsed: isUsed === 'true' ? true : isUsed === 'false' ? false : null,
            packageId: packageId || null,
            batchId: batchId || null,
            limit: parseInt(limit) || 20
          };
          
          await listMemberCodes(options);
          showMenu();
        });
      });
    });
  });
}

// 处理删除
async function handleDelete() {
  rl.question('请输入要删除的会员码: ', async (code) => {
    if (code) {
      await deleteMemberCode(code);
    } else {
      console.log('会员码不能为空');
    }
    showMenu();
  });
}

// 命令行参数处理
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 交互式模式
    console.log('正在连接腾讯云数据库...');
    try {
      // 测试数据库连接
      await db.collection('memberData').limit(1).get();
      console.log('✅ 数据库连接成功');
      showMenu();
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      console.log('请检查环境变量 TCB_ENV 是否正确设置');
      process.exit(1);
    }
    return;
  }
  
  const command = args[0];
  
  switch (command) {
    case 'create':
      const createOptions = {};
      for (let i = 1; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        if (key && value) {
          createOptions[key] = value;
        }
      }
      await createMemberCode(createOptions);
      break;
      
    case 'batch':
      const batchOptions = {};
      for (let i = 1; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        if (key && value) {
          if (key === 'count') {
            batchOptions[key] = parseInt(value);
          } else {
            batchOptions[key] = value;
          }
        }
      }
      await batchCreateMemberCodes(batchOptions);
      break;
      
    case 'list':
      const listOptions = {};
      for (let i = 1; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        if (key && value) {
          if (key === 'isUsed') {
            listOptions[key] = value === 'true';
          } else if (key === 'limit') {
            listOptions[key] = parseInt(value);
          } else {
            listOptions[key] = value;
          }
        }
      }
      await listMemberCodes(listOptions);
      break;
      
    case 'stats':
      await getStats();
      break;
      
    case 'delete':
      if (args[1]) {
        await deleteMemberCode(args[1]);
      } else {
        console.log('请提供要删除的会员码');
      }
      break;
      
    default:
      console.log('腾讯云会员码管理工具');
      console.log('');
      console.log('用法:');
      console.log('  node cloudbase-member-code-manager.js                    - 交互式模式');
      console.log('  node cloudbase-member-code-manager.js create [选项]      - 创建单个会员码');
      console.log('  node cloudbase-member-code-manager.js batch [选项]       - 批量创建会员码');
      console.log('  node cloudbase-member-code-manager.js list [选项]        - 列出会员码');
      console.log('  node cloudbase-member-code-manager.js stats             - 显示统计信息');
      console.log('  node cloudbase-member-code-manager.js delete <code>     - 删除会员码');
      console.log('');
      console.log('创建选项:');
      console.log('  --packageId     套餐ID (pkg_monthly 或 pkg_permanent)');
      console.log('  --customCode    自定义会员码');
      console.log('  --expireAt      过期时间 (ISO格式)');
      console.log('  --batchId       批次ID');
      console.log('  --prefix        会员码前缀 (默认: VIP)');
      console.log('');
      console.log('批量创建选项:');
      console.log('  --count         创建数量 (默认: 10)');
      console.log('  --packageId     套餐ID');
      console.log('  --expireAt      过期时间');
      console.log('  --batchId       批次ID');
      console.log('  --prefix        会员码前缀');
      console.log('');
      console.log('列表选项:');
      console.log('  --isUsed        过滤使用状态 (true/false)');
      console.log('  --packageId     过滤套餐ID');
      console.log('  --batchId       过滤批次ID');
      console.log('  --limit         限制数量 (默认: 20)');
      console.log('');
      console.log('示例:');
      console.log('  node cloudbase-member-code-manager.js create --packageId pkg_permanent --customCode MYVIP001');
      console.log('  node cloudbase-member-code-manager.js batch --count 50 --packageId pkg_monthly --prefix MONTH');
      console.log('  node cloudbase-member-code-manager.js list --isUsed false --limit 10');
      console.log('  node cloudbase-member-code-manager.js delete MYVIP001');
      break;
  }
  
  process.exit(0);
}

if (require.main === module) {
  main().catch(error => {
    console.error('程序执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  createMemberCode,
  batchCreateMemberCodes,
  listMemberCodes,
  getStats,
  deleteMemberCode
};
