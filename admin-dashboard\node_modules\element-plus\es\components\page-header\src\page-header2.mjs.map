{"version": 3, "file": "page-header2.mjs", "sources": ["../../../../../../packages/components/page-header/src/page-header.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      {\n        [ns.m('has-breadcrumb')]: !!$slots.breadcrumb,\n        [ns.m('has-extra')]: !!$slots.extra,\n        [ns.is('contentful')]: !!$slots.default,\n      },\n    ]\"\n  >\n    <div v-if=\"$slots.breadcrumb\" :class=\"ns.e('breadcrumb')\">\n      <slot name=\"breadcrumb\" />\n    </div>\n    <div :class=\"ns.e('header')\">\n      <div :class=\"ns.e('left')\">\n        <div\n          :class=\"ns.e('back')\"\n          role=\"button\"\n          tabindex=\"0\"\n          @click=\"handleClick\"\n        >\n          <div\n            v-if=\"icon || $slots.icon\"\n            :aria-label=\"title || t('el.pageHeader.title')\"\n            :class=\"ns.e('icon')\"\n          >\n            <slot name=\"icon\">\n              <el-icon v-if=\"icon\">\n                <component :is=\"icon\" />\n              </el-icon>\n            </slot>\n          </div>\n          <div :class=\"ns.e('title')\">\n            <slot name=\"title\">{{ title || t('el.pageHeader.title') }}</slot>\n          </div>\n        </div>\n        <el-divider direction=\"vertical\" />\n        <div :class=\"ns.e('content')\">\n          <slot name=\"content\">{{ content }}</slot>\n        </div>\n      </div>\n\n      <div v-if=\"$slots.extra\" :class=\"ns.e('extra')\">\n        <slot name=\"extra\" />\n      </div>\n    </div>\n\n    <div v-if=\"$slots.default\" :class=\"ns.e('main')\">\n      <slot />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ElDivider } from '@element-plus/components/divider'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { pageHeaderEmits, pageHeaderProps } from './page-header'\n\ndefineOptions({\n  name: 'ElPageHeader',\n})\n\ndefineProps(pageHeaderProps)\nconst emit = defineEmits(pageHeaderEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('page-header')\n\nfunction handleClick() {\n  emit('back')\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;mCA4Dc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,aAAa,CAAA,CAAA;AAErC,IAAA,SAAS,WAAc,GAAA;AACrB,MAAA,IAAA,CAAK,MAAM,CAAA,CAAA;AAAA,KACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}