{"version": 3, "file": "panel-date-range.mjs", "sources": ["../../../../../../../packages/components/date-picker/src/props/panel-date-range.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { panelRangeSharedProps, panelSharedProps } from './shared'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const panelDateRangeProps = buildProps({\n  ...panelSharedProps,\n  ...panelRangeSharedProps,\n} as const)\n\nexport type PanelDateRangeProps = ExtractPropTypes<typeof panelDateRangeProps>\nexport type PanelDateRangePropsPublic = __ExtractPublicPropTypes<\n  typeof panelDateRangeProps\n>\n"], "names": [], "mappings": ";;;AAEY,MAAC,mBAAmB,GAAG,UAAU,CAAC;AAC9C,EAAE,GAAG,gBAAgB;AACrB,EAAE,GAAG,qBAAqB;AAC1B,CAAC;;;;"}