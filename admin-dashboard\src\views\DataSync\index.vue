<template>
  <div class="data-sync-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据同步管理</h1>
      <p class="page-subtitle">管理用户数据同步记录和备份</p>
    </div>

    <!-- 同步统计 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#1890ff"><Refresh /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.totalSyncs }}</div>
            <div class="stat-label">总同步次数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#52c41a"><CircleCheckFilled /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.successRate }}%</div>
            <div class="stat-label">成功率</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#faad14"><Clock /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.todayCount }}</div>
            <div class="stat-label">今日同步</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#f5222d"><FolderOpened /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatDataSize(syncStats.totalDataSize) }}</div>
            <div class="stat-label">总数据量</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 新增：同步队列状态 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#722ed1"><Timer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.queueCount || 0 }}</div>
            <div class="stat-label">队列任务</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#13c2c2"><Upload /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.directUploadCount || 0 }}</div>
            <div class="stat-label">直传成功</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#fa8c16"><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.failedCount || 0 }}</div>
            <div class="stat-label">失败次数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon size="32" color="#eb2f96"><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ syncStats.avgDuration || '0s' }}</div>
            <div class="stat-label">平均耗时</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 搜索和操作栏 -->
    <div class="dashboard-card">
      <div class="search-bar">
        <div class="search-left">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索用户名、同步ID"
            style="width: 300px"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <el-select
            v-model="searchForm.status"
            placeholder="同步状态"
            style="width: 120px"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="成功" value="success" />
            <el-option label="失败" value="failed" />
            <el-option label="进行中" value="syncing" />
          </el-select>

          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            style="width: 240px"
          />

          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>

          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </div>

        <div class="search-right">
          <el-button type="success" @click="handleManualSync">
            <el-icon><Refresh /></el-icon>
            手动同步
          </el-button>

          <el-button type="info" @click="handleViewQueue">
            <el-icon><List /></el-icon>
            同步队列
          </el-button>

          <el-button type="warning" @click="handleCleanRecords">
            <el-icon><Delete /></el-icon>
            清理记录
          </el-button>

          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出记录
          </el-button>

          <el-button type="primary" @click="handleSyncConfig">
            <el-icon><Setting /></el-icon>
            同步配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 同步记录表格 -->
    <div class="dashboard-card">
      <el-table
        v-loading="loading"
        :data="syncRecords"
        style="width: 100%"
      >
        <el-table-column prop="id" label="同步ID" width="180" show-overflow-tooltip />

        <el-table-column prop="username" label="用户" width="120">
          <template #default="{ row }">
            <el-button type="text" @click="viewUser(row.userId)">
              {{ row.username }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column label="同步内容" width="200">
          <template #default="{ row }">
            <div class="sync-content">
              <el-tag
                v-for="content in row.syncContent"
                :key="content"
                size="small"
                style="margin-right: 4px; margin-bottom: 4px;"
              >
                {{ content }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="同步方式" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getSyncMethodTagType(row.syncMethod)"
              size="small"
            >
              {{ getSyncMethodText(row.syncMethod) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="数据大小" width="100">
          <template #default="{ row }">
            {{ formatDataSize(row.dataSize) }}
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag
              :type="getStatusTagType(row.status)"
              size="small"
            >
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="耗时" width="80">
          <template #default="{ row }">
            {{ row.duration || '-' }}
          </template>
        </el-table-column>

        <el-table-column prop="timestamp" label="同步时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.timestamp) }}
          </template>
        </el-table-column>

        <el-table-column label="错误信息" show-overflow-tooltip>
          <template #default="{ row }">
            <span v-if="row.error" class="error-message">{{ row.error }}</span>
            <span v-else class="success-message">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleViewDetail(row)"
            >
              详情
            </el-button>

            <el-button
              v-if="row.status === 'failed'"
              type="warning"
              size="small"
              @click="handleRetry(row)"
            >
              重试
            </el-button>

            <el-dropdown
              trigger="click"
              @command="(command) => handleDropdownCommand(command, row)"
            >
              <el-button size="small" type="info">
                更多
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="viewUser">查看用户</el-dropdown-item>
                  <el-dropdown-item command="downloadData" v-if="row.status === 'success'">下载数据</el-dropdown-item>
                  <el-dropdown-item command="copyId">复制ID</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除记录</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </div>

    <!-- 同步详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="同步详情"
      width="800px"
    >
      <div v-if="currentRecord">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="同步ID">{{ currentRecord.id }}</el-descriptions-item>
          <el-descriptions-item label="用户">{{ currentRecord.username }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="数据大小">{{ formatDataSize(currentRecord.dataSize) }}</el-descriptions-item>
          <el-descriptions-item label="耗时">{{ currentRecord.duration || '-' }}</el-descriptions-item>
          <el-descriptions-item label="同步时间">{{ formatDate(currentRecord.timestamp) }}</el-descriptions-item>
        </el-descriptions>

        <div style="margin-top: 20px;">
          <h4>同步内容</h4>
          <el-tag
            v-for="content in currentRecord.syncContent"
            :key="content"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ content }}
          </el-tag>
        </div>

        <div v-if="currentRecord.error" style="margin-top: 20px;">
          <h4>错误信息</h4>
          <el-alert
            :title="currentRecord.error"
            type="error"
            :closable="false"
            show-icon
          />
        </div>

        <div v-if="currentRecord.details" style="margin-top: 20px;">
          <h4>详细信息</h4>
          <el-input
            v-model="currentRecord.details"
            type="textarea"
            :rows="6"
            readonly
          />
        </div>
      </div>
    </el-dialog>

    <!-- 手动同步对话框 -->
    <el-dialog
      v-model="syncDialogVisible"
      title="手动同步"
      width="500px"
    >
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="选择用户">
          <el-select v-model="syncForm.userId" placeholder="请选择用户" style="width: 100%">
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="同步内容">
          <el-checkbox-group v-model="syncForm.syncContent">
            <el-checkbox label="小说">小说数据</el-checkbox>
            <el-checkbox label="角色卡片">角色卡片</el-checkbox>
            <el-checkbox label="知识库">知识库文档</el-checkbox>
            <el-checkbox label="用户设置">用户设置</el-checkbox>
            <el-checkbox label="写作风格">写作风格包</el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="同步方式">
          <el-radio-group v-model="syncForm.syncMethod">
            <el-radio label="auto">自动选择</el-radio>
            <el-radio label="direct">直传优先</el-radio>
            <el-radio label="api">API代理</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="分块上传">
          <el-switch v-model="syncForm.enableChunked" />
          <span style="margin-left: 10px; color: #8c8c8c;">
            大数据自动分块上传
          </span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="syncDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleStartSync" :loading="syncLoading">
          开始同步
        </el-button>
      </template>
    </el-dialog>

    <!-- 同步队列对话框 -->
    <el-dialog
      v-model="queueDialogVisible"
      title="同步队列"
      width="800px"
    >
      <el-table :data="syncQueue" v-loading="queueLoading">
        <el-table-column prop="id" label="任务ID" width="180" />
        <el-table-column prop="userId" label="用户ID" width="120" />
        <el-table-column prop="type" label="任务类型" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getQueueStatusTagType(row.status)" size="small">
              {{ getQueueStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createdAt" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button
              v-if="row.status === 'pending'"
              type="danger"
              size="small"
              @click="handleCancelQueueTask(row.id)"
            >
              取消
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

    <!-- 清理记录对话框 -->
    <el-dialog
      v-model="cleanDialogVisible"
      title="清理同步记录"
      width="500px"
    >
      <el-form :model="cleanForm" label-width="120px">
        <el-form-item label="清理条件">
          <el-radio-group v-model="cleanForm.type">
            <el-radio label="date">按日期清理</el-radio>
            <el-radio label="status">按状态清理</el-radio>
            <el-radio label="all">清理全部</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item v-if="cleanForm.type === 'date'" label="清理日期前">
          <el-date-picker
            v-model="cleanForm.beforeDate"
            type="date"
            placeholder="选择日期"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item v-if="cleanForm.type === 'status'" label="记录状态">
          <el-select v-model="cleanForm.status" placeholder="选择状态" style="width: 100%">
            <el-option label="成功记录" value="success" />
            <el-option label="失败记录" value="failed" />
          </el-select>
        </el-form-item>

        <el-alert
          title="警告：此操作不可恢复，请谨慎操作！"
          type="warning"
          :closable="false"
          show-icon
        />
      </el-form>

      <template #footer>
        <el-button @click="cleanDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="handleConfirmClean" :loading="cleanLoading">
          确认清理
        </el-button>
      </template>
    </el-dialog>

    <!-- 同步配置对话框 -->
    <el-dialog
      v-model="configDialogVisible"
      title="同步配置"
      width="600px"
    >
      <el-form :model="syncConfig" label-width="140px">
        <el-form-item label="默认同步方式">
          <el-radio-group v-model="syncConfig.defaultSyncMethod">
            <el-radio label="auto">自动选择</el-radio>
            <el-radio label="direct">直传优先</el-radio>
            <el-radio label="api">API代理</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="分块上传阈值">
          <el-input-number
            v-model="syncConfig.chunkThreshold"
            :min="1"
            :max="100"
            :step="1"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #8c8c8c;">MB</span>
        </el-form-item>

        <el-form-item label="最大重试次数">
          <el-input-number
            v-model="syncConfig.maxRetries"
            :min="1"
            :max="10"
            :step="1"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item label="同步超时时间">
          <el-input-number
            v-model="syncConfig.timeout"
            :min="10"
            :max="300"
            :step="10"
            style="width: 200px"
          />
          <span style="margin-left: 10px; color: #8c8c8c;">秒</span>
        </el-form-item>

        <el-form-item label="自动清理记录">
          <el-switch v-model="syncConfig.autoClean" />
          <span style="margin-left: 10px; color: #8c8c8c;">
            自动清理30天前的成功记录
          </span>
        </el-form-item>

        <el-form-item label="启用压缩">
          <el-switch v-model="syncConfig.enableCompression" />
          <span style="margin-left: 10px; color: #8c8c8c;">
            对大数据进行压缩传输
          </span>
        </el-form-item>

        <el-form-item label="并发任务数">
          <el-input-number
            v-model="syncConfig.concurrentTasks"
            :min="1"
            :max="5"
            :step="1"
            style="width: 200px"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="configDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveConfig" :loading="configLoading">
          保存配置
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { syncApi } from '@/utils/api'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const loading = ref(false)
const syncLoading = ref(false)
const queueLoading = ref(false)
const cleanLoading = ref(false)
const configLoading = ref(false)
const detailDialogVisible = ref(false)
const syncDialogVisible = ref(false)
const queueDialogVisible = ref(false)
const cleanDialogVisible = ref(false)
const configDialogVisible = ref(false)
const currentRecord = ref(null)

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  dateRange: null as any
})

// 手动同步表单
const syncForm = reactive({
  userId: '',
  syncContent: ['小说', '角色卡片', '知识库', '用户设置'],
  syncMethod: 'auto',
  enableChunked: true
})

// 同步队列数据
const syncQueue = ref([])

// 清理表单
const cleanForm = reactive({
  type: 'date',
  beforeDate: null,
  status: ''
})

// 同步配置
const syncConfig = reactive({
  defaultSyncMethod: 'auto',
  chunkThreshold: 10,
  maxRetries: 3,
  timeout: 60,
  autoClean: true,
  enableCompression: true,
  concurrentTasks: 2
})

// 分页信息
const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 同步统计
const syncStats = ref({
  totalSyncs: 0,
  successRate: 0,
  todayCount: 0,
  totalDataSize: 0,
  queueCount: 0,
  directUploadCount: 0,
  failedCount: 0,
  avgDuration: '0s'
})

// 用户列表（用于手动同步）
const userList = ref([])

// 同步记录数据
const syncRecords = ref([])

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap = {
    'success': 'success',
    'failed': 'danger',
    'syncing': 'warning'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    'success': '成功',
    'failed': '失败',
    'syncing': '同步中'
  }
  return textMap[status] || '未知'
}

// 获取同步方式标签类型
const getSyncMethodTagType = (method: string) => {
  const typeMap = {
    'direct': 'success',
    'api': 'info',
    'chunked': 'warning',
    'auto': ''
  }
  return typeMap[method] || 'info'
}

// 获取同步方式文本
const getSyncMethodText = (method: string) => {
  const textMap = {
    'direct': '直传',
    'api': 'API',
    'chunked': '分块',
    'auto': '自动'
  }
  return textMap[method] || '未知'
}

// 获取队列状态标签类型
const getQueueStatusTagType = (status: string) => {
  const typeMap = {
    'pending': 'warning',
    'running': 'info',
    'completed': 'success',
    'failed': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取队列状态文本
const getQueueStatusText = (status: string) => {
  const textMap = {
    'pending': '等待中',
    'running': '执行中',
    'completed': '已完成',
    'failed': '失败'
  }
  return textMap[status] || '未知'
}



// 格式化数据大小
const formatDataSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 格式化日期
const formatDate = (dateString: string) => {
  return dayjs(dateString).format('YYYY-MM-DD HH:mm:ss')
}

// 查看用户
const viewUser = (userId: string) => {
  router.push(`/users/${userId}`)
}

// 处理搜索
const handleSearch = () => {
  pagination.page = 1
  loadSyncRecords()
}

// 处理重置
const handleReset = () => {
  searchForm.keyword = ''
  searchForm.status = ''
  searchForm.dateRange = null
  pagination.page = 1
  loadSyncRecords()
}

// 处理手动同步
const handleManualSync = () => {
  syncDialogVisible.value = true
}

// 处理查看队列
const handleViewQueue = async () => {
  queueDialogVisible.value = true
  await loadSyncQueue()
}

// 处理清理记录
const handleCleanRecords = () => {
  cleanDialogVisible.value = true
}

// 处理同步配置
const handleSyncConfig = async () => {
  configDialogVisible.value = true
  await loadSyncConfig()
}

// 处理导出
const handleExport = async () => {
  try {
    const params = {
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = searchForm.dateRange[0]
      params.endDate = searchForm.dateRange[1]
    }

    const response = await syncApi.exportSyncRecords(params)

    // 创建下载链接
    const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `sync_records_${dayjs().format('YYYY-MM-DD')}.xlsx`
    link.click()
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

// 处理查看详情
const handleViewDetail = (record: any) => {
  currentRecord.value = record
  detailDialogVisible.value = true
}

// 处理重试
const handleRetry = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要重试这次同步吗？', '重试确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    ElMessage.success('重试同步已启动')
    loadSyncRecords()
  } catch (error) {
    // 用户取消操作
  }
}

// 处理下拉菜单命令
const handleDropdownCommand = async (command: string, row: any) => {
  switch (command) {
    case 'viewUser':
      router.push(`/users/${row.userId}`)
      break
    case 'downloadData':
      await handleDownloadData(row)
      break
    case 'copyId':
      await navigator.clipboard.writeText(row.id)
      ElMessage.success('ID已复制到剪贴板')
      break
    case 'delete':
      await handleDeleteRecord(row)
      break
  }
}

// 处理下载数据
const handleDownloadData = async (record: any) => {
  try {
    const response = await syncApi.getSyncDetail(record.id)
    if (response.data.success && response.data.data.syncData) {
      const blob = new Blob([JSON.stringify(response.data.data.syncData, null, 2)],
        { type: 'application/json' })
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `sync_data_${record.id}.json`
      link.click()
      window.URL.revokeObjectURL(url)
      ElMessage.success('数据下载成功')
    }
  } catch (error) {
    ElMessage.error('下载数据失败')
  }
}

// 处理删除记录
const handleDeleteRecord = async (record: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这条同步记录吗？', '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用删除API
    ElMessage.success('记录删除成功')
    loadSyncRecords()
  } catch (error) {
    // 用户取消操作
  }
}

// 处理开始同步
const handleStartSync = async () => {
  if (!syncForm.userId) {
    ElMessage.warning('请选择要同步的用户')
    return
  }

  if (syncForm.syncContent.length === 0) {
    ElMessage.warning('请选择要同步的内容')
    return
  }

  syncLoading.value = true
  try {
    await syncApi.triggerUserSync({
      userId: syncForm.userId,
      syncContent: syncForm.syncContent,
      syncMethod: syncForm.syncMethod,
      enableChunked: syncForm.enableChunked
    })

    ElMessage.success('同步已启动')
    syncDialogVisible.value = false
    loadSyncRecords()
  } catch (error) {
    ElMessage.error('启动同步失败')
  } finally {
    syncLoading.value = false
  }
}

// 处理页面大小变化
const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadSyncRecords()
}

// 处理页面变化
const handlePageChange = (page: number) => {
  pagination.page = page
  loadSyncRecords()
}

// 处理取消队列任务
const handleCancelQueueTask = async (taskId: string) => {
  try {
    await ElMessageBox.confirm('确定要取消这个同步任务吗？', '取消确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里应该调用取消任务API
    ElMessage.success('任务已取消')
    loadSyncQueue()
  } catch (error) {
    // 用户取消操作
  }
}

// 处理确认清理
const handleConfirmClean = async () => {
  if (cleanForm.type === 'date' && !cleanForm.beforeDate) {
    ElMessage.warning('请选择清理日期')
    return
  }

  if (cleanForm.type === 'status' && !cleanForm.status) {
    ElMessage.warning('请选择记录状态')
    return
  }

  cleanLoading.value = true
  try {
    const params: any = {}

    if (cleanForm.type === 'date') {
      params.beforeDate = dayjs(cleanForm.beforeDate).format('YYYY-MM-DD')
    } else if (cleanForm.type === 'status') {
      params.status = cleanForm.status
    }

    await syncApi.cleanSyncRecords(params)

    ElMessage.success('记录清理成功')
    cleanDialogVisible.value = false
    loadSyncRecords()
    loadSyncStats()
  } catch (error) {
    ElMessage.error('清理记录失败')
  } finally {
    cleanLoading.value = false
  }
}

// 处理保存配置
const handleSaveConfig = async () => {
  configLoading.value = true
  try {
    await syncApi.updateSyncConfig(syncConfig)

    ElMessage.success('配置保存成功')
    configDialogVisible.value = false
  } catch (error) {
    ElMessage.error('保存配置失败')
  } finally {
    configLoading.value = false
  }
}

// 加载同步统计
const loadSyncStats = async () => {
  try {
    const response = await syncApi.getSyncStats()
    if (response.data.success) {
      syncStats.value = response.data.data
    }
  } catch (error) {
    console.error('加载同步统计失败:', error)
  }
}

// 加载同步队列
const loadSyncQueue = async () => {
  queueLoading.value = true
  try {
    const response = await syncApi.getSyncQueue()
    if (response.data.success) {
      syncQueue.value = response.data.data || []
    }
  } catch (error) {
    console.error('加载同步队列失败:', error)
    ElMessage.error('加载同步队列失败')
  } finally {
    queueLoading.value = false
  }
}

// 加载同步配置
const loadSyncConfig = async () => {
  try {
    const response = await syncApi.getSyncConfig()
    if (response.data.success) {
      Object.assign(syncConfig, response.data.data)
    }
  } catch (error) {
    console.error('加载同步配置失败:', error)
  }
}

// 加载用户列表
const loadUserList = async () => {
  try {
    const response = await fetch('/api/users?size=100', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (response.ok) {
      const result = await response.json()
      if (result.success) {
        userList.value = result.data.users || []
      }
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
  }
}

// 加载同步记录
const loadSyncRecords = async () => {
  loading.value = true
  try {
    const params: any = {
      page: pagination.page,
      size: pagination.size,
      keyword: searchForm.keyword,
      status: searchForm.status
    }

    if (searchForm.dateRange && searchForm.dateRange.length === 2) {
      params.startDate = dayjs(searchForm.dateRange[0]).format('YYYY-MM-DD')
      params.endDate = dayjs(searchForm.dateRange[1]).format('YYYY-MM-DD')
    }

    const response = await syncApi.getSyncRecords(params)

    if (response.data.success) {
      syncRecords.value = response.data.data.syncRecords || []
      pagination.total = response.data.data.total || 0
    } else {
      ElMessage.error(response.data.message || '加载同步记录失败')
    }
  } catch (error) {
    console.error('加载同步记录失败:', error)
    ElMessage.error('加载同步记录失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadSyncStats()
  loadUserList()
  loadSyncRecords()
})
</script>

<style scoped>
.data-sync-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.search-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sync-content {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.error-message {
  color: #ff4d4f;
}

.success-message {
  color: #d9d9d9;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

/* 响应式 */
@media (max-width: 768px) {
  .search-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-left,
  .search-right {
    justify-content: center;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<style scoped>
.data-sync-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
}

.coming-soon h2 {
  font-size: 24px;
  color: #262626;
  margin: 20px 0 12px 0;
}

.coming-soon p {
  color: #8c8c8c;
}
</style>
