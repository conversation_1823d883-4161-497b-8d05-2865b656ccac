import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("monitor.tencentcloudapi.com", "2018-07-24", clientConfig);
    }
    async UpdateGrafanaNotificationChannel(req, cb) {
        return this.request("UpdateGrafanaNotificationChannel", req, cb);
    }
    async DescribeAlarmSmsQuota(req, cb) {
        return this.request("DescribeAlarmSmsQuota", req, cb);
    }
    async CreateAlertRule(req, cb) {
        return this.request("CreateAlertRule", req, cb);
    }
    async DeletePrometheusClusterAgent(req, cb) {
        return this.request("DeletePrometheusClusterAgent", req, cb);
    }
    async DescribeBindingPolicyObjectList(req, cb) {
        return this.request("DescribeBindingPolicyObjectList", req, cb);
    }
    async UpdateAlertRule(req, cb) {
        return this.request("UpdateAlertRule", req, cb);
    }
    async ExportPrometheusReadOnlyDynamicAPI(req, cb) {
        return this.request("ExportPrometheusReadOnlyDynamicAPI", req, cb);
    }
    async DescribeMonitorResourceInfo(req, cb) {
        return this.request("DescribeMonitorResourceInfo", req, cb);
    }
    async ModifyPrometheusAlertPolicy(req, cb) {
        return this.request("ModifyPrometheusAlertPolicy", req, cb);
    }
    async DescribePrometheusAlertGroups(req, cb) {
        return this.request("DescribePrometheusAlertGroups", req, cb);
    }
    async DescribePrometheusInstanceUsage(req, cb) {
        return this.request("DescribePrometheusInstanceUsage", req, cb);
    }
    async DescribePolicyObjectCount(req, cb) {
        return this.request("DescribePolicyObjectCount", req, cb);
    }
    async ModifyAlarmReceivers(req, cb) {
        return this.request("ModifyAlarmReceivers", req, cb);
    }
    async BindingPolicyObject(req, cb) {
        return this.request("BindingPolicyObject", req, cb);
    }
    async DeletePrometheusAlertGroups(req, cb) {
        return this.request("DeletePrometheusAlertGroups", req, cb);
    }
    async CreateGrafanaInstance(req, cb) {
        return this.request("CreateGrafanaInstance", req, cb);
    }
    async CreateAlarmPolicy(req, cb) {
        return this.request("CreateAlarmPolicy", req, cb);
    }
    async DescribeProductList(req, cb) {
        return this.request("DescribeProductList", req, cb);
    }
    async DescribePrometheusRecordRules(req, cb) {
        return this.request("DescribePrometheusRecordRules", req, cb);
    }
    async UpdateSSOAccount(req, cb) {
        return this.request("UpdateSSOAccount", req, cb);
    }
    async DescribePrometheusClusterAgents(req, cb) {
        return this.request("DescribePrometheusClusterAgents", req, cb);
    }
    async DescribePrometheusZones(req, cb) {
        return this.request("DescribePrometheusZones", req, cb);
    }
    async DescribeGrafanaEnvironments(req, cb) {
        return this.request("DescribeGrafanaEnvironments", req, cb);
    }
    async UpdateRecordingRule(req, cb) {
        return this.request("UpdateRecordingRule", req, cb);
    }
    async DeleteGrafanaNotificationChannel(req, cb) {
        return this.request("DeleteGrafanaNotificationChannel", req, cb);
    }
    async DescribeStatisticData(req, cb) {
        return this.request("DescribeStatisticData", req, cb);
    }
    async DescribeGrafanaConfig(req, cb) {
        return this.request("DescribeGrafanaConfig", req, cb);
    }
    async ModifyAlarmPolicyInfo(req, cb) {
        return this.request("ModifyAlarmPolicyInfo", req, cb);
    }
    async ModifyAlarmPolicyStatus(req, cb) {
        return this.request("ModifyAlarmPolicyStatus", req, cb);
    }
    async DeletePrometheusScrapeJobs(req, cb) {
        return this.request("DeletePrometheusScrapeJobs", req, cb);
    }
    async EnableGrafanaSSO(req, cb) {
        return this.request("EnableGrafanaSSO", req, cb);
    }
    async DeleteAlertRules(req, cb) {
        return this.request("DeleteAlertRules", req, cb);
    }
    async DescribeBaseMetrics(req, cb) {
        return this.request("DescribeBaseMetrics", req, cb);
    }
    async DescribeAlarmEvents(req, cb) {
        return this.request("DescribeAlarmEvents", req, cb);
    }
    async DescribePhoneAlarmFlowTotalCount(req, cb) {
        return this.request("DescribePhoneAlarmFlowTotalCount", req, cb);
    }
    async DeleteAlarmNotices(req, cb) {
        return this.request("DeleteAlarmNotices", req, cb);
    }
    async ModifyAlarmNotice(req, cb) {
        return this.request("ModifyAlarmNotice", req, cb);
    }
    async DescribePrometheusInstanceDetail(req, cb) {
        return this.request("DescribePrometheusInstanceDetail", req, cb);
    }
    async CreatePrometheusScrapeJob(req, cb) {
        return this.request("CreatePrometheusScrapeJob", req, cb);
    }
    async UnBindingPolicyObject(req, cb) {
        return this.request("UnBindingPolicyObject", req, cb);
    }
    async DescribeConditionsTemplateList(req, cb) {
        return this.request("DescribeConditionsTemplateList", req, cb);
    }
    async DescribePrometheusScrapeJobs(req, cb) {
        return this.request("DescribePrometheusScrapeJobs", req, cb);
    }
    async DeleteServiceDiscovery(req, cb) {
        return this.request("DeleteServiceDiscovery", req, cb);
    }
    async CreatePrometheusClusterAgent(req, cb) {
        return this.request("CreatePrometheusClusterAgent", req, cb);
    }
    async BindingPolicyTag(req, cb) {
        return this.request("BindingPolicyTag", req, cb);
    }
    async CreateAlarmShield(req, cb) {
        return this.request("CreateAlarmShield", req, cb);
    }
    async CreatePrometheusMultiTenantInstancePostPayMode(req, cb) {
        return this.request("CreatePrometheusMultiTenantInstancePostPayMode", req, cb);
    }
    async UpdateGrafanaEnvironments(req, cb) {
        return this.request("UpdateGrafanaEnvironments", req, cb);
    }
    async DescribePolicyConditionList(req, cb) {
        return this.request("DescribePolicyConditionList", req, cb);
    }
    async CreateConditionsTemplate(req, cb) {
        return this.request("CreateConditionsTemplate", req, cb);
    }
    async DescribeAlarmNoticeCallbacks(req, cb) {
        return this.request("DescribeAlarmNoticeCallbacks", req, cb);
    }
    async GetPrometheusAgentManagementCommand(req, cb) {
        return this.request("GetPrometheusAgentManagementCommand", req, cb);
    }
    async UpdatePrometheusScrapeJob(req, cb) {
        return this.request("UpdatePrometheusScrapeJob", req, cb);
    }
    async DescribePrometheusInstanceInitStatus(req, cb) {
        return this.request("DescribePrometheusInstanceInitStatus", req, cb);
    }
    async UpgradeGrafanaInstance(req, cb) {
        return this.request("UpgradeGrafanaInstance", req, cb);
    }
    async DescribeGrafanaChannels(req, cb) {
        return this.request("DescribeGrafanaChannels", req, cb);
    }
    async DeleteGrafanaIntegration(req, cb) {
        return this.request("DeleteGrafanaIntegration", req, cb);
    }
    async BindPrometheusManagedGrafana(req, cb) {
        return this.request("BindPrometheusManagedGrafana", req, cb);
    }
    async UnBindingAllPolicyObject(req, cb) {
        return this.request("UnBindingAllPolicyObject", req, cb);
    }
    async DeletePolicyGroup(req, cb) {
        return this.request("DeletePolicyGroup", req, cb);
    }
    async UpdateServiceDiscovery(req, cb) {
        return this.request("UpdateServiceDiscovery", req, cb);
    }
    async DescribeMonitorTypes(req, cb) {
        return this.request("DescribeMonitorTypes", req, cb);
    }
    async EnableSSOCamCheck(req, cb) {
        return this.request("EnableSSOCamCheck", req, cb);
    }
    async InstallPlugins(req, cb) {
        return this.request("InstallPlugins", req, cb);
    }
    async CreateExporterIntegration(req, cb) {
        return this.request("CreateExporterIntegration", req, cb);
    }
    async DeleteRecordingRules(req, cb) {
        return this.request("DeleteRecordingRules", req, cb);
    }
    async DescribeProductEventList(req, cb) {
        return this.request("DescribeProductEventList", req, cb);
    }
    async DescribeAlarmHistories(req, cb) {
        return this.request("DescribeAlarmHistories", req, cb);
    }
    async CreatePrometheusGlobalNotification(req, cb) {
        return this.request("CreatePrometheusGlobalNotification", req, cb);
    }
    async DeleteSSOAccount(req, cb) {
        return this.request("DeleteSSOAccount", req, cb);
    }
    async DescribeRemoteURLs(req, cb) {
        return this.request("DescribeRemoteURLs", req, cb);
    }
    async UpdatePrometheusAlertGroupState(req, cb) {
        return this.request("UpdatePrometheusAlertGroupState", req, cb);
    }
    async DescribeAlarmNotice(req, cb) {
        return this.request("DescribeAlarmNotice", req, cb);
    }
    async CreateGrafanaIntegration(req, cb) {
        return this.request("CreateGrafanaIntegration", req, cb);
    }
    async DescribePrometheusTemp(req, cb) {
        return this.request("DescribePrometheusTemp", req, cb);
    }
    async UpdateGrafanaWhiteList(req, cb) {
        return this.request("UpdateGrafanaWhiteList", req, cb);
    }
    async SetDefaultAlarmPolicy(req, cb) {
        return this.request("SetDefaultAlarmPolicy", req, cb);
    }
    async DeleteGrafanaInstance(req, cb) {
        return this.request("DeleteGrafanaInstance", req, cb);
    }
    async DeletePrometheusConfig(req, cb) {
        return this.request("DeletePrometheusConfig", req, cb);
    }
    async CreateSSOAccount(req, cb) {
        return this.request("CreateSSOAccount", req, cb);
    }
    async ModifyAlarmPolicyNotice(req, cb) {
        return this.request("ModifyAlarmPolicyNotice", req, cb);
    }
    async EnableGrafanaInternet(req, cb) {
        return this.request("EnableGrafanaInternet", req, cb);
    }
    async CreateGrafanaNotificationChannel(req, cb) {
        return this.request("CreateGrafanaNotificationChannel", req, cb);
    }
    async ModifyGrafanaInstance(req, cb) {
        return this.request("ModifyGrafanaInstance", req, cb);
    }
    async ResumeGrafanaInstance(req, cb) {
        return this.request("ResumeGrafanaInstance", req, cb);
    }
    async CreatePrometheusTemp(req, cb) {
        return this.request("CreatePrometheusTemp", req, cb);
    }
    async DescribeSSOAccount(req, cb) {
        return this.request("DescribeSSOAccount", req, cb);
    }
    async DescribeBasicAlarmList(req, cb) {
        return this.request("DescribeBasicAlarmList", req, cb);
    }
    async CreatePrometheusAgent(req, cb) {
        return this.request("CreatePrometheusAgent", req, cb);
    }
    async SyncPrometheusTemp(req, cb) {
        return this.request("SyncPrometheusTemp", req, cb);
    }
    async ModifyRemoteURLs(req, cb) {
        return this.request("ModifyRemoteURLs", req, cb);
    }
    async CreateAlarmNotice(req, cb) {
        return this.request("CreateAlarmNotice", req, cb);
    }
    async UpdateAlertRuleState(req, cb) {
        return this.request("UpdateAlertRuleState", req, cb);
    }
    async GetTopNMonitorData(req, cb) {
        return this.request("GetTopNMonitorData", req, cb);
    }
    async ModifyPrometheusTemp(req, cb) {
        return this.request("ModifyPrometheusTemp", req, cb);
    }
    async UnbindPrometheusManagedGrafana(req, cb) {
        return this.request("UnbindPrometheusManagedGrafana", req, cb);
    }
    async DeleteAlarmShields(req, cb) {
        return this.request("DeleteAlarmShields", req, cb);
    }
    async RunPrometheusInstance(req, cb) {
        return this.request("RunPrometheusInstance", req, cb);
    }
    async DescribeExporterIntegrations(req, cb) {
        return this.request("DescribeExporterIntegrations", req, cb);
    }
    async DescribeInstalledPlugins(req, cb) {
        return this.request("DescribeInstalledPlugins", req, cb);
    }
    async DescribePrometheusGlobalNotification(req, cb) {
        return this.request("DescribePrometheusGlobalNotification", req, cb);
    }
    async UpgradeGrafanaDashboard(req, cb) {
        return this.request("UpgradeGrafanaDashboard", req, cb);
    }
    async DescribeAlarmNotices(req, cb) {
        return this.request("DescribeAlarmNotices", req, cb);
    }
    async DescribeDNSConfig(req, cb) {
        return this.request("DescribeDNSConfig", req, cb);
    }
    async CreatePrometheusAlertGroup(req, cb) {
        return this.request("CreatePrometheusAlertGroup", req, cb);
    }
    async ModifyPrometheusGlobalNotification(req, cb) {
        return this.request("ModifyPrometheusGlobalNotification", req, cb);
    }
    async DescribePrometheusAgentInstances(req, cb) {
        return this.request("DescribePrometheusAgentInstances", req, cb);
    }
    async DeletePrometheusRecordRuleYaml(req, cb) {
        return this.request("DeletePrometheusRecordRuleYaml", req, cb);
    }
    async DescribePrometheusInstances(req, cb) {
        return this.request("DescribePrometheusInstances", req, cb);
    }
    async DescribeGrafanaInstances(req, cb) {
        return this.request("DescribeGrafanaInstances", req, cb);
    }
    async ModifyPrometheusRecordRuleYaml(req, cb) {
        return this.request("ModifyPrometheusRecordRuleYaml", req, cb);
    }
    async UpdateGrafanaConfig(req, cb) {
        return this.request("UpdateGrafanaConfig", req, cb);
    }
    async UpdatePrometheusAlertGroup(req, cb) {
        return this.request("UpdatePrometheusAlertGroup", req, cb);
    }
    async DescribeGrafanaNotificationChannels(req, cb) {
        return this.request("DescribeGrafanaNotificationChannels", req, cb);
    }
    async ModifyPrometheusAgentExternalLabels(req, cb) {
        return this.request("ModifyPrometheusAgentExternalLabels", req, cb);
    }
    async ModifyPolicyGroup(req, cb) {
        return this.request("ModifyPolicyGroup", req, cb);
    }
    async DescribeAccidentEventList(req, cb) {
        return this.request("DescribeAccidentEventList", req, cb);
    }
    async DescribePrometheusTempSync(req, cb) {
        return this.request("DescribePrometheusTempSync", req, cb);
    }
    async DescribeAlarmMetrics(req, cb) {
        return this.request("DescribeAlarmMetrics", req, cb);
    }
    async ModifyAlarmPolicyTasks(req, cb) {
        return this.request("ModifyAlarmPolicyTasks", req, cb);
    }
    async DeleteExporterIntegration(req, cb) {
        return this.request("DeleteExporterIntegration", req, cb);
    }
    async DeletePrometheusAlertPolicy(req, cb) {
        return this.request("DeletePrometheusAlertPolicy", req, cb);
    }
    async UpdatePrometheusAgentStatus(req, cb) {
        return this.request("UpdatePrometheusAgentStatus", req, cb);
    }
    async CreateRecordingRule(req, cb) {
        return this.request("CreateRecordingRule", req, cb);
    }
    async DescribeAlertRules(req, cb) {
        return this.request("DescribeAlertRules", req, cb);
    }
    async DescribePrometheusGlobalConfig(req, cb) {
        return this.request("DescribePrometheusGlobalConfig", req, cb);
    }
    async DescribePrometheusAgents(req, cb) {
        return this.request("DescribePrometheusAgents", req, cb);
    }
    async DescribeAlarmPolicies(req, cb) {
        return this.request("DescribeAlarmPolicies", req, cb);
    }
    async DescribePrometheusTargetsTMP(req, cb) {
        return this.request("DescribePrometheusTargetsTMP", req, cb);
    }
    async DescribePrometheusRegions(req, cb) {
        return this.request("DescribePrometheusRegions", req, cb);
    }
    async DescribeGrafanaWhiteList(req, cb) {
        return this.request("DescribeGrafanaWhiteList", req, cb);
    }
    async CreatePrometheusAlertPolicy(req, cb) {
        return this.request("CreatePrometheusAlertPolicy", req, cb);
    }
    async CleanGrafanaInstance(req, cb) {
        return this.request("CleanGrafanaInstance", req, cb);
    }
    async UninstallGrafanaDashboard(req, cb) {
        return this.request("UninstallGrafanaDashboard", req, cb);
    }
    async DescribePrometheusAlertPolicy(req, cb) {
        return this.request("DescribePrometheusAlertPolicy", req, cb);
    }
    async DescribePolicyGroupList(req, cb) {
        return this.request("DescribePolicyGroupList", req, cb);
    }
    async ModifyPrometheusInstanceAttributes(req, cb) {
        return this.request("ModifyPrometheusInstanceAttributes", req, cb);
    }
    async DeleteAlarmPolicy(req, cb) {
        return this.request("DeleteAlarmPolicy", req, cb);
    }
    async GetMonitorData(req, cb) {
        return this.request("GetMonitorData", req, cb);
    }
    async CreateExternalCluster(req, cb) {
        return this.request("CreateExternalCluster", req, cb);
    }
    async UpdateDNSConfig(req, cb) {
        return this.request("UpdateDNSConfig", req, cb);
    }
    async DescribeServiceDiscovery(req, cb) {
        return this.request("DescribeServiceDiscovery", req, cb);
    }
    async DescribeGrafanaIntegrations(req, cb) {
        return this.request("DescribeGrafanaIntegrations", req, cb);
    }
    async DescribePrometheusConfig(req, cb) {
        return this.request("DescribePrometheusConfig", req, cb);
    }
    async DescribePolicyGroupInfo(req, cb) {
        return this.request("DescribePolicyGroupInfo", req, cb);
    }
    async UpdateExporterIntegration(req, cb) {
        return this.request("UpdateExporterIntegration", req, cb);
    }
    async DescribePrometheusIntegrationMetrics(req, cb) {
        return this.request("DescribePrometheusIntegrationMetrics", req, cb);
    }
    async DescribeAllNamespaces(req, cb) {
        return this.request("DescribeAllNamespaces", req, cb);
    }
    async CreateServiceDiscovery(req, cb) {
        return this.request("CreateServiceDiscovery", req, cb);
    }
    async CreatePrometheusRecordRuleYaml(req, cb) {
        return this.request("CreatePrometheusRecordRuleYaml", req, cb);
    }
    async ModifyAlarmPolicyCondition(req, cb) {
        return this.request("ModifyAlarmPolicyCondition", req, cb);
    }
    async DeletePrometheusTemp(req, cb) {
        return this.request("DeletePrometheusTemp", req, cb);
    }
    async DescribeExternalClusterUninstallCommand(req, cb) {
        return this.request("DescribeExternalClusterUninstallCommand", req, cb);
    }
    async DestroyPrometheusInstance(req, cb) {
        return this.request("DestroyPrometheusInstance", req, cb);
    }
    async UninstallGrafanaPlugins(req, cb) {
        return this.request("UninstallGrafanaPlugins", req, cb);
    }
    async UpdateGrafanaIntegration(req, cb) {
        return this.request("UpdateGrafanaIntegration", req, cb);
    }
    async CreatePolicyGroup(req, cb) {
        return this.request("CreatePolicyGroup", req, cb);
    }
    async TerminatePrometheusInstances(req, cb) {
        return this.request("TerminatePrometheusInstances", req, cb);
    }
    async DescribePrometheusInstancesOverview(req, cb) {
        return this.request("DescribePrometheusInstancesOverview", req, cb);
    }
    async DeletePrometheusTempSync(req, cb) {
        return this.request("DeletePrometheusTempSync", req, cb);
    }
    async CreatePrometheusConfig(req, cb) {
        return this.request("CreatePrometheusConfig", req, cb);
    }
    async DescribePluginOverviews(req, cb) {
        return this.request("DescribePluginOverviews", req, cb);
    }
    async DescribeClusterAgentCreatingProgress(req, cb) {
        return this.request("DescribeClusterAgentCreatingProgress", req, cb);
    }
    async DescribeRecordingRules(req, cb) {
        return this.request("DescribeRecordingRules", req, cb);
    }
    async ModifyPrometheusConfig(req, cb) {
        return this.request("ModifyPrometheusConfig", req, cb);
    }
    async DescribeExternalClusterRegisterCommand(req, cb) {
        return this.request("DescribeExternalClusterRegisterCommand", req, cb);
    }
    async DescribeAlarmPolicy(req, cb) {
        return this.request("DescribeAlarmPolicy", req, cb);
    }
}
