{"name": "@cloudbase/node-sdk", "version": "2.11.0", "description": "tencent cloud base server sdk for node.js", "main": "lib/index.js", "scripts": {"eslint": "npx eslint \"./**/*.ts\"", "eslint-fix": "npx eslint --fix \"./**/*.ts\"", "build": "rm -rf lib/* && npm run tsc", "tsc": "npx tsc -p tsconfig.json", "tsc:w": "npx tsc -p tsconfig.json -w", "test": "npx jest --detect<PERSON>pen<PERSON>andles --verbose --coverage --runInBand", "coverage": "npx jest --detect<PERSON><PERSON>Handles --coverage", "coveralls": "cat ./coverage/lcov.info | coveralls", "prepare": "husky install"}, "repository": {"type": "git", "url": "https://github.com/TencentCloudBase/node-sdk"}, "bugs": {"url": "https://github.com/TencentCloudBase/node-sdk/issues"}, "homepage": "https://github.com/TencentCloudBase/node-sdk#readme", "keywords": ["node sdk"], "author": "lukejyhuang", "license": "MIT", "typings": "types/index.d.ts", "dependencies": {"@cloudbase/database": "1.4.1", "@cloudbase/signature-nodejs": "1.0.0-beta.0", "agentkeepalive": "^4.3.0", "axios": "^0.21.1", "jsonwebtoken": "^8.5.1", "request": "^2.87.0", "retry": "^0.13.1", "xml2js": "^0.5.0"}, "devDependencies": {"@types/jest": "^29.5.1", "@types/node": "^10.12.12", "@types/retry": "^0.12.2", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "babel-eslint": "^10.0.3", "coveralls": "^3.1.1", "eslint": "^8.38.0", "eslint-config-alloy": "^5.0.0", "eslint-plugin-prettier": "^4.2.1", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^13.2.1", "power-assert": "^1.6.1", "prettier": "^2.8.7", "ts-jest": "^29.1.0", "typescript": "^5.0.4"}, "engines": {"node": ">=8.6.0"}, "husky": {"hooks": {"pre-commit": "npm run build && git add . && lint-staged"}}, "lint-staged": {"*.ts": ["eslint --fix", "git add"]}}