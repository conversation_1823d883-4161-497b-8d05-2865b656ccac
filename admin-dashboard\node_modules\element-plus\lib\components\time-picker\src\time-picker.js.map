{"version": 3, "file": "time-picker.js", "sources": ["../../../../../../packages/components/time-picker/src/time-picker.tsx"], "sourcesContent": ["import { defineComponent, provide, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport customParseFormat from 'dayjs/plugin/customParseFormat.js'\nimport { UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport {\n  DEFAULT_FORMATS_TIME,\n  PICKER_POPPER_OPTIONS_INJECTION_KEY,\n} from './constants'\nimport Picker from './common/picker.vue'\nimport TimePickPanel from './time-picker-com/panel-time-pick.vue'\nimport TimeRangePanel from './time-picker-com/panel-time-range.vue'\nimport { timePickerDefaultProps } from './common/props'\n\ndayjs.extend(customParseFormat)\n\nexport default defineComponent({\n  name: 'ElTimePicker',\n  install: null,\n  props: {\n    ...timePickerDefaultProps,\n    /**\n     * @description whether to pick a time range\n     */\n    isRange: Boolean,\n  },\n  emits: [UPDATE_MODEL_EVENT],\n  setup(props, ctx) {\n    const commonPicker = ref<InstanceType<typeof Picker>>()\n    const [type, Panel] = props.isRange\n      ? ['timerange', TimeRangePanel]\n      : ['time', TimePickPanel]\n\n    const modelUpdater = (value: any) => ctx.emit(UPDATE_MODEL_EVENT, value)\n    provide(PICKER_POPPER_OPTIONS_INJECTION_KEY, props.popperOptions)\n    ctx.expose({\n      /**\n       * @description focus the Input component\n       */\n      focus: () => {\n        commonPicker.value?.focus()\n      },\n      /**\n       * @description blur the Input component\n       */\n      blur: () => {\n        commonPicker.value?.blur()\n      },\n      /**\n       * @description open the TimePicker popper\n       */\n      handleOpen: () => {\n        commonPicker.value?.handleOpen()\n      },\n      /**\n       * @description close the TimePicker popper\n       */\n      handleClose: () => {\n        commonPicker.value?.handleClose()\n      },\n    })\n\n    return () => {\n      const format = props.format ?? DEFAULT_FORMATS_TIME\n\n      return (\n        <Picker\n          {...props}\n          ref={commonPicker}\n          type={type}\n          format={format}\n          onUpdate:modelValue={modelUpdater}\n        >\n          {{\n            default: (props: any) => <Panel {...props} />,\n          }}\n        </Picker>\n      )\n    }\n  },\n})\n"], "names": ["dayjs", "extend", "customParseFormat", "defineComponent", "name", "install", "props", "UPDATE_MODEL_EVENT", "isRange", "Boolean", "ref", "TimeRangePanel", "TimePickPanel", "setup", "commonPicker", "type", "modelUpdater", "value", "ctx", "emit", "provide", "PICKER_POPPER_OPTIONS_INJECTION_KEY", "expose", "focus", "blur", "_createVNode", "Picker", "_mergeProps", "handleOpen", "handleClose"], "mappings": ";;;;;;;;;;;;;;;;;;;AAaAA,yBAAK,CAACC,MAAN,CAAaC,qCAAb,CAAA,CAAA;AAEA,iBAAeC,mBAAe,CAAC;AAC7BC,EAAAA,IAAI,EAAE,cADuB;AAE7BC,EAAAA,OAAO,EAAE,IAFoB;EAG7BC,KAAK,EAAE;;AAEL,IAAA,OAAA,EAAA,OAAA;AACJ,GAAA;AACA,EAAA,KAAA,EAAA,CAAAC,wBAAA,CAAA;AACIC,EAAAA,KAAAA,CAAAA,KAASC,EAAAA,GAAAA,EAAAA;IARkB,MAAA,YAAA,GAAAC,OAAA,EAAA,CAAA;IAUxB,MAAGH,CAAAA,IAAAA,EAAAA,KAAAA,CAAAA,GAAAA,KAVqB,CAAA,OAAA,GAAA,CAAA,WAAA,EAAAI,yBAAA,CAAA,GAAA,CAAA,MAAA,EAAAC,wBAAA,CAAA,CAAA;;AAW7BC,IAAAA,WAAMP,CAAAA,6CAAY,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA;IAChB,GAAMQ,CAAAA,MAAAA,CAAAA;AACN,MAAA,KAAOC,EAAD;;QAIAC,CAAAA,EAAAA,GAAAA,YAAgBC,CAAAA,KAAeC,KAAG,IAACC,GAAKZ,KAAAA,CAAAA,GAAAA,EAAAA,CAAAA,KAAT,EAA6BU,CAAAA;;AAClEG,MAAAA,IAAAA,EAAQC,MAAD;QACHC,MAAJ,CAAW;AACT,QAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,EAAA,CAAA;AACN,OAAA;AACA,MAAA,UAAA,EAAA,MAAA;AACMC,QAAAA,MAAa,CAAA;QACXT,CAAY,EAAA,GAAA,YAAZ,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,UAAA,EAAA,CAAA;OALO;;AAOT,QAAA,IAAA,EAAA,CAAA;AACN,QAAA,CAAA,EAAA,GAAA,YAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,WAAA,EAAA,CAAA;AACA,OAAA;AACMU,KAAAA,CAAAA,CAAAA;WACc,MAAA;MACb,IAZQ,EAAA,CAAA;;AAaT,MAAA,OAAAC,eAAA,CAAAC,iBAAA,EAAAC,cAAA,CAAA,KAAA,EAAA;AACN,QAAA,KAAA,EAAA,YAAA;AACA,QAAA,MAAA,EAAA,IAAA;AACMC,QAAAA,QAAU,EAAE,MAAM;QAChBd,qBAAA,EAAA,YAAA;OAjBO,CAAA,EAAA;;AAmBT,OAAA,CAAA,CAAA;AACN,KAAA,CAAA;AACA,GAAA;AACMe,CAAAA,CAAAA;;;;"}