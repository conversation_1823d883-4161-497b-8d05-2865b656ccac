{"version": 3, "file": "pager2.mjs", "sources": ["../../../../../../../packages/components/pagination/src/components/pager.vue"], "sourcesContent": ["<template>\n  <ul :class=\"nsPager.b()\" @click=\"onPagerClick\" @keyup.enter=\"onEnter\">\n    <li\n      v-if=\"pageCount > 0\"\n      :class=\"[\n        nsPager.is('active', currentPage === 1),\n        nsPager.is('disabled', disabled),\n      ]\"\n      class=\"number\"\n      :aria-current=\"currentPage === 1\"\n      :aria-label=\"t('el.pagination.currentPage', { pager: 1 })\"\n      :tabindex=\"tabindex\"\n    >\n      1\n    </li>\n    <li\n      v-if=\"showPrevMore\"\n      :class=\"prevMoreKls\"\n      :tabindex=\"tabindex\"\n      :aria-label=\"t('el.pagination.prevPages', { pager: pagerCount - 2 })\"\n      @mouseenter=\"onMouseEnter(true)\"\n      @mouseleave=\"quickPrevHover = false\"\n      @focus=\"onFocus(true)\"\n      @blur=\"quickPrevFocus = false\"\n    >\n      <d-arrow-left v-if=\"(quickPrevHover || quickPrevFocus) && !disabled\" />\n      <more-filled v-else />\n    </li>\n    <li\n      v-for=\"pager in pagers\"\n      :key=\"pager\"\n      :class=\"[\n        nsPager.is('active', currentPage === pager),\n        nsPager.is('disabled', disabled),\n      ]\"\n      class=\"number\"\n      :aria-current=\"currentPage === pager\"\n      :aria-label=\"t('el.pagination.currentPage', { pager })\"\n      :tabindex=\"tabindex\"\n    >\n      {{ pager }}\n    </li>\n    <li\n      v-if=\"showNextMore\"\n      :class=\"nextMoreKls\"\n      :tabindex=\"tabindex\"\n      :aria-label=\"t('el.pagination.nextPages', { pager: pagerCount - 2 })\"\n      @mouseenter=\"onMouseEnter()\"\n      @mouseleave=\"quickNextHover = false\"\n      @focus=\"onFocus()\"\n      @blur=\"quickNextFocus = false\"\n    >\n      <d-arrow-right v-if=\"(quickNextHover || quickNextFocus) && !disabled\" />\n      <more-filled v-else />\n    </li>\n    <li\n      v-if=\"pageCount > 1\"\n      :class=\"[\n        nsPager.is('active', currentPage === pageCount),\n        nsPager.is('disabled', disabled),\n      ]\"\n      class=\"number\"\n      :aria-current=\"currentPage === pageCount\"\n      :aria-label=\"t('el.pagination.currentPage', { pager: pageCount })\"\n      :tabindex=\"tabindex\"\n    >\n      {{ pageCount }}\n    </li>\n  </ul>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, watchEffect } from 'vue'\nimport { DArrowLeft, DArrowRight, MoreFilled } from '@element-plus/icons-vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { paginationPagerProps } from './pager'\n\ndefineOptions({\n  name: 'ElPaginationPager',\n})\nconst props = defineProps(paginationPagerProps)\nconst emit = defineEmits([CHANGE_EVENT])\nconst nsPager = useNamespace('pager')\nconst nsIcon = useNamespace('icon')\nconst { t } = useLocale()\n\nconst showPrevMore = ref(false)\nconst showNextMore = ref(false)\nconst quickPrevHover = ref(false)\nconst quickNextHover = ref(false)\nconst quickPrevFocus = ref(false)\nconst quickNextFocus = ref(false)\nconst pagers = computed(() => {\n  const pagerCount = props.pagerCount\n  const halfPagerCount = (pagerCount - 1) / 2\n  const currentPage = Number(props.currentPage)\n  const pageCount = Number(props.pageCount)\n  let showPrevMore = false\n  let showNextMore = false\n  if (pageCount > pagerCount) {\n    if (currentPage > pagerCount - halfPagerCount) {\n      showPrevMore = true\n    }\n    if (currentPage < pageCount - halfPagerCount) {\n      showNextMore = true\n    }\n  }\n  const array: number[] = []\n  if (showPrevMore && !showNextMore) {\n    const startPage = pageCount - (pagerCount - 2)\n    for (let i = startPage; i < pageCount; i++) {\n      array.push(i)\n    }\n  } else if (!showPrevMore && showNextMore) {\n    for (let i = 2; i < pagerCount; i++) {\n      array.push(i)\n    }\n  } else if (showPrevMore && showNextMore) {\n    const offset = Math.floor(pagerCount / 2) - 1\n    for (let i = currentPage - offset; i <= currentPage + offset; i++) {\n      array.push(i)\n    }\n  } else {\n    for (let i = 2; i < pageCount; i++) {\n      array.push(i)\n    }\n  }\n  return array\n})\n\nconst prevMoreKls = computed(() => [\n  'more',\n  'btn-quickprev',\n  nsIcon.b(),\n  nsPager.is('disabled', props.disabled),\n])\nconst nextMoreKls = computed(() => [\n  'more',\n  'btn-quicknext',\n  nsIcon.b(),\n  nsPager.is('disabled', props.disabled),\n])\n\nconst tabindex = computed(() => (props.disabled ? -1 : 0))\nwatchEffect(() => {\n  const halfPagerCount = (props.pagerCount - 1) / 2\n  showPrevMore.value = false\n  showNextMore.value = false\n  if (props.pageCount! > props.pagerCount) {\n    if (props.currentPage > props.pagerCount - halfPagerCount) {\n      showPrevMore.value = true\n    }\n    if (props.currentPage < props.pageCount! - halfPagerCount) {\n      showNextMore.value = true\n    }\n  }\n})\nfunction onMouseEnter(forward = false) {\n  if (props.disabled) return\n  if (forward) {\n    quickPrevHover.value = true\n  } else {\n    quickNextHover.value = true\n  }\n}\nfunction onFocus(forward = false) {\n  if (forward) {\n    quickPrevFocus.value = true\n  } else {\n    quickNextFocus.value = true\n  }\n}\nfunction onEnter(e: UIEvent) {\n  const target = e.target as HTMLElement\n  if (\n    target.tagName.toLowerCase() === 'li' &&\n    Array.from(target.classList).includes('number')\n  ) {\n    const newPage = Number(target.textContent)\n    if (newPage !== props.currentPage) {\n      emit(CHANGE_EVENT, newPage)\n    }\n  } else if (\n    target.tagName.toLowerCase() === 'li' &&\n    Array.from(target.classList).includes('more')\n  ) {\n    onPagerClick(e)\n  }\n}\nfunction onPagerClick(event: UIEvent) {\n  const target = event.target as HTMLElement\n  if (target.tagName.toLowerCase() === 'ul' || props.disabled) {\n    return\n  }\n  let newPage = Number(target.textContent)\n  const pageCount = props.pageCount!\n  const currentPage = props.currentPage\n  const pagerCountOffset = props.pagerCount - 2\n  if (target.className.includes('more')) {\n    if (target.className.includes('quickprev')) {\n      newPage = currentPage - pagerCountOffset\n    } else if (target.className.includes('quicknext')) {\n      newPage = currentPage + pagerCountOffset\n    }\n  }\n  if (!Number.isNaN(+newPage)) {\n    if (newPage < 1) {\n      newPage = 1\n    }\n    if (newPage > pageCount) {\n      newPage = pageCount\n    }\n  }\n  if (newPage !== currentPage) {\n    emit(CHANGE_EVENT, newPage)\n  }\n}\n</script>\n"], "names": ["showPrevMore", "showNextMore"], "mappings": ";;;;;;;;mCA8Ec,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAGA,IAAM,MAAA,OAAA,GAAU,aAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,MAAA,GAAS,aAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AAExB,IAAM,MAAA,YAAA,GAAe,IAAI,KAAK,CAAA,CAAA;AAC9B,IAAM,MAAA,YAAA,GAAe,IAAI,KAAK,CAAA,CAAA;AAC9B,IAAM,MAAA,cAAA,GAAiB,IAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,cAAA,GAAiB,IAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,cAAA,GAAiB,IAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,cAAA,GAAiB,IAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,MAAA,GAAS,SAAS,MAAM;AAC5B,MAAA,MAAM,aAAa,KAAM,CAAA,UAAA,CAAA;AACzB,MAAM,MAAA,cAAA,GAAA,CAAkB,aAAa,CAAK,IAAA,CAAA,CAAA;AAC1C,MAAM,MAAA,WAAA,GAAc,MAAO,CAAA,KAAA,CAAM,WAAW,CAAA,CAAA;AAC5C,MAAM,MAAA,SAAA,GAAY,MAAO,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AACxC,MAAA,IAAIA,aAAe,GAAA,KAAA,CAAA;AACnB,MAAA,IAAIC,aAAe,GAAA,KAAA,CAAA;AACnB,MAAA,IAAI,YAAY,UAAY,EAAA;AAC1B,QAAI,IAAA,WAAA,GAAc,aAAa,cAAgB,EAAA;AAC7C,UAAAD,aAAe,GAAA,IAAA,CAAA;AAAA,SACjB;AACA,QAAI,IAAA,WAAA,GAAc,YAAY,cAAgB,EAAA;AAC5C,UAAAC,aAAe,GAAA,IAAA,CAAA;AAAA,SACjB;AAAA,OACF;AACA,MAAA,MAAM,QAAkB,EAAC,CAAA;AACzB,MAAID,IAAAA,aAAAA,IAAgB,CAACC,aAAc,EAAA;AACjC,QAAM,MAAA,SAAA,GAAY,aAAa,UAAa,GAAA,CAAA,CAAA,CAAA;AAC5C,QAAA,KAAA,IAAS,CAAI,GAAA,SAAA,EAAW,CAAI,GAAA,SAAA,EAAW,CAAK,EAAA,EAAA;AAC1C,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACF,MAAA,IAAW,CAACD,aAAAA,IAAgBC,aAAc,EAAA;AACxC,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,UAAA,EAAY,CAAK,EAAA,EAAA;AACnC,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACF,MAAA,IAAWD,iBAAgBC,aAAc,EAAA;AACvC,QAAA,MAAM,MAAS,GAAA,IAAA,CAAK,KAAM,CAAA,UAAA,GAAa,CAAC,CAAI,GAAA,CAAA,CAAA;AAC5C,QAAA,KAAA,IAAS,IAAI,WAAc,GAAA,MAAA,EAAQ,CAAK,IAAA,WAAA,GAAc,QAAQ,CAAK,EAAA,EAAA;AACjE,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACK,MAAA;AACL,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,SAAA,EAAW,CAAK,EAAA,EAAA;AAClC,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACF;AACA,MAAO,OAAA,KAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AAAA,MACjC,MAAA;AAAA,MACA,eAAA;AAAA,MACA,OAAO,CAAE,EAAA;AAAA,MACT,OAAQ,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,KACtC,CAAA,CAAA;AACD,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AAAA,MACjC,MAAA;AAAA,MACA,eAAA;AAAA,MACA,OAAO,CAAE,EAAA;AAAA,MACT,OAAQ,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,KACtC,CAAA,CAAA;AAED,IAAA,MAAM,WAAW,QAAS,CAAA,MAAO,KAAM,CAAA,QAAA,GAAW,KAAK,CAAE,CAAA,CAAA;AACzD,IAAA,WAAA,CAAY,MAAM;AAChB,MAAM,MAAA,cAAA,GAAA,CAAkB,KAAM,CAAA,UAAA,GAAa,CAAK,IAAA,CAAA,CAAA;AAChD,MAAA,YAAA,CAAa,KAAQ,GAAA,KAAA,CAAA;AACrB,MAAA,YAAA,CAAa,KAAQ,GAAA,KAAA,CAAA;AACrB,MAAI,IAAA,KAAA,CAAM,SAAa,GAAA,KAAA,CAAM,UAAY,EAAA;AACvC,QAAA,IAAI,KAAM,CAAA,WAAA,GAAc,KAAM,CAAA,UAAA,GAAa,cAAgB,EAAA;AACzD,UAAA,YAAA,CAAa,KAAQ,GAAA,IAAA,CAAA;AAAA,SACvB;AACA,QAAA,IAAI,KAAM,CAAA,WAAA,GAAc,KAAM,CAAA,SAAA,GAAa,cAAgB,EAAA;AACzD,UAAA,YAAA,CAAa,KAAQ,GAAA,IAAA,CAAA;AAAA,SACvB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AACD,IAAS,SAAA,YAAA,CAAa,UAAU,KAAO,EAAA;AACrC,MAAA,IAAI,MAAM,QAAU;AACpB,QAAA,OAAa;AACX,MAAA,IAAA,OAAA,EAAA;AAAuB,QAClB,cAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACL,OAAA,MAAA;AAAuB,QACzB,cAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACF;AACA,KAAS;AACP,IAAA,SAAa,OAAA,CAAA,OAAA,GAAA,KAAA,EAAA;AACX,MAAA,IAAA,OAAA,EAAA;AAAuB,QAClB,cAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AACL,OAAA,MAAA;AAAuB,QACzB,cAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OACF;AACA,KAAA;AACE,IAAA,iBAAe,CAAE,EAAA;AACjB,MAAA,MACS,MAAA,GAAA,CAAA,CAAA,MAAoB,CAAA;AAG3B,MAAM,IAAA,MAAA,CAAA,OAAU,CAAO,WAAA,EAAO,KAAW,IAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,QAAA,CAAA,QAAA,CAAA,EAAA;AACzC,QAAI,MAAA,OAAA,GAAY,MAAM,CAAa,MAAA,CAAA,WAAA,CAAA,CAAA;AACjC,QAAA,IAAA,iBAAmB,CAAO,WAAA,EAAA;AAAA,UAC5B,IAAA,CAAA,YAAA,EAAA,OAAA,CAAA,CAAA;AAAA,SAEA;AAGA,OAAA,MAAA,IAAA,MAAc,CAAA,OAAA,CAAA,WAAA,EAAA,KAAA,IAAA,IAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA;AAAA,QAChB,YAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OACF;AACA,KAAA;AACE,IAAA,qBAAqB,CAAA,KAAA,EAAA;AACrB,MAAA,YAAmB,GAAA,KAAA,CAAA,MAAA,CAAA;AACjB,MAAA,IAAA,MAAA,CAAA,OAAA,CAAA,WAAA,EAAA,KAAA,IAAA,IAAA,KAAA,CAAA,QAAA,EAAA;AAAA,QACF,OAAA;AACA,OAAI;AACJ,MAAA,IAAA,gBAAwB,CAAA,MAAA,CAAA,WAAA,CAAA,CAAA;AACxB,MAAA,MAAM,iBAAoB,CAAA,SAAA,CAAA;AAC1B,MAAM,MAAA,WAAA,GAAA,KAAmB,YAAmB,CAAA;AAC5C,MAAA,MAAW,gBAAmB,GAAA,KAAA,CAAA,UAAS,GAAA,CAAA,CAAA;AACrC,MAAA,IAAA,MAAW,CAAA,SAAA,CAAA,QAAmB,CAAA,MAAA,CAAA,EAAA;AAC5B,QAAA,IAAA,MAAA,CAAA,SAAwB,CAAA,QAAA,CAAA,WAAA,CAAA,EAAA;AAAA,UACf,OAAA,GAAA,WAAiB,GAAA;AAC1B,SAAA,MAAA,IAAA,MAAwB,CAAA,SAAA,CAAA,QAAA,CAAA,WAAA,CAAA,EAAA;AAAA,UAC1B,OAAA,GAAA,WAAA,GAAA,gBAAA,CAAA;AAAA,SACF;AACA,OAAA;AACE,MAAA,IAAA,CAAA,YAAiB,CAAA,CAAA,OAAA,CAAA,EAAA;AACf,QAAU,IAAA,OAAA,GAAA,CAAA,EAAA;AAAA,UACZ,OAAA,GAAA,CAAA,CAAA;AACA,SAAA;AACE,QAAU,IAAA,OAAA,GAAA,SAAA,EAAA;AAAA,UACZ,OAAA,GAAA,SAAA,CAAA;AAAA,SACF;AACA,OAAA;AACE,MAAA,IAAA,uBAA0B,EAAA;AAAA,QAC5B,IAAA,CAAA,YAAA,EAAA,OAAA,CAAA,CAAA;AAAA,OACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}