const fs = require('fs');
const path = require('path');

console.log('🔍 检查服务器端数据状态...\n');

// 检查数据库文件
const dbPath = path.join(__dirname, 'data', 'db.json');
console.log('📁 数据库文件路径:', dbPath);

if (fs.existsSync(dbPath)) {
  console.log('✅ 数据库文件存在');
  
  try {
    const dbContent = fs.readFileSync(dbPath, 'utf8');
    const db = JSON.parse(dbContent);
    
    console.log('\n📊 数据库内容统计:');
    console.log('   用户数量:', db.users ? db.users.length : 0);
    console.log('   同步数据记录:', db.syncData ? db.syncData.length : 0);
    
    // 检查用户信息
    if (db.users && db.users.length > 0) {
      console.log('\n👤 用户列表:');
      db.users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.username} (ID: ${user.id})`);
        console.log(`      会员状态: ${user.isMember ? '✅ 会员' : '❌ 非会员'}`);
        console.log(`      同步启用: ${user.isDataSyncEnabled ? '✅ 启用' : '❌ 禁用'}`);
      });
    }
    
    // 检查同步数据
    if (db.syncData && db.syncData.length > 0) {
      console.log('\n📦 同步数据记录:');
      db.syncData.forEach((record, index) => {
        console.log(`   ${index + 1}. 用户ID: ${record.userId}`);
        console.log(`      上传时间: ${record.timestamp}`);
        console.log(`      数据大小: ${JSON.stringify(record.data).length} bytes`);
        
        // 检查数据内容
        if (record.data) {
          const data = record.data;
          console.log(`      包含数据:`);
          if (data.novels) console.log(`        - 小说: ${Array.isArray(data.novels) ? data.novels.length : 0} 本`);
          if (data.characterCards) console.log(`        - 角色卡片: ${Array.isArray(data.characterCards) ? data.characterCards.length : 0} 个`);
          if (data.characterTypes) console.log(`        - 角色类型: ${Array.isArray(data.characterTypes) ? data.characterTypes.length : 0} 个`);
          if (data.knowledgeDocuments) console.log(`        - 知识库: ${Array.isArray(data.knowledgeDocuments) ? data.knowledgeDocuments.length : 0} 个`);
          if (data.userSettings) console.log(`        - 用户设置: ✅`);
        }
        console.log('');
      });
    } else {
      console.log('\n⚠️ 没有找到任何同步数据记录！');
      console.log('   这说明还没有设备成功上传过数据到云端');
    }
    
    // 检查特定用户的数据
    const targetUserId = '87ca175d-227c-4afe-95ef-1249b7d7cd3f'; // wblx7的用户ID
    const userSyncData = db.syncData ? db.syncData.filter(record => record.userId === targetUserId) : [];
    
    console.log(`\n🎯 用户 wblx7 (${targetUserId}) 的同步数据:`);
    if (userSyncData.length > 0) {
      const latestData = userSyncData[userSyncData.length - 1];
      console.log('   最新同步时间:', latestData.timestamp);
      console.log('   数据内容:');
      if (latestData.data.novels) {
        console.log(`     📚 小说数量: ${latestData.data.novels.length}`);
        if (latestData.data.novels.length > 0) {
          console.log('     前几本小说:');
          latestData.data.novels.slice(0, 3).forEach((novel, i) => {
            console.log(`       ${i + 1}. ${novel.title || '未命名'}`);
          });
        }
      }
    } else {
      console.log('   ❌ 该用户还没有上传过任何数据');
    }
    
  } catch (e) {
    console.log('❌ 解析数据库文件失败:', e.message);
  }
} else {
  console.log('❌ 数据库文件不存在');
}

// 检查数据目录
const dataDir = path.join(__dirname, 'data');
console.log('\n📁 数据目录检查:');
if (fs.existsSync(dataDir)) {
  const files = fs.readdirSync(dataDir);
  console.log('   目录内容:', files);
  
  files.forEach(file => {
    const filePath = path.join(dataDir, file);
    const stats = fs.statSync(filePath);
    console.log(`   ${file}: ${stats.size} bytes, 修改时间: ${stats.mtime}`);
  });
} else {
  console.log('   ❌ 数据目录不存在');
}

console.log('\n🏁 检查完成');
console.log('\n💡 解决建议:');
console.log('1. 如果没有同步数据记录，需要先在电脑端上传数据');
console.log('2. 如果有数据但小说数量为0，说明电脑端数据收集有问题');
console.log('3. 确保用户是会员且启用了数据同步功能');
