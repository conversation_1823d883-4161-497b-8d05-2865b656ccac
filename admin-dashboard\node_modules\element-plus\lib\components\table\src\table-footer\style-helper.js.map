{"version": 3, "file": "style-helper.js", "sources": ["../../../../../../../packages/components/table/src/table-footer/style-helper.ts"], "sourcesContent": ["import { useNamespace } from '@element-plus/hooks'\nimport {\n  ensurePosition,\n  getFixedColumnOffset,\n  getFixedColumnsClass,\n} from '../util'\nimport useMapState from './mapState-helper'\n\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { DefaultRow } from '../table/defaults'\nimport type { TableFooter } from '.'\n\nfunction useStyle<T extends DefaultRow>(props: TableFooter<T>) {\n  const { columns } = useMapState()\n  const ns = useNamespace('table')\n\n  const getCellClasses = (columns: TableColumnCtx<T>[], cellIndex: number) => {\n    const column = columns[cellIndex]\n    const classes = [\n      ns.e('cell'),\n      column.id,\n      column.align,\n      column.labelClassName,\n      ...getFixedColumnsClass(ns.b(), cellIndex, column.fixed, props.store),\n    ]\n    if (column.className) {\n      classes.push(column.className)\n    }\n    if (!column.children) {\n      classes.push(ns.is('leaf'))\n    }\n    return classes\n  }\n\n  const getCellStyles = (column: TableColumnCtx<T>, cellIndex: number) => {\n    const fixedStyle = getFixedColumnOffset(\n      cellIndex,\n      column.fixed,\n      props.store\n    )\n    ensurePosition(fixedStyle, 'left')\n    ensurePosition(fixedStyle, 'right')\n    return fixedStyle\n  }\n\n  return {\n    getCellClasses,\n    getCellStyles,\n    columns,\n  }\n}\n\nexport default useStyle\n"], "names": ["useMapState", "useNamespace", "getFixedColumnsClass", "getFixedColumnOffset", "ensurePosition"], "mappings": ";;;;;;;;AAOA,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,MAAM,EAAE,OAAO,EAAE,GAAGA,yBAAW,EAAE,CAAC;AACpC,EAAE,MAAM,EAAE,GAAGC,kBAAY,CAAC,OAAO,CAAC,CAAC;AACnC,EAAE,MAAM,cAAc,GAAG,CAAC,QAAQ,EAAE,SAAS,KAAK;AAClD,IAAI,MAAM,MAAM,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC;AACvC,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC;AAClB,MAAM,MAAM,CAAC,EAAE;AACf,MAAM,MAAM,CAAC,KAAK;AAClB,MAAM,MAAM,CAAC,cAAc;AAC3B,MAAM,GAAGC,yBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC;AAC3E,KAAK,CAAC;AACN,IAAI,IAAI,MAAM,CAAC,SAAS,EAAE;AAC1B,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;AACrC,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC1B,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AAClC,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB,GAAG,CAAC;AACJ,EAAE,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,SAAS,KAAK;AAC/C,IAAI,MAAM,UAAU,GAAGC,yBAAoB,CAAC,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;AAClF,IAAIC,mBAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;AACvC,IAAIA,mBAAc,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;AACxC,IAAI,OAAO,UAAU,CAAC;AACtB,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,cAAc;AAClB,IAAI,aAAa;AACjB,IAAI,OAAO;AACX,GAAG,CAAC;AACJ;;;;"}