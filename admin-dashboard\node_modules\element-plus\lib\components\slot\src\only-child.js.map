{"version": 3, "file": "only-child.js", "sources": ["../../../../../../packages/components/slot/src/only-child.tsx"], "sourcesContent": ["import {\n  Comment,\n  Fragment,\n  Text,\n  cloneVNode,\n  defineComponent,\n  inject,\n  withDirectives,\n} from 'vue'\nimport { NOOP, debugWarn, isObject } from '@element-plus/utils'\nimport {\n  FORWARD_REF_INJECTION_KEY,\n  useForwardRefDirective,\n  useNamespace,\n} from '@element-plus/hooks'\n\nimport type { Ref, VNode } from 'vue'\n\nconst NAME = 'ElOnlyChild'\n\nexport const OnlyChild = defineComponent({\n  name: NAME,\n  setup(_, { slots, attrs }) {\n    const forwardRefInjection = inject(FORWARD_REF_INJECTION_KEY)\n    const forwardRefDirective = useForwardRefDirective(\n      forwardRefInjection?.setForwardRef ?? NOOP\n    )\n    return () => {\n      const defaultSlot = slots.default?.(attrs)\n      if (!defaultSlot) return null\n\n      if (defaultSlot.length > 1) {\n        debugWarn(NAME, 'requires exact only one valid child.')\n        return null\n      }\n\n      const firstLegitNode = findFirstLegitChild(defaultSlot)\n      if (!firstLegitNode) {\n        debugWarn(NAME, 'no valid child node found')\n        return null\n      }\n\n      return withDirectives(cloneVNode(firstLegitNode!, attrs), [\n        [forwardRefDirective],\n      ])\n    }\n  },\n})\n\nfunction findFirstLegitChild(node: VNode[] | undefined): VNode | null {\n  if (!node) return null\n  const children = node as VNode[]\n  for (const child of children) {\n    /**\n     * when user uses h(Fragment, [text]) to render plain string,\n     * this switch case just cannot handle, when the value is primitives\n     * we should just return the wrapped string\n     */\n    if (isObject(child)) {\n      switch (child.type) {\n        case Comment:\n          continue\n        case Text:\n        case 'svg':\n          return wrapTextContent(child)\n        case Fragment:\n          return findFirstLegitChild(child.children as VNode[])\n        default:\n          return child\n      }\n    }\n    return wrapTextContent(child)\n  }\n  return null\n}\n\nfunction wrapTextContent(s: string | VNode) {\n  const ns = useNamespace('only-child')\n  return <span class={ns.e('content')}>{s}</span>\n}\n\nexport type OnlyChildExpose = {\n  forwardRef: Ref<HTMLElement>\n}\n"], "names": ["NAME", "<PERSON><PERSON><PERSON><PERSON>", "defineComponent", "name", "slots", "attrs", "forwardRefInjection", "inject", "FORWARD_REF_INJECTION_KEY", "forwardRefDirective", "useForwardRefDirective", "setForwardRef", "NOOP", "defaultSlot", "debugWarn", "firstLegitNode", "findFirstLegitChild", "withDirectives", "cloneVNode", "node", "children", "child", "Text", "Fragment", "isObject", "_createVNode"], "mappings": ";;;;;;;;;;AAkBA,MAAMA,IAAI,GAAG,aAAb,CAAA;AAEaC,MAAAA,SAAS,GAAGC,mBAAe,CAAC;AACvCC,EAAAA,IAAI,EAAEH,IADiC;;IAElC,KAAA;IAAMI,KAAF;AAASC,GAAAA,EAAAA;AAAT,IAAkB,IAAA,EAAA,CAAA;AACzB,IAAA,MAAMC,mBAAmB,GAAGC,UAAM,CAACC,+BAAD,CAAlC,CAAA;IACA,MAAMC,mBAAmB,GAAGC,4BAAsB,CAChDJ,yBAAqBK,IAAAA,IAAAA,GAAiBC,KAAAA,CADU,GAAlD,mBAAA,CAAA,aAAA,KAAA,IAAA,GAAA,EAAA,GAAAA,WAAA,CAAA,CAAA;AAGA,IAAA,OAAO,MAAM;AACX,MAAA,IAAA;AACA,MAAA,MAAKC,WAAa,GAAA,CAAA,GAAA,GAAA,KAAA,CAAA,OAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,GAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;;AAElB,QAAA,OAAe,IAAA,CAAA;AACbC,MAAAA,IAAAA,WAAUd,CAAD,MAAO,GAAA,CAAA,EAAA;AAChB,QAAAc,eAAA,CAAA,IAAA,EAAA,sCAAA,CAAA,CAAA;AACD,QAAA,OAAA,IAAA,CAAA;;AAED,MAAA,MAAMC,cAAc,GAAGC,mBAAmB,CAACH,WAAD,CAA1C,CAAA;;QACIC,iDAAiB,CAAA,CAAA;AACnBA,QAAAA,OAAAA,IAAUd,CAAAA;AACV,OAAA;AACD,MAAA,OAAAiB,kBAAA,CAAAC,cAAA,CAAA,cAAA,EAAA,KAAA,CAAA,EAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA;;AAED,GAAA;;AAIH,SAAA,mBAAA,CAAA,IAAA,EAAA;;AA1BsC,IAAlC,OAAA,IAAA,CAAA;;AA6BP,EAASF,KAAAA,MAAAA,KAAAA,IAAAA,QAAoBG,EAAAA;AAC3B,IAAA,IAAKA,eAAM,CAAA,KAAA,CAAA,EAAA;MACLC,QAAAA,KAAWD,CAAjB,IAAA;;AACA,UAAWE,SAASD;AAClB,QAAA,KAAAE,QAAA,CAAA;AACJ,QAAA,KAAA,KAAA;AACA,UAAA,OAAA,eAAA,CAAA,KAAA,CAAA,CAAA;AACA,QAAA,KAAAC,YAAA;AACA,UAAA,OAAA,mBAAA,CAAA,KAAA,CAAA,QAAA,CAAA,CAAA;AACI,QAAIC;UACMH,OAAAA,KAAR,CAAA;AACE,OAAA;AACE,KAAA;;AACF,GAAA;AACA,EAAA,OAAA,IAAK,CAAL;;;AAEA,EAAA,MAAA,EAAA,GAAKE,oBAAL,CAAA,YAAA,CAAA,CAAA;AACE,EAAA,OAAAE,eAA0B,CAAA,MAAA,EAAA;;AAC5B,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACE;;;;"}