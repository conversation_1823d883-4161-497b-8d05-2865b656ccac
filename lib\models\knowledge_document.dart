import 'package:uuid/uuid.dart';

class KnowledgeDocument {
  final String id;
  String title;
  String content;
  String category;
  String? filePath;  // 上传文件的路径
  String? fileType;  // 文件类型
  bool isVisible;    // 是否在列表中显示
  bool isReadOnly;   // 是否只读（不可查看和编辑内容）
  List<String> tags; // 标签列表
  bool isDefault;    // 是否为默认分类
  final DateTime createdAt;
  DateTime updatedAt;

  KnowledgeDocument({
    String? id,
    required this.title,
    required this.content,
    required this.category,
    this.filePath,
    this.fileType,
    this.isVisible = true,
    this.isReadOnly = false,
    this.tags = const [],
    this.isDefault = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) :
    id = id ?? const Uuid().v4(),
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'content': content,
    'category': category,
    'filePath': filePath,
    'fileType': fileType,
    'isVisible': isVisible,
    'isReadOnly': isReadOnly,
    'tags': tags,
    'isDefault': isDefault,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
  };

  factory KnowledgeDocument.fromJson(Map<String, dynamic> json) => KnowledgeDocument(
    id: json['id'] as String,
    title: json['title'] as String,
    content: json['content'] as String,
    category: json['category'] as String,
    filePath: json['filePath'] as String?,
    fileType: json['fileType'] as String?,
    isVisible: json['isVisible'] as bool? ?? true,
    isReadOnly: json['isReadOnly'] as bool? ?? false,
    tags: List<String>.from(json['tags'] as List? ?? []),
    isDefault: json['isDefault'] as bool? ?? false,
    createdAt: DateTime.parse(json['createdAt'] as String),
    updatedAt: DateTime.parse(json['updatedAt'] as String),
  );
} 