import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

import 'package:http/io_client.dart';

/// 简单的Gemini API测试工具
class GeminiTest {
  /// 测试基本的Gemini API连接
  static Future<Map<String, dynamic>> testBasicConnection({
    required String apiKey,
    String model = 'gemini-1.5-flash',
  }) async {
    try {
      print('开始测试Gemini API连接...');

      // 使用最基本的HTTP客户端
      final client = http.Client();

      // 构建URL
      final url = 'https://generativelanguage.googleapis.com/v1beta/models/$model:generateContent?key=$apiKey';
      print('请求URL: $url');

      // 构建请求体
      final requestBody = {
        'contents': [
          {
            'parts': [
              {'text': '请简单回答"你好"'}
            ]
          }
        ],
        'generationConfig': {
          'maxOutputTokens': 50,
          'temperature': 0.1,
        }
      };

      print('请求体: ${jsonEncode(requestBody)}');

      // 发送请求
      final startTime = DateTime.now();
      final response = await client.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      ).timeout(const Duration(seconds: 30));

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      client.close();

      print('响应状态码: ${response.statusCode}');
      print('响应时间: ${duration.inMilliseconds}ms');
      print('响应体: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          final text = data['candidates'][0]['content']['parts'][0]['text'];
          return {
            'success': true,
            'message': '连接成功',
            'response': text,
            'duration': duration.inMilliseconds,
          };
        } else {
          return {
            'success': false,
            'message': '响应格式异常',
            'response': response.body,
          };
        }
      } else {
        return {
          'success': false,
          'message': 'HTTP错误: ${response.statusCode}',
          'response': response.body,
        };
      }
    } catch (e) {
      print('测试失败: $e');
      return {
        'success': false,
        'message': '连接失败: $e',
        'error': e.toString(),
      };
    }
  }
  
  /// 测试网络连接
  static Future<Map<String, dynamic>> testNetworkConnection() async {
    try {
      print('测试网络连接...');

      final client = http.Client();
      final response = await client.get(
        Uri.parse('https://www.google.com'),
      ).timeout(const Duration(seconds: 10));

      client.close();

      if (response.statusCode == 200) {
        return {
          'success': true,
          'message': '网络连接正常',
        };
      } else {
        return {
          'success': false,
          'message': '网络连接异常: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': '网络连接失败: $e',
      };
    }
  }

  /// 测试DNS解析
  static Future<Map<String, dynamic>> testDnsResolution() async {
    try {
      print('测试DNS解析...');

      final addresses = await InternetAddress.lookup('generativelanguage.googleapis.com');

      if (addresses.isNotEmpty) {
        return {
          'success': true,
          'message': 'DNS解析成功',
          'addresses': addresses.map((addr) => addr.address).toList(),
        };
      } else {
        return {
          'success': false,
          'message': 'DNS解析失败：未找到地址',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'DNS解析失败: $e',
      };
    }
  }

  /// 测试Socket连接
  static Future<Map<String, dynamic>> testSocketConnection() async {
    try {
      print('测试Socket连接...');

      final socket = await Socket.connect(
        'generativelanguage.googleapis.com',
        443,
      ).timeout(const Duration(seconds: 15));

      socket.destroy();

      return {
        'success': true,
        'message': 'Socket连接成功',
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Socket连接失败: $e',
      };
    }
  }



  /// 综合诊断测试
  static Future<Map<String, dynamic>> comprehensiveDiagnosis({
    required String apiKey,
  }) async {
    final results = <String, dynamic>{};

    // 1. 测试DNS解析
    print('\n=== 步骤1: DNS解析测试 ===');
    results['dns'] = await testDnsResolution();
    print('DNS测试结果: ${results['dns']['success'] ? '成功' : '失败'}');

    // 2. 测试Socket连接
    print('\n=== 步骤2: Socket连接测试 ===');
    results['socket'] = await testSocketConnection();
    print('Socket测试结果: ${results['socket']['success'] ? '成功' : '失败'}');

    // 3. 测试基本网络
    print('\n=== 步骤3: 基本网络测试 ===');
    results['network'] = await testNetworkConnection();
    print('网络测试结果: ${results['network']['success'] ? '成功' : '失败'}');

    // 4. 测试Gemini API
    print('\n=== 步骤4: Gemini API测试 ===');
    results['gemini'] = await testBasicConnection(apiKey: apiKey);
    print('Gemini测试结果: ${results['gemini']['success'] ? '成功' : '失败'}');

    // 分析结果
    final allSuccess = results.values.every((result) => result['success'] == true);

    return {
      'success': allSuccess,
      'message': allSuccess ? '所有测试通过' : '部分测试失败',
      'details': results,
    };
  }



  /// 使用系统代理测试Gemini API连接
  static Future<Map<String, dynamic>> testWithSystemProxy({
    required String apiKey,
    String model = 'gemini-1.5-flash',
  }) async {
    try {
      print('开始使用系统代理测试Gemini API连接...');

      // 创建支持系统代理的HTTP客户端
      final httpClient = HttpClient();

      // 设置超时
      httpClient.connectionTimeout = const Duration(seconds: 30);
      httpClient.idleTimeout = const Duration(seconds: 120);

      // 强制使用系统代理设置
      httpClient.findProxy = (uri) {
        print('正在为 ${uri.host} 查找系统代理...');

        // 检查环境变量
        final httpsProxy = Platform.environment['HTTPS_PROXY'] ??
            Platform.environment['https_proxy'];
        final httpProxy = Platform.environment['HTTP_PROXY'] ??
            Platform.environment['http_proxy'];

        if (httpsProxy != null) {
          print('使用HTTPS_PROXY: $httpsProxy');
          return 'PROXY $httpsProxy';
        } else if (httpProxy != null) {
          print('使用HTTP_PROXY: $httpProxy');
          return 'PROXY $httpProxy';
        }

        // 对于Google API，强制尝试常见代理端口
        if (uri.host.contains('googleapis.com')) {
          print('Google API请求，尝试常见代理端口');
          // 按优先级尝试常见代理端口
          return 'PROXY 127.0.0.1:7890; PROXY 127.0.0.1:7891; PROXY 127.0.0.1:10809; PROXY 127.0.0.1:1080; DIRECT';
        }

        print('使用直连');
        return 'DIRECT';
      };

      // 完全跳过SSL证书验证，解决所有SSL握手问题
      httpClient.badCertificateCallback = (cert, host, port) {
        print('Gemini测试 - 跳过SSL证书验证: $host:$port');
        print('证书主题: ${cert.subject}');
        print('证书颁发者: ${cert.issuer}');
        // 对所有主机都返回true，完全跳过证书验证
        return true;
      };

      final client = IOClient(httpClient);

      // 构建URL
      final url = 'https://generativelanguage.googleapis.com/v1beta/models/$model:generateContent?key=$apiKey';
      print('系统代理请求URL: $url');

      // 构建请求体
      final requestBody = {
        'contents': [
          {
            'parts': [
              {'text': '请简单回答"你好"'}
            ]
          }
        ],
        'generationConfig': {
          'maxOutputTokens': 50,
          'temperature': 0.1,
        }
      };

      print('系统代理请求体: ${jsonEncode(requestBody)}');

      // 发送请求
      final startTime = DateTime.now();
      final response = await client.post(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        },
        body: jsonEncode(requestBody),
      ).timeout(const Duration(seconds: 30));

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      client.close();

      print('系统代理响应状态码: ${response.statusCode}');
      print('系统代理响应时间: ${duration.inMilliseconds}ms');
      print('系统代理响应体: ${response.body}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          final text = data['candidates'][0]['content']['parts'][0]['text'];
          return {
            'success': true,
            'message': '系统代理连接成功',
            'response': text,
            'duration': duration.inMilliseconds,
          };
        } else {
          return {
            'success': false,
            'message': '系统代理响应格式异常',
            'response': response.body,
          };
        }
      } else {
        return {
          'success': false,
          'message': '系统代理HTTP错误: ${response.statusCode}',
          'response': response.body,
        };
      }
    } catch (e) {
      print('系统代理测试失败: $e');
      return {
        'success': false,
        'message': '系统代理连接失败: $e',
        'error': e.toString(),
      };
    }
  }
}
