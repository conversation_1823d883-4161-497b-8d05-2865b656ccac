{"version": 3, "file": "pt-br.js", "sources": ["../../../../../packages/locale/lang/pt-br.ts"], "sourcesContent": ["export default {\n  name: 'pt-br',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Confirmar',\n      clear: '<PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>a',\n      today: '<PERSON><PERSON>',\n      cancel: 'Cancelar',\n      clear: '<PERSON><PERSON>',\n      confirm: 'Confirmar',\n      selectDate: 'Selecione a data',\n      selectTime: 'Selecione a hora',\n      startDate: 'Data inicial',\n      startTime: 'Hora inicial',\n      endDate: 'Data final',\n      endTime: 'Hora final',\n      prevYear: 'Ano anterior',\n      nextYear: 'Próximo ano',\n      prevMonth: 'Mês anterior',\n      nextMonth: 'Próximo mês',\n      year: '',\n      month1: 'Janeiro',\n      month2: 'Fevereiro',\n      month3: 'Março',\n      month4: 'Abril',\n      month5: 'Maio',\n      month6: 'Junho',\n      month7: 'Julho',\n      month8: 'Agosto',\n      month9: 'Setem<PERSON>',\n      month10: 'Outubro',\n      month11: 'Novembro',\n      month12: 'Dezembro',\n      // week: 'semana',\n      weeks: {\n        sun: 'Dom',\n        mon: 'Seg',\n        tue: 'Ter',\n        wed: 'Qua',\n        thu: 'Qui',\n        fri: 'Sex',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Fev',\n        mar: 'Mar',\n        apr: 'Abr',\n        may: 'Mai',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Ago',\n        sep: 'Set',\n        oct: 'Out',\n        nov: 'Nov',\n        dec: 'Dez',\n      },\n    },\n    select: {\n      loading: 'Carregando',\n      noMatch: 'Sem resultados',\n      noData: 'Sem dados',\n      placeholder: 'Selecione',\n    },\n    mention: {\n      loading: 'Carregando',\n    },\n    cascader: {\n      noMatch: 'Sem resultados',\n      loading: 'Carregando',\n      placeholder: 'Selecione',\n      noData: 'Sem dados',\n    },\n    pagination: {\n      goto: 'Ir para',\n      pagesize: '/página',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mensagem',\n      confirm: 'Confirmar',\n      cancel: 'Cancelar',\n      error: 'Erro!',\n    },\n    upload: {\n      deleteTip: 'aperte delete para apagar',\n      delete: 'Apagar',\n      preview: 'Pré-visualizar',\n      continue: 'Continuar',\n    },\n    table: {\n      emptyText: 'Sem dados',\n      confirmFilter: 'Confirmar',\n      resetFilter: 'Limpar',\n      clearFilter: 'Todos',\n      sumText: 'Total',\n    },\n    tour: {\n      next: 'Próximo',\n      previous: 'Anterior',\n      finish: 'Finalizar',\n    },\n    tree: {\n      emptyText: 'Sem dados',\n    },\n    transfer: {\n      noMatch: 'Sem resultados',\n      noData: 'Sem dados',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Digite uma palavra-chave',\n      noCheckedFormat: '{total} itens',\n      hasCheckedFormat: '{checked}/{total} selecionados',\n    },\n    image: {\n      error: 'Erro ao carregar imagem',\n    },\n    pageHeader: {\n      title: 'Voltar',\n    },\n    popconfirm: {\n      confirmButtonText: 'Sim',\n      cancelButtonText: 'Não',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;;;AAAA,WAAe;AACf,EAAE,IAAI,EAAE,OAAO;AACf,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,YAAY;AACzB,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,OAAO;AAClB,MAAM,KAAK,EAAE,MAAM;AACnB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,QAAQ;AACrB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,UAAU,EAAE,kBAAkB;AACpC,MAAM,UAAU,EAAE,kBAAkB;AACpC,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,SAAS,EAAE,cAAc;AAC/B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,QAAQ,EAAE,cAAc;AAC9B,MAAM,QAAQ,EAAE,gBAAgB;AAChC,MAAM,SAAS,EAAE,iBAAiB;AAClC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,MAAM;AACpB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,OAAO,EAAE,SAAS;AACxB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,OAAO,EAAE,UAAU;AACzB,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,WAAW,EAAE,WAAW;AAC9B,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,YAAY;AAC3B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,OAAO,EAAE,YAAY;AAC3B,MAAM,WAAW,EAAE,WAAW;AAC9B,MAAM,MAAM,EAAE,WAAW;AACzB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,SAAS;AACrB,MAAM,QAAQ,EAAE,YAAY;AAC5B,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,UAAU;AACvB,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,KAAK,EAAE,OAAO;AACpB,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,2BAA2B;AAC5C,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,OAAO,EAAE,mBAAmB;AAClC,MAAM,QAAQ,EAAE,WAAW;AAC3B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,WAAW;AAC5B,MAAM,aAAa,EAAE,WAAW;AAChC,MAAM,WAAW,EAAE,QAAQ;AAC3B,MAAM,WAAW,EAAE,OAAO;AAC1B,MAAM,OAAO,EAAE,OAAO;AACtB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,YAAY;AACxB,MAAM,QAAQ,EAAE,UAAU;AAC1B,MAAM,MAAM,EAAE,WAAW;AACzB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,WAAW;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,0BAA0B;AACnD,MAAM,eAAe,EAAE,eAAe;AACtC,MAAM,gBAAgB,EAAE,gCAAgC;AACxD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,yBAAyB;AACtC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,QAAQ;AAChC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}