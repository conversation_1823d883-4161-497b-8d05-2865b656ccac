{"version": 3, "file": "drawer2.mjs", "sources": ["../../../../../../packages/components/drawer/src/drawer.vue"], "sourcesContent": ["<template>\n  <el-teleport\n    :to=\"appendTo\"\n    :disabled=\"appendTo !== 'body' ? false : !appendToBody\"\n  >\n    <transition\n      :name=\"ns.b('fade')\"\n      @after-enter=\"afterEnter\"\n      @after-leave=\"afterLeave\"\n      @before-leave=\"beforeLeave\"\n    >\n      <el-overlay\n        v-show=\"visible\"\n        :mask=\"modal\"\n        :overlay-class=\"modalClass\"\n        :z-index=\"zIndex\"\n        @click=\"onModalClick\"\n      >\n        <el-focus-trap\n          loop\n          :trapped=\"visible\"\n          :focus-trap-el=\"drawerRef\"\n          :focus-start-el=\"focusStartRef\"\n          @focus-after-trapped=\"onOpenAutoFocus\"\n          @focus-after-released=\"onCloseAutoFocus\"\n          @focusout-prevented=\"onFocusoutPrevented\"\n          @release-requested=\"onCloseRequested\"\n        >\n          <div\n            ref=\"drawerRef\"\n            aria-modal=\"true\"\n            :aria-label=\"title || undefined\"\n            :aria-labelledby=\"!title ? titleId : undefined\"\n            :aria-describedby=\"bodyId\"\n            v-bind=\"$attrs\"\n            :class=\"[ns.b(), direction, visible && 'open']\"\n            :style=\"\n              isHorizontal ? 'width: ' + drawerSize : 'height: ' + drawerSize\n            \"\n            role=\"dialog\"\n            @click.stop\n          >\n            <span ref=\"focusStartRef\" :class=\"ns.e('sr-focus')\" tabindex=\"-1\" />\n            <header v-if=\"withHeader\" :class=\"[ns.e('header'), headerClass]\">\n              <slot\n                v-if=\"!$slots.title\"\n                name=\"header\"\n                :close=\"handleClose\"\n                :title-id=\"titleId\"\n                :title-class=\"ns.e('title')\"\n              >\n                <span\n                  v-if=\"!$slots.title\"\n                  :id=\"titleId\"\n                  role=\"heading\"\n                  :aria-level=\"headerAriaLevel\"\n                  :class=\"ns.e('title')\"\n                >\n                  {{ title }}\n                </span>\n              </slot>\n              <slot v-else name=\"title\">\n                <!-- DEPRECATED SLOT -->\n              </slot>\n              <button\n                v-if=\"showClose\"\n                :aria-label=\"t('el.drawer.close')\"\n                :class=\"ns.e('close-btn')\"\n                type=\"button\"\n                @click=\"handleClose\"\n              >\n                <el-icon :class=\"ns.e('close')\">\n                  <close />\n                </el-icon>\n              </button>\n            </header>\n            <template v-if=\"rendered\">\n              <div :id=\"bodyId\" :class=\"[ns.e('body'), bodyClass]\">\n                <slot />\n              </div>\n            </template>\n            <div v-if=\"$slots.footer\" :class=\"[ns.e('footer'), footerClass]\">\n              <slot name=\"footer\" />\n            </div>\n          </div>\n        </el-focus-trap>\n      </el-overlay>\n    </transition>\n  </el-teleport>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, useSlots } from 'vue'\nimport { Close } from '@element-plus/icons-vue'\nimport { ElOverlay } from '@element-plus/components/overlay'\nimport ElFocusTrap from '@element-plus/components/focus-trap'\nimport ElTeleport from '@element-plus/components/teleport'\nimport { useDialog } from '@element-plus/components/dialog'\nimport { addUnit } from '@element-plus/utils'\nimport ElIcon from '@element-plus/components/icon'\nimport { useDeprecated, useLocale, useNamespace } from '@element-plus/hooks'\nimport { drawerEmits, drawerProps } from './drawer'\n\ndefineOptions({\n  name: 'ElDrawer',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(drawerProps)\ndefineEmits(drawerEmits)\nconst slots = useSlots()\n\nuseDeprecated(\n  {\n    scope: 'el-drawer',\n    from: 'the title slot',\n    replacement: 'the header slot',\n    version: '3.0.0',\n    ref: 'https://element-plus.org/en-US/component/drawer.html#slots',\n  },\n  computed(() => !!slots.title)\n)\n\nconst drawerRef = ref<HTMLElement>()\nconst focusStartRef = ref<HTMLElement>()\nconst ns = useNamespace('drawer')\nconst { t } = useLocale()\nconst {\n  afterEnter,\n  afterLeave,\n  beforeLeave,\n  visible,\n  rendered,\n  titleId,\n  bodyId,\n  zIndex,\n  onModalClick,\n  onOpenAutoFocus,\n  onCloseAutoFocus,\n  onFocusoutPrevented,\n  onCloseRequested,\n  handleClose,\n} = useDialog(props, drawerRef)\n\nconst isHorizontal = computed(\n  () => props.direction === 'rtl' || props.direction === 'ltr'\n)\nconst drawerSize = computed(() => addUnit(props.size))\n\ndefineExpose({\n  handleClose,\n  afterEnter,\n  afterLeave,\n})\n</script>\n"], "names": ["_openBlock", "_createBlock", "_unref"], "mappings": ";;;;;;;;;;;;;;mCAuGc,CAAA;AAAA,EACZ,IAAM,EAAA,UAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAA,aAAA,CAAA;AAAA,MACE,KAAA,EAAA,WAAA;AAAA,MAAA,IACS,EAAA,gBAAA;AAAA,MAAA,WACD,EAAA,iBAAA;AAAA,MAAA,OACO,EAAA,OAAA;AAAA,MAAA,GACJ,EAAA,4DAAA;AAAA,KAAA,EAAA,QACJ,CAAA,MAAA,CAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACP,MAAA,SAAA,GAAA,GAAA,EAAA,CAAA;AAAA,IAAA,MACS,aAAQ,MAAM,EAAK,CAAA;AAAA,IAC9B,MAAA,EAAA,GAAA,YAAA,CAAA,QAAA,CAAA,CAAA;AAEA,IAAA,MAAM,iBAA6B,EAAA,CAAA;AACnC,IAAA,MAAM;AACN,MAAM;AACN,MAAM,UAAI;AACV,MAAM,WAAA;AAAA,MACJ,OAAA;AAAA,MACA,QAAA;AAAA,MACA,OAAA;AAAA,MACA,MAAA;AAAA,MACA,MAAA;AAAA,MACA,YAAA;AAAA,MACA,eAAA;AAAA,MACA,gBAAA;AAAA,MACA,mBAAA;AAAA,MACA,gBAAA;AAAA,MACA,WAAA;AAAA,KACA,GAAA,SAAA,CAAA,KAAA,EAAA,SAAA,CAAA,CAAA;AAAA,IACA,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA,KAAA,CAAA,SAAA,KAAA,KAAA,IAAA,KAAA,CAAA,SAAA,KAAA,KAAA,CAAA,CAAA;AAAA,IACA,MAAA,UAAA,GAAA,QAAA,CAAA,MAAA,OAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAAA,IACF,MAAc,CAAA;AAEd,MAAA,WAAqB;AAAA,MACnB,UAAM;AAAiD,MACzD,UAAA;AACA,KAAA,CAAA,CAAA;AAEA,IAAa,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACX,OAAAA,SAAA,EAAA,EAAAC,WAAA,CAAAC,KAAA,CAAA,UAAA,CAAA,EAAA;AAAA,QACA,EAAA,EAAA,IAAA,CAAA,QAAA;AAAA,QACA,QAAA,EAAA,IAAA,CAAA,QAAA,KAAA,MAAA,GAAA,KAAA,GAAA,CAAA,IAAA,CAAA,YAAA;AAAA,OACD,EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}