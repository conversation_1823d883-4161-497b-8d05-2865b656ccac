import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // 定义岱宗AI主题色 - 自然雅致风格
  static const Color deepEmerald = Color(0xFF2D5016);     // 深翠绿 - 主色调
  static const Color inkGreen = Color(0xFF1B4332);        // 墨绿 - 主色调
  static const Color champagneGold = Color(0xFFF7E7CE);   // 香槟金 - 辅助色
  static const Color ivoryWhite = Color(0xFFFFFDD0);      // 象牙白 - 辅助色
  static const Color amberYellow = Color(0xFFFFBF00);     // 琥珀黄 - 强调色

  // 保持兼容性的别名
  static const Color forestGreen = deepEmerald;
  static const Color earthYellow = champagneGold;
  static const Color lightGreen = Color(0xFF4F7942);      // 竹绿 - 中间色调
  static const Color lightYellow = ivoryWhite;

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    fontFamily: GoogleFonts.notoSerif().fontFamily,
    // 设置亮色模式下的背景色为象牙白
    scaffoldBackgroundColor: ivoryWhite,
    colorScheme: ColorScheme.fromSeed(
      seedColor: deepEmerald,
      brightness: Brightness.light,
      primary: deepEmerald,
      secondary: lightGreen,
      tertiary: champagneGold,
      surface: champagneGold.withAlpha(77),
      // 提高亮色模式下的文字对比度
      onSurface: inkGreen, // 使用墨绿色文字，提高对比度和雅致感
      onPrimary: ivoryWhite,
      onSecondary: inkGreen,
      onTertiary: inkGreen,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: deepEmerald,
      foregroundColor: ivoryWhite,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    listTileTheme: ListTileThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: deepEmerald,
        foregroundColor: ivoryWhite,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 3,
        shadowColor: inkGreen.withAlpha(77),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: champagneGold.withAlpha(102),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: lightGreen.withAlpha(128)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: BorderSide(color: lightGreen.withAlpha(128)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: deepEmerald, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      hintStyle: TextStyle(color: inkGreen.withAlpha(153)),
    ),
    // 添加下拉菜单主题
    dropdownMenuTheme: DropdownMenuThemeData(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(ivoryWhite),
        elevation: WidgetStateProperty.all(8),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: champagneGold, width: 1),
          ),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
      textStyle: TextStyle(
        color: inkGreen,
        fontSize: 16,
      ),
    ),
    // 添加弹出菜单主题
    popupMenuTheme: PopupMenuThemeData(
      color: Colors.white,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        color: Color(0xFF333333),
        fontSize: 16,
      ),
    ),
    // 添加页面过渡动画
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    // 添加滚动物理效果
    scrollbarTheme: ScrollbarThemeData(
      thickness: WidgetStateProperty.all(6),
      thumbColor:
          WidgetStateProperty.all(const Color.fromRGBO(58, 107, 53, 0.5)),
      radius: const Radius.circular(3),
      crossAxisMargin: 2,
      mainAxisMargin: 2,
    ),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    fontFamily: GoogleFonts.notoSerif().fontFamily,
    // 设置深色模式下的背景色为深墨绿
    scaffoldBackgroundColor: const Color(0xFF0F1A0C),
    colorScheme: ColorScheme.fromSeed(
      seedColor: lightGreen,
      brightness: Brightness.dark,
      primary: lightGreen,
      secondary: champagneGold,
      tertiary: amberYellow,
      // 提高深色模式下的对比度
      surface: const Color(0xFF1B2818),
      onSurface: champagneGold, // 使用香槟金文字，提高对比度和雅致感
      onPrimary: inkGreen,
      onSecondary: inkGreen,
      onTertiary: inkGreen,
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: inkGreen,
      foregroundColor: champagneGold,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    listTileTheme: ListTileThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: lightGreen,
        foregroundColor: inkGreen,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        elevation: 3,
        shadowColor: Colors.black.withAlpha(102),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.grey.shade800,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: lightGreen),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    ),
    // 添加下拉菜单主题
    dropdownMenuTheme: DropdownMenuThemeData(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(const Color(0xFF2A2A2A)),
        elevation: WidgetStateProperty.all(8),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
      textStyle: const TextStyle(
        inherit: true,
        color: Color(0xFFF5F5F5),
        fontSize: 16,
      ),
    ),
    // 添加弹出菜单主题
    popupMenuTheme: PopupMenuThemeData(
      color: const Color(0xFF2A2A2A),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        inherit: true,
        color: Color(0xFFF5F5F5),
        fontSize: 16,
      ),
    ),
    // 添加对话框主题
    dialogTheme: DialogTheme(
      backgroundColor: const Color(0xFF2A2A2A),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      titleTextStyle: const TextStyle(
        inherit: true,
        color: Color(0xFFF5F5F5),
        fontSize: 20,
        fontWeight: FontWeight.w600,
      ),
      contentTextStyle: const TextStyle(
        inherit: true,
        color: Color(0xFFE0E0E0),
        fontSize: 16,
      ),
    ),
    // 添加Snackbar主题
    snackBarTheme: SnackBarThemeData(
      backgroundColor: const Color(0xFF2A2A2A),
      contentTextStyle: const TextStyle(
        inherit: true,
        color: Color(0xFFF5F5F5),
        fontSize: 16,
      ),
      actionTextColor: lightGreen,
      behavior: SnackBarBehavior.floating,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      elevation: 8,
    ),
    // 添加文本按钮主题
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: lightGreen,
        textStyle: const TextStyle(
          inherit: true,
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
    ),
    // 添加页面过渡动画
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    // 添加滚动物理效果
    scrollbarTheme: ScrollbarThemeData(
      thickness: WidgetStateProperty.all(6),
      thumbColor:
          WidgetStateProperty.all(const Color.fromRGBO(147, 184, 132, 0.5)),
      radius: const Radius.circular(3),
      crossAxisMargin: 2,
      mainAxisMargin: 2,
    ),
  );
}
