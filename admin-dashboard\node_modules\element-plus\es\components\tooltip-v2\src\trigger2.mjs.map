{"version": 3, "file": "trigger2.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/trigger.vue"], "sourcesContent": ["<template>\n  <forward-ref v-if=\"nowrap\" :set-ref=\"setTriggerRef\" only-child>\n    <slot />\n  </forward-ref>\n  <button v-else ref=\"triggerRef\" v-bind=\"$attrs\">\n    <slot />\n  </button>\n</template>\n\n<script setup lang=\"ts\">\nimport { inject, onBeforeUnmount, watch } from 'vue'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { tooltipV2RootKey } from './constants'\nimport ForwardRef from './forward-ref'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2CommonProps } from './common'\n\ndefineOptions({\n  name: 'ElTooltipV2Trigger',\n})\n\nconst props = defineProps({\n  ...tooltipV2CommonProps,\n  ...tooltipV2TriggerProps,\n})\n\n/**\n * onOpen opens the tooltip instantly, onTrigger acts a lil bit differently,\n * it will check if delayDuration is set to greater than 0 and based on that result,\n * if true, it opens the tooltip after delayDuration, otherwise it opens it instantly.\n */\nconst { onClose, onOpen, onDelayOpen, triggerRef, contentId } =\n  inject(tooltipV2RootKey)!\n\nlet isMousedown = false\n\nconst setTriggerRef = (el: HTMLElement | null) => {\n  triggerRef.value = el\n}\n\nconst onMouseup = () => {\n  isMousedown = false\n}\n\nconst onMouseenter = composeEventHandlers(props.onMouseEnter, onDelayOpen)\n\nconst onMouseleave = composeEventHandlers(props.onMouseLeave, onClose)\n\nconst onMousedown = composeEventHandlers(props.onMouseDown, () => {\n  onClose()\n  isMousedown = true\n  document.addEventListener('mouseup', onMouseup, { once: true })\n})\n\nconst onFocus = composeEventHandlers(props.onFocus, () => {\n  if (!isMousedown) onOpen()\n})\n\nconst onBlur = composeEventHandlers(props.onBlur, onClose)\n\nconst onClick = composeEventHandlers(props.onClick, (e) => {\n  if ((e as MouseEvent).detail === 0) onClose()\n})\n\nconst events = {\n  blur: onBlur,\n  click: onClick,\n  focus: onFocus,\n  mousedown: onMousedown,\n  mouseenter: onMouseenter,\n  mouseleave: onMouseleave,\n}\n\nconst setEvents = <T extends (e: Event) => void>(\n  el: HTMLElement | null | undefined,\n  events: Record<string, T>,\n  type: 'addEventListener' | 'removeEventListener'\n) => {\n  if (el) {\n    Object.entries(events).forEach(([name, handler]) => {\n      el[type](name, handler)\n    })\n  }\n}\n\nwatch(triggerRef, (triggerEl, previousTriggerEl) => {\n  setEvents(triggerEl, events, 'addEventListener')\n  setEvents(previousTriggerEl, events, 'removeEventListener')\n\n  if (triggerEl) {\n    triggerEl.setAttribute('aria-describedby', contentId.value)\n  }\n})\n\nonBeforeUnmount(() => {\n  setEvents(triggerRef.value, events, 'removeEventListener')\n  document.removeEventListener('mouseup', onMouseup)\n})\n</script>\n"], "names": ["events"], "mappings": ";;;;;;;;mCAiBc,CAAA;AAAA,EACZ,IAAM,EAAA,oBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;AAYA,IAAM,MAAA,EAAE,SAAS,MAAQ,EAAA,WAAA,EAAa,YAAY,SAAU,EAAA,GAC1D,OAAO,gBAAgB,CAAA,CAAA;AAEzB,IAAA,IAAI,WAAc,GAAA,KAAA,CAAA;AAElB,IAAM,MAAA,aAAA,GAAgB,CAAC,EAA2B,KAAA;AAChD,MAAA,UAAA,CAAW,KAAQ,GAAA,EAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAA,MAAM,YAAY,MAAM;AACtB,MAAc,WAAA,GAAA,KAAA,CAAA;AAAA,KAChB,CAAA;AAEA,IAAA,MAAM,YAAe,GAAA,oBAAA,CAAqB,KAAM,CAAA,YAAA,EAAc,WAAW,CAAA,CAAA;AAEzE,IAAA,MAAM,YAAe,GAAA,oBAAA,CAAqB,KAAM,CAAA,YAAA,EAAc,OAAO,CAAA,CAAA;AAErE,IAAA,MAAM,WAAc,GAAA,oBAAA,CAAqB,KAAM,CAAA,WAAA,EAAa,MAAM;AAChE,MAAQ,OAAA,EAAA,CAAA;AACR,MAAc,WAAA,GAAA,IAAA,CAAA;AACd,MAAA,QAAA,CAAS,iBAAiB,SAAW,EAAA,SAAA,EAAW,EAAE,IAAA,EAAM,MAAM,CAAA,CAAA;AAAA,KAC/D,CAAA,CAAA;AAED,IAAA,MAAM,OAAU,GAAA,oBAAA,CAAqB,KAAM,CAAA,OAAA,EAAS,MAAM;AACxD,MAAI,IAAA,CAAC;AAAoB,QAC1B,MAAA,EAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,MAAU,GAAA,oBAAA,CAAA,KAA2B,CAAA,MAAA,EAAA,OAAgB,CAAA,CAAA;AACzD,IAAK,MAAA,OAA4B,GAAA,oBAAW,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,KAAA;AAAA,MAC7C,IAAA,CAAA,CAAA,MAAA,KAAA,CAAA;AAED,QAAA,OAAe,EAAA,CAAA;AAAA,KAAA,CACb,CAAM;AAAA,IAAA,MACC,MAAA,GAAA;AAAA,MACP,IAAO,EAAA,MAAA;AAAA,MACP,KAAW,EAAA,OAAA;AAAA,MACX,KAAY,EAAA,OAAA;AAAA,MACZ,SAAY,EAAA,WAAA;AAAA,MACd,UAAA,EAAA,YAAA;AAEA,MAAA,UAAkB,EAAA,YAEhBA;AAGA,KAAA,CAAA;AACE,IAAO,MAAA,SAAA,GAAA,CAAQA,WAAQ,EAAA,IAAQ,KAAE;AAC/B,MAAG,IAAA,EAAA,EAAA;AAAmB,QACxB,MAAC,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA,EAAA,OAAA,CAAA,KAAA;AAAA,UACH,EAAA,CAAA,IAAA,CAAA,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AAAA,SACF,CAAA,CAAA;AAEA,OAAM;AACJ,KAAU,CAAA;AACV,IAAU,KAAA,CAAA,UAAA,EAAA,CAAA,SAAA,EAAA,iBAAgD,KAAA;AAE1D,MAAA,SAAe,CAAA,SAAA,EAAA,MAAA,EAAA,kBAAA,CAAA,CAAA;AACb,MAAU,SAAA,CAAA,iBAAiC,EAAA,MAAA,EAAA,qBAAe,CAAA,CAAA;AAAA,MAC5D,IAAA,SAAA,EAAA;AAAA,QACD,SAAA,CAAA,YAAA,CAAA,kBAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAED,OAAA;AACE,KAAU,CAAA,CAAA;AACV,IAAS,eAAA,CAAA,MAAA;AAAwC,MAClD,SAAA,CAAA,UAAA,CAAA,KAAA,EAAA,MAAA,EAAA,qBAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;"}