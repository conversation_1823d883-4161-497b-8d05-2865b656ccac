{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/tree/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Tree from './src/tree.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElTree: SFCWithInstall<typeof Tree> = withInstall(Tree)\n\nexport default ElTree\n\nexport * from './src/tree.type'\nexport * from './src/instance'\nexport * from './src/tokens'\n"], "names": ["withInstall", "Tree"], "mappings": ";;;;;;;;AAEY,MAAC,MAAM,GAAGA,mBAAW,CAACC,eAAI;;;;;;;;"}