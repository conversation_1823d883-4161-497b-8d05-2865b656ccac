{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-deprecated/index.ts"], "sourcesContent": ["import { unref, watch } from 'vue'\nimport { debugWarn } from '@element-plus/utils'\n\nimport type { MaybeRef } from '@vueuse/core'\n\ntype DeprecationParam = {\n  from: string\n  replacement: string\n  scope: string\n  version: string\n  ref: string\n  type?: 'API' | 'Attribute' | 'Event' | 'Slot'\n}\n\nexport const useDeprecated = (\n  { from, replacement, scope, version, ref, type = 'API' }: DeprecationParam,\n  condition: MaybeRef<boolean>\n) => {\n  watch(\n    () => unref(condition),\n    (val) => {\n      if (val) {\n        debugWarn(\n          scope,\n          `[${type}] ${from} is about to be deprecated in version ${version}, please use ${replacement} instead.\nFor more detail, please visit: ${ref}\n`\n        )\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n}\n"], "names": [], "mappings": ";;;AAEY,MAAC,aAAa,GAAG,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI,GAAG,KAAK,EAAE,EAAE,SAAS,KAAK;AACtG,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,KAAK;AACzC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,sCAAsC,EAAE,OAAO,CAAC,aAAa,EAAE,WAAW,CAAC;AACpH,+BAA+B,EAAE,GAAG,CAAC;AACrC,CAAC,CAAC,CAAC;AACH,KAAK;AACL,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,CAAC,CAAC;AACL;;;;"}