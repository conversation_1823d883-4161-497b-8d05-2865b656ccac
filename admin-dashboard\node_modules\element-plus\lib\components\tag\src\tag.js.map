{"version": 3, "file": "tag.js", "sources": ["../../../../../../packages/components/tag/src/tag.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { componentSizes } from '@element-plus/constants'\n\nimport type Tag from './tag.vue'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const tagProps = buildProps({\n  /**\n   * @description type of Tag\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'info', 'warning', 'danger'],\n    default: 'primary',\n  },\n  /**\n   * @description whether Tag can be removed\n   */\n  closable: Boolean,\n  /**\n   * @description whether to disable animations\n   */\n  disableTransitions: Boolean,\n  /**\n   * @description whether Tag has a highlighted border\n   */\n  hit: Boolean,\n  /**\n   * @description background color of the Tag\n   */\n  color: String,\n  /**\n   * @description size of Tag\n   */\n  size: {\n    type: String,\n    values: componentSizes,\n  },\n  /**\n   * @description theme of Tag\n   */\n  effect: {\n    type: String,\n    values: ['dark', 'light', 'plain'],\n    default: 'light',\n  },\n  /**\n   * @description whether Tag is rounded\n   */\n  round: Boolean,\n} as const)\nexport type TagProps = ExtractPropTypes<typeof tagProps>\nexport type TagPropsPublic = __ExtractPublicPropTypes<typeof tagProps>\n\nexport const tagEmits = {\n  close: (evt: MouseEvent) => evt instanceof MouseEvent,\n  click: (evt: MouseEvent) => evt instanceof MouseEvent,\n}\nexport type TagEmits = typeof tagEmits\n\nexport type TagInstance = InstanceType<typeof Tag> & unknown\n"], "names": ["buildProps", "componentSizes"], "mappings": ";;;;;;;AAEY,MAAC,QAAQ,GAAGA,kBAAU,CAAC;AACnC,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;AAC/D,IAAI,OAAO,EAAE,SAAS;AACtB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,kBAAkB,EAAE,OAAO;AAC7B,EAAE,GAAG,EAAE,OAAO;AACd,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAEC,mBAAc;AAC1B,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;AACtC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,KAAK,EAAE,OAAO;AAChB,CAAC,EAAE;AACS,MAAC,QAAQ,GAAG;AACxB,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C,EAAE,KAAK,EAAE,CAAC,GAAG,KAAK,GAAG,YAAY,UAAU;AAC3C;;;;;"}