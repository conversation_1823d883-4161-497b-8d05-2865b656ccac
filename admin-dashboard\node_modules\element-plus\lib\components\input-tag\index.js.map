{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/input-tag/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport InputTag from './src/input-tag.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElInputTag: SFCWithInstall<typeof InputTag> = withInstall(InputTag)\nexport default ElInputTag\n\nexport * from './src/input-tag'\nexport type { InputTagInstance } from './src/instance'\n"], "names": ["withInstall", "InputTag"], "mappings": ";;;;;;;;AAEY,MAAC,UAAU,GAAGA,mBAAW,CAACC,qBAAQ;;;;;;;"}