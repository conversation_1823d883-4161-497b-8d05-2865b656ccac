{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/project/vs code/novel_new/novel_app/android/app/.cxx/RelWithDebInfo/4u3d1b1p/armeabi-v7a", "source": "D:/element/flutter/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}