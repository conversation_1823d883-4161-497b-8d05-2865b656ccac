# 🚀 腾讯云CloudBase生产环境部署完整指南

## 📋 您的环境信息
- **环境名称**: novel-app
- **环境ID**: novel-app-2gywkgnn15cbd6a8
- **API地址**: https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/api
- **管理后台**: https://novel-app-2gywkgnn15cbd6a8.service.tcloudbase.com/admin

## 📋 部署前准备清单

### 1. **腾讯云账号准备**
- [ ] 注册腾讯云账号并完成实名认证
- [ ] 开通CloudBase服务
- [ ] 创建生产环境（记录环境ID）
- [ ] 确保账户余额充足

### 2. **本地环境准备**
- [ ] 安装Node.js (版本 >= 16.13)
- [ ] 安装npm或yarn
- [ ] 安装CloudBase CLI: `npm install -g @cloudbase/cli`

### 3. **安全配置准备**
- [ ] 生成强密码作为JWT密钥
- [ ] 设置安全的管理员密码
- [ ] 准备SSL证书（如需自定义域名）

## 🔧 部署步骤

### 第一步：获取环境ID
1. 登录腾讯云控制台
2. 进入CloudBase服务
3. 创建新环境或选择现有环境
4. 复制环境ID（格式：`your-app-xxxxxxxx`）

### 第二步：配置安全参数
编辑 `cloudbaserc.json` 文件，修改以下参数：

```json
{
  "envId": "YOUR_ACTUAL_ENV_ID",
  "functions": [
    {
      "envVariables": {
        "JWT_SECRET": "your-super-secure-jwt-secret-key-2024",
        "ADMIN_USERNAME": "your-admin-username",
        "ADMIN_PASSWORD": "your-secure-admin-password"
      }
    }
  ]
}
```

### 第三步：执行部署
**Windows用户：**
```cmd
cd cloudbase\cloudbase-deploy
deploy-production.bat YOUR_ENV_ID
```

**Linux/Mac用户：**
```bash
cd cloudbase/cloudbase-deploy
chmod +x deploy-production.sh
./deploy-production.sh YOUR_ENV_ID
```

### 第四步：验证部署
1. 访问API健康检查：`https://YOUR_ENV_ID.service.tcloudbase.com/api/health`
2. 测试管理员登录：`https://YOUR_ENV_ID.service.tcloudbase.com/api/admin/login`
3. 检查用户注册接口：`https://YOUR_ENV_ID.service.tcloudbase.com/api/auth/register`

## 📱 更新Flutter应用配置

### 修改API配置文件
找到 `lib/config/api_config.dart` 文件，更新API地址：

```dart
class ApiConfig {
  // 生产环境API地址
  static const String baseUrl = 'https://YOUR_ENV_ID.service.tcloudbase.com/api';
  
  // 其他配置保持不变
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String syncEndpoint = '/sync';
}
```

### 更新后台管理系统配置
修改 `admin-dashboard/vite.config.ts` 中的代理配置：

```typescript
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'https://YOUR_ENV_ID.service.tcloudbase.com',
        changeOrigin: true,
        secure: true
      }
    }
  }
})
```

## 🗄️ 数据库迁移

### 从本地JSON迁移到CloudBase数据库
1. 导出现有用户数据
2. 使用CloudBase控制台导入数据
3. 或使用迁移脚本自动导入

### 数据库集合结构
- `users` - 用户信息
- `novels` - 小说数据
- `memberCodes` - 会员码
- `syncRecords` - 同步记录

## 🔒 安全配置

### 1. **环境变量安全**
- JWT_SECRET: 使用强随机字符串
- ADMIN_PASSWORD: 使用复杂密码
- 不要在代码中硬编码敏感信息

### 2. **访问控制**
- 配置CORS白名单
- 设置API访问频率限制
- 启用HTTPS强制访问

### 3. **数据安全**
- 定期备份数据库
- 启用数据库访问日志
- 配置数据加密

## 📊 监控和维护

### 1. **性能监控**
- 在CloudBase控制台查看函数执行统计
- 监控API响应时间
- 设置异常告警

### 2. **日志管理**
- 查看云函数执行日志
- 配置错误日志收集
- 定期清理过期日志

### 3. **成本优化**
- 监控资源使用情况
- 调整函数内存配置
- 优化数据库查询

## 🚨 故障排除

### 常见问题及解决方案

1. **部署失败**
   - 检查网络连接
   - 验证环境ID是否正确
   - 确认CloudBase CLI登录状态

2. **API访问失败**
   - 检查CORS配置
   - 验证HTTP触发器设置
   - 查看云函数执行日志

3. **数据库连接失败**
   - 确认数据库权限设置
   - 检查集合索引配置
   - 验证数据格式

## 📞 技术支持

如遇到部署问题，可以：
1. 查看CloudBase官方文档
2. 联系腾讯云技术支持
3. 在项目GitHub提交Issue

## 🎯 部署后检查清单

- [ ] API健康检查通过
- [ ] 用户注册登录正常
- [ ] 管理后台可以访问
- [ ] 数据同步功能正常
- [ ] 会员码系统工作正常
- [ ] 小说创作功能正常
- [ ] 性能监控已配置
- [ ] 备份策略已设置

完成以上所有步骤后，您的小说应用就可以正式上线运行了！
