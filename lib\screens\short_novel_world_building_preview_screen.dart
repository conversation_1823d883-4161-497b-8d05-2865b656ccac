import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ShortNovelWorldBuildingPreviewScreen extends StatefulWidget {
  final String worldBuilding;
  final Function(String) onWorldBuildingConfirmed;

  const ShortNovelWorldBuildingPreviewScreen({
    super.key,
    required this.worldBuilding,
    required this.onWorldBuildingConfirmed,
  });

  @override
  State<ShortNovelWorldBuildingPreviewScreen> createState() =>
      _ShortNovelWorldBuildingPreviewScreenState();
}

class _ShortNovelWorldBuildingPreviewScreenState
    extends State<ShortNovelWorldBuildingPreviewScreen> {
  late TextEditingController _controller;
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.worldBuilding);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('世界观预览'),
        actions: [
          IconButton(
            icon: Icon(_isEditing ? Icons.check : Icons.edit),
            onPressed: () {
              if (_isEditing) {
                // 保存修改
                widget.onWorldBuildingConfirmed(_controller.text);
                setState(() => _isEditing = false);
              } else {
                // 进入编辑模式
                setState(() => _isEditing = true);
              }
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // 提示信息
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Text(
                '请仔细查看生成的世界观和角色信息，确认无误后点击"确认并继续"按钮生成详细大纲。',
                style: TextStyle(color: Colors.blue),
              ),
            ),
            const SizedBox(height: 16),
            
            // 世界观内容
            Expanded(
              child: _isEditing
                  ? TextField(
                      controller: _controller,
                      maxLines: null,
                      expands: true,
                      textAlignVertical: TextAlignVertical.top,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: '在此编辑世界观和角色信息...',
                      ),
                      style: const TextStyle(fontSize: 16),
                    )
                  : Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: SingleChildScrollView(
                        child: Text(
                          _controller.text,
                          style: const TextStyle(fontSize: 16, height: 1.5),
                        ),
                      ),
                    ),
            ),
            
            // 操作按钮
            if (!_isEditing) ...[
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        setState(() => _isEditing = true);
                      },
                      icon: const Icon(Icons.edit),
                      label: const Text('修改世界观'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        widget.onWorldBuildingConfirmed(_controller.text);
                        Get.back();
                      },
                      icon: const Icon(Icons.arrow_forward),
                      label: const Text('确认并继续'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
