{"version": 3, "file": "upload-dragger.js", "sources": ["../../../../../../packages/components/upload/src/upload-dragger.ts"], "sourcesContent": ["import { buildProps, isArray } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type UploadDragger from './upload-dragger.vue'\n\nexport const uploadDraggerProps = buildProps({\n  disabled: <PERSON><PERSON><PERSON>,\n} as const)\nexport type UploadDraggerProps = ExtractPropTypes<typeof uploadDraggerProps>\nexport type UploadDraggerPropsPublic = __ExtractPublicPropTypes<\n  typeof uploadDraggerProps\n>\n\nexport const uploadDraggerEmits = {\n  file: (file: File[]) => isArray(file),\n}\nexport type UploadDraggerEmits = typeof uploadDraggerEmits\n\nexport type UploadDraggerInstance = InstanceType<typeof UploadDragger> & unknown\n"], "names": ["buildProps", "isArray"], "mappings": ";;;;;;;AACY,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC,EAAE;AACS,MAAC,kBAAkB,GAAG;AAClC,EAAE,IAAI,EAAE,CAAC,IAAI,KAAKC,cAAO,CAAC,IAAI,CAAC;AAC/B;;;;;"}