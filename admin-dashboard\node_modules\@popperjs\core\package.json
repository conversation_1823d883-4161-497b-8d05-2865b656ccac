{"name": "@sxzz/popperjs-es", "version": "2.11.7", "description": "Tooltip and Popover Positioning Engine", "main": "dist/index.js", "module": "dist/index.mjs", "unpkg": "dist/index.iife.js", "exports": {".": {"require": "./dist/index.js", "import": "./dist/index.mjs"}, "./*": "./*"}, "types": "./index.d.ts", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "github:popperjs/popper-core", "keywords": ["tooltip", "popover", "dropdown", "popup", "popper", "positioning engine"], "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}, "files": ["index.d.ts", "/dist", "/lib"], "sideEffects": false, "publishConfig": {"access": "public"}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "proseWrap": "always"}, "babel": {"extends": "./.config/babel.config"}, "jest": {"preset": "./.config/jest.config"}, "eslintConfig": {"extends": "./.config/eslint.config"}, "husky": {"hooks": {"pre-commit": "pretty-quick --staged"}}, "devDependencies": {"@ampproject/rollup-plugin-closure-compiler": "^0.26.0", "@atomico/rollup-plugin-sizes": "^1.1.4", "@babel/cli": "^7.12.17", "@babel/core": "^7.12.17", "@babel/plugin-transform-flow-strip-types": "^7.12.13", "@babel/plugin-transform-runtime": "^7.12.17", "@babel/preset-env": "^7.12.17", "@fezvrasta/tsc-silent": "^1.3.0", "@khanacademy/flow-to-ts": "^0.3.0", "@rollup/plugin-babel": "^5.3.1", "@rollup/plugin-replace": "^2.3.4", "babel-eslint": "^10.0.3", "babel-jest": "^26.6.3", "babel-plugin-add-import-extension": "^1.4.4", "babel-plugin-annotate-pure-calls": "^0.4.0", "babel-plugin-dev-expression": "^0.2.2", "babel-plugin-inline-replace-variables": "^1.3.1", "babel-plugin-transform-inline-environment-variables": "^0.4.3", "concurrently": "^5.3.0", "dotenv": "^8.2.0", "esbuild": "^0.14.38", "esbuild-plugin-flow": "^0.3.2", "eslint": "^7.20.0", "eslint-plugin-flowtype": "^5.2.2", "eslint-plugin-import": "^2.22.1", "eslint-plugin-unused-imports": "^1.1.0", "esno": "^0.14.1", "flow-bin": "^0.139.0", "flow-copy-source": "^2.0.9", "get-port-cli": "^2.0.0", "husky": "^5.0.9", "jest": "^26.6.3", "jest-environment-jsdom-sixteen": "^1.0.3", "jest-environment-puppeteer": "^4.4.0", "jest-image-snapshot": "^4.3.0", "jest-puppeteer": "^4.4.0", "pinst": "^2.1.4", "poster": "^0.0.9", "prettier": "^2.2.1", "pretty-quick": "^3.1.0", "puppeteer": "^10.4.0", "replace-in-files-cli": "^1.0.0", "rollup": "^2.70.2", "rollup-plugin-esbuild": "^4.9.1", "rollup-plugin-flow-entry": "^0.3.3", "rollup-plugin-license": "^2.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^4.2.0", "serve": "^11.3.2", "typescript": "^4.1.5"}, "scripts": {"clean": "rimraf lib && rimraf dist && rimraf test/visual/dist", "test": "yarn test:unit && yarn test:functional", "test:unit": "jest --coverage src", "test:functional": "DEV_PORT=`get-port` jest tests/functional", "test:flow": "flow", "test:typescript": "tsc --project tests/typescript/tsconfig.json", "test:eslint": "eslint .", "dev": "NODE_ENV=dev concurrently 'yarn serve' 'yarn build:dev --watch'", "serve": "serve -l ${DEV_PORT:-5000} tests/visual", "build": "yarn clean && esmo build/build.ts && pnpm run build:typescript", "build:typescript": "rimraf dist/typescript; flow-to-ts \"src/**/*.js\" --write --inline-utility-types; tsc-silent --project .config/tsconfig.json --createSourceFile .config/createSourceFile.js --suppress @; rimraf \"src/**/*.ts\""}, "readme": "<!-- <HEADER> // IGNORE IT -->\n<p align=\"center\">\n  <img src=\"https://rawcdn.githack.com/popperjs/popper-core/8805a5d7599e14619c9e7ac19a3713285d8e5d7f/docs/src/images/popper-logo-outlined.svg\" alt=\"Popper\" height=\"300px\"/>\n</p>\n\n<div align=\"center\">\n  <h1>Tooltip & Popover Positioning Engine</h1>\n</div>\n\n<p align=\"center\">\n  <a href=\"https://www.npmjs.com/package/@popperjs/core\">\n    <img src=\"https://img.shields.io/npm/v/@popperjs/core?style=for-the-badge\" alt=\"npm version\" />\n  </a>\n  <a href=\"https://www.npmjs.com/package/@popperjs/core\">\n    <img src=\"https://img.shields.io/endpoint?style=for-the-badge&url=https://runkit.io/fezvrasta/combined-npm-downloads/1.0.0?packages=popper.js,@popperjs/core\" alt=\"npm downloads per month (popper.js + @popperjs/core)\" />\n  </a>\n  <a href=\"https://rollingversions.com/popperjs/popper-core\">\n    <img src=\"https://img.shields.io/badge/Rolling%20Versions-Enabled-brightgreen?style=for-the-badge\" alt=\"Rolling Versions\" />\n  </a>\n</p>\n\n<br />\n<!-- </HEADER> // NOW BEGINS THE README -->\n\n**Positioning tooltips and popovers is difficult. Popper is here to help!**\n\nGiven an element, such as a button, and a tooltip element describing it, Popper\nwill automatically put the tooltip in the right place near the button.\n\nIt will position _any_ UI element that \"pops out\" from the flow of your document\nand floats near a target element. The most common example is a tooltip, but it\nalso includes popovers, drop-downs, and more. All of these can be generically\ndescribed as a \"popper\" element.\n\n## Demo\n\n[![Popper visualized](https://i.imgur.com/F7qWsmV.jpg)](https://popper.js.org)\n\n## Docs\n\n- [v2.x (latest)](https://popper.js.org/docs/v2/)\n- [v1.x](https://popper.js.org/docs/v1/)\n\nWe've created a\n[Migration Guide](https://popper.js.org/docs/v2/migration-guide/) to help you\nmigrate from Popper 1 to Popper 2.\n\nTo contribute to the Popper website and documentation, please visit the\n[dedicated repository](https://github.com/popperjs/website).\n\n## Why not use pure CSS?\n\n- **Clipping and overflow issues**: Pure CSS poppers will not be prevented from\n  overflowing clipping boundaries, such as the viewport. It will get partially\n  cut off or overflows if it's near the edge since there is no dynamic\n  positioning logic. When using Popper, your popper will always be positioned in\n  the right place without needing manual adjustments.\n- **No flipping**: CSS poppers will not flip to a different placement to fit\n  better in view if necessary. While you can manually adjust for the main axis\n  overflow, this feature cannot be achieved via CSS alone. Popper automatically\n  flips the tooltip to make it fit in view as best as possible for the user.\n- **No virtual positioning**: CSS poppers cannot follow the mouse cursor or be\n  used as a context menu. Popper allows you to position your tooltip relative to\n  any coordinates you desire.\n- **Slower development cycle**: When pure CSS is used to position popper\n  elements, the lack of dynamic positioning means they must be carefully placed\n  to consider overflow on all screen sizes. In reusable component libraries,\n  this means a developer can't just add the component anywhere on the page,\n  because these issues need to be considered and adjusted for every time. With\n  Popper, you can place your elements anywhere and they will be positioned\n  correctly, without needing to consider different screen sizes, layouts, etc.\n  This massively speeds up development time because this work is automatically\n  offloaded to Popper.\n- **Lack of extensibility**: CSS poppers cannot be easily extended to fit any\n  arbitrary use case you may need to adjust for. Popper is built with\n  extensibility in mind.\n\n## Why Popper?\n\nWith the CSS drawbacks out of the way, we now move on to Popper in the\nJavaScript space itself.\n\nNaive JavaScript tooltip implementations usually have the following problems:\n\n- **Scrolling containers**: They don't ensure the tooltip stays with the\n  reference element while scrolling when inside any number of scrolling\n  containers.\n- **DOM context**: They often require the tooltip move outside of its original\n  DOM context because they don't handle `offsetParent` contexts.\n- **Compatibility**: Popper handles an incredible number of edge cases regarding\n  different browsers and environments (mobile viewports, RTL, scrollbars enabled\n  or disabled, etc.). Popper is a popular and well-maintained library, so you\n  can be confident positioning will work for your users on any device.\n- **Configurability**: They often lack advanced configurability to suit any\n  possible use case.\n- **Size**: They are usually relatively large in size, or require an ancient\n  jQuery dependency.\n- **Performance**: They often have runtime performance issues and update the\n  tooltip position too slowly.\n\n**Popper solves all of these key problems in an elegant, performant manner.** It\nis a lightweight ~3 kB library that aims to provide a reliable and extensible\npositioning engine you can use to ensure all your popper elements are positioned\nin the right place.\n\nWhen you start writing your own popper implementation, you'll quickly run into\nall of the problems mentioned above. These widgets are incredibly common in our\nUIs; we've done the hard work figuring this out so you don't need to spend hours\nfixing and handling numerous edge cases that we already ran into while building\nthe library!\n\nPopper is used in popular libraries like Bootstrap, Foundation, Material UI, and\nmore. It's likely you've already used popper elements on the web positioned by\nPopper at some point in the past few years.\n\nSince we write UIs using powerful abstraction libraries such as React or Angular\nnowadays, you'll also be glad to know Popper can fully integrate with them and\nbe a good citizen together with your other components. Check out `react-popper`\nfor the official Popper wrapper for React.\n\n## Installation\n\n### 1. Package Manager\n\n```bash\n# With npm\nnpm i @popperjs/core\n\n# With Yarn\nyarn add @popperjs/core\n```\n\n### 2. CDN\n\n```html\n<!-- Development version -->\n<script src=\"https://unpkg.com/@popperjs/core@2/dist/umd/popper.js\"></script>\n\n<!-- Production version -->\n<script src=\"https://unpkg.com/@popperjs/core@2\"></script>\n```\n\n### 3. Direct Download?\n\nManaging dependencies by \"directly downloading\" them and placing them into your\nsource code is not recommended for a variety of reasons, including missing out\non feat/fix updates easily. Please use a versioning management system like a CDN\nor npm/Yarn.\n\n## Usage\n\nThe most straightforward way to get started is to import Popper from the `unpkg`\nCDN, which includes all of its features. You can call the `Popper.createPopper`\nconstructor to create new popper instances.\n\nHere is a complete example:\n\n```html\n<!DOCTYPE html>\n<title>Popper example</title>\n\n<style>\n  #tooltip {\n    background-color: #333;\n    color: white;\n    padding: 5px 10px;\n    border-radius: 4px;\n    font-size: 13px;\n  }\n</style>\n\n<button id=\"button\" aria-describedby=\"tooltip\">I'm a button</button>\n<div id=\"tooltip\" role=\"tooltip\">I'm a tooltip</div>\n\n<script src=\"https://unpkg.com/@popperjs/core@^2.0.0\"></script>\n<script>\n  const button = document.querySelector('#button');\n  const tooltip = document.querySelector('#tooltip');\n\n  // Pass the button, the tooltip, and some options, and Popper will do the\n  // magic positioning for you:\n  Popper.createPopper(button, tooltip, {\n    placement: 'right',\n  });\n</script>\n```\n\nVisit the [tutorial](https://popper.js.org/docs/v2/tutorial/) for an example of\nhow to build your own tooltip from scratch using Popper.\n\n### Module bundlers\n\nYou can import the `createPopper` constructor from the fully-featured file:\n\n```js\nimport { createPopper } from '@popperjs/core';\n\nconst button = document.querySelector('#button');\nconst tooltip = document.querySelector('#tooltip');\n\n// Pass the button, the tooltip, and some options, and Popper will do the\n// magic positioning for you:\ncreatePopper(button, tooltip, {\n  placement: 'right',\n});\n```\n\nAll the modifiers listed in the docs menu will be enabled and \"just work\", so\nyou don't need to think about setting Popper up. The size of Popper including\nall of its features is about 5 kB minzipped, but it may grow a bit in the\nfuture.\n\n#### Popper Lite (tree-shaking)\n\nIf bundle size is important, you'll want to take advantage of tree-shaking. The\nlibrary is built in a modular way to allow to import only the parts you really\nneed.\n\n```js\nimport { createPopperLite as createPopper } from '@popperjs/core';\n```\n\nThe Lite version includes the most necessary modifiers that will compute the\noffsets of the popper, compute and add the positioning styles, and add event\nlisteners. This is close in bundle size to pure CSS tooltip libraries, and\nbehaves somewhat similarly.\n\nHowever, this does not include the features that makes Popper truly useful.\n\nThe two most useful modifiers not included in Lite are `preventOverflow` and\n`flip`:\n\n```js\nimport {\n  createPopperLite as createPopper,\n  preventOverflow,\n  flip,\n} from '@popperjs/core';\n\nconst button = document.querySelector('#button');\nconst tooltip = document.querySelector('#tooltip');\n\ncreatePopper(button, tooltip, {\n  modifiers: [preventOverflow, flip],\n});\n```\n\nAs you make more poppers, you may be finding yourself needing other modifiers\nprovided by the library.\n\nSee [tree-shaking](https://popper.js.org/docs/v2/performance/#tree-shaking) for more\ninformation.\n\n## Distribution targets\n\nPopper is distributed in 3 different versions, in 3 different file formats.\n\nThe 3 file formats are:\n\n- `esm` (works with `import` syntax — **recommended**)\n- `umd` (works with `<script>` tags or RequireJS)\n- `cjs` (works with `require()` syntax)\n\nThere are two different `esm` builds, one for bundler consumers (e.g. webpack,\nRollup, etc..), which is located under `/lib`, and one for browsers with native\nsupport for ES Modules, under `/dist/esm`. The only difference within the two,\nis that the browser-compatible version doesn't make use of\n`process.env.NODE_ENV` to run development checks.\n\nThe 3 versions are:\n\n- `popper`: includes all the modifiers (features) in one file (**default**);\n- `popper-lite`: includes only the minimum amount of modifiers to provide the\n  basic functionality;\n- `popper-base`: doesn't include any modifier, you must import them separately;\n\nBelow you can find the size of each version, minified and compressed with the\n[Brotli compression algorithm](https://medium.com/groww-engineering/enable-brotli-compression-in-webpack-with-fallback-to-gzip-397a57cf9fc6):\n\n<!-- Don't change the labels to use hyphens, it breaks, even when encoded -->\n\n![](https://badge-size.now.sh/https://unpkg.com/@popperjs/core/dist/umd/popper.min.js?compression=brotli&label=popper)\n![](https://badge-size.now.sh/https://unpkg.com/@popperjs/core/dist/umd/popper-lite.min.js?compression=brotli&label=popper%20lite)\n![](https://badge-size.now.sh/https://unpkg.com/@popperjs/core/dist/umd/popper-base.min.js?compression=brotli&label=popper%20base)\n\n## Hacking the library\n\nIf you want to play with the library, implement new features, fix a bug you\nfound, or simply experiment with it, this section is for you!\n\nFirst of all, make sure to have\n[Yarn installed](https://yarnpkg.com/lang/en/docs/install).\n\nInstall the development dependencies:\n\n```bash\nyarn install\n```\n\nAnd run the development environment:\n\n```bash\nyarn dev\n```\n\nThen, simply open one the development server web page:\n\n```bash\n# macOS and Linux\nopen localhost:5000\n\n# Windows\nstart localhost:5000\n```\n\nFrom there, you can open any of the examples (`.html` files) to fiddle with\nthem.\n\nNow any change you will made to the source code, will be automatically compiled,\nyou just need to refresh the page.\n\nIf the page is not working properly, try to go in _\"Developer Tools >\nApplication > Clear storage\"_ and click on \"_Clear site data_\".  \nTo run the examples you need a browser with\n[JavaScript modules via script tag support](https://caniuse.com/#feat=es6-module).\n\n## Test Suite\n\nPopper is currently tested with unit tests, and functional tests. Both of them\nare run by Jest.\n\n### Unit Tests\n\nThe unit tests use JSDOM to provide a primitive document object API, they are\nused to ensure the utility functions behave as expected in isolation.\n\n### Functional Tests\n\nThe functional tests run with Puppeteer, to take advantage of a complete browser\nenvironment. They are currently running on Chromium, and Firefox.\n\nYou can run them with `yarn test:functional`. Set the `PUPPETEER_BROWSER`\nenvironment variable to `firefox` to run them on the Mozilla browser.\n\nThe assertions are written in form of image snapshots, so that it's easy to\nassert for the correct Popper behavior without having to write a lot of offsets\ncomparisons manually.\n\nYou can mark a `*.test.js` file to run in the Puppeteer environment by\nprepending a `@jest-environment puppeteer` JSDoc comment to the interested file.\n\nHere's an example of a basic functional test:\n\n```js\n/**\n * @jest-environment puppeteer\n * @flow\n */\nimport { screenshot } from '../utils/puppeteer.js';\n\nit('should position the popper on the right', async () => {\n  const page = await browser.newPage();\n  await page.goto(`${TEST_URL}/basic.html`);\n\n  expect(await screenshot(page)).toMatchImageSnapshot();\n});\n```\n\nYou can find the complete\n[`jest-puppeteer` documentation here](https://github.com/smooth-code/jest-puppeteer#api),\nand the\n[`jest-image-snapshot` documentation here](https://github.com/americanexpress/jest-image-snapshot#%EF%B8%8F-api).\n\n## License\n\nMIT\n"}