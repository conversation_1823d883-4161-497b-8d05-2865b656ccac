import 'package:flutter/material.dart';
import 'package:novel_app/models/text_modification.dart';
import 'package:novel_app/services/text_diff_service.dart';

/// Diff视图组件 - 显示文本差异对比
class DiffViewWidget extends StatelessWidget {
  final String originalText;
  final List<TextModification> modifications;
  final Function(String modificationId, ModificationStatus status)? onModificationStatusChanged;
  final Function(ModificationStatus status)? onBatchStatusChanged;
  final bool showLineNumbers;
  final bool compactMode;

  const DiffViewWidget({
    Key? key,
    required this.originalText,
    required this.modifications,
    this.onModificationStatusChanged,
    this.onBatchStatusChanged,
    this.showLineNumbers = true,
    this.compactMode = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    print('🎨 DiffViewWidget构建: 修改数量=${modifications.length}');
    for (int i = 0; i < modifications.length; i++) {
      final mod = modifications[i];
      print('  修改$i: ${mod.type.name} 行${mod.startLine}-${mod.endLine} 状态=${mod.status.name}');
    }

    if (modifications.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: theme.dividerColor),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          '暂无修改建议',
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.textTheme.bodyMedium?.color?.withOpacity(0.6),
          ),
        ),
      );
    }

    final diff = TextDiffService.calculateModificationDiff(originalText, modifications);

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: theme.dividerColor),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(context, diff.stats),
          if (!compactMode) _buildModificationsList(context),
          _buildDiffContent(context, diff),
          if (modifications.any((m) => m.status == ModificationStatus.pending))
            _buildActionButtons(context),
        ],
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader(BuildContext context, DiffStats stats) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.compare_arrows, size: 16, color: theme.colorScheme.primary),
          const SizedBox(width: 8),
          Text(
            '修改预览',
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const Spacer(),
          if (stats.totalChanges > 0) ...[
            _buildStatChip(context, '+${stats.addedLines}', Colors.green),
            const SizedBox(width: 4),
            _buildStatChip(context, '-${stats.deletedLines}', Colors.red),
            const SizedBox(width: 4),
            _buildStatChip(context, '~${stats.modifiedLines}', Colors.orange),
          ],
        ],
      ),
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(BuildContext context, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 11,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  /// 构建修改建议列表
  Widget _buildModificationsList(BuildContext context) {
    if (modifications.isEmpty) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '修改建议 (${modifications.length})',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const SizedBox(height: 8),
          ...modifications.map((mod) => _buildModificationItem(context, mod)),
        ],
      ),
    );
  }

  /// 构建单个修改建议项
  Widget _buildModificationItem(BuildContext context, TextModification modification) {
    final theme = Theme.of(context);
    Color statusColor;
    IconData statusIcon;
    
    switch (modification.status) {
      case ModificationStatus.pending:
        statusColor = theme.colorScheme.primary;
        statusIcon = Icons.schedule;
        break;
      case ModificationStatus.accepted:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case ModificationStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
      case ModificationStatus.applied:
        statusColor = Colors.blue;
        statusIcon = Icons.done_all;
        break;
    }
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: statusColor.withOpacity(0.3)),
        borderRadius: BorderRadius.circular(6),
        color: statusColor.withOpacity(0.05),
      ),
      child: Row(
        children: [
          Icon(statusIcon, size: 16, color: statusColor),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '第${modification.startLine}-${modification.endLine}行: ${_getTypeText(modification.type)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                if (modification.reason.isNotEmpty) ...[
                  const SizedBox(height: 2),
                  Text(
                    modification.reason,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.textTheme.bodySmall?.color?.withOpacity(0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),
          if (modification.status == ModificationStatus.pending && onModificationStatusChanged != null) ...[
            IconButton(
              icon: const Icon(Icons.check, size: 16),
              onPressed: () {
                print('✅ 点击接受修改: ${modification.id}');
                onModificationStatusChanged!(modification.id, ModificationStatus.accepted);
              },
              tooltip: '接受',
              color: Colors.green,
            ),
            IconButton(
              icon: const Icon(Icons.close, size: 16),
              onPressed: () {
                print('❌ 点击拒绝修改: ${modification.id}');
                onModificationStatusChanged!(modification.id, ModificationStatus.rejected);
              },
              tooltip: '拒绝',
              color: Colors.red,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建差异内容
  Widget _buildDiffContent(BuildContext context, TextDiff diff) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 400),
      child: SingleChildScrollView(
        child: Column(
          children: diff.lines.map((line) => _buildDiffLine(context, line)).toList(),
        ),
      ),
    );
  }

  /// 构建差异行
  Widget _buildDiffLine(BuildContext context, DiffLine line) {
    final theme = Theme.of(context);
    Color? backgroundColor;
    Color? textColor;
    String prefix = '';
    
    switch (line.type) {
      case DiffType.unchanged:
        backgroundColor = null;
        textColor = theme.textTheme.bodyMedium?.color;
        prefix = ' ';
        break;
      case DiffType.deleted:
        backgroundColor = Colors.red.withOpacity(0.1);
        textColor = Colors.red.shade700;
        prefix = '-';
        break;
      case DiffType.added:
        backgroundColor = Colors.green.withOpacity(0.1);
        textColor = Colors.green.shade700;
        prefix = '+';
        break;
      case DiffType.modifiedOriginal:
        backgroundColor = Colors.orange.withOpacity(0.1);
        textColor = Colors.orange.shade700;
        prefix = '~';
        break;
      case DiffType.modifiedNew:
        backgroundColor = Colors.blue.withOpacity(0.1);
        textColor = Colors.blue.shade700;
        prefix = '~';
        break;
    }
    
    return Container(
      color: backgroundColor,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showLineNumbers) ...[
            SizedBox(
              width: 40,
              child: Text(
                '${line.originalLineNumber ?? line.newLineNumber ?? ''}',
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.textTheme.bodySmall?.color?.withOpacity(0.5),
                  fontFamily: 'monospace',
                ),
                textAlign: TextAlign.right,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Text(
            prefix,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontFamily: 'monospace',
            ),
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              line.content,
              style: TextStyle(
                color: textColor,
                fontFamily: 'monospace',
                fontSize: 13,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context) {
    final theme = Theme.of(context);
    final pendingCount = modifications.where((m) => m.status == ModificationStatus.pending).length;
    
    if (pendingCount == 0 || onBatchStatusChanged == null) {
      return const SizedBox.shrink();
    }
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(8),
          bottomRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          Text(
            '待处理: $pendingCount 项修改',
            style: theme.textTheme.bodySmall,
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: () {
              print('❌ 点击全部拒绝');
              onBatchStatusChanged!(ModificationStatus.rejected);
            },
            icon: const Icon(Icons.close, size: 16),
            label: const Text('全部拒绝'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: () {
              print('✅ 点击全部接受');
              onBatchStatusChanged!(ModificationStatus.accepted);
            },
            icon: const Icon(Icons.check, size: 16),
            label: const Text('全部接受'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取修改类型文本
  String _getTypeText(ModificationType type) {
    switch (type) {
      case ModificationType.replace:
        return '替换';
      case ModificationType.insert:
        return '插入';
      case ModificationType.delete:
        return '删除';
    }
  }
}
