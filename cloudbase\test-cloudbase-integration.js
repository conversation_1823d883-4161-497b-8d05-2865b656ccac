#!/usr/bin/env node

/**
 * 岱宗文脉 - CloudBase集成测试脚本
 * 测试CloudBase云函数API和数据库连接
 */

const axios = require('axios');
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function success(message) {
  log(`✅ ${message}`, 'green');
}

function error(message) {
  log(`❌ ${message}`, 'red');
}

function warning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function info(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function step(message) {
  log(`\n🔧 ${message}`, 'cyan');
}

// 测试配置
const API_BASE_URL = 'https://api.dznovel.top/api';
const TEST_TIMEOUT = 10000; // 10秒超时

// 创建axios实例
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: TEST_TIMEOUT,
  headers: {
    'Content-Type': 'application/json'
  }
});

// 测试结果统计
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

async function runTest(testName, testFunction) {
  testResults.total++;
  try {
    info(`测试: ${testName}`);
    await testFunction();
    testResults.passed++;
    success(`${testName} - 通过`);
  } catch (err) {
    testResults.failed++;
    testResults.errors.push({ test: testName, error: err.message });
    error(`${testName} - 失败: ${err.message}`);
  }
}

// 测试API连通性
async function testApiConnectivity() {
  const response = await api.get('/health');
  if (response.status !== 200) {
    throw new Error(`API健康检查失败，状态码: ${response.status}`);
  }
}

// 测试管理员登录
async function testAdminLogin() {
  const response = await api.post('/admin/login', {
    username: 'admin',
    password: 'admin123'
  });
  
  if (!response.data.success) {
    throw new Error('管理员登录失败');
  }
  
  if (!response.data.data.token) {
    throw new Error('未返回管理员token');
  }
  
  // 保存token用于后续测试
  api.defaults.headers.Authorization = `Bearer ${response.data.data.token}`;
}

// 测试管理员token验证
async function testAdminTokenVerification() {
  const response = await api.get('/admin/verify');
  
  if (!response.data.success) {
    throw new Error('管理员token验证失败');
  }
}

// 测试仪表板数据
async function testDashboardData() {
  const response = await api.get('/dashboard');
  
  if (!response.data.success) {
    throw new Error('获取仪表板数据失败');
  }
  
  const data = response.data.data;
  if (!data.stats || typeof data.stats.totalUsers !== 'number') {
    throw new Error('仪表板数据格式不正确');
  }
}

// 测试用户列表
async function testUsersList() {
  const response = await api.get('/users');
  
  if (!response.data.success) {
    throw new Error('获取用户列表失败');
  }
  
  const data = response.data.data;
  if (!Array.isArray(data.users)) {
    throw new Error('用户列表数据格式不正确');
  }
}

// 测试会员码列表
async function testMemberCodesList() {
  const response = await api.get('/member-codes');
  
  if (!response.data.success) {
    throw new Error('获取会员码列表失败');
  }
  
  const data = response.data.data;
  if (!Array.isArray(data.memberCodes)) {
    throw new Error('会员码列表数据格式不正确');
  }
}

// 测试系统状态
async function testSystemStatus() {
  const response = await api.get('/system/status');
  
  if (!response.data.success) {
    throw new Error('获取系统状态失败');
  }
  
  const data = response.data.data;
  if (!data.apiStatus || !data.databaseStatus) {
    throw new Error('系统状态数据格式不正确');
  }
}

// 测试同步统计
async function testSyncStats() {
  const response = await api.get('/sync/stats');
  
  if (!response.data.success) {
    throw new Error('获取同步统计失败');
  }
}

// 主测试函数
async function runAllTests() {
  log('\n🚀 开始CloudBase集成测试...', 'magenta');
  log(`📡 API地址: ${API_BASE_URL}`, 'blue');
  log(`⏱️  超时时间: ${TEST_TIMEOUT}ms`, 'blue');
  
  // 基础连通性测试
  step('基础连通性测试');
  await runTest('API连通性', testApiConnectivity);
  
  // 管理员认证测试
  step('管理员认证测试');
  await runTest('管理员登录', testAdminLogin);
  await runTest('管理员token验证', testAdminTokenVerification);
  
  // 数据接口测试
  step('数据接口测试');
  await runTest('仪表板数据', testDashboardData);
  await runTest('用户列表', testUsersList);
  await runTest('会员码列表', testMemberCodesList);
  await runTest('系统状态', testSystemStatus);
  await runTest('同步统计', testSyncStats);
  
  // 输出测试结果
  step('测试结果');
  log(`\n📊 测试统计:`, 'magenta');
  log(`   总计: ${testResults.total}`, 'blue');
  log(`   通过: ${testResults.passed}`, 'green');
  log(`   失败: ${testResults.failed}`, 'red');
  
  if (testResults.failed > 0) {
    log(`\n❌ 失败的测试:`, 'red');
    testResults.errors.forEach(err => {
      log(`   - ${err.test}: ${err.error}`, 'red');
    });
  }
  
  const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
  log(`\n🎯 成功率: ${successRate}%`, successRate === '100.0' ? 'green' : 'yellow');
  
  if (testResults.failed === 0) {
    success('\n🎉 所有测试通过！CloudBase集成正常工作。');
  } else {
    warning('\n⚠️  部分测试失败，请检查CloudBase配置。');
  }
  
  return testResults.failed === 0;
}

// 运行测试
if (require.main === module) {
  runAllTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(err => {
      error(`测试执行异常: ${err.message}`);
      process.exit(1);
    });
}

module.exports = { runAllTests };
