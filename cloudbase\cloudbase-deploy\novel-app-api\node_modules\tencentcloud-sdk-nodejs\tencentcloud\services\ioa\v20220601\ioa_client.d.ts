import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { CreatePrivilegeCodeRequest, DescribeDLPFileDetectResultRequest, CreateDeviceVirtualGroupResponse, DescribeSoftCensusListByDeviceResponse, DescribeSoftwareInformationResponse, DescribeDeviceHardwareInfoListResponse, DescribeRootAccountGroupResponse, DescribeDeviceVirtualGroupsRequest, DescribeVirtualDevicesRequest, DescribeDeviceInfoResponse, DescribeDevicesResponse, DescribeDeviceInfoRequest, ModifyVirtualDeviceGroupsResponse, DescribeLocalAccountsResponse, DescribeRootAccountGroupRequest, DescribeAccountGroupsRequest, DescribeAccountGroupsResponse, DescribeDeviceVirtualGroupsResponse, DescribeLocalAccountsRequest, DescribeSoftwareInformationRequest, CreateDeviceVirtualGroupRequest, DescribeDevicesRequest, CreateDeviceTaskRequest, DescribeSoftCensusListByDeviceRequest, ModifyVirtualDeviceGroupsRequest, CreateDeviceTaskResponse, CreateDLPFileDetectionTaskResponse, DescribeDLPFileDetectResultResponse, CreateDLPFileDetectionTaskRequest, CreatePrivilegeCodeResponse, DescribeDeviceHardwareInfoListRequest, DescribeVirtualDevicesResponse } from "./ioa_models";
/**
 * ioa client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 创建终端自定义分组，私有化调用path为：/capi/Assets/Device/CreateDeviceVirtualGroup
     */
    CreateDeviceVirtualGroup(req: CreateDeviceVirtualGroupRequest, cb?: (error: string, rep: CreateDeviceVirtualGroupResponse) => void): Promise<CreateDeviceVirtualGroupResponse>;
    /**
     * 获取账号列表，支持分页，模糊搜索，私有化调用path为：/capi/Assets/Account/DescribeLocalAccounts
     */
    DescribeLocalAccounts(req: DescribeLocalAccountsRequest, cb?: (error: string, rep: DescribeLocalAccountsResponse) => void): Promise<DescribeLocalAccountsResponse>;
    /**
     * 生成特权码，私有化调用path为：capi/Assets/Device/CreatePrivilegeCode，生成的特权码、卸载码，仅对该设备当天有效
     */
    CreatePrivilegeCode(req: CreatePrivilegeCodeRequest, cb?: (error: string, rep: CreatePrivilegeCodeResponse) => void): Promise<CreatePrivilegeCodeResponse>;
    /**
     * 展示自定义分组终端列表，私有化调用path为：/capi/Assets/DescribeVirtualDevices
     */
    DescribeVirtualDevices(req: DescribeVirtualDevicesRequest, cb?: (error: string, rep: DescribeVirtualDevicesResponse) => void): Promise<DescribeVirtualDevicesResponse>;
    /**
     * 查询满足条件的终端数据详情，私有化调用path为：/capi/Assets/Device/DescribeDevices
     */
    DescribeDevices(req: DescribeDevicesRequest, cb?: (error: string, rep: DescribeDevicesResponse) => void): Promise<DescribeDevicesResponse>;
    /**
     * 查询终端自定义分组列表，私有化调用path为：/capi/Assets/Device/DescribeDeviceVirtualGroups
     */
    DescribeDeviceVirtualGroups(req: DescribeDeviceVirtualGroupsRequest, cb?: (error: string, rep: DescribeDeviceVirtualGroupsResponse) => void): Promise<DescribeDeviceVirtualGroupsResponse>;
    /**
     * 提交送检任务
     */
    CreateDLPFileDetectionTask(req: CreateDLPFileDetectionTaskRequest, cb?: (error: string, rep: CreateDLPFileDetectionTaskResponse) => void): Promise<CreateDLPFileDetectionTaskResponse>;
    /**
     * 创建获取终端进程网络服务信息任务，私有化调用path为：capi/Assets/Device/DescribeDeviceInfo
     */
    CreateDeviceTask(req: CreateDeviceTaskRequest, cb?: (error: string, rep: CreateDeviceTaskResponse) => void): Promise<CreateDeviceTaskResponse>;
    /**
     * 获取终端进程网络服务信息，私有化调用path为：capi/Assets/Device/DescribeDeviceInfo
     */
    DescribeDeviceInfo(req: DescribeDeviceInfoRequest, cb?: (error: string, rep: DescribeDeviceInfoResponse) => void): Promise<DescribeDeviceInfoResponse>;
    /**
     * 查看指定终端的软件详情列表,私有化调用path为：capi/Software/DescribeSoftwareInformation
     */
    DescribeSoftwareInformation(req: DescribeSoftwareInformationRequest, cb?: (error: string, rep: DescribeSoftwareInformationResponse) => void): Promise<DescribeSoftwareInformationResponse>;
    /**
     * 查询满足条件的查询终端硬件信息列表，私有化调用path为：/capi/Assets/Device/DescribeDeviceHardwareInfoList
     */
    DescribeDeviceHardwareInfoList(req: DescribeDeviceHardwareInfoListRequest, cb?: (error: string, rep: DescribeDeviceHardwareInfoListResponse) => void): Promise<DescribeDeviceHardwareInfoListResponse>;
    /**
     * 终端自定义分组增减终端，私有化调用path为：/capi/Assets/Device/ModifyVirtualDeviceGroups
     */
    ModifyVirtualDeviceGroups(req: ModifyVirtualDeviceGroupsRequest, cb?: (error: string, rep: ModifyVirtualDeviceGroupsResponse) => void): Promise<ModifyVirtualDeviceGroupsResponse>;
    /**
     * 查询账号根分组详情。对应“用户与授权管理”里内置不可见的全网根账号组，所有新建的目录，都挂在该全网根账号组下。
     */
    DescribeRootAccountGroup(req?: DescribeRootAccountGroupRequest, cb?: (error: string, rep: DescribeRootAccountGroupResponse) => void): Promise<DescribeRootAccountGroupResponse>;
    /**
     * webservice查询文件检测结果
     */
    DescribeDLPFileDetectResult(req: DescribeDLPFileDetectResultRequest, cb?: (error: string, rep: DescribeDLPFileDetectResultResponse) => void): Promise<DescribeDLPFileDetectResultResponse>;
    /**
     * 查看终端树下的软件列表,私有化调用path为：capi/Software/DescribeSoftCensusListByDevice
     */
    DescribeSoftCensusListByDevice(req: DescribeSoftCensusListByDeviceRequest, cb?: (error: string, rep: DescribeSoftCensusListByDeviceResponse) => void): Promise<DescribeSoftCensusListByDeviceResponse>;
    /**
     * 以分页的方式查询账号分组列表，私有化调用path为：/capi/Assets/DescribeAccountGroups
     */
    DescribeAccountGroups(req: DescribeAccountGroupsRequest, cb?: (error: string, rep: DescribeAccountGroupsResponse) => void): Promise<DescribeAccountGroupsResponse>;
}
