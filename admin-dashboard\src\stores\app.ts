import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 侧边栏折叠状态
  const sidebarCollapsed = ref(false)
  
  // 移动端侧边栏显示状态
  const mobileSidebarOpen = ref(false)
  
  // 主题模式
  const isDark = ref(false)
  
  // 加载状态
  const loading = ref(false)
  
  // 系统信息
  const systemInfo = ref({
    version: '1.0.0',
    buildTime: new Date().toISOString(),
    apiUrl: import.meta.env.VITE_API_URL || '/api'
  })

  // 切换侧边栏
  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  // 切换移动端侧边栏
  const toggleMobileSidebar = () => {
    mobileSidebarOpen.value = !mobileSidebarOpen.value
  }

  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value
    document.documentElement.classList.toggle('dark', isDark.value)
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
  }

  // 设置加载状态
  const setLoading = (state: boolean) => {
    loading.value = state
  }

  // 初始化应用
  const initApp = () => {
    // 恢复主题设置
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme === 'dark') {
      isDark.value = true
      document.documentElement.classList.add('dark')
    }

    // 恢复侧边栏状态
    const savedSidebarState = localStorage.getItem('sidebar_collapsed')
    if (savedSidebarState === 'true') {
      sidebarCollapsed.value = true
    }
  }

  // 监听侧边栏状态变化
  watch(sidebarCollapsed, (newVal) => {
    localStorage.setItem('sidebar_collapsed', String(newVal))
  })

  return {
    sidebarCollapsed,
    mobileSidebarOpen,
    isDark,
    loading,
    systemInfo,
    toggleSidebar,
    toggleMobileSidebar,
    toggleTheme,
    setLoading,
    initApp
  }
})
