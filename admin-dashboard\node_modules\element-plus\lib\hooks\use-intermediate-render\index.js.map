{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-intermediate-render/index.ts"], "sourcesContent": ["import { nextTick, unref, watch } from 'vue'\n\nimport type { Ref } from 'vue'\n\nexport type UseDelayedRenderProps = {\n  indicator: Ref<boolean>\n  intermediateIndicator: Ref<boolean>\n  shouldSetIntermediate?: (step: 'show' | 'hide') => boolean\n  beforeShow?: () => void\n  beforeHide?: () => void\n  afterShow?: () => void\n  afterHide?: () => void\n}\n\nexport const useDelayedRender = ({\n  indicator,\n  intermediateIndicator,\n  shouldSetIntermediate = () => true,\n  beforeShow,\n  afterShow,\n  afterHide,\n  beforeHide,\n}: UseDelayedRenderProps) => {\n  watch(\n    () => unref(indicator),\n    (val) => {\n      if (val) {\n        beforeShow?.()\n        nextTick(() => {\n          if (!unref(indicator)) return\n          if (shouldSetIntermediate('show')) {\n            intermediateIndicator.value = true\n          }\n        })\n      } else {\n        beforeHide?.()\n        nextTick(() => {\n          if (unref(indicator)) return\n\n          if (shouldSetIntermediate('hide')) {\n            intermediateIndicator.value = false\n          }\n        })\n      }\n    }\n  )\n\n  // because we don't always set the value ourselves, so that we\n  // simply watch the value's state, then invoke the corresponding hook.\n  watch(\n    () => intermediateIndicator.value,\n    (val) => {\n      if (val) {\n        afterShow?.()\n      } else {\n        afterHide?.()\n      }\n    }\n  )\n}\n"], "names": ["watch", "unref", "nextTick"], "mappings": ";;;;;;AACY,MAAC,gBAAgB,GAAG,CAAC;AACjC,EAAE,SAAS;AACX,EAAE,qBAAqB;AACvB,EAAE,qBAAqB,GAAG,MAAM,IAAI;AACpC,EAAE,UAAU;AACZ,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,CAAC,KAAK;AACN,EAAEA,SAAK,CAAC,MAAMC,SAAK,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,KAAK;AACzC,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;AACjD,MAAMC,YAAQ,CAAC,MAAM;AACrB,QAAQ,IAAI,CAACD,SAAK,CAAC,SAAS,CAAC;AAC7B,UAAU,OAAO;AACjB,QAAQ,IAAI,qBAAqB,CAAC,MAAM,CAAC,EAAE;AAC3C,UAAU,qBAAqB,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7C,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,MAAM;AACX,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,EAAE,CAAC;AACjD,MAAMC,YAAQ,CAAC,MAAM;AACrB,QAAQ,IAAID,SAAK,CAAC,SAAS,CAAC;AAC5B,UAAU,OAAO;AACjB,QAAQ,IAAI,qBAAqB,CAAC,MAAM,CAAC,EAAE;AAC3C,UAAU,qBAAqB,CAAC,KAAK,GAAG,KAAK,CAAC;AAC9C,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAED,SAAK,CAAC,MAAM,qBAAqB,CAAC,KAAK,EAAE,CAAC,GAAG,KAAK;AACpD,IAAI,IAAI,GAAG,EAAE;AACb,MAAM,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,EAAE,CAAC;AAC/C,KAAK,MAAM;AACX,MAAM,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,SAAS,EAAE,CAAC;AAC/C,KAAK;AACL,GAAG,CAAC,CAAC;AACL;;;;"}