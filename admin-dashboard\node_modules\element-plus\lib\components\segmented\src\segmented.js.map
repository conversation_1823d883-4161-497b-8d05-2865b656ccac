{"version": 3, "file": "segmented.js", "sources": ["../../../../../../packages/components/segmented/src/segmented.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isBoolean,\n  isNumber,\n  isString,\n} from '@element-plus/utils'\nimport { useAriaProps, useSizeProp } from '@element-plus/hooks'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\n\nimport type { Option } from './types'\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type Segmented from './segmented.vue'\n\nexport interface Props {\n  label?: string\n  value?: string\n  disabled?: string\n}\n\nexport const defaultProps: Required<Props> = {\n  label: 'label',\n  value: 'value',\n  disabled: 'disabled',\n}\n\nexport const segmentedProps = buildProps({\n  direction: {\n    type: definePropType<'vertical' | 'horizontal'>(String),\n    default: 'horizontal',\n  },\n  /**\n   * @description options of segmented\n   */\n  options: {\n    type: definePropType<Option[]>(Array),\n    default: () => [],\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: [String, Number, Boolean],\n    default: undefined,\n  },\n  /**\n   * @description configuration options, see the following table\n   */\n  props: {\n    type: definePropType<Props>(Object),\n    default: () => defaultProps,\n  },\n  /**\n   * @description fit width of parent content\n   */\n  block: Boolean,\n  /**\n   * @description size of component\n   */\n  size: useSizeProp,\n  /**\n   * @description whether segmented is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description native input id\n   */\n  id: String,\n  /**\n   * @description native `name` attribute\n   */\n  name: String,\n  ...useAriaProps(['ariaLabel']),\n})\n\nexport type SegmentedProps = ExtractPropTypes<typeof segmentedProps>\nexport type SegmentedPropsPublic = __ExtractPublicPropTypes<\n  typeof segmentedProps\n>\n\nexport const segmentedEmits = {\n  [UPDATE_MODEL_EVENT]: (val: any) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n  [CHANGE_EVENT]: (val: any) =>\n    isString(val) || isNumber(val) || isBoolean(val),\n}\nexport type SegmentedEmits = typeof segmentedEmits\n\nexport type SegmentedInstance = InstanceType<typeof Segmented> & unknown\n"], "names": ["buildProps", "definePropType", "useSizeProp", "useAriaProps", "UPDATE_MODEL_EVENT", "isString", "isNumber", "isBoolean", "CHANGE_EVENT"], "mappings": ";;;;;;;;;;;AASY,MAAC,YAAY,GAAG;AAC5B,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE;AACU,MAAC,cAAc,GAAGA,kBAAU,CAAC;AACzC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,YAAY;AACzB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEA,sBAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,EAAE;AACrB,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACnC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAEA,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM,YAAY;AAC/B,GAAG;AACH,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,IAAI,EAAEC,iBAAW;AACnB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,GAAGC,oBAAY,CAAC,CAAC,WAAW,CAAC,CAAC;AAChC,CAAC,EAAE;AACS,MAAC,cAAc,GAAG;AAC9B,EAAE,CAACC,wBAAkB,GAAG,CAAC,GAAG,KAAKC,eAAQ,CAAC,GAAG,CAAC,IAAIC,cAAQ,CAAC,GAAG,CAAC,IAAIC,eAAS,CAAC,GAAG,CAAC;AACjF,EAAE,CAACC,kBAAY,GAAG,CAAC,GAAG,KAAKH,eAAQ,CAAC,GAAG,CAAC,IAAIC,cAAQ,CAAC,GAAG,CAAC,IAAIC,eAAS,CAAC,GAAG,CAAC;AAC3E;;;;;;"}