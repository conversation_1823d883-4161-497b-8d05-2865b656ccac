import 'package:hive/hive.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order.g.dart';

/// 订单状态枚举
@HiveType(typeId: 23)
enum OrderStatus {
  @HiveField(0)
  pending, // 待支付
  @HiveField(1)
  paid, // 已支付
  @HiveField(2)
  cancelled, // 已取消
  @HiveField(3)
  refunded, // 已退款
  @HiveField(4)
  expired, // 已过期
}

/// 支付方式枚举
@HiveType(typeId: 24)
enum PaymentMethod {
  @HiveField(0)
  wechat, // 微信支付
  @HiveField(1)
  alipay, // 支付宝
  @HiveField(2)
  memberCode, // 会员码
}

/// 订单模型
@HiveType(typeId: 25)
@JsonSerializable()
class Order extends HiveObject {
  @HiveField(0)
  final String id;

  @HiveField(1)
  final String userId;

  @HiveField(2)
  final String packageId; // 套餐ID

  @HiveField(3)
  final String packageName; // 套餐名称

  @HiveField(4)
  final double amount; // 订单金额

  @HiveField(5)
  OrderStatus status;

  @HiveField(6)
  PaymentMethod? paymentMethod;

  @HiveField(7)
  final DateTime createdAt;

  @HiveField(8)
  DateTime updatedAt;

  @HiveField(9)
  DateTime? paidAt; // 支付时间

  @HiveField(10)
  String? transactionId; // 第三方支付交易ID

  @HiveField(11)
  String? memberCode; // 使用的会员码

  @HiveField(12)
  DateTime? expireAt; // 订单过期时间

  @HiveField(13)
  Map<String, dynamic>? metadata; // 额外信息

  Order({
    required this.id,
    required this.userId,
    required this.packageId,
    required this.packageName,
    required this.amount,
    this.status = OrderStatus.pending,
    this.paymentMethod,
    required this.createdAt,
    required this.updatedAt,
    this.paidAt,
    this.transactionId,
    this.memberCode,
    this.expireAt,
    this.metadata,
  });

  /// 检查订单是否已过期
  bool get isExpired {
    if (expireAt == null) return false;
    return DateTime.now().isAfter(expireAt!);
  }

  /// 检查订单是否可以支付
  bool get canPay {
    return status == OrderStatus.pending && !isExpired;
  }

  factory Order.fromJson(Map<String, dynamic> json) => _$OrderFromJson(json);
  Map<String, dynamic> toJson() => _$OrderToJson(this);
}

/// 支付请求模型
@JsonSerializable()
class PaymentRequest {
  final String orderId;
  final PaymentMethod paymentMethod;
  final String? memberCode; // 会员码支付时需要

  PaymentRequest({
    required this.orderId,
    required this.paymentMethod,
    this.memberCode,
  });

  factory PaymentRequest.fromJson(Map<String, dynamic> json) => _$PaymentRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentRequestToJson(this);
}

/// 支付响应模型
@JsonSerializable()
class PaymentResponse {
  final bool success;
  final String? paymentUrl; // 支付链接
  final String? qrCode; // 二维码内容
  final Map<String, dynamic>? paymentData; // 支付数据
  final String? message;

  PaymentResponse({
    required this.success,
    this.paymentUrl,
    this.qrCode,
    this.paymentData,
    this.message,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) => _$PaymentResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PaymentResponseToJson(this);
}