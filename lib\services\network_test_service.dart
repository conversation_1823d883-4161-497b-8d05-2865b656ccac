import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../config/api_config.dart';

/// 网络连接测试服务
class NetworkTestService {
  static final Dio _dio = Dio(BaseOptions(
    connectTimeout: Duration(seconds: 10),
    receiveTimeout: Duration(seconds: 10),
    sendTimeout: Duration(seconds: 10),
  ));

  /// 测试服务器连接
  static Future<Map<String, dynamic>> testServerConnection() async {
    final results = <String, dynamic>{};
    
    print('🔍 开始网络连接测试...');
    
    // 测试健康检查端点
    try {
      print('📡 测试服务器健康检查...');
      final response = await _dio.get('${ApiConfig.baseUrl.replaceAll('/api', '')}/api/health');
      
      if (response.statusCode == 200) {
        results['health_check'] = {
          'success': true,
          'status_code': response.statusCode,
          'data': response.data,
          'message': '服务器连接正常'
        };
        print('✅ 服务器健康检查成功');
      } else {
        results['health_check'] = {
          'success': false,
          'status_code': response.statusCode,
          'message': '服务器响应异常'
        };
        print('⚠️ 服务器响应异常: ${response.statusCode}');
      }
    } catch (e) {
      results['health_check'] = {
        'success': false,
        'error': e.toString(),
        'message': '无法连接到服务器'
      };
      print('❌ 服务器健康检查失败: $e');
    }
    
    // 测试登录端点
    try {
      print('🔐 测试登录端点...');
      final response = await _dio.post(
        '${ApiConfig.baseUrl}/auth/login',
        data: {
          'username': 'test_connection',
          'password': 'invalid_password'
        },
      );
      
      // 即使登录失败，能收到响应也说明连接正常
      results['login_endpoint'] = {
        'success': true,
        'status_code': response.statusCode,
        'message': '登录端点可访问'
      };
      print('✅ 登录端点连接正常');
    } catch (e) {
      if (e is DioException) {
        if (e.response != null) {
          // 收到响应，说明连接正常
          results['login_endpoint'] = {
            'success': true,
            'status_code': e.response!.statusCode,
            'message': '登录端点可访问（收到错误响应）'
          };
          print('✅ 登录端点连接正常（收到错误响应）');
        } else {
          // 连接失败
          results['login_endpoint'] = {
            'success': false,
            'error': e.toString(),
            'message': '无法连接到登录端点'
          };
          print('❌ 登录端点连接失败: $e');
        }
      } else {
        results['login_endpoint'] = {
          'success': false,
          'error': e.toString(),
          'message': '登录端点测试异常'
        };
        print('❌ 登录端点测试异常: $e');
      }
    }
    
    // 测试网络延迟
    try {
      print('⏱️ 测试网络延迟...');
      final startTime = DateTime.now();
      await _dio.get('${ApiConfig.baseUrl.replaceAll('/api', '')}/api/health');
      final endTime = DateTime.now();
      final latency = endTime.difference(startTime).inMilliseconds;
      
      results['latency'] = {
        'success': true,
        'latency_ms': latency,
        'message': '网络延迟: ${latency}ms'
      };
      print('✅ 网络延迟: ${latency}ms');
    } catch (e) {
      results['latency'] = {
        'success': false,
        'error': e.toString(),
        'message': '无法测试网络延迟'
      };
      print('❌ 网络延迟测试失败: $e');
    }
    
    print('🏁 网络连接测试完成');
    return results;
  }

  /// 显示网络测试结果
  static void showTestResults(Map<String, dynamic> results) {
    final buffer = StringBuffer();
    buffer.writeln('🔍 网络连接测试结果:\n');
    
    // 健康检查结果
    final healthCheck = results['health_check'];
    if (healthCheck != null) {
      buffer.writeln('📡 服务器健康检查:');
      if (healthCheck['success']) {
        buffer.writeln('   ✅ ${healthCheck['message']}');
        if (healthCheck['data'] != null) {
          buffer.writeln('   📊 服务器信息: ${healthCheck['data']['service']}');
        }
      } else {
        buffer.writeln('   ❌ ${healthCheck['message']}');
        if (healthCheck['error'] != null) {
          buffer.writeln('   🔍 错误详情: ${healthCheck['error']}');
        }
      }
      buffer.writeln('');
    }
    
    // 登录端点结果
    final loginEndpoint = results['login_endpoint'];
    if (loginEndpoint != null) {
      buffer.writeln('🔐 登录端点测试:');
      if (loginEndpoint['success']) {
        buffer.writeln('   ✅ ${loginEndpoint['message']}');
      } else {
        buffer.writeln('   ❌ ${loginEndpoint['message']}');
        if (loginEndpoint['error'] != null) {
          buffer.writeln('   🔍 错误详情: ${loginEndpoint['error']}');
        }
      }
      buffer.writeln('');
    }
    
    // 网络延迟结果
    final latency = results['latency'];
    if (latency != null) {
      buffer.writeln('⏱️ 网络延迟测试:');
      if (latency['success']) {
        buffer.writeln('   ✅ ${latency['message']}');
      } else {
        buffer.writeln('   ❌ ${latency['message']}');
      }
      buffer.writeln('');
    }
    
    // 总结
    final allSuccess = (healthCheck?['success'] ?? false) && 
                      (loginEndpoint?['success'] ?? false) && 
                      (latency?['success'] ?? false);
    
    if (allSuccess) {
      buffer.writeln('🎉 网络连接正常，可以正常使用应用功能');
    } else {
      buffer.writeln('⚠️ 网络连接存在问题，请检查:');
      buffer.writeln('   1. 手机和电脑是否在同一WiFi网络');
      buffer.writeln('   2. 服务器是否正在运行');
      buffer.writeln('   3. 防火墙是否阻止了连接');
      buffer.writeln('   4. IP地址配置是否正确');
    }
    
    Get.dialog(
      AlertDialog(
        title: Text('网络连接测试'),
        content: SingleChildScrollView(
          child: Text(
            buffer.toString(),
            style: TextStyle(fontFamily: 'monospace', fontSize: 12),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: Text('确定'),
          ),
        ],
      ),
    );
  }
}
