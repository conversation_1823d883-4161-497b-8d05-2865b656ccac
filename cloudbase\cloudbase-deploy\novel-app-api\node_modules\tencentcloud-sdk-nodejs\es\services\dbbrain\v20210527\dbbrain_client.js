import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("dbbrain.tencentcloudapi.com", "2021-05-27", clientConfig);
    }
    async DescribeDBAutonomyEvents(req, cb) {
        return this.request("DescribeDBAutonomyEvents", req, cb);
    }
    async DescribeTopSpaceTableTimeSeries(req, cb) {
        return this.request("DescribeTopSpaceTableTimeSeries", req, cb);
    }
    async DescribeSecurityAuditLogDownloadUrls(req, cb) {
        return this.request("DescribeSecurityAuditLogDownloadUrls", req, cb);
    }
    async DeleteDBDiagReportTasks(req, cb) {
        return this.request("DeleteDBDiagReportTasks", req, cb);
    }
    async DescribeMailProfile(req, cb) {
        return this.request("DescribeMailProfile", req, cb);
    }
    async CreateSchedulerMailProfile(req, cb) {
        return this.request("CreateSchedulerMailProfile", req, cb);
    }
    async DescribeSqlTemplate(req, cb) {
        return this.request("DescribeSqlTemplate", req, cb);
    }
    async DescribeDBPerfTimeSeries(req, cb) {
        return this.request("DescribeDBPerfTimeSeries", req, cb);
    }
    async DescribeTopSpaceSchemas(req, cb) {
        return this.request("DescribeTopSpaceSchemas", req, cb);
    }
    async DescribeMySqlProcessList(req, cb) {
        return this.request("DescribeMySqlProcessList", req, cb);
    }
    async CreateMailProfile(req, cb) {
        return this.request("CreateMailProfile", req, cb);
    }
    async DescribeDBDiagReportTasks(req, cb) {
        return this.request("DescribeDBDiagReportTasks", req, cb);
    }
    async DescribeRedisTopHotKeys(req, cb) {
        return this.request("DescribeRedisTopHotKeys", req, cb);
    }
    async UpdateMonitorSwitch(req, cb) {
        return this.request("UpdateMonitorSwitch", req, cb);
    }
    async DescribeRedisTopKeyPrefixList(req, cb) {
        return this.request("DescribeRedisTopKeyPrefixList", req, cb);
    }
    async CreateRedisBigKeyAnalysisTask(req, cb) {
        return this.request("CreateRedisBigKeyAnalysisTask", req, cb);
    }
    async DeleteAuditLogFile(req, cb) {
        return this.request("DeleteAuditLogFile", req, cb);
    }
    async DescribeAllUserGroup(req, cb) {
        return this.request("DescribeAllUserGroup", req, cb);
    }
    async DescribeDBDiagEvents(req, cb) {
        return this.request("DescribeDBDiagEvents", req, cb);
    }
    async DescribeAuditInstanceList(req, cb) {
        return this.request("DescribeAuditInstanceList", req, cb);
    }
    async DeleteRedisBigKeyAnalysisTasks(req, cb) {
        return this.request("DeleteRedisBigKeyAnalysisTasks", req, cb);
    }
    async DescribeTopSpaceSchemaTimeSeries(req, cb) {
        return this.request("DescribeTopSpaceSchemaTimeSeries", req, cb);
    }
    async DescribeSlowLogTimeSeriesStats(req, cb) {
        return this.request("DescribeSlowLogTimeSeriesStats", req, cb);
    }
    async DescribeRedisProcessList(req, cb) {
        return this.request("DescribeRedisProcessList", req, cb);
    }
    async DescribeSlowLogUserHostStats(req, cb) {
        return this.request("DescribeSlowLogUserHostStats", req, cb);
    }
    async DescribeRedisCmdPerfTimeSeries(req, cb) {
        return this.request("DescribeRedisCmdPerfTimeSeries", req, cb);
    }
    async CreateSqlFilter(req, cb) {
        return this.request("CreateSqlFilter", req, cb);
    }
    async DescribeDBSpaceStatus(req, cb) {
        return this.request("DescribeDBSpaceStatus", req, cb);
    }
    async CreateProxySessionKillTask(req, cb) {
        return this.request("CreateProxySessionKillTask", req, cb);
    }
    async DescribeHealthScore(req, cb) {
        return this.request("DescribeHealthScore", req, cb);
    }
    async CancelKillTask(req, cb) {
        return this.request("CancelKillTask", req, cb);
    }
    async CreateSecurityAuditLogExportTask(req, cb) {
        return this.request("CreateSecurityAuditLogExportTask", req, cb);
    }
    async CloseAuditService(req, cb) {
        return this.request("CloseAuditService", req, cb);
    }
    async DescribeUserSqlAdvice(req, cb) {
        return this.request("DescribeUserSqlAdvice", req, cb);
    }
    async CancelRedisBigKeyAnalysisTasks(req, cb) {
        return this.request("CancelRedisBigKeyAnalysisTasks", req, cb);
    }
    async DeleteSecurityAuditLogExportTasks(req, cb) {
        return this.request("DeleteSecurityAuditLogExportTasks", req, cb);
    }
    async DescribeDBAutonomyActions(req, cb) {
        return this.request("DescribeDBAutonomyActions", req, cb);
    }
    async DescribeRedisBigKeyAnalysisTasks(req, cb) {
        return this.request("DescribeRedisBigKeyAnalysisTasks", req, cb);
    }
    async DescribeRedisCommandCostStatistics(req, cb) {
        return this.request("DescribeRedisCommandCostStatistics", req, cb);
    }
    async CreateAuditLogFile(req, cb) {
        return this.request("CreateAuditLogFile", req, cb);
    }
    async DescribeRedisTopBigKeys(req, cb) {
        return this.request("DescribeRedisTopBigKeys", req, cb);
    }
    async DescribeSecurityAuditLogExportTasks(req, cb) {
        return this.request("DescribeSecurityAuditLogExportTasks", req, cb);
    }
    async DescribeSlowLogTopSqls(req, cb) {
        return this.request("DescribeSlowLogTopSqls", req, cb);
    }
    async DescribeAllUserContact(req, cb) {
        return this.request("DescribeAllUserContact", req, cb);
    }
    async CancelDBAutonomyEvent(req, cb) {
        return this.request("CancelDBAutonomyEvent", req, cb);
    }
    async DescribeSlowLogs(req, cb) {
        return this.request("DescribeSlowLogs", req, cb);
    }
    async DescribeRedisCommandOverview(req, cb) {
        return this.request("DescribeRedisCommandOverview", req, cb);
    }
    async DescribeDBDiagEvent(req, cb) {
        return this.request("DescribeDBDiagEvent", req, cb);
    }
    async DescribeUserAutonomyProfile(req, cb) {
        return this.request("DescribeUserAutonomyProfile", req, cb);
    }
    async DescribeDBDiagHistory(req, cb) {
        return this.request("DescribeDBDiagHistory", req, cb);
    }
    async DescribeProxyProcessStatistics(req, cb) {
        return this.request("DescribeProxyProcessStatistics", req, cb);
    }
    async CreateDBDiagReportTask(req, cb) {
        return this.request("CreateDBDiagReportTask", req, cb);
    }
    async DescribeDiagDBInstances(req, cb) {
        return this.request("DescribeDiagDBInstances", req, cb);
    }
    async DeleteSqlFilters(req, cb) {
        return this.request("DeleteSqlFilters", req, cb);
    }
    async ModifyAlarmPolicy(req, cb) {
        return this.request("ModifyAlarmPolicy", req, cb);
    }
    async CreateUserAutonomyProfile(req, cb) {
        return this.request("CreateUserAutonomyProfile", req, cb);
    }
    async OpenAuditService(req, cb) {
        return this.request("OpenAuditService", req, cb);
    }
    async AddUserContact(req, cb) {
        return this.request("AddUserContact", req, cb);
    }
    async DescribeIndexRecommendInfo(req, cb) {
        return this.request("DescribeIndexRecommendInfo", req, cb);
    }
    async VerifyUserAccount(req, cb) {
        return this.request("VerifyUserAccount", req, cb);
    }
    async ModifyAuditService(req, cb) {
        return this.request("ModifyAuditService", req, cb);
    }
    async DescribeIndexRecommendAggregationSlowLogs(req, cb) {
        return this.request("DescribeIndexRecommendAggregationSlowLogs", req, cb);
    }
    async ModifyDiagDBInstanceConf(req, cb) {
        return this.request("ModifyDiagDBInstanceConf", req, cb);
    }
    async CancelDBAutonomyAction(req, cb) {
        return this.request("CancelDBAutonomyAction", req, cb);
    }
    async DescribeRedisSlowLogTopSqls(req, cb) {
        return this.request("DescribeRedisSlowLogTopSqls", req, cb);
    }
    async CreateKillTask(req, cb) {
        return this.request("CreateKillTask", req, cb);
    }
    async UpdateAgentSwitch(req, cb) {
        return this.request("UpdateAgentSwitch", req, cb);
    }
    async DescribeNoPrimaryKeyTables(req, cb) {
        return this.request("DescribeNoPrimaryKeyTables", req, cb);
    }
    async DescribeTopSpaceTables(req, cb) {
        return this.request("DescribeTopSpaceTables", req, cb);
    }
    async DescribeSqlFilters(req, cb) {
        return this.request("DescribeSqlFilters", req, cb);
    }
    async DescribeAlarmTemplate(req, cb) {
        return this.request("DescribeAlarmTemplate", req, cb);
    }
    async DescribeAuditLogFiles(req, cb) {
        return this.request("DescribeAuditLogFiles", req, cb);
    }
    async KillMySqlThreads(req, cb) {
        return this.request("KillMySqlThreads", req, cb);
    }
    async CreateDBDiagReportUrl(req, cb) {
        return this.request("CreateDBDiagReportUrl", req, cb);
    }
    async DescribeDBDiagReportContent(req, cb) {
        return this.request("DescribeDBDiagReportContent", req, cb);
    }
    async DescribeDBAutonomyAction(req, cb) {
        return this.request("DescribeDBAutonomyAction", req, cb);
    }
    async ModifySqlFilters(req, cb) {
        return this.request("ModifySqlFilters", req, cb);
    }
    async DescribeProxySessionKillTasks(req, cb) {
        return this.request("DescribeProxySessionKillTasks", req, cb);
    }
    async ModifyUserAutonomyProfile(req, cb) {
        return this.request("ModifyUserAutonomyProfile", req, cb);
    }
    async DescribeSlowLogQueryTimeStats(req, cb) {
        return this.request("DescribeSlowLogQueryTimeStats", req, cb);
    }
}
