岱宗文脉 - Visual C++ 运行时库说明
========================================

如果您在运行应用程序时遇到以下错误：
- "找不到 VCRUNTIME140_1.dll"
- "应用程序无法正常启动(0xc000007b)"
- "缺少 MSVCP140.dll"

解决方案：
========================================

方案一：自动安装（推荐）
安装程序会自动检测并安装必要的 Visual C++ 运行时库。
如果自动安装失败，请尝试方案二。

方案二：手动安装
1. 下载并安装 Microsoft Visual C++ Redistributable for Visual Studio 2019 (x64)
   下载地址：https://aka.ms/vs/16/release/vc_redist.x64.exe

2. 或者使用应用目录中的 VC_redist.x64.exe 文件进行安装

方案三：使用兼容模式启动
如果上述方案都无效，请使用"岱宗文脉 (兼容模式)"快捷方式启动应用。
该模式会优先使用应用目录中的 DLL 文件。

方案四：手动复制DLL文件
将应用目录中的以下文件复制到 C:\Windows\System32\ 目录：
- vcruntime140_1.dll
- vcruntime140.dll  
- msvcp140.dll

注意：方案四需要管理员权限。

技术支持：
========================================
如果问题仍然存在，请联系技术支持并提供以下信息：
- Windows 版本
- 错误截图
- 应用程序日志

联系方式：https://www.dznovel.top/
