"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTmpSecret = void 0;
const metadata_1 = require("./metadata");
const cloudplatform_1 = require("./cloudplatform");
const kDefaultRole = process.env.ROLE_NAME || 'TCB_QcsRole';
const kCBRRole = process.env.CBR_ROLE;
class SecretManager {
    constructor() {
        this.roleName = '';
        this.roleName = (0, cloudplatform_1.checkIsInCBR)() && kCBRRole ? kCBRRole : kDefaultRole;
        this.tmpSecret = null;
    }
    /* istanbul ignore next */
    async getTmpSecret() {
        if (this.tmpSecret) {
            const now = new Date().getTime();
            const expire = this.tmpSecret.expire * 1000;
            const oneHour = 3600 * 1000;
            if (now < expire - oneHour) {
                // 密钥没过期
                return this.tmpSecret;
            }
            else {
                // 密钥过期
                return await this.fetchTmpSecret();
            }
        }
        else {
            return await this.fetchTmpSecret();
        }
    }
    /* istanbul ignore next */
    async fetchTmpSecret() {
        const body = await (0, metadata_1.lookupCredentials)(this.roleName);
        const payload = JSON.parse(body);
        this.tmpSecret = {
            id: payload.TmpSecretId,
            key: payload.TmpSecretKey,
            expire: payload.ExpiredTime,
            token: payload.Token
        };
        return this.tmpSecret;
    }
}
const globalSecretManager = new SecretManager();
async function getTmpSecret() {
    return await globalSecretManager.getTmpSecret();
}
exports.getTmpSecret = getTmpSecret;
