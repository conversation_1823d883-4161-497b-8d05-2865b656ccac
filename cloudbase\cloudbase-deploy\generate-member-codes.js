const fs = require('fs');
const path = require('path');

// 生成会员码
function generateMemberCode(prefix = 'VIP', length = 8) {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = prefix;
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 生成10个永久会员码
function generatePermanentMemberCodes(count = 10) {
  const codes = [];
  const batchId = `batch_${Date.now()}`;
  
  for (let i = 0; i < count; i++) {
    const code = generateMemberCode('VIP', 8);
    codes.push({
      id: `code_${Date.now()}_${i}`,
      code: code,
      packageId: 'pkg_permanent',
      isUsed: false,
      usedBy: null,
      usedAt: null,
      expireAt: null, // 永久有效
      batchId: batchId,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }
  
  return codes;
}

// 读取现有数据库
const dbPath = path.join(__dirname, 'data', 'db.json');
let db;
try {
  db = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
} catch (error) {
  console.error('读取数据库失败:', error);
  process.exit(1);
}

// 确保memberCodes数组存在
if (!db.memberCodes) {
  db.memberCodes = [];
}

// 生成10个永久会员码
const newCodes = generatePermanentMemberCodes(10);

// 添加到数据库
db.memberCodes.push(...newCodes);

// 保存数据库
try {
  fs.writeFileSync(dbPath, JSON.stringify(db, null, 2));
  console.log('🎉 成功生成10个永久会员码：');
  console.log('');
  newCodes.forEach((codeData, index) => {
    console.log(`${index + 1}. ${codeData.code}`);
  });
  console.log('');
  console.log('📝 会员码详情：');
  console.log(`   类型: 永久会员`);
  console.log(`   数量: ${newCodes.length} 个`);
  console.log(`   批次ID: ${newCodes[0].batchId}`);
  console.log(`   创建时间: ${new Date().toLocaleString()}`);
  console.log('');
  console.log('✅ 会员码已保存到数据库中，可以立即使用！');
} catch (error) {
  console.error('保存数据库失败:', error);
  process.exit(1);
}
