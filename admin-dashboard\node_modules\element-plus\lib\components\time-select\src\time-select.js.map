{"version": 3, "file": "time-select.js", "sources": ["../../../../../../packages/components/time-select/src/time-select.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport { CircleClose, Clock } from '@element-plus/icons-vue'\nimport { useEmptyValuesProps, useSizeProp } from '@element-plus/hooks'\n\nimport type { PopperEffect } from '@element-plus/components/popper'\nimport type TimeSelect from './time-select.vue'\nimport type { Component, ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const timeSelectProps = buildProps({\n  /**\n   * @description set format of time\n   */\n  format: {\n    type: String,\n    default: 'HH:mm',\n  },\n  /**\n   * @description binding value\n   */\n  modelValue: String,\n  /**\n   * @description whether TimeSelect is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether the input is editable\n   */\n  editable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description Tooltip theme, built-in theme: `dark` / `light`\n   */\n  effect: {\n    type: definePropType<PopperEffect>(String),\n    default: 'light',\n  },\n  /**\n   * @description whether to show clear button\n   */\n  clearable: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description size of Input\n   */\n  size: useSizeProp,\n  /**\n   * @description placeholder in non-range mode\n   */\n  placeholder: String,\n  /**\n   * @description start time\n   */\n  start: {\n    type: String,\n    default: '09:00',\n  },\n  /**\n   * @description end time\n   */\n  end: {\n    type: String,\n    default: '18:00',\n  },\n  /**\n   * @description time step\n   */\n  step: {\n    type: String,\n    default: '00:30',\n  },\n  /**\n   * @description minimum time, any time before this time will be disabled\n   */\n  minTime: String,\n  /**\n   * @description maximum time, any time after this time will be disabled\n   */\n  maxTime: String,\n  /**\n   * @description whether `end` is included in options\n   */\n  includeEndTime: Boolean,\n  /**\n   * @description same as `name` in native input\n   */\n  name: String,\n  /**\n   * @description custom prefix icon component\n   */\n  prefixIcon: {\n    type: definePropType<string | Component>([String, Object]),\n    default: () => Clock,\n  },\n  /**\n   * @description custom clear icon component\n   */\n  clearIcon: {\n    type: definePropType<string | Component>([String, Object]),\n    default: () => CircleClose,\n  },\n  ...useEmptyValuesProps,\n} as const)\n\nexport type TimeSelectProps = ExtractPropTypes<typeof timeSelectProps>\nexport type TimeSelectPropsPublic = __ExtractPublicPropTypes<\n  typeof timeSelectProps\n>\n\nexport type TimeSelectInstance = InstanceType<typeof TimeSelect> & unknown\n"], "names": ["buildProps", "definePropType", "useSizeProp", "Clock", "CircleClose", "useEmptyValuesProps"], "mappings": ";;;;;;;;;AAGY,MAAC,eAAe,GAAGA,kBAAU,CAAC;AAC1C,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,UAAU,EAAE,MAAM;AACpB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAEC,iBAAW;AACnB,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,OAAO,EAAE,MAAM;AACjB,EAAE,OAAO,EAAE,MAAM;AACjB,EAAE,cAAc,EAAE,OAAO;AACzB,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAED,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAME,cAAK;AACxB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEF,sBAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAMG,oBAAW;AAC9B,GAAG;AACH,EAAE,GAAGC,2BAAmB;AACxB,CAAC;;;;"}