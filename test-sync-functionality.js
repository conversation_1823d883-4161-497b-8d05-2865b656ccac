#!/usr/bin/env node

/**
 * 测试数据同步功能
 * 验证删除COS直传功能后的数据同步是否正常工作
 */

const http = require('http');
const https = require('https');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'https://api.dznovel.top/api', // 生产环境API
  testUser: {
    phone: '13800138000',
    password: 'test123456'
  }
};

// 发送HTTP请求的辅助函数
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const isHttps = url.startsWith('https');
    const client = isHttps ? https : http;
    
    const requestOptions = {
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SyncTest/1.0',
        ...options.headers
      }
    };

    const req = client.request(url, requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({ status: res.statusCode, data: result });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// 测试数据同步上传
async function testSyncUpload(token) {
  console.log('📤 测试数据同步上传...');
  
  const testData = {
    novels: [
      {
        id: 'test-novel-1',
        title: '测试小说',
        content: '这是一个测试小说的内容...',
        createdAt: new Date().toISOString()
      }
    ],
    userSettings: {
      theme: 'dark',
      fontSize: 16
    },
    characterCards: [],
    knowledgeDocuments: []
  };

  try {
    const response = await makeRequest(`${TEST_CONFIG.baseUrl}/sync/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`
      },
      body: {
        data: testData,
        timestamp: new Date().toISOString()
      }
    });

    if (response.status === 200 && response.data.success) {
      console.log('✅ 数据同步上传测试成功');
      return true;
    } else {
      console.log('❌ 数据同步上传测试失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ 数据同步上传测试异常:', error.message);
    return false;
  }
}

// 测试数据同步下载
async function testSyncDownload(token) {
  console.log('📥 测试数据同步下载...');
  
  try {
    const response = await makeRequest(`${TEST_CONFIG.baseUrl}/sync/download`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    if (response.status === 200 && response.data.success) {
      console.log('✅ 数据同步下载测试成功');
      console.log('📊 下载的数据大小:', JSON.stringify(response.data.data).length, '字节');
      return true;
    } else {
      console.log('❌ 数据同步下载测试失败:', response.data);
      return false;
    }
  } catch (error) {
    console.log('❌ 数据同步下载测试异常:', error.message);
    return false;
  }
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始测试数据同步功能...');
  console.log('🌐 测试环境:', TEST_CONFIG.baseUrl);
  
  // 注意：这里需要一个有效的测试token
  // 在实际测试中，您需要先登录获取token
  const testToken = 'your-test-token-here';
  
  if (testToken === 'your-test-token-here') {
    console.log('⚠️ 请先设置有效的测试token');
    console.log('💡 您可以通过登录接口获取token，然后替换testToken变量');
    return;
  }

  let uploadSuccess = false;
  let downloadSuccess = false;

  try {
    // 测试上传
    uploadSuccess = await testSyncUpload(testToken);
    
    // 等待一秒
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 测试下载
    downloadSuccess = await testSyncDownload(testToken);
    
  } catch (error) {
    console.log('❌ 测试过程中发生异常:', error.message);
  }

  // 输出测试结果
  console.log('\n📊 测试结果总结:');
  console.log(`   数据上传: ${uploadSuccess ? '✅ 通过' : '❌ 失败'}`);
  console.log(`   数据下载: ${downloadSuccess ? '✅ 通过' : '❌ 失败'}`);
  
  if (uploadSuccess && downloadSuccess) {
    console.log('\n🎉 所有测试通过！数据同步功能正常工作');
  } else {
    console.log('\n⚠️ 部分测试失败，请检查API实现');
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('💥 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = { runTests, testSyncUpload, testSyncDownload };
