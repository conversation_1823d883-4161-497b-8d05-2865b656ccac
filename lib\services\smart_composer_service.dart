import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:uuid/uuid.dart';
import '../models/smart_composer_models.dart';
import '../models/novel.dart';
import '../models/text_modification.dart';

/// Smart Composer AI 服务
/// 提供与各种 LLM 提供商的集成
class SmartComposerService {
  static const _uuid = Uuid();
  
  /// 发送聊天消息
  Future<String> sendChatMessage({
    required ChatModel model,
    required LLMProvider provider,
    required List<ChatMessage> messages,
    String? systemPrompt,
    Map<String, dynamic>? memoryContext,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    switch (provider.type) {
      case LLMProviderType.openai:
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          memoryContext: memoryContext,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.anthropic:
        return _sendAnthropicMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.gemini:
        return _sendGeminiMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.deepseek:
        return _sendDeepSeekMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          memoryContext: memoryContext,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.openaiCompatible:
      case LLMProviderType.openrouter:
      case LLMProviderType.groq:
      case LLMProviderType.perplexity:
      case LLMProviderType.mistral:
        // 这些提供商都使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.ollama:
      case LLMProviderType.lmStudio:
        // 本地模型也使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.azureOpenai:
        // Azure OpenAI 也使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      case LLMProviderType.morph:
        // Morph 也使用 OpenAI 兼容的 API
        return _sendOpenAIMessage(
          provider: provider,
          model: model,
          messages: messages,
          systemPrompt: systemPrompt,
          temperature: temperature,
          maxTokens: maxTokens,
        );
      default:
        throw UnsupportedError('不支持的提供商类型: ${provider.type}');
    }
  }

  /// OpenAI API 调用
  Future<String> _sendOpenAIMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    Map<String, dynamic>? memoryContext,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('OpenAI API Key 未配置');
    }

    // 构建完整的API URL
    String fullUrl;
    if (provider.baseUrl != null && provider.baseUrl!.isNotEmpty) {
      // 使用提供商的baseUrl
      final baseUrl = provider.baseUrl!;
      if (baseUrl.endsWith('/chat/completions')) {
        // 已经是完整的URL
        fullUrl = baseUrl;
      } else if (baseUrl.contains('/chat/completions')) {
        // URL中包含chat/completions路径
        fullUrl = baseUrl;
      } else if (baseUrl.endsWith('/v1')) {
        // 以/v1结尾，添加chat/completions
        fullUrl = '$baseUrl/chat/completions';
      } else if (baseUrl.endsWith('/')) {
        // 以/结尾，添加v1/chat/completions
        fullUrl = '${baseUrl}v1/chat/completions';
      } else {
        // 没有特殊结尾，添加/v1/chat/completions
        fullUrl = '$baseUrl/v1/chat/completions';
      }
    } else {
      // 默认使用OpenAI官方API
      fullUrl = 'https://api.openai.com/v1/chat/completions';
    }

    final url = Uri.parse(fullUrl);

    final requestMessages = <Map<String, dynamic>>[];

    // 构建完整的系统提示（包含memory上下文）
    String? fullSystemPrompt = systemPrompt;
    if (memoryContext != null && memoryContext.isNotEmpty) {
      final memoryContent = _buildMemoryContextPrompt(memoryContext);
      if (memoryContent.isNotEmpty) {
        fullSystemPrompt = fullSystemPrompt != null
            ? '$fullSystemPrompt\n\n$memoryContent'
            : memoryContent;
      }
    }

    // 添加系统提示
    if (fullSystemPrompt != null && fullSystemPrompt.isNotEmpty) {
      requestMessages.add({
        'role': 'system',
        'content': fullSystemPrompt,
      });
    }

    // 添加对话消息
    for (final message in messages) {
      requestMessages.add({
        'role': message.role,
        'content': message.content,
      });
    }

    final requestBody = {
      'model': model.model,
      'messages': requestMessages,
      'temperature': temperature,
      'max_tokens': maxTokens,
    };

    print('Smart Composer: 发送请求到 $url');
    print('Smart Composer: 使用模型 ${model.model}');
    print('Smart Composer: 提供商类型 ${provider.type}');

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: json.encode(requestBody),
    );

    print('Smart Composer: 收到响应，状态码 ${response.statusCode}');

    if (response.statusCode != 200) {
      String errorMessage = 'OpenAI API 调用失败: ${response.statusCode}';

      try {
        final errorData = json.decode(response.body) as Map<String, dynamic>;
        if (errorData.containsKey('error')) {
          final error = errorData['error'];
          if (error is Map<String, dynamic>) {
            errorMessage += ' - ${error['message'] ?? error['type'] ?? '未知错误'}';
          } else {
            errorMessage += ' - $error';
          }
        }
      } catch (e) {
        errorMessage += ' - ${response.body}';
      }

      // 针对常见错误提供更友好的提示
      if (response.statusCode == 404) {
        errorMessage += '\n提示：请检查API端点URL和模型名称是否正确';
      } else if (response.statusCode == 401) {
        errorMessage += '\n提示：请检查API密钥是否正确';
      } else if (response.statusCode == 429) {
        errorMessage += '\n提示：API调用频率过高，请稍后重试';
      }

      throw Exception(errorMessage);
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final choices = responseData['choices'] as List;
    
    if (choices.isEmpty) {
      throw Exception('OpenAI API 返回空响应');
    }

    final message = choices.first['message'] as Map<String, dynamic>;
    return message['content'] as String;
  }

  /// Anthropic API 调用
  Future<String> _sendAnthropicMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('Anthropic API Key 未配置');
    }

    final baseUrl = provider.baseUrl ?? 'https://api.anthropic.com/v1';
    final url = Uri.parse('$baseUrl/messages');

    final requestMessages = <Map<String, dynamic>>[];
    
    // Anthropic 不支持系统消息在 messages 数组中，需要单独处理
    for (final message in messages) {
      if (message.role != 'system') {
        requestMessages.add({
          'role': message.role,
          'content': message.content,
        });
      }
    }

    final requestBody = {
      'model': model.model,
      'messages': requestMessages,
      'temperature': temperature,
      'max_tokens': maxTokens,
    };

    // 添加系统提示
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      requestBody['system'] = systemPrompt;
    }

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('Anthropic API 调用失败: ${response.statusCode} ${response.body}');
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final content = responseData['content'] as List;
    
    if (content.isEmpty) {
      throw Exception('Anthropic API 返回空响应');
    }

    final textContent = content.first as Map<String, dynamic>;
    return textContent['text'] as String;
  }

  /// Google Gemini API 调用
  Future<String> _sendGeminiMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('Gemini API Key 未配置');
    }

    final baseUrl = provider.baseUrl ?? 'https://generativelanguage.googleapis.com/v1beta';
    final url = Uri.parse('$baseUrl/models/${model.model}:generateContent?key=$apiKey');

    final contents = <Map<String, dynamic>>[];
    
    // 构建对话内容
    String fullPrompt = '';
    if (systemPrompt != null && systemPrompt.isNotEmpty) {
      fullPrompt += '$systemPrompt\n\n';
    }
    
    for (final message in messages) {
      if (message.role == 'user') {
        fullPrompt += 'User: ${message.content}\n';
      } else if (message.role == 'assistant') {
        fullPrompt += 'Assistant: ${message.content}\n';
      }
    }

    contents.add({
      'parts': [
        {'text': fullPrompt}
      ]
    });

    final requestBody = {
      'contents': contents,
      'generationConfig': {
        'temperature': temperature,
        'maxOutputTokens': maxTokens,
      },
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('Gemini API 调用失败: ${response.statusCode} ${response.body}');
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final candidates = responseData['candidates'] as List;
    
    if (candidates.isEmpty) {
      throw Exception('Gemini API 返回空响应');
    }

    final candidate = candidates.first as Map<String, dynamic>;
    final content = candidate['content'] as Map<String, dynamic>;
    final parts = content['parts'] as List;
    
    if (parts.isEmpty) {
      throw Exception('Gemini API 返回空内容');
    }

    final part = parts.first as Map<String, dynamic>;
    return part['text'] as String;
  }

  /// DeepSeek API 调用
  Future<String> _sendDeepSeekMessage({
    required LLMProvider provider,
    required ChatModel model,
    required List<ChatMessage> messages,
    String? systemPrompt,
    Map<String, dynamic>? memoryContext,
    double temperature = 0.7,
    int maxTokens = 2000,
  }) async {
    final apiKey = provider.apiKey;
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('DeepSeek API Key 未配置');
    }

    // 构建完整的API URL
    String fullUrl;
    if (provider.baseUrl != null && provider.baseUrl!.isNotEmpty) {
      final baseUrl = provider.baseUrl!;
      if (baseUrl.endsWith('/chat/completions')) {
        fullUrl = baseUrl;
      } else if (baseUrl.contains('/chat/completions')) {
        fullUrl = baseUrl;
      } else if (baseUrl.endsWith('/v1')) {
        fullUrl = '$baseUrl/chat/completions';
      } else if (baseUrl.endsWith('/')) {
        fullUrl = '${baseUrl}v1/chat/completions';
      } else {
        fullUrl = '$baseUrl/v1/chat/completions';
      }
    } else {
      fullUrl = 'https://api.deepseek.com/v1/chat/completions';
    }

    final url = Uri.parse(fullUrl);

    print('DeepSeek: 发送请求到 $fullUrl');
    print('DeepSeek: 使用模型 ${model.model}');

    final requestMessages = <Map<String, dynamic>>[];

    // 构建完整的系统提示（包含memory上下文）
    String? fullSystemPrompt = systemPrompt;
    if (memoryContext != null && memoryContext.isNotEmpty) {
      final memoryContent = _buildMemoryContextPrompt(memoryContext);
      if (memoryContent.isNotEmpty) {
        fullSystemPrompt = fullSystemPrompt != null
            ? '$fullSystemPrompt\n\n$memoryContent'
            : memoryContent;
      }
    }

    // 添加系统提示
    if (fullSystemPrompt != null && fullSystemPrompt.isNotEmpty) {
      requestMessages.add({
        'role': 'system',
        'content': fullSystemPrompt,
      });
    }

    // 添加对话消息
    for (final message in messages) {
      requestMessages.add({
        'role': message.role,
        'content': message.content,
      });
    }

    final requestBody = {
      'model': model.model,
      'messages': requestMessages,
      'temperature': temperature,
      'max_tokens': maxTokens,
    };

    final response = await http.post(
      url,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $apiKey',
      },
      body: json.encode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('DeepSeek API 调用失败: ${response.statusCode} ${response.body}');
    }

    final responseData = json.decode(response.body) as Map<String, dynamic>;
    final choices = responseData['choices'] as List;
    
    if (choices.isEmpty) {
      throw Exception('DeepSeek API 返回空响应');
    }

    final message = choices.first['message'] as Map<String, dynamic>;
    return message['content'] as String;
  }

  /// 创建新的聊天消息
  ChatMessage createUserMessage(String content) {
    return ChatMessage(
      id: _uuid.v4(),
      role: 'user',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// 创建助手回复消息
  ChatMessage createAssistantMessage(String content) {
    return ChatMessage(
      id: _uuid.v4(),
      role: 'assistant',
      content: content,
      timestamp: DateTime.now(),
    );
  }

  /// 创建创作模式助手回复消息
  ChatMessage createCreativeEditMessage(String content, CreativeEditResponse editResponse) {
    return ChatMessage(
      id: _uuid.v4(),
      role: 'assistant',
      content: content,
      timestamp: DateTime.now(),
      messageType: ChatMessageType.creativeEdit,
      editResponse: editResponse,
    );
  }

  /// 解析AI响应，检测是否为创作模式修改建议
  ChatMessage parseAIResponse(String response) {
    print('🔍 解析AI响应: ${response.substring(0, response.length > 200 ? 200 : response.length)}...');

    try {
      // 清理响应文本，移除可能的markdown代码块标记
      String cleanedResponse = response.trim();

      // 清理Grok模型的<think>标签
      cleanedResponse = _cleanThinkTags(cleanedResponse);

      if (cleanedResponse.startsWith('```json')) {
        cleanedResponse = cleanedResponse.substring(7);
      }
      if (cleanedResponse.endsWith('```')) {
        cleanedResponse = cleanedResponse.substring(0, cleanedResponse.length - 3);
      }
      cleanedResponse = cleanedResponse.trim();

      print('🧹 清理后的响应: ${cleanedResponse.substring(0, cleanedResponse.length > 200 ? 200 : cleanedResponse.length)}...');

      // 尝试解析为JSON格式的创作模式响应
      final jsonResponse = json.decode(cleanedResponse) as Map<String, dynamic>;

      print('✅ JSON解析成功: ${jsonResponse['response_type']}');

      if (jsonResponse['response_type'] == 'creative_edit') {
        final editResponse = CreativeEditResponse.fromJson(jsonResponse);
        print('🎯 创建创作模式消息，修改数量: ${editResponse.modifications.length}');
        return createCreativeEditMessage(editResponse.summary, editResponse);
      }
    } catch (e) {
      // 如果不是JSON格式或解析失败，当作普通消息处理
      print('❌ JSON解析失败: $e');
    }

    // 普通助手消息
    print('💬 创建普通助手消息');
    return createAssistantMessage(response);
  }

  /// 验证创作模式响应的格式
  bool isValidCreativeEditResponse(String response) {
    try {
      final jsonResponse = json.decode(response.trim()) as Map<String, dynamic>;
      return jsonResponse['response_type'] == 'creative_edit' &&
             jsonResponse.containsKey('modifications') &&
             jsonResponse.containsKey('summary') &&
             jsonResponse.containsKey('overall_explanation');
    } catch (e) {
      return false;
    }
  }

  /// 提取创作模式响应中的修改建议
  List<TextModification> extractModifications(String response) {
    try {
      final jsonResponse = json.decode(response.trim()) as Map<String, dynamic>;
      if (jsonResponse['response_type'] == 'creative_edit') {
        final editResponse = CreativeEditResponse.fromJson(jsonResponse);
        return editResponse.modifications;
      }
    } catch (e) {
      // 解析失败
    }
    return [];
  }

  /// 测试JSON解析功能
  void testJsonParsing() {
    const testJson = '''
{
  "response_type": "creative_edit",
  "summary": "根据用户指令删除章节的第二段内容。",
  "modifications": [
    {
      "id": "mod_1",
      "type": "delete",
      "start_line": 5,
      "end_line": 9,
      "original_text": "他坐在那张老旧的中脑桌前，指尖无意识地敲打着桌面。前世的记忆如潮水般涌来，其中最为清晰的，便是那老如同有着之血脉的巨额债务。那笔钱，是他母亲不存在的投资项目间高利贷债的，利滚利，最终将他们逼入泥潭，成为他后来际遇产。众叛亲离的起点。",
      "new_text": "",
      "reason": "根据用户指令删除第二段内容"
    }
  ],
  "overall_explanation": "已按照用户要求删除了第二段内容，这段内容描述了主角的回忆和债务情况。删除后文章将更加简洁，直接进入主要情节。"
}''';

    try {
      final message = parseAIResponse(testJson);
      print('🧪 测试解析结果: 消息类型=${message.messageType.name}');
      if (message.editResponse != null) {
        print('  修改建议数量: ${message.editResponse!.modifications.length}');
        for (final mod in message.editResponse!.modifications) {
          print('  - ${mod.type.name}: 行${mod.startLine}-${mod.endLine}');
        }
      }
    } catch (e) {
      print('❌ 测试解析失败: $e');
    }
  }

  /// 为小说章节生成基本信息提示（不包含章节内容，内容通过memory传递）
  String generateNovelBasicInfoPrompt(Novel novel, Chapter? currentChapter) {
    final buffer = StringBuffer();

    buffer.writeln('以下是当前小说的基本信息：');
    buffer.writeln('标题：${novel.title}');
    buffer.writeln('类型：${novel.genre}');
    if (novel.style?.isNotEmpty == true) {
      buffer.writeln('风格：${novel.style}');
    }
    buffer.writeln();

    if (novel.outline.isNotEmpty) {
      buffer.writeln('小说大纲：');
      buffer.writeln(novel.outline);
      buffer.writeln();
    }

    if (currentChapter != null) {
      buffer.writeln('当前章节：第${currentChapter.number}章 - ${currentChapter.title}');
      buffer.writeln();
    }

    buffer.writeln('注意：章节内容和引用章节通过memory参数传递，请参考memory中的上下文信息。');

    return buffer.toString();
  }

  /// 为小说章节生成上下文提示（已弃用，请使用memory参数传递）
  @Deprecated('请使用memory参数传递章节内容，而不是显式包含在提示词中')
  String generateNovelContextPrompt(Novel novel, Chapter? currentChapter) {
    // 重定向到基本信息方法，不再包含章节内容
    return generateNovelBasicInfoPrompt(novel, currentChapter);
  }

  /// 构建memory上下文提示
  String _buildMemoryContextPrompt(Map<String, dynamic> memoryContext) {
    final buffer = StringBuffer();

    // 添加小说基本信息
    if (memoryContext.containsKey('novel_info')) {
      final novelInfo = memoryContext['novel_info'] as Map<String, dynamic>;
      buffer.writeln('=== 小说信息 ===');
      if (novelInfo['title'] != null) {
        buffer.writeln('标题：${novelInfo['title']}');
      }
      if (novelInfo['genre'] != null) {
        buffer.writeln('类型：${novelInfo['genre']}');
      }
      if (novelInfo['outline'] != null && novelInfo['outline'].toString().isNotEmpty) {
        buffer.writeln('大纲：${novelInfo['outline']}');
      }
      buffer.writeln();
    }

    // 添加当前章节基本信息（内容在提示词中提供）
    if (memoryContext.containsKey('current_chapter')) {
      final chapterInfo = memoryContext['current_chapter'] as Map<String, dynamic>;
      buffer.writeln('=== 当前章节信息 ===');
      if (chapterInfo['number'] != null) {
        buffer.writeln('章节：第${chapterInfo['number']}章');
      }
      if (chapterInfo['title'] != null) {
        buffer.writeln('标题：${chapterInfo['title']}');
      }
      if (chapterInfo['content_note'] != null) {
        buffer.writeln('说明：${chapterInfo['content_note']}');
      }
      buffer.writeln();
    }

    // 添加引用的其他章节
    if (memoryContext.containsKey('referenced_chapters')) {
      final referencedChapters = memoryContext['referenced_chapters'] as List<Map<String, dynamic>>;
      if (referencedChapters.isNotEmpty) {
        buffer.writeln('=== 引用章节 ===');
        for (final chapter in referencedChapters) {
          buffer.writeln('第${chapter['number']}章：${chapter['title']}');
          if (chapter['content'] != null) {
            final content = chapter['content'].toString();
            // 限制引用章节的长度
            final limitedContent = content.length > 1000
                ? '${content.substring(0, 1000)}...'
                : content;
            buffer.writeln(limitedContent);
          }
          buffer.writeln();
        }
      }
    }

    // 添加上下文信息
    if (memoryContext.containsKey('context_info')) {
      final contextInfo = memoryContext['context_info'] as Map<String, dynamic>;
      buffer.writeln('=== 上下文信息 ===');
      contextInfo.forEach((key, value) {
        buffer.writeln('$key：$value');
      });
      buffer.writeln();
    }

    return buffer.toString();
  }

  /// 清理Grok模型输出的<think>标签
  String _cleanThinkTags(String content) {
    if (content.isEmpty) return content;

    String cleaned = content;

    // 移除<think>...</think>标签及其内容
    cleaned = cleaned.replaceAll(RegExp(r'<think>.*?</think>', dotAll: true), '');

    // 处理未闭合的<think>标签：查找<think>标签的位置
    final thinkStartIndex = cleaned.indexOf('<think>');
    if (thinkStartIndex != -1) {
      // 如果找到<think>标签，尝试保留标签前的内容
      final beforeThink = cleaned.substring(0, thinkStartIndex).trim();
      if (beforeThink.isNotEmpty) {
        cleaned = beforeThink;
      } else {
        // 如果<think>标签前没有内容，尝试查找标签后是否有有效的JSON
        final afterThink = cleaned.substring(thinkStartIndex + 7); // 跳过<think>
        final jsonMatch = RegExp(r'(\[.*\]|\{.*\})', dotAll: true).firstMatch(afterThink);
        if (jsonMatch != null) {
          cleaned = jsonMatch.group(1)!;
        } else {
          // 如果都没有，移除整个<think>部分
          cleaned = cleaned.replaceAll(RegExp(r'<think>.*$', dotAll: true), '');
        }
      }
    }

    // 清理多余的空白字符
    cleaned = cleaned.trim();

    // 如果清理后内容为空，返回原内容（避免过度清理）
    if (cleaned.isEmpty) {
      print("[SmartComposerService] 警告：清理<think>标签后内容为空，返回原内容");
      return content;
    }

    if (cleaned != content) {
      print("[SmartComposerService] 已清理<think>标签，原长度: ${content.length}, 清理后长度: ${cleaned.length}");
    }
    return cleaned;
  }
}
