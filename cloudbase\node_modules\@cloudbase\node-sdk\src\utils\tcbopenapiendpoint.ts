/* eslint-disable complexity */
export function buildUrl(options: {
  envId?: string
  region?: string
  protocol?: 'http' | 'https'
  serviceUrl?: string
  seqId?: string
  isInternal?: boolean
  name: string
  path: string
}): string {
  const endpoint = `https://${options.envId}.api.tcloudbasegateway.com/v1/cloudrun/${options.name}`
  const path = options.path.startsWith('/') ? options.path : `/${options.path}`
  return `${endpoint}${path}`
}

export function buildCommonOpenApiUrlWithPath(options: {
  envId: string
  protocol?: 'http' | 'https'
  path: string
}): string {
  return `${options.protocol || 'https'}://${options.envId}.api.tcloudbasegateway.com${options.path}`
}
