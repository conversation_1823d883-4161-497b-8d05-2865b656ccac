import{_ as Pe}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                   *//* empty css                 *//* empty css                        *//* empty css                      *//* empty css                     *//* empty css                       *//* empty css                   *//* empty css                        *//* empty css                  *//* empty css                   */import{d as Fe,r as c,a as B,y as Q,o as je,c as b,b as m,e as t,w as o,f as Oe,m as Re,a2 as Ne,q as Le,A as Me,a4 as qe,H as W,a5 as Ye,a6 as Ge,v as n,Z as Je,i as f,E as He,B as C,a7 as Ke,h as i,t as v,_ as Ze,$ as Qe,a9 as We,g as Xe,l as et,a3 as tt,I as X,F as at,G as lt,j as P,ad as ot,a0 as st,u as nt,U as rt}from"./index-CAzH2L69.js";const it={class:"member-codes-container"},dt={class:"dashboard-card"},ut={class:"action-bar"},pt={class:"action-left"},ct={class:"action-right"},mt={class:"dashboard-card"},gt={class:"code-cell"},ft={class:"member-code"},_t={key:0},vt={key:1,class:"unused"},yt={key:0},kt={key:1,class:"unused"},bt={key:0},ht={key:1,class:"permanent"},xt={class:"pagination-container"},Vt={class:"input-tip"},Ct={class:"codes-preview"},It={key:0,class:"error-reason"},wt={key:0,class:"no-codes"},At=Fe({__name:"index",setup(St){const ee=nt(),F=c(!1),j=c(!1),O=c(!1),R=c(!1),I=c(!1),w=c(!1),A=c(!1),V=c([]),S=c(),U=c(),D=c(),N=c(""),L=c(""),g=B({page:1,size:20,total:0}),K=c([]),u=B({packageId:"pkg_permanent",count:10,prefix:"VIP",expireAt:null}),d=B({packageId:"pkg_permanent",codesInput:"",expireAt:null}),r=B({id:"",code:"",packageId:"pkg_permanent",expireAt:null}),te={packageId:[{required:!0,message:"请选择会员类型",trigger:"change"}],count:[{required:!0,message:"请输入生成数量",trigger:"blur"}],prefix:[{required:!0,message:"请输入前缀",trigger:"blur"}]},ae={packageId:[{required:!0,message:"请选择会员类型",trigger:"change"}],codes:[{required:!0,message:"请输入会员码",trigger:"blur"}]},le={code:[{required:!0,message:"请输入会员码",trigger:"blur"},{min:3,max:20,message:"会员码长度应在3-20个字符之间",trigger:"blur"},{pattern:/^[A-Z0-9]+$/,message:"会员码只能包含大写字母和数字",trigger:"blur"}],packageId:[{required:!0,message:"请选择会员类型",trigger:"change"}]},oe=l=>{switch(l){case"pkg_permanent":return"success";case"pkg_monthly":return"warning";default:return"info"}},se=l=>{switch(l){case"pkg_permanent":return"永久会员";case"pkg_monthly":return"月会员";default:return"未知"}},ne=l=>l.isUsed?"success":l.expireAt&&new Date(l.expireAt)<new Date?"danger":"info",re=l=>l.isUsed?"已使用":l.expireAt&&new Date(l.expireAt)<new Date?"已过期":"未使用",ie=l=>l.substring(0,8)+"...",M=l=>st(l).format("YYYY-MM-DD HH:mm"),de=async l=>{try{await navigator.clipboard.writeText(l),n.success("会员码已复制到剪贴板")}catch{n.error("复制失败")}},ue=l=>{ee.push(`/users/${l}`)},Z=()=>{g.page=1,h()},pe=l=>{V.value=l},ce=async()=>{if(V.value.length===0){n.warning("请选择要删除的会员码");return}try{await rt.confirm(`确定要删除选中的 ${V.value.length} 个会员码吗？`,"批量删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const l=V.value.map(x=>x.code),e=await fetch("/api/member-codes/batch-delete",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({codeIds:l})}),s=await e.json();e.ok&&s.success?(n.success(s.message),V.value=[],h()):n.error(s.message||"删除失败")}catch(l){l.message&&(console.error("批量删除失败:",l),n.error("删除失败"))}},me=l=>{r.id=l.id,r.code=l.code,r.packageId=l.packageId,r.expireAt=l.expireAt?new Date(l.expireAt):null,A.value=!0},ge=async()=>{if(D.value)try{if(!await D.value.validate())return;R.value=!0;const e=await fetch(`/api/member-codes/${r.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({code:r.code,packageId:r.packageId,expireAt:r.expireAt?r.expireAt.toISOString():null})}),s=await e.json();e.ok&&s.success?(n.success(s.message),A.value=!1,h()):n.error(s.message||"保存失败")}catch(l){console.error("保存会员码失败:",l),n.error("保存失败")}finally{R.value=!1}},fe=()=>{r.id="",r.code="",r.packageId="pkg_permanent",r.expireAt=null,D.value&&D.value.resetFields()},_e=async l=>{try{const e=await fetch(`/api/member-codes/${l.code}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}}),s=await e.json();e.ok&&s.success?(n.success(s.message),h()):n.error(s.message||"删除失败")}catch(e){console.error("删除会员码失败:",e),n.error("删除失败")}},q=Q(()=>d.codesInput.trim()?d.codesInput.split(`
`).map(e=>e.trim()).filter(e=>e.length>0).map(e=>{const s=ve(e);return{value:e,isValid:s.isValid,error:s.error}}):[]),z=Q(()=>q.value.filter(l=>l.isValid).map(l=>l.value)),ve=l=>!l||l.length<3?{isValid:!1,error:"长度不足"}:l.length>20?{isValid:!1,error:"长度过长"}:/^[A-Z0-9]+$/.test(l)?{isValid:!0,error:""}:{isValid:!1,error:"只能包含大写字母和数字"},ye=()=>{w.value=!0},ke=()=>{I.value=!0},be=async()=>{if(U.value){if(z.value.length===0){n.warning("请输入有效的会员码");return}try{O.value=!0;const l=await fetch("/api/member-codes/generate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({packageId:d.packageId,customCodes:z.value,expireAt:d.expireAt?d.expireAt.toISOString():null})}),e=await l.json();l.ok&&e.success?(n.success(e.message),w.value=!1,h()):n.error(e.message||"添加失败")}catch(l){console.error("添加自定义会员码失败:",l),n.error("添加失败")}finally{O.value=!1}}},he=async()=>{if(S.value)try{if(!await S.value.validate())return;j.value=!0;const e=await fetch("/api/member-codes/generate",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("admin_token")}`},body:JSON.stringify({packageId:u.packageId,count:u.count,prefix:u.prefix,expireAt:u.expireAt?u.expireAt.toISOString():null})}),s=await e.json();e.ok&&s.success?(n.success(s.message),I.value=!1,h()):n.error(s.message||"生成失败")}catch(l){console.error("生成会员码失败:",l),n.error("生成失败")}finally{j.value=!1}},xe=()=>{d.codesInput="",d.packageId="pkg_permanent",d.expireAt=null,U.value&&U.value.resetFields()},Ve=()=>{S.value&&S.value.resetFields()},Ce=l=>{g.size=l,g.page=1,h()},Ie=l=>{g.page=l,h()},h=async()=>{F.value=!0;try{const l=new URLSearchParams({page:g.page.toString(),size:g.size.toString(),keyword:N.value,status:L.value}),e=await fetch(`/api/member-codes?${l}`,{headers:{Authorization:`Bearer ${localStorage.getItem("admin_token")}`}});if(e.ok){const s=await e.json();s.success?(K.value=s.data.memberCodes||[],g.total=s.data.total||0):n.error(s.message||"加载会员码列表失败")}else n.error("网络请求失败")}catch(l){console.error("加载会员码列表失败:",l),n.error("加载会员码列表失败")}finally{F.value=!1}};return je(()=>{h()}),(l,e)=>{const s=C("Search"),x=He,T=Re,y=Ke,$=Ne,p=Le,we=C("EditPen"),Ae=C("Plus"),Se=C("Delete"),k=Ze,De=C("CopyDocument"),Y=Qe,Ee=We,Ue=Je,ze=Ye,_=et,Te=C("InfoFilled"),G=tt,J=Xe,H=Ge,$e=ot,Be=qe;return f(),b("div",it,[e[31]||(e[31]=m("div",{class:"page-header"},[m("h1",{class:"page-title"},"会员码管理"),m("p",{class:"page-subtitle"},"管理和生成会员激活码")],-1)),m("div",dt,[m("div",ut,[m("div",pt,[t(T,{modelValue:N.value,"onUpdate:modelValue":e[0]||(e[0]=a=>N.value=a),placeholder:"搜索会员码",style:{width:"300px"},clearable:"",onKeyup:Oe(Z,["enter"])},{prefix:o(()=>[t(x,null,{default:o(()=>[t(s)]),_:1})]),_:1},8,["modelValue"]),t($,{modelValue:L.value,"onUpdate:modelValue":e[1]||(e[1]=a=>L.value=a),placeholder:"状态筛选",style:{width:"120px"},clearable:""},{default:o(()=>[t(y,{label:"全部",value:""}),t(y,{label:"未使用",value:"unused"}),t(y,{label:"已使用",value:"used"}),t(y,{label:"已过期",value:"expired"})]),_:1},8,["modelValue"]),t(p,{type:"primary",onClick:Z},{default:o(()=>[t(x,null,{default:o(()=>[t(s)]),_:1}),e[20]||(e[20]=i(" 搜索 "))]),_:1,__:[20]})]),m("div",ct,[t(p,{type:"primary",onClick:ye},{default:o(()=>[t(x,null,{default:o(()=>[t(we)]),_:1}),e[21]||(e[21]=i(" 添加自定义会员码 "))]),_:1,__:[21]}),t(p,{type:"success",onClick:ke},{default:o(()=>[t(x,null,{default:o(()=>[t(Ae)]),_:1}),e[22]||(e[22]=i(" 批量生成会员码 "))]),_:1,__:[22]}),t(p,{type:"danger",disabled:V.value.length===0,onClick:ce},{default:o(()=>[t(x,null,{default:o(()=>[t(Se)]),_:1}),i(" 批量删除 ("+v(V.value.length)+") ",1)]),_:1},8,["disabled"])])])]),m("div",mt,[Me((f(),W(Ue,{data:K.value,onSelectionChange:pe,style:{width:"100%"}},{default:o(()=>[t(k,{type:"selection",width:"55"}),t(k,{prop:"code",label:"会员码",width:"150"},{default:o(({row:a})=>[m("div",gt,[m("code",ft,v(a.code),1),t(p,{type:"text",size:"small",onClick:E=>de(a.code)},{default:o(()=>[t(x,null,{default:o(()=>[t(De)]),_:1})]),_:2},1032,["onClick"])])]),_:1}),t(k,{label:"类型",width:"100"},{default:o(({row:a})=>[t(Y,{type:oe(a.packageId),size:"small"},{default:o(()=>[i(v(se(a.packageId)),1)]),_:2},1032,["type"])]),_:1}),t(k,{label:"状态",width:"100"},{default:o(({row:a})=>[t(Y,{type:ne(a),size:"small"},{default:o(()=>[i(v(re(a)),1)]),_:2},1032,["type"])]),_:1}),t(k,{prop:"usedBy",label:"使用者",width:"120"},{default:o(({row:a})=>[a.usedBy?(f(),b("span",_t,[t(p,{type:"text",onClick:E=>ue(a.usedBy)},{default:o(()=>[i(v(ie(a.usedBy)),1)]),_:2},1032,["onClick"])])):(f(),b("span",vt,"未使用"))]),_:1}),t(k,{prop:"usedAt",label:"使用时间",width:"160"},{default:o(({row:a})=>[a.usedAt?(f(),b("span",yt,v(M(a.usedAt)),1)):(f(),b("span",kt,"-"))]),_:1}),t(k,{prop:"expireAt",label:"过期时间",width:"160"},{default:o(({row:a})=>[a.expireAt?(f(),b("span",bt,v(M(a.expireAt)),1)):(f(),b("span",ht,"永久有效"))]),_:1}),t(k,{prop:"batchId",label:"批次ID",width:"180","show-overflow-tooltip":""}),t(k,{prop:"createdAt",label:"创建时间",width:"160"},{default:o(({row:a})=>[i(v(M(a.createdAt)),1)]),_:1}),t(k,{label:"操作",width:"160",fixed:"right"},{default:o(({row:a})=>[t(p,{type:"primary",size:"small",onClick:E=>me(a),disabled:a.isUsed},{default:o(()=>e[23]||(e[23]=[i(" 编辑 ")])),_:2,__:[23]},1032,["onClick","disabled"]),t(Ee,{title:"确定要删除这个会员码吗？",onConfirm:E=>_e(a)},{reference:o(()=>[t(p,{type:"danger",size:"small",disabled:a.isUsed},{default:o(()=>e[24]||(e[24]=[i(" 删除 ")])),_:2,__:[24]},1032,["disabled"])]),_:2},1032,["onConfirm"])]),_:1})]),_:1},8,["data"])),[[Be,F.value]]),m("div",xt,[t(ze,{"current-page":g.page,"onUpdate:currentPage":e[2]||(e[2]=a=>g.page=a),"page-size":g.size,"onUpdate:pageSize":e[3]||(e[3]=a=>g.size=a),"page-sizes":[10,20,50,100],total:g.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:Ce,onCurrentChange:Ie},null,8,["current-page","page-size","total"])])]),t(H,{modelValue:w.value,"onUpdate:modelValue":e[8]||(e[8]=a=>w.value=a),title:"添加自定义会员码",width:"600px",onClose:xe},{footer:o(()=>[t(p,{onClick:e[7]||(e[7]=a=>w.value=!1)},{default:o(()=>e[26]||(e[26]=[i("取消")])),_:1,__:[26]}),t(p,{type:"primary",onClick:be,loading:O.value,disabled:P(z).length===0},{default:o(()=>[i(" 添加 "+v(P(z).length)+" 个会员码 ",1)]),_:1},8,["loading","disabled"])]),default:o(()=>[t(J,{ref_key:"customFormRef",ref:U,model:d,rules:ae,"label-width":"100px"},{default:o(()=>[t(_,{label:"会员类型",prop:"packageId"},{default:o(()=>[t($,{modelValue:d.packageId,"onUpdate:modelValue":e[4]||(e[4]=a=>d.packageId=a),style:{width:"100%"}},{default:o(()=>[t(y,{label:"永久会员",value:"pkg_permanent"}),t(y,{label:"月会员",value:"pkg_monthly"})]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"会员码",prop:"codes"},{default:o(()=>[t(T,{modelValue:d.codesInput,"onUpdate:modelValue":e[5]||(e[5]=a=>d.codesInput=a),type:"textarea",rows:6,placeholder:`请输入自定义会员码，每行一个
例如：
MYVIP001
CUSTOM123
SPECIAL999`,style:{width:"100%"}},null,8,["modelValue"]),m("div",Vt,[t(x,null,{default:o(()=>[t(Te)]),_:1}),e[25]||(e[25]=m("span",null,"每行输入一个会员码，系统会自动检查重复",-1))])]),_:1}),t(_,{label:"过期时间",prop:"expireAt"},{default:o(()=>[t(G,{modelValue:d.expireAt,"onUpdate:modelValue":e[6]||(e[6]=a=>d.expireAt=a),type:"datetime",placeholder:"选择过期时间（可选）",style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(_,{label:"预览"},{default:o(()=>[m("div",Ct,[(f(!0),b(at,null,lt(P(q),(a,E)=>(f(),W(Y,{key:E,type:a.isValid?"success":"danger",style:{margin:"2px 4px 2px 0"}},{default:o(()=>[i(v(a.value)+" ",1),a.isValid?X("",!0):(f(),b("span",It,v(a.error),1))]),_:2},1032,["type"]))),128)),P(q).length===0?(f(),b("div",wt," 请在上方输入会员码 ")):X("",!0)])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(H,{modelValue:I.value,"onUpdate:modelValue":e[14]||(e[14]=a=>I.value=a),title:"批量生成会员码",width:"500px",onClose:Ve},{footer:o(()=>[t(p,{onClick:e[13]||(e[13]=a=>I.value=!1)},{default:o(()=>e[27]||(e[27]=[i("取消")])),_:1,__:[27]}),t(p,{type:"primary",onClick:he,loading:j.value},{default:o(()=>e[28]||(e[28]=[i(" 生成 ")])),_:1,__:[28]},8,["loading"])]),default:o(()=>[t(J,{ref_key:"generateFormRef",ref:S,model:u,rules:te,"label-width":"100px"},{default:o(()=>[t(_,{label:"会员类型",prop:"packageId"},{default:o(()=>[t($,{modelValue:u.packageId,"onUpdate:modelValue":e[9]||(e[9]=a=>u.packageId=a),style:{width:"100%"}},{default:o(()=>[t(y,{label:"永久会员",value:"pkg_permanent"}),t(y,{label:"月会员",value:"pkg_monthly"})]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"生成数量",prop:"count"},{default:o(()=>[t($e,{modelValue:u.count,"onUpdate:modelValue":e[10]||(e[10]=a=>u.count=a),min:1,max:100,style:{width:"100%"}},null,8,["modelValue"])]),_:1}),t(_,{label:"前缀",prop:"prefix"},{default:o(()=>[t(T,{modelValue:u.prefix,"onUpdate:modelValue":e[11]||(e[11]=a=>u.prefix=a),placeholder:"如: VIP"},null,8,["modelValue"])]),_:1}),t(_,{label:"过期时间",prop:"expireAt"},{default:o(()=>[t(G,{modelValue:u.expireAt,"onUpdate:modelValue":e[12]||(e[12]=a=>u.expireAt=a),type:"datetime",placeholder:"选择过期时间（可选）",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),t(H,{modelValue:A.value,"onUpdate:modelValue":e[19]||(e[19]=a=>A.value=a),title:"编辑会员码",width:"500px",onClose:fe},{footer:o(()=>[t(p,{onClick:e[18]||(e[18]=a=>A.value=!1)},{default:o(()=>e[29]||(e[29]=[i("取消")])),_:1,__:[29]}),t(p,{type:"primary",onClick:ge,loading:R.value},{default:o(()=>e[30]||(e[30]=[i(" 保存 ")])),_:1,__:[30]},8,["loading"])]),default:o(()=>[t(J,{ref_key:"editFormRef",ref:D,model:r,rules:le,"label-width":"100px"},{default:o(()=>[t(_,{label:"会员码",prop:"code"},{default:o(()=>[t(T,{modelValue:r.code,"onUpdate:modelValue":e[15]||(e[15]=a=>r.code=a),placeholder:"请输入会员码"},null,8,["modelValue"])]),_:1}),t(_,{label:"会员类型",prop:"packageId"},{default:o(()=>[t($,{modelValue:r.packageId,"onUpdate:modelValue":e[16]||(e[16]=a=>r.packageId=a),style:{width:"100%"}},{default:o(()=>[t(y,{label:"永久会员",value:"pkg_permanent"}),t(y,{label:"月会员",value:"pkg_monthly"})]),_:1},8,["modelValue"])]),_:1}),t(_,{label:"过期时间",prop:"expireAt"},{default:o(()=>[t(G,{modelValue:r.expireAt,"onUpdate:modelValue":e[17]||(e[17]=a=>r.expireAt=a),type:"datetime",placeholder:"选择过期时间（可选）",style:{width:"100%"}},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"])])}}}),Nt=Pe(At,[["__scopeId","data-v-73d3249e"]]);export{Nt as default};
