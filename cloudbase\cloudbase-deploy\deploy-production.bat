@echo off
setlocal enabledelayedexpansion

REM 腾讯云CloudBase生产环境部署脚本 (Windows版本)
REM 岱宗文脉 - 统一CloudBase架构部署
REM 使用方法: deploy-production.bat [ENV_ID]
REM 默认环境: novel-app-2gywkgnn15cbd6a8

if "%1"=="" (
    set ENV_ID=novel-app-2gywkgnn15cbd6a8
    echo [WARNING] 未提供环境ID，使用默认环境: !ENV_ID!
) else (
    set ENV_ID=%1
)

echo [STEP] 开始部署到腾讯云CloudBase环境: %ENV_ID%

REM 1. 检查必要工具
echo [STEP] 检查必要工具...

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] npm 未安装，请先安装 npm
    pause
    exit /b 1
)

where tcb >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] CloudBase CLI 未安装，正在安装...
    npm install -g @cloudbase/cli
)

echo [INFO] 工具检查完成

REM 2. 更新配置文件
echo [STEP] 更新配置文件...

REM 备份原配置
copy cloudbaserc.json cloudbaserc.json.backup >nul

REM 使用PowerShell替换环境ID
powershell -Command "(Get-Content cloudbaserc.json) -replace 'YOUR_ENV_ID_HERE', '%ENV_ID%' | Set-Content cloudbaserc.json"

echo [INFO] 配置文件更新完成

REM 3. 安装依赖
echo [STEP] 安装项目依赖...

cd novel-app-api
npm install --production
if %errorlevel% neq 0 (
    echo [ERROR] 依赖安装失败
    pause
    exit /b 1
)
cd ..

echo [INFO] 依赖安装完成

REM 4. 检查登录状态
echo [STEP] 检查CloudBase登录状态...

tcb auth list >nul 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] 未登录CloudBase，请先登录
    tcb login
)

echo [INFO] 登录状态检查完成

REM 5. 部署云函数
echo [STEP] 部署云函数...

tcb fn deploy novel-app-api --envId %ENV_ID%
if %errorlevel% neq 0 (
    echo [ERROR] 云函数部署失败
    pause
    exit /b 1
)

echo [INFO] 云函数部署成功

REM 6. 创建HTTP触发器
echo [STEP] 配置HTTP触发器...

tcb fn trigger create novel-app-api --trigger-name http-trigger --type http --path /api --envId %ENV_ID%

echo [INFO] HTTP触发器配置完成

REM 7. 显示部署信息
echo [STEP] 部署完成！

echo.
echo ==========================================
echo 部署信息:
echo 环境ID: %ENV_ID%
echo API地址: https://%ENV_ID%.service.tcloudbase.com/api
echo 管理后台: https://%ENV_ID%.service.tcloudbase.com/admin
echo ==========================================
echo.

echo [INFO] Flutter应用和后台管理系统已配置使用此API地址
echo [WARNING] 请记得修改生产环境的JWT密钥和管理员密码！
echo [INFO] 后台管理系统地址: https://admin.dznovel.top

echo.
echo [INFO] 🎉 CloudBase统一架构部署完成！
echo [INFO] 📚 查看架构文档: ../ARCHITECTURE_UPDATE.md
pause
