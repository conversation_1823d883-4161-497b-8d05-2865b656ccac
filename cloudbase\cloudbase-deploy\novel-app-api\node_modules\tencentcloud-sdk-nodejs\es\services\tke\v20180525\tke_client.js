import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tke.tencentcloudapi.com", "2018-05-25", clientConfig);
    }
    async DeletePrometheusClusterAgent(req, cb) {
        return this.request("DeletePrometheusClusterAgent", req, cb);
    }
    async CreateECMInstances(req, cb) {
        return this.request("CreateECMInstances", req, cb);
    }
    async DescribeClusterVirtualNode(req, cb) {
        return this.request("DescribeClusterVirtualNode", req, cb);
    }
    async CreateCluster(req, cb) {
        return this.request("CreateCluster", req, cb);
    }
    async DescribeSupportedRuntime(req, cb) {
        return this.request("DescribeSupportedRuntime", req, cb);
    }
    async InstallEdgeLogAgent(req, cb) {
        return this.request("InstallEdgeLogAgent", req, cb);
    }
    async DescribeOpenPolicyList(req, cb) {
        return this.request("DescribeOpenPolicyList", req, cb);
    }
    async CreateClusterVirtualNodePool(req, cb) {
        return this.request("CreateClusterVirtualNodePool", req, cb);
    }
    async ModifyClusterAsGroupOptionAttribute(req, cb) {
        return this.request("ModifyClusterAsGroupOptionAttribute", req, cb);
    }
    async EnableVpcCniNetworkType(req, cb) {
        return this.request("EnableVpcCniNetworkType", req, cb);
    }
    async DeleteCluster(req, cb) {
        return this.request("DeleteCluster", req, cb);
    }
    async UpdateEdgeClusterVersion(req, cb) {
        return this.request("UpdateEdgeClusterVersion", req, cb);
    }
    async UninstallEdgeLogAgent(req, cb) {
        return this.request("UninstallEdgeLogAgent", req, cb);
    }
    async DescribeExternalNodeSupportConfig(req, cb) {
        return this.request("DescribeExternalNodeSupportConfig", req, cb);
    }
    async DeleteEdgeClusterInstances(req, cb) {
        return this.request("DeleteEdgeClusterInstances", req, cb);
    }
    async DescribePostNodeResources(req, cb) {
        return this.request("DescribePostNodeResources", req, cb);
    }
    async SetNodePoolNodeProtection(req, cb) {
        return this.request("SetNodePoolNodeProtection", req, cb);
    }
    async DescribeEKSContainerInstanceEvent(req, cb) {
        return this.request("DescribeEKSContainerInstanceEvent", req, cb);
    }
    async DescribeEdgeAvailableExtraArgs(req, cb) {
        return this.request("DescribeEdgeAvailableExtraArgs", req, cb);
    }
    async DescribeClusterCommonNames(req, cb) {
        return this.request("DescribeClusterCommonNames", req, cb);
    }
    async DescribePrometheusInstanceInitStatus(req, cb) {
        return this.request("DescribePrometheusInstanceInitStatus", req, cb);
    }
    async EnableEventPersistence(req, cb) {
        return this.request("EnableEventPersistence", req, cb);
    }
    async DescribePrometheusAlertHistory(req, cb) {
        return this.request("DescribePrometheusAlertHistory", req, cb);
    }
    async DescribeIPAMD(req, cb) {
        return this.request("DescribeIPAMD", req, cb);
    }
    async CreateClusterRouteTable(req, cb) {
        return this.request("CreateClusterRouteTable", req, cb);
    }
    async GetUpgradeInstanceProgress(req, cb) {
        return this.request("GetUpgradeInstanceProgress", req, cb);
    }
    async CreatePrometheusTemplate(req, cb) {
        return this.request("CreatePrometheusTemplate", req, cb);
    }
    async DescribePodsBySpec(req, cb) {
        return this.request("DescribePodsBySpec", req, cb);
    }
    async DescribeReservedInstances(req, cb) {
        return this.request("DescribeReservedInstances", req, cb);
    }
    async DeleteClusterVirtualNode(req, cb) {
        return this.request("DeleteClusterVirtualNode", req, cb);
    }
    async DeleteBackupStorageLocation(req, cb) {
        return this.request("DeleteBackupStorageLocation", req, cb);
    }
    async DescribeTKEEdgeExternalKubeconfig(req, cb) {
        return this.request("DescribeTKEEdgeExternalKubeconfig", req, cb);
    }
    async DescribeAvailableTKEEdgeVersion(req, cb) {
        return this.request("DescribeAvailableTKEEdgeVersion", req, cb);
    }
    async DescribeTKEEdgeClusters(req, cb) {
        return this.request("DescribeTKEEdgeClusters", req, cb);
    }
    async UninstallLogAgent(req, cb) {
        return this.request("UninstallLogAgent", req, cb);
    }
    async DescribePodChargeInfo(req, cb) {
        return this.request("DescribePodChargeInfo", req, cb);
    }
    async DescribeVersions(req, cb) {
        return this.request("DescribeVersions", req, cb);
    }
    async ModifyPrometheusAgentExternalLabels(req, cb) {
        return this.request("ModifyPrometheusAgentExternalLabels", req, cb);
    }
    async DescribeEdgeClusterExtraArgs(req, cb) {
        return this.request("DescribeEdgeClusterExtraArgs", req, cb);
    }
    async DeleteEKSCluster(req, cb) {
        return this.request("DeleteEKSCluster", req, cb);
    }
    async DescribeEdgeLogSwitches(req, cb) {
        return this.request("DescribeEdgeLogSwitches", req, cb);
    }
    async ModifyReservedInstanceScope(req, cb) {
        return this.request("ModifyReservedInstanceScope", req, cb);
    }
    async DescribeOSImages(req, cb) {
        return this.request("DescribeOSImages", req, cb);
    }
    async ModifyClusterTags(req, cb) {
        return this.request("ModifyClusterTags", req, cb);
    }
    async CreateCLSLogConfig(req, cb) {
        return this.request("CreateCLSLogConfig", req, cb);
    }
    async EnableClusterAudit(req, cb) {
        return this.request("EnableClusterAudit", req, cb);
    }
    async CreateBackupStorageLocation(req, cb) {
        return this.request("CreateBackupStorageLocation", req, cb);
    }
    async ModifyClusterVirtualNodePool(req, cb) {
        return this.request("ModifyClusterVirtualNodePool", req, cb);
    }
    async DescribeAvailableClusterVersion(req, cb) {
        return this.request("DescribeAvailableClusterVersion", req, cb);
    }
    async ModifyPrometheusConfig(req, cb) {
        return this.request("ModifyPrometheusConfig", req, cb);
    }
    async DescribeVpcCniPodLimits(req, cb) {
        return this.request("DescribeVpcCniPodLimits", req, cb);
    }
    async UpdateClusterVersion(req, cb) {
        return this.request("UpdateClusterVersion", req, cb);
    }
    async DescribeClusterPendingReleases(req, cb) {
        return this.request("DescribeClusterPendingReleases", req, cb);
    }
    async CreatePrometheusRecordRuleYaml(req, cb) {
        return this.request("CreatePrometheusRecordRuleYaml", req, cb);
    }
    async DeleteImageCaches(req, cb) {
        return this.request("DeleteImageCaches", req, cb);
    }
    async DeleteClusterRouteTable(req, cb) {
        return this.request("DeleteClusterRouteTable", req, cb);
    }
    async DescribeTKEEdgeScript(req, cb) {
        return this.request("DescribeTKEEdgeScript", req, cb);
    }
    async ModifyPrometheusAlertPolicy(req, cb) {
        return this.request("ModifyPrometheusAlertPolicy", req, cb);
    }
    async UpdateAddon(req, cb) {
        return this.request("UpdateAddon", req, cb);
    }
    async DescribeClusterAuthenticationOptions(req, cb) {
        return this.request("DescribeClusterAuthenticationOptions", req, cb);
    }
    async ModifyClusterAsGroupAttribute(req, cb) {
        return this.request("ModifyClusterAsGroupAttribute", req, cb);
    }
    async InstallAddon(req, cb) {
        return this.request("InstallAddon", req, cb);
    }
    async DeleteECMInstances(req, cb) {
        return this.request("DeleteECMInstances", req, cb);
    }
    async DescribeClusterNodePoolDetail(req, cb) {
        return this.request("DescribeClusterNodePoolDetail", req, cb);
    }
    async DeletePrometheusTemplateSync(req, cb) {
        return this.request("DeletePrometheusTemplateSync", req, cb);
    }
    async CreateClusterInstances(req, cb) {
        return this.request("CreateClusterInstances", req, cb);
    }
    async ModifyClusterAttribute(req, cb) {
        return this.request("ModifyClusterAttribute", req, cb);
    }
    async DeleteClusterAsGroups(req, cb) {
        return this.request("DeleteClusterAsGroups", req, cb);
    }
    async DeleteClusterRoute(req, cb) {
        return this.request("DeleteClusterRoute", req, cb);
    }
    async UninstallClusterRelease(req, cb) {
        return this.request("UninstallClusterRelease", req, cb);
    }
    async AddVpcCniSubnets(req, cb) {
        return this.request("AddVpcCniSubnets", req, cb);
    }
    async DescribeImages(req, cb) {
        return this.request("DescribeImages", req, cb);
    }
    async DescribeExistedInstances(req, cb) {
        return this.request("DescribeExistedInstances", req, cb);
    }
    async ListClusterInspectionResults(req, cb) {
        return this.request("ListClusterInspectionResults", req, cb);
    }
    async DescribeClusterNodePools(req, cb) {
        return this.request("DescribeClusterNodePools", req, cb);
    }
    async DescribeTKEEdgeClusterCredential(req, cb) {
        return this.request("DescribeTKEEdgeClusterCredential", req, cb);
    }
    async UpgradeClusterInstances(req, cb) {
        return this.request("UpgradeClusterInstances", req, cb);
    }
    async DescribeClusterRouteTables(req, cb) {
        return this.request("DescribeClusterRouteTables", req, cb);
    }
    async DeleteEdgeCVMInstances(req, cb) {
        return this.request("DeleteEdgeCVMInstances", req, cb);
    }
    async AddNodeToNodePool(req, cb) {
        return this.request("AddNodeToNodePool", req, cb);
    }
    async DescribeClusters(req, cb) {
        return this.request("DescribeClusters", req, cb);
    }
    async DescribeClusterEndpointStatus(req, cb) {
        return this.request("DescribeClusterEndpointStatus", req, cb);
    }
    async CreateReservedInstances(req, cb) {
        return this.request("CreateReservedInstances", req, cb);
    }
    async UpdateImageCache(req, cb) {
        return this.request("UpdateImageCache", req, cb);
    }
    async DescribeBatchModifyTagsStatus(req, cb) {
        return this.request("DescribeBatchModifyTagsStatus", req, cb);
    }
    async DescribeRegions(req, cb) {
        return this.request("DescribeRegions", req, cb);
    }
    async DescribeReservedInstanceUtilizationRate(req, cb) {
        return this.request("DescribeReservedInstanceUtilizationRate", req, cb);
    }
    async AddExistedInstances(req, cb) {
        return this.request("AddExistedInstances", req, cb);
    }
    async ModifyPrometheusAlertRule(req, cb) {
        return this.request("ModifyPrometheusAlertRule", req, cb);
    }
    async ModifyClusterEndpointSP(req, cb) {
        return this.request("ModifyClusterEndpointSP", req, cb);
    }
    async DisableClusterDeletionProtection(req, cb) {
        return this.request("DisableClusterDeletionProtection", req, cb);
    }
    async DescribePrometheusTargets(req, cb) {
        return this.request("DescribePrometheusTargets", req, cb);
    }
    async DeleteEKSContainerInstances(req, cb) {
        return this.request("DeleteEKSContainerInstances", req, cb);
    }
    async DescribeClusterInstances(req, cb) {
        return this.request("DescribeClusterInstances", req, cb);
    }
    async DescribeEdgeCVMInstances(req, cb) {
        return this.request("DescribeEdgeCVMInstances", req, cb);
    }
    async DescribeClusterControllers(req, cb) {
        return this.request("DescribeClusterControllers", req, cb);
    }
    async DescribeAddonValues(req, cb) {
        return this.request("DescribeAddonValues", req, cb);
    }
    async AddClusterCIDR(req, cb) {
        return this.request("AddClusterCIDR", req, cb);
    }
    async CreateImageCache(req, cb) {
        return this.request("CreateImageCache", req, cb);
    }
    async DeleteClusterVirtualNodePool(req, cb) {
        return this.request("DeleteClusterVirtualNodePool", req, cb);
    }
    async DisableEncryptionProtection(req, cb) {
        return this.request("DisableEncryptionProtection", req, cb);
    }
    async UpdateEKSContainerInstance(req, cb) {
        return this.request("UpdateEKSContainerInstance", req, cb);
    }
    async DescribePrometheusTemp(req, cb) {
        return this.request("DescribePrometheusTemp", req, cb);
    }
    async DeleteAddon(req, cb) {
        return this.request("DeleteAddon", req, cb);
    }
    async CreateEksLogConfig(req, cb) {
        return this.request("CreateEksLogConfig", req, cb);
    }
    async DrainClusterVirtualNode(req, cb) {
        return this.request("DrainClusterVirtualNode", req, cb);
    }
    async RestartEKSContainerInstances(req, cb) {
        return this.request("RestartEKSContainerInstances", req, cb);
    }
    async DeletePrometheusConfig(req, cb) {
        return this.request("DeletePrometheusConfig", req, cb);
    }
    async ModifyNodePoolDesiredCapacityAboutAsg(req, cb) {
        return this.request("ModifyNodePoolDesiredCapacityAboutAsg", req, cb);
    }
    async ModifyClusterAuthenticationOptions(req, cb) {
        return this.request("ModifyClusterAuthenticationOptions", req, cb);
    }
    async CreateEKSCluster(req, cb) {
        return this.request("CreateEKSCluster", req, cb);
    }
    async DescribeClusterExtraArgs(req, cb) {
        return this.request("DescribeClusterExtraArgs", req, cb);
    }
    async UpdateClusterKubeconfig(req, cb) {
        return this.request("UpdateClusterKubeconfig", req, cb);
    }
    async DescribeClusterStatus(req, cb) {
        return this.request("DescribeClusterStatus", req, cb);
    }
    async ModifyNodePoolInstanceTypes(req, cb) {
        return this.request("ModifyNodePoolInstanceTypes", req, cb);
    }
    async ModifyMasterComponent(req, cb) {
        return this.request("ModifyMasterComponent", req, cb);
    }
    async ModifyClusterImage(req, cb) {
        return this.request("ModifyClusterImage", req, cb);
    }
    async DescribeLogConfigs(req, cb) {
        return this.request("DescribeLogConfigs", req, cb);
    }
    async CreatePrometheusTemp(req, cb) {
        return this.request("CreatePrometheusTemp", req, cb);
    }
    async ListClusterInspectionResultsItems(req, cb) {
        return this.request("ListClusterInspectionResultsItems", req, cb);
    }
    async EnableEncryptionProtection(req, cb) {
        return this.request("EnableEncryptionProtection", req, cb);
    }
    async DescribePrometheusTemplateSync(req, cb) {
        return this.request("DescribePrometheusTemplateSync", req, cb);
    }
    async DisableVpcCniNetworkType(req, cb) {
        return this.request("DisableVpcCniNetworkType", req, cb);
    }
    async DescribePrometheusInstance(req, cb) {
        return this.request("DescribePrometheusInstance", req, cb);
    }
    async CreatePrometheusGlobalNotification(req, cb) {
        return this.request("CreatePrometheusGlobalNotification", req, cb);
    }
    async DescribeClusterEndpointVipStatus(req, cb) {
        return this.request("DescribeClusterEndpointVipStatus", req, cb);
    }
    async DescribeEKSContainerInstances(req, cb) {
        return this.request("DescribeEKSContainerInstances", req, cb);
    }
    async DescribeLogSwitches(req, cb) {
        return this.request("DescribeLogSwitches", req, cb);
    }
    async AcquireClusterAdminRole(req, cb) {
        return this.request("AcquireClusterAdminRole", req, cb);
    }
    async CreateClusterRoute(req, cb) {
        return this.request("CreateClusterRoute", req, cb);
    }
    async DescribeClusterReleaseHistory(req, cb) {
        return this.request("DescribeClusterReleaseHistory", req, cb);
    }
    async RollbackClusterRelease(req, cb) {
        return this.request("RollbackClusterRelease", req, cb);
    }
    async ModifyClusterNodePool(req, cb) {
        return this.request("ModifyClusterNodePool", req, cb);
    }
    async DescribeEncryptionStatus(req, cb) {
        return this.request("DescribeEncryptionStatus", req, cb);
    }
    async CreateEKSContainerInstances(req, cb) {
        return this.request("CreateEKSContainerInstances", req, cb);
    }
    async DescribePrometheusRecordRules(req, cb) {
        return this.request("DescribePrometheusRecordRules", req, cb);
    }
    async DeletePrometheusAlertRule(req, cb) {
        return this.request("DeletePrometheusAlertRule", req, cb);
    }
    async DescribePrometheusGlobalNotification(req, cb) {
        return this.request("DescribePrometheusGlobalNotification", req, cb);
    }
    async ScaleInClusterMaster(req, cb) {
        return this.request("ScaleInClusterMaster", req, cb);
    }
    async DescribeClusterLevelChangeRecords(req, cb) {
        return this.request("DescribeClusterLevelChangeRecords", req, cb);
    }
    async CreateClusterEndpoint(req, cb) {
        return this.request("CreateClusterEndpoint", req, cb);
    }
    async DescribePodDeductionRate(req, cb) {
        return this.request("DescribePodDeductionRate", req, cb);
    }
    async ModifyPrometheusGlobalNotification(req, cb) {
        return this.request("ModifyPrometheusGlobalNotification", req, cb);
    }
    async DeleteReservedInstances(req, cb) {
        return this.request("DeleteReservedInstances", req, cb);
    }
    async DescribePrometheusAgentInstances(req, cb) {
        return this.request("DescribePrometheusAgentInstances", req, cb);
    }
    async ScaleOutClusterMaster(req, cb) {
        return this.request("ScaleOutClusterMaster", req, cb);
    }
    async CreatePrometheusAlertRule(req, cb) {
        return this.request("CreatePrometheusAlertRule", req, cb);
    }
    async DeletePrometheusRecordRuleYaml(req, cb) {
        return this.request("DeletePrometheusRecordRuleYaml", req, cb);
    }
    async DescribeBackupStorageLocations(req, cb) {
        return this.request("DescribeBackupStorageLocations", req, cb);
    }
    async ModifyPrometheusTemp(req, cb) {
        return this.request("ModifyPrometheusTemp", req, cb);
    }
    async ModifyPrometheusRecordRuleYaml(req, cb) {
        return this.request("ModifyPrometheusRecordRuleYaml", req, cb);
    }
    async DescribeClusterLevelAttribute(req, cb) {
        return this.request("DescribeClusterLevelAttribute", req, cb);
    }
    async DescribeClusterSecurity(req, cb) {
        return this.request("DescribeClusterSecurity", req, cb);
    }
    async RenewReservedInstances(req, cb) {
        return this.request("RenewReservedInstances", req, cb);
    }
    async DescribePrometheusClusterAgents(req, cb) {
        return this.request("DescribePrometheusClusterAgents", req, cb);
    }
    async DeleteClusterNodePool(req, cb) {
        return this.request("DeleteClusterNodePool", req, cb);
    }
    async CreateTKEEdgeCluster(req, cb) {
        return this.request("CreateTKEEdgeCluster", req, cb);
    }
    async DescribeResourceUsage(req, cb) {
        return this.request("DescribeResourceUsage", req, cb);
    }
    async DescribePrometheusTempSync(req, cb) {
        return this.request("DescribePrometheusTempSync", req, cb);
    }
    async DeleteClusterEndpointVip(req, cb) {
        return this.request("DeleteClusterEndpointVip", req, cb);
    }
    async DescribeECMInstances(req, cb) {
        return this.request("DescribeECMInstances", req, cb);
    }
    async DeleteTKEEdgeCluster(req, cb) {
        return this.request("DeleteTKEEdgeCluster", req, cb);
    }
    async GetMostSuitableImageCache(req, cb) {
        return this.request("GetMostSuitableImageCache", req, cb);
    }
    async DescribeClusterEndpoints(req, cb) {
        return this.request("DescribeClusterEndpoints", req, cb);
    }
    async CreatePrometheusClusterAgent(req, cb) {
        return this.request("CreatePrometheusClusterAgent", req, cb);
    }
    async DeletePrometheusAlertPolicy(req, cb) {
        return this.request("DeletePrometheusAlertPolicy", req, cb);
    }
    async DescribeEKSClusterCredential(req, cb) {
        return this.request("DescribeEKSClusterCredential", req, cb);
    }
    async DescribeClusterReleaseDetails(req, cb) {
        return this.request("DescribeClusterReleaseDetails", req, cb);
    }
    async DisableClusterAudit(req, cb) {
        return this.request("DisableClusterAudit", req, cb);
    }
    async CreateEdgeCVMInstances(req, cb) {
        return this.request("CreateEdgeCVMInstances", req, cb);
    }
    async DescribeClusterRoutes(req, cb) {
        return this.request("DescribeClusterRoutes", req, cb);
    }
    async DescribePrometheusGlobalConfig(req, cb) {
        return this.request("DescribePrometheusGlobalConfig", req, cb);
    }
    async SyncPrometheusTemp(req, cb) {
        return this.request("SyncPrometheusTemp", req, cb);
    }
    async ModifyOpenPolicyList(req, cb) {
        return this.request("ModifyOpenPolicyList", req, cb);
    }
    async DescribeEksContainerInstanceLog(req, cb) {
        return this.request("DescribeEksContainerInstanceLog", req, cb);
    }
    async CheckInstancesUpgradeAble(req, cb) {
        return this.request("CheckInstancesUpgradeAble", req, cb);
    }
    async CreatePrometheusDashboard(req, cb) {
        return this.request("CreatePrometheusDashboard", req, cb);
    }
    async DescribePrometheusOverviews(req, cb) {
        return this.request("DescribePrometheusOverviews", req, cb);
    }
    async RemoveNodeFromNodePool(req, cb) {
        return this.request("RemoveNodeFromNodePool", req, cb);
    }
    async DescribeTKEEdgeClusterStatus(req, cb) {
        return this.request("DescribeTKEEdgeClusterStatus", req, cb);
    }
    async CreatePrometheusAlertPolicy(req, cb) {
        return this.request("CreatePrometheusAlertPolicy", req, cb);
    }
    async DeletePrometheusTemplate(req, cb) {
        return this.request("DeletePrometheusTemplate", req, cb);
    }
    async DescribePrometheusAlertPolicy(req, cb) {
        return this.request("DescribePrometheusAlertPolicy", req, cb);
    }
    async DescribePrometheusAgents(req, cb) {
        return this.request("DescribePrometheusAgents", req, cb);
    }
    async DisableEventPersistence(req, cb) {
        return this.request("DisableEventPersistence", req, cb);
    }
    async DeleteClusterEndpoint(req, cb) {
        return this.request("DeleteClusterEndpoint", req, cb);
    }
    async CreateClusterRelease(req, cb) {
        return this.request("CreateClusterRelease", req, cb);
    }
    async SyncPrometheusTemplate(req, cb) {
        return this.request("SyncPrometheusTemplate", req, cb);
    }
    async DescribeClusterVirtualNodePools(req, cb) {
        return this.request("DescribeClusterVirtualNodePools", req, cb);
    }
    async GetClusterLevelPrice(req, cb) {
        return this.request("GetClusterLevelPrice", req, cb);
    }
    async DescribeRIUtilizationDetail(req, cb) {
        return this.request("DescribeRIUtilizationDetail", req, cb);
    }
    async DescribeClusterKubeconfig(req, cb) {
        return this.request("DescribeClusterKubeconfig", req, cb);
    }
    async DescribeEdgeClusterUpgradeInfo(req, cb) {
        return this.request("DescribeEdgeClusterUpgradeInfo", req, cb);
    }
    async DeleteLogConfigs(req, cb) {
        return this.request("DeleteLogConfigs", req, cb);
    }
    async DescribePrometheusConfig(req, cb) {
        return this.request("DescribePrometheusConfig", req, cb);
    }
    async CancelClusterRelease(req, cb) {
        return this.request("CancelClusterRelease", req, cb);
    }
    async DescribeEdgeClusterInstances(req, cb) {
        return this.request("DescribeEdgeClusterInstances", req, cb);
    }
    async DescribeClusterAsGroupOption(req, cb) {
        return this.request("DescribeClusterAsGroupOption", req, cb);
    }
    async DescribeEKSClusters(req, cb) {
        return this.request("DescribeEKSClusters", req, cb);
    }
    async DescribeClusterAsGroups(req, cb) {
        return this.request("DescribeClusterAsGroups", req, cb);
    }
    async CreateClusterNodePool(req, cb) {
        return this.request("CreateClusterNodePool", req, cb);
    }
    async CreateEdgeLogConfig(req, cb) {
        return this.request("CreateEdgeLogConfig", req, cb);
    }
    async DescribeClusterInspectionResultsOverview(req, cb) {
        return this.request("DescribeClusterInspectionResultsOverview", req, cb);
    }
    async DeletePrometheusTemp(req, cb) {
        return this.request("DeletePrometheusTemp", req, cb);
    }
    async DescribeEnableVpcCniProgress(req, cb) {
        return this.request("DescribeEnableVpcCniProgress", req, cb);
    }
    async DescribePrometheusAlertRule(req, cb) {
        return this.request("DescribePrometheusAlertRule", req, cb);
    }
    async DescribeMasterComponent(req, cb) {
        return this.request("DescribeMasterComponent", req, cb);
    }
    async EnableClusterDeletionProtection(req, cb) {
        return this.request("EnableClusterDeletionProtection", req, cb);
    }
    async ForwardTKEEdgeApplicationRequestV3(req, cb) {
        return this.request("ForwardTKEEdgeApplicationRequestV3", req, cb);
    }
    async DescribeImageCaches(req, cb) {
        return this.request("DescribeImageCaches", req, cb);
    }
    async DescribeClusterReleases(req, cb) {
        return this.request("DescribeClusterReleases", req, cb);
    }
    async UpgradeClusterRelease(req, cb) {
        return this.request("UpgradeClusterRelease", req, cb);
    }
    async DescribeEKSContainerInstanceRegions(req, cb) {
        return this.request("DescribeEKSContainerInstanceRegions", req, cb);
    }
    async UpdateTKEEdgeCluster(req, cb) {
        return this.request("UpdateTKEEdgeCluster", req, cb);
    }
    async ModifyPrometheusTemplate(req, cb) {
        return this.request("ModifyPrometheusTemplate", req, cb);
    }
    async DescribePrometheusTemplates(req, cb) {
        return this.request("DescribePrometheusTemplates", req, cb);
    }
    async DescribeAddon(req, cb) {
        return this.request("DescribeAddon", req, cb);
    }
    async DescribePrometheusInstancesOverview(req, cb) {
        return this.request("DescribePrometheusInstancesOverview", req, cb);
    }
    async DeleteClusterInstances(req, cb) {
        return this.request("DeleteClusterInstances", req, cb);
    }
    async DeletePrometheusTempSync(req, cb) {
        return this.request("DeletePrometheusTempSync", req, cb);
    }
    async CreatePrometheusConfig(req, cb) {
        return this.request("CreatePrometheusConfig", req, cb);
    }
    async RunPrometheusInstance(req, cb) {
        return this.request("RunPrometheusInstance", req, cb);
    }
    async UpdateEKSCluster(req, cb) {
        return this.request("UpdateEKSCluster", req, cb);
    }
    async GetTkeAppChartList(req, cb) {
        return this.request("GetTkeAppChartList", req, cb);
    }
    async CreateClusterVirtualNode(req, cb) {
        return this.request("CreateClusterVirtualNode", req, cb);
    }
    async InstallLogAgent(req, cb) {
        return this.request("InstallLogAgent", req, cb);
    }
    async CheckEdgeClusterCIDR(req, cb) {
        return this.request("CheckEdgeClusterCIDR", req, cb);
    }
    async CreateClusterEndpointVip(req, cb) {
        return this.request("CreateClusterEndpointVip", req, cb);
    }
    async ModifyClusterRuntimeConfig(req, cb) {
        return this.request("ModifyClusterRuntimeConfig", req, cb);
    }
    async DescribeRouteTableConflicts(req, cb) {
        return this.request("DescribeRouteTableConflicts", req, cb);
    }
}
