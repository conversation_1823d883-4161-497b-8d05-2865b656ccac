import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { ExportBashEventsRequest, DescribeSearchTemplatesResponse, ModifyJavaMemShellPluginSwitchResponse, ExportReverseShellEventsResponse, DescribeBaselineDetectOverviewResponse, DescribeLogHistogramRequest, ModifyWarningSettingResponse, DescribeAssetWebServiceInfoListResponse, ModifyVulDefenceEventStatusRequest, DescribeAssetAppProcessListResponse, ModifyWebHookPolicyRequest, DescribeBashPoliciesRequest, DescribeLoginWhiteHostListRequest, ExportRansomDefenseBackupListResponse, DeleteWebHookPolicyResponse, DescribeAssetAppCountResponse, DescribeAttackTopResponse, ModifyLicenseOrderRequest, DescribeBaselineTopResponse, DeleteBaselineStrategyResponse, ModifyWebHookRuleRequest, DescribeBashEventsInfoRequest, ExportAssetDatabaseListRequest, ClearLocalStorageResponse, DescribeUndoVulCountsResponse, DescribeBaselineScanScheduleResponse, DeleteBashEventsResponse, DescribeLoginWhiteCombinedListResponse, DescribeAssetProcessInfoListRequest, ModifyBaselineRuleRequest, SearchLogResponse, ExportWebPageEventListRequest, DescribeAssetWebFrameListRequest, DescribeMonthInspectionReportResponse, SyncMachinesResponse, DescribeBaselineHostTopResponse, DeleteBashRulesResponse, DescribeDirectConnectInstallCommandResponse, DescribeSecurityEventsCntResponse, RetryVulFixResponse, ExportVulListResponse, ExportRansomDefenseMachineListRequest, DescribeHistoryServiceResponse, DescribeRiskProcessEventsRequest, DescribeAssetTypesResponse, DescribeScreenEmergentMsgRequest, ModifyWebPageProtectSettingResponse, DeleteBaselineRuleIgnoreResponse, ModifyAutoOpenProVersionConfigResponse, DescribeVulInfoCvssResponse, DescribeBaselineStrategyDetailRequest, DescribeRaspMaxCpuRequest, DescribeAssetUserInfoResponse, DescribeRansomDefenseStrategyListRequest, DescribeMachineClearHistoryRequest, DescribeServersAndRiskAndFirstInfoResponse, ModifyMalwareWhiteListRequest, DescribeRaspRuleVulsRequest, DescribeAssetJarListRequest, DescribeJavaMemShellPluginInfoRequest, DescribeMachineFileTamperRulesRequest, ExportVulDefenceEventResponse, SetLocalStorageItemRequest, ExportRansomDefenseMachineListResponse, DescribeFileTamperRuleCountRequest, DescribeBaselineBasicInfoRequest, DescribeDefenceEventDetailRequest, DescribeMachineGeneralResponse, DescribeMachineClearHistoryResponse, DescribeProVersionInfoRequest, RemoveMachineResponse, DescribeVulCountByDatesRequest, EditReverseShellRulesRequest, DescribeAssetWebServiceCountRequest, CreateLicenseOrderResponse, DescribeWebPageGeneralizeResponse, DescribeAssetWebLocationPathListRequest, DeleteLoginWhiteListRequest, DeleteRiskDnsEventRequest, RansomDefenseRollbackResponse, ExportAssetSystemPackageListRequest, DescribePrivilegeEventsResponse, DescribeRaspRuleVulsResponse, DescribeNetAttackWhiteListRequest, DescribeOverviewStatisticsResponse, ModifyMalwareTimingScanSettingsRequest, DescribeAssetTotalCountRequest, DescribeWebHookRulesResponse, DescribeRaspRulesRequest, DescribeMonthInspectionReportRequest, DescribeDirectConnectInstallCommandRequest, DescribeBaselineDetectListResponse, DescribeNetAttackSettingResponse, DescribeAESKeyResponse, DescribeBashRulesResponse, DescribeAvailableExpertServiceDetailResponse, DescribeLogStorageConfigRequest, ExportAssetWebLocationListResponse, ChangeRuleEventsIgnoreStatusRequest, ModifyWebHookReceiverResponse, DescribeRansomDefenseMachineListRequest, ExportAssetCoreModuleListRequest, ExportAssetMachineDetailResponse, ModifyNetAttackWhiteListResponse, DescribeMachineRegionsRequest, ExportWebPageEventListResponse, ModifyMachineAutoClearConfigRequest, ModifyEventAttackStatusRequest, DeleteRaspRulesResponse, DescribeVulLevelCountResponse, DescribeVersionStatisticsResponse, DescribeAvailableExpertServiceDetailRequest, DescribeMachineFileTamperRulesResponse, DescribeServerRelatedDirInfoResponse, DeleteLogExportRequest, DescribeUsualLoginPlacesResponse, DescribeAttackEventsRequest, DescribeRiskDnsListRequest, CreateWhiteListOrderRequest, DescribeRansomDefenseStrategyDetailResponse, EditBashRulesResponse, DescribeLogIndexResponse, DescribeRansomDefenseBackupListRequest, ScanVulResponse, DeleteMalwareWhiteListResponse, DescribeOverviewStatisticsRequest, DeleteScanTaskRequest, DescribeRiskBatchStatusResponse, DescribeAlarmIncidentNodesRequest, DescribeAssetWebAppPluginListRequest, DescribeRansomDefenseMachineListResponse, DescribeTagMachinesRequest, DescribeScreenAttackHotspotRequest, ScanVulAgainRequest, DeleteSearchTemplateRequest, ExportJavaMemShellPluginsResponse, DescribeAssetTotalCountResponse, ExportNonlocalLoginPlacesRequest, DescribeTagsResponse, DescribeRansomDefenseTrendRequest, DescribeLicenseListResponse, DescribeJavaMemShellListResponse, DescribeBaselineRuleListRequest, DescribeVulOverviewRequest, ModifyWebPageProtectDirResponse, DescribeMachinesSimpleResponse, DescribeNetAttackSettingRequest, DescribeScreenRiskAssetsTopRequest, DeletePrivilegeEventsRequest, DeleteWebHookRuleResponse, ModifyNetAttackWhiteListRequest, DescribeVulDefenceSettingRequest, DescribeTrialReportRequest, DescribeFileTamperEventsRequest, DescribeBaselineItemInfoResponse, DescribeProtectDirListRequest, DescribeScreenMachineRegionsResponse, ExportVulDefencePluginEventResponse, ExportAssetMachineDetailRequest, DescribeBanModeRequest, ModifyRaspMaxCpuRequest, StopAssetScanRequest, DescribeSecurityEventStatRequest, DescribeESAggregationsResponse, ExportVulInfoResponse, ExportAssetRecentMachineInfoRequest, ExportReverseShellEventsRequest, DeleteLoginWhiteListResponse, DescribeVertexDetailResponse, DeleteRiskDnsEventResponse, ModifyLoginWhiteRecordRequest, ModifyRiskDnsPolicyResponse, DescribeScanTaskDetailsRequest, DescribeBaselineHostIgnoreListResponse, DeleteMalwaresResponse, ExportRiskDnsEventListResponse, DescribeAssetInitServiceListRequest, ScanVulAgainResponse, DescribeWebHookReceiverUsageRequest, DescribeAssetUserKeyListRequest, SeparateMalwaresResponse, DescribeTagsRequest, ExportAssetJarListResponse, ExportVulEffectHostListRequest, DescribeNetAttackWhiteListResponse, CheckBashPolicyParamsRequest, DescribeStrategyExistRequest, DescribeSecurityDynamicsRequest, CheckBashRuleParamsRequest, DescribeRiskDnsEventInfoRequest, DescribeIgnoreBaselineRuleRequest, DescribeAssetTypeTopRequest, CheckFileTamperRuleResponse, ExportNonlocalLoginPlacesResponse, DeleteMaliciousRequestWhiteListRequest, DescribeAssetJarListResponse, ExportAssetProcessInfoListResponse, ExportMaliciousRequestsRequest, DeleteNetAttackWhiteListResponse, DescribeWebHookPolicyResponse, SetLocalStorageItemResponse, ExportBashEventsNewRequest, DescribeWebPageProtectStatRequest, ModifyFileTamperRuleResponse, DescribeBaselineRuleCategoryListResponse, ExportVulDetectionReportRequest, ModifyWarningHostConfigRequest, ExportMalwaresResponse, DescribeScanVulSettingRequest, DescribeServersAndRiskAndFirstInfoRequest, DescribeMalwareRiskWarningRequest, StartBaselineDetectRequest, DescribeVulStoreListRequest, EditPrivilegeRulesRequest, DescribeAssetDiskListRequest, DescribeClientExceptionRequest, DescribeAssetPlanTaskListResponse, DescribeBaselineRuleRequest, ExportRansomDefenseEventsListResponse, ExportAssetPortInfoListRequest, DescribeHistoryAccountsRequest, DescribeAssetMachineDetailRequest, ExportTasksRequest, CancelIgnoreVulResponse, FixBaselineDetectRequest, DescribeSecurityBroadcastInfoRequest, IgnoreImpactedHostsResponse, DescribeCanFixVulMachineRequest, DescribeRansomDefenseStrategyMachinesResponse, DescribeBaselineRuleIgnoreListRequest, DescribeAssetMachineDetailResponse, DescribeRansomDefenseStrategyListResponse, DescribeAttackTrendsRequest, DescribeBaselineDownloadListRequest, DeleteNetAttackWhiteListRequest, DescribeStrategyExistResponse, DeleteReverseShellEventsRequest, DeleteBashPoliciesRequest, ExportAssetAppListResponse, DescribeLogTypeRequest, DescribeVulTopResponse, DescribeBaselineRuleCategoryListRequest, DescribeAssetWebServiceProcessListResponse, ChangeStrategyEnableStatusResponse, ExportRansomDefenseStrategyListResponse, IgnoreImpactedHostsRequest, SearchLogRequest, DescribeBaselineWeakPasswordListRequest, DescribeRansomDefenseStrategyMachinesRequest, ExportBaselineFixListRequest, ModifyOrderAttributeResponse, DescribeAssetPortCountRequest, ModifyWebHookPolicyResponse, DescribeFileTamperEventsResponse, DescribeVulLabelsResponse, DescribeMachinesRequest, DescribeRansomDefenseBackupListResponse, DescribeVulEffectHostListRequest, ModifyNetAttackSettingResponse, DeletePrivilegeRulesRequest, ModifyBaselinePolicyStateRequest, DescribeLicenseBindScheduleResponse, DescribeMalwareInfoRequest, DescribeVersionStatisticsRequest, DescribeLogTypeResponse, ModifyRiskDnsPolicyStatusResponse, ExportPrivilegeEventsResponse, ExportAssetRecentMachineInfoResponse, DescribeMachineRegionListRequest, ExportAssetWebFrameListRequest, ModifyBaselinePolicyResponse, DescribeReverseShellEventsRequest, DescribeCanNotSeparateMachineResponse, DescribeEventByTableResponse, ModifyLogStorageConfigRequest, DescribeAttackVulTypeListRequest, DescribeLogStorageStatisticRequest, DeleteBaselinePolicyRequest, SetLocalStorageExpireRequest, ExportAssetInitServiceListRequest, DeleteWebHookReceiverRequest, DescribeAssetRecentMachineInfoRequest, CheckBashRuleParamsResponse, DeleteLicenseRecordRequest, ExportBaselineListRequest, DescribeAssetWebLocationPathListResponse, DeleteProtectDirRequest, ExportIgnoreBaselineRuleResponse, DescribeBaselineRuleListResponse, DescribeBaselineHostIgnoreListRequest, ExportFileTamperEventsResponse, DescribeBanStatusResponse, DescribeVulListRequest, DescribeLicenseBindListResponse, DescribeWebHookReceiverUsageResponse, DescribeJavaMemShellInfoRequest, DescribePrivilegeRulesRequest, ExportAssetAppListRequest, ModifyFileTamperEventsRequest, ModifyOrderAttributeRequest, DescribeAssetSystemPackageListResponse, ExportAssetWebFrameListResponse, CheckBashPolicyParamsResponse, DescribeClientExceptionResponse, DescribeAssetMachineListResponse, DescribeWebPageGeneralizeRequest, DescribeBaselineDetailRequest, GetLocalStorageItemResponse, DescribeScreenDefenseTrendsRequest, DescribeBruteAttackListResponse, DescribeAssetInfoRequest, DescribeJavaMemShellPluginListRequest, DescribeWebHookPolicyRequest, EditReverseShellRulesResponse, DescribeAssetUserKeyListResponse, DescribeVulLabelsRequest, DescribeAssetAppListRequest, UntrustMalwaresResponse, DescribeExpertServiceListResponse, DescribeAccountStatisticsResponse, DescribeProVersionStatusResponse, DescribeBashEventsInfoNewRequest, DeleteBaselineWeakPasswordRequest, ScanAssetRequest, DescribeBaselineRuleResponse, ModifyMachineRemarkRequest, ModifyVulDefenceEventStatusResponse, DescribeBaselineItemRiskTopRequest, DeleteMaliciousRequestsResponse, DescribeWebHookReceiverRequest, CreateIncidentBacktrackingRequest, DescribeVulDefencePluginStatusRequest, DescribeBashEventsInfoNewResponse, GetLocalStorageItemRequest, ExportBruteAttacksRequest, DeleteMachineResponse, DeleteRaspRulesRequest, ExportBaselineRuleDetectListRequest, SetLocalStorageExpireResponse, DescribeBaselineDownloadListResponse, RecoverMalwaresRequest, DescribeAssetTypeTopResponse, DescribeVulDefencePluginExceptionCountRequest, DescribeLogIndexRequest, DescribeAssetCoreModuleInfoResponse, DescribeAssetEnvListRequest, StopNoticeBanTipsRequest, DescribeScanMalwareScheduleRequest, DescribeBashEventsResponse, ModifyVulDefenceSettingResponse, DescribeEventByTableRequest, DescribeTrialReportResponse, DescribeBashEventsRequest, DeleteMachineClearHistoryRequest, DescribeLogHistogramResponse, DescribeUsersConfigRequest, DeleteMachineRequest, DescribeAssetWebLocationListResponse, DescribeAssetDiskListResponse, ExportAssetPlanTaskListRequest, DescribeAssetHostTotalCountResponse, DescribeMachineSnapshotRequest, StopAssetScanResponse, DescribeProtectNetListResponse, TrustMalwaresRequest, ChangeRuleEventsIgnoreStatusResponse, SeparateMalwaresRequest, DeletePrivilegeRulesResponse, CreateMalwareWhiteListResponse, CreateWhiteListOrderResponse, CreateProtectServerRequest, DescribeMachineListRequest, CreateEmergencyVulScanResponse, DescribeTagMachinesResponse, DescribeHotVulTopResponse, DescribeRansomDefenseMachineStrategyInfoRequest, DescribeAssetCoreModuleInfoRequest, DescribeProductStatusResponse, ModifyJavaMemShellPluginSwitchRequest, DescribeIgnoreHostAndItemConfigResponse, RemoveLocalStorageItemResponse, DescribeFileTamperRuleInfoResponse, DescribeSafeInfoRequest, DescribeVdbAndPocInfoResponse, DescribeAttackEventsResponse, DescribeRansomDefenseMachineStrategyInfoResponse, DescribeReverseShellEventInfoResponse, ExportJavaMemShellPluginsRequest, DescribeBanWhiteListResponse, ModifyBaselineRuleIgnoreResponse, CreateVulFixResponse, DescribeOpenPortStatisticsResponse, ExportRiskDnsEventListRequest, DescribeRaspMaxCpuResponse, DescribeMachineGeneralRequest, ExportRiskProcessEventsResponse, DescribeAssetDatabaseCountResponse, DescribeMalWareListRequest, DescribeProVersionInfoResponse, DeleteTagsResponse, DescribeSecurityEventsCntRequest, ModifyRiskEventsStatusRequest, ExportBaselineEffectHostListRequest, ScanBaselineRequest, RansomDefenseRollbackRequest, DescribeVulCountByDatesResponse, DescribeMachineOsListResponse, DescribeVulDefenceEventRequest, ModifyBaselineRuleResponse, ExportMaliciousRequestsResponse, RetryVulFixRequest, DescribeRiskDnsEventListRequest, DescribeLogKafkaDeliverInfoResponse, DeleteBashRulesRequest, CreateProtectServerResponse, ExportRansomDefenseStrategyMachinesRequest, DescribeMalwareWhiteListAffectListResponse, ModifyMaliciousRequestWhiteListRequest, DescribeRansomDefenseEventsListRequest, ModifyRaspMaxCpuResponse, DescribeBaselineItemInfoRequest, ExportAssetPlanTaskListResponse, ExportRansomDefenseBackupListRequest, ModifyRaspRulesRequest, DescribeBaselineHostRiskTopRequest, DescribeABTestConfigRequest, ExportAssetSystemPackageListResponse, ModifyRansomDefenseEventsStatusRequest, DescribeUsersConfigResponse, DescribeAttackStatisticsResponse, DescribeRansomDefenseStateRequest, ExportAssetJarListRequest, CreateVulFixRequest, CheckFirstScanBaselineResponse, ModifyBanStatusRequest, DescribeBaselineHostTopRequest, DescribeVulDefenceListResponse, RemoveLocalStorageItemRequest, CreateBuyBindTaskRequest, EditPrivilegeRulesResponse, DescribeVulHostTopResponse, TrustMalwaresResponse, DescribeHistoryServiceRequest, CreateMaliciousRequestWhiteListRequest, DescribeWarningListRequest, SyncAssetScanRequest, ExportFileTamperEventsRequest, DescribeServerRelatedDirInfoRequest, ModifyBanWhiteListRequest, DescribeESAggregationsRequest, DescribeReverseShellRulesResponse, DeleteWebHookRuleRequest, DescribeBruteAttackListRequest, DescribeVdbAndPocInfoRequest, ModifyBashPolicyStatusRequest, DescribeVulListResponse, KeysLocalStorageRequest, DescribeUndoVulCountsRequest, RemoveMachineRequest, DescribeWebPageServiceInfoRequest, ModifyLicenseBindsResponse, CheckFileTamperRuleRequest, DescribeBaselineStrategyListResponse, DescribeBaselineFixListResponse, CreateRansomDefenseStrategyRequest, DescribeMalwareFileRequest, DescribeLicenseListRequest, CreateMalwareWhiteListRequest, ModifyLicenseUnBindsRequest, DescribeBaselineRuleDetectListRequest, DescribeVulOverviewResponse, ExportRansomDefenseStrategyListRequest, DeleteMaliciousRequestsRequest, DescribeBanWhiteListRequest, DescribeWebPageServiceInfoResponse, DescribeJavaMemShellPluginListResponse, ModifyLogKafkaStateResponse, DescribeMalwareRiskOverviewResponse, DeleteMaliciousRequestWhiteListResponse, DescribeJavaMemShellListRequest, SwitchBashRulesRequest, DescribeAgentInstallationTokenResponse, DescribeAssetMachineTagTopResponse, FixBaselineDetectResponse, CreateScanMalwareSettingResponse, DescribeRansomDefenseTrendResponse, ExportProtectDirListResponse, ModifyWebPageProtectDirRequest, ModifyReverseShellRulesAggregationResponse, ModifyJavaMemShellsStatusRequest, DescribeFileTamperRuleInfoRequest, ExportAttackEventsRequest, ModifyBaselineRuleIgnoreRequest, ModifyAutoOpenProVersionConfigRequest, DescribeLogDeliveryKafkaOptionsRequest, DescribeAssetWebLocationInfoResponse, CreateIncidentBacktrackingResponse, StartBaselineDetectResponse, ExportVulListRequest, ModifyBashPolicyResponse, ExportLicenseDetailRequest, CreateLicenseOrderRequest, CreateNetAttackWhiteListResponse, CreateLogExportResponse, ExportVulDefenceListResponse, ModifyLogKafkaDeliverTypeResponse, DescribeProtectNetListRequest, DescribeBaselineScanScheduleRequest, RetryCreateSnapshotRequest, ExportVulDefenceEventRequest, DescribeVulDefenceSettingResponse, DescribeEmergencyVulListResponse, DestroyOrderResponse, DescribeAssetUserListResponse, DeleteMalwaresRequest, RecoverMalwaresResponse, DeleteBaselineRuleRequest, DescribeIgnoreHostAndItemConfigRequest, DescribeReverseShellRulesRequest, DescribeScanVulSettingResponse, DescribeSecurityBroadcastsResponse, DescribeSearchLogsResponse, DescribeAssetDatabaseInfoResponse, ExportBaselineItemListResponse, SyncMachinesRequest, SetBashEventsStatusRequest, DescribeBaselineEffectHostListResponse, DescribeWebHookRuleResponse, DescribeBaselineStrategyListRequest, DescribeBaselineRuleIgnoreListResponse, ExportJavaMemShellsRequest, DescribeVersionCompareChartResponse, DescribeMachineDefenseCntResponse, DescribeVertexDetailRequest, ExportAssetWebServiceInfoListRequest, SetBashEventsStatusResponse, DescribeAssetUserInfoRequest, ExportBaselineItemListRequest, UpdateBaselineStrategyResponse, DescribeAgentInstallCommandRequest, DescribeMachineRegionListResponse, ExportPrivilegeEventsRequest, ModifyWebPageProtectSwitchRequest, DescribeScreenGeneralStatRequest, DescribeExpertServiceListRequest, ModifyUsersConfigResponse, DescribeAttackEventInfoRequest, CreateLogExportRequest, DescribeLicenseResponse, DescribeMalwareFileResponse, DescribeWebHookRuleRequest, DescribeSecurityBroadcastInfoResponse, DescribeLogDeliveryKafkaOptionsResponse, DescribeScreenRiskAssetsTopResponse, ScanBaselineResponse, CheckLogKafkaConnectionStateRequest, ModifyNetAttackSettingRequest, DescribeRiskDnsListResponse, ExportRiskDnsPolicyListRequest, DescribeAssetWebAppListResponse, ExportAssetProcessInfoListRequest, DescribeAssetWebAppCountResponse, DescribeScanTaskStatusRequest, DescribeVulStoreListResponse, DescribeLoginWhiteHostListResponse, DeleteBaselineRuleResponse, ModifyMaliciousRequestWhiteListResponse, DeleteMachineTagRequest, DescribeScreenMachineRegionsRequest, SwitchBashRulesResponse, ModifyWebHookPolicyStatusRequest, DescribeExportMachinesResponse, DescribeScanTaskStatusResponse, DescribeBaselineItemRiskTopResponse, DescribeAssetLoadInfoResponse, DescribeMachineRiskCntResponse, ModifyWebPageProtectSwitchResponse, DescribeAssetWebFrameCountResponse, DescribeWebPageEventListResponse, DescribeAssetWebLocationCountResponse, DescribeBashEventsNewResponse, DeleteWebHookReceiverResponse, ModifyBashPolicyStatusResponse, DeleteProtectDirResponse, DescribeLogExportsResponse, DescribeVulTrendResponse, TestWebHookRuleRequest, DescribeOpenPortStatisticsRequest, DescribeMalwareTimingScanSettingRequest, DescribeFileTamperRuleCountResponse, ExportAssetUserListRequest, ModifyBaselineWeakPasswordRequest, CreateBanWhiteListResponse, DescribeLogStorageStatisticResponse, ModifyBaselinePolicyStateResponse, DescribeCanFixVulMachineResponse, CreateSearchTemplateResponse, DescribeWebPageEventListRequest, DescribeBaselineFixListRequest, DescribeAssetProcessCountResponse, DescribeEmergencyResponseListRequest, DescribeScanStateResponse, EditTagsRequest, DeleteReverseShellRulesRequest, UntrustMalwaresRequest, DescribeScreenEventsCntRequest, DescribeBaselineEffectHostListRequest, ExportSecurityTrendsRequest, DescribeDefenceEventDetailResponse, ExportBaselineHostDetectListRequest, DescribeAssetDatabaseInfoRequest, ModifyFileTamperEventsResponse, ExportAssetDatabaseListResponse, DescribeAssetUserCountRequest, CreateRansomDefenseStrategyResponse, ExportVulDefencePluginEventRequest, DescribeRansomDefenseRollBackTaskListRequest, DescribeWarningHostConfigRequest, ModifyBanWhiteListResponse, ExportAssetWebServiceInfoListResponse, ExportAssetMachineListResponse, ExportSecurityTrendsResponse, DescribeReverseShellEventInfoRequest, ModifyMalwareTimingScanSettingsResponse, DescribeRiskDnsInfoResponse, ExportRiskProcessEventsRequest, DescribeLogKafkaDeliverInfoRequest, DescribeBaselineWeakPasswordListResponse, DeleteNonlocalLoginPlacesResponse, ModifyFileTamperRuleStatusResponse, ModifyLogKafkaAccessResponse, DeleteMalwareScanTaskResponse, DescribeBaselineDetailResponse, DescribeWebHookReceiverResponse, ExportAttackEventsResponse, ExportIgnoreRuleEffectHostListResponse, DescribeBaselinePolicyListResponse, DescribeBanRegionsResponse, DescribeLogStorageRecordResponse, DeletePrivilegeEventsResponse, DescribeMachineInfoResponse, DescribeImportMachineInfoRequest, DescribeRansomDefenseRollBackTaskListResponse, ExportAssetEnvListRequest, ModifyMachineRemarkResponse, DescribeVulEffectHostListResponse, DescribeSearchLogsRequest, ScanVulSettingResponse, DescribeAssetJarInfoResponse, DescribeVulInfoCvssRequest, DescribeFileTamperRulesResponse, DescribeAssetProcessCountRequest, ModifyBanStatusResponse, DescribeLoginWhiteListResponse, DescribeUsualLoginPlacesRequest, ModifyWebHookReceiverRequest, ExportAssetInitServiceListResponse, ModifyFileTamperRuleRequest, DescribeScanMalwareScheduleResponse, ExportRansomDefenseEventsListRequest, DescribeScreenProtectionCntRequest, DescribeBaselineItemListRequest, DescribeScreenHostInvasionRequest, ModifyWebPageProtectSettingRequest, ExportVulInfoRequest, DescribeAssetUserCountResponse, DescribeScreenBroadcastsRequest, CreateSearchLogResponse, DescribeRiskDnsPolicyListResponse, DescribeSecurityTrendsResponse, StopNoticeBanTipsResponse, DescribeEmergencyVulListRequest, DescribeSecurityDynamicsResponse, DeleteReverseShellEventsResponse, ExportAssetEnvListResponse, DescribeAttackTrendsResponse, DescribeVulTrendRequest, DescribeAttackStatisticsRequest, ExportFileTamperRulesRequest, AddLoginWhiteListsResponse, ModifyLogKafkaStateRequest, DeleteAllJavaMemShellsResponse, DescribeMalwareWhiteListRequest, ModifyUsersConfigRequest, DescribeGeneralStatRequest, DescribeHotVulTopRequest, ScanAssetResponse, DeleteBanWhiteListRequest, DescribeAssetWebFrameCountRequest, DescribeIgnoreRuleEffectHostListRequest, ExportAssetMachineListRequest, DescribeScreenProtectionStatResponse, ExportBaselineItemDetectListRequest, CreateNetAttackWhiteListRequest, DescribeMalwareInfoResponse, DeleteRiskDnsPolicyResponse, DescribeLogStorageRecordRequest, ExportBaselineHostDetectListResponse, CreateBanWhiteListRequest, DescribeMachineRiskCntRequest, ExportRansomDefenseStrategyMachinesResponse, DescribePrivilegeEventsRequest, DescribeAssetEnvListResponse, DescribeVulHostCountScanTimeRequest, DescribeSecurityBroadcastsRequest, DescribeVulDefenceEventResponse, ExportBaselineEffectHostListResponse, ModifyLogKafkaAccessRequest, DescribeVulEffectModulesResponse, DeleteWebPageEventLogResponse, DescribeLicenseBindScheduleRequest, DeleteRiskDnsPolicyRequest, DescribeBruteAttackRulesRequest, DescribeProcessStatisticsResponse, DeleteBaselineRuleIgnoreRequest, DeleteNonlocalLoginPlacesRequest, DescribeAssetWebAppPluginListResponse, DescribeAssetWebAppCountRequest, DescribeAssetTypesRequest, DescribeLoginWhiteCombinedListRequest, AddLoginWhiteListsRequest, DeleteSearchTemplateResponse, DeleteBashPoliciesResponse, DescribeEmergencyResponseListResponse, DescribeFileTamperEventRuleInfoResponse, DeleteBaselinePolicyResponse, ModifyVulDefenceSettingRequest, ModifyLoginWhiteRecordResponse, DescribeMalwareWhiteListAffectListRequest, DeleteWebHookPolicyRequest, ModifyRansomDefenseStrategyStatusRequest, DescribeAssetAppListResponse, DescribeWarningHostConfigResponse, DescribeVulDefencePluginStatusResponse, ExportAssetWebLocationListRequest, DescribeRansomDefenseEventsListResponse, DescribeVulEffectModulesRequest, ModifyRiskDnsPolicyStatusRequest, DescribeScreenProtectionCntResponse, ModifyRansomDefenseEventsStatusResponse, DescribeBaselineDefaultStrategyListRequest, CreateScanMalwareSettingRequest, DescribeMalwareTimingScanSettingResponse, DescribeHostLoginListRequest, ModifyRansomDefenseStrategyStatusResponse, ModifyLoginWhiteInfoResponse, ScanTaskAgainResponse, DescribeAssetWebServiceProcessListRequest, DescribeScreenMachinesResponse, ExportBashEventsResponse, ScanVulSettingRequest, ExportVulDetectionExcelRequest, CreateEmergencyVulScanRequest, StopBaselineDetectRequest, DescribeProtectDirRelatedServerResponse, ModifyWebHookRuleStatusResponse, DescribeBaselineItemIgnoreListRequest, DescribeAssetWebServiceCountResponse, ExportScanTaskDetailsResponse, DescribeVulTopRequest, DescribeScreenProtectionStatRequest, ModifyLogKafkaDeliverTypeRequest, DescribeBaselineItemDetectListRequest, ExportBaselineItemDetectListResponse, DescribeBanStatusRequest, DescribeAssetRecentMachineInfoResponse, DescribeBaselineBasicInfoResponse, DescribeMalWareListResponse, ModifyLicenseUnBindsResponse, DescribeWebHookRulesRequest, ModifyMalwareWhiteListResponse, DescribeAssetPortInfoListResponse, DescribeMalwareRiskOverviewRequest, DescribeProtectDirListResponse, DescribeMaliciousRequestWhiteListResponse, DescribeBaselinePolicyListRequest, DeleteBruteAttacksResponse, ExportTasksResponse, ExportAssetWebAppListResponse, DescribeIgnoreBaselineRuleResponse, DescribeVulEmergentMsgRequest, DescribeMachineOsListRequest, DescribeMalwareRiskWarningResponse, DescribeBashRulesRequest, ExportLicenseDetailResponse, DescribeBanModeResponse, DescribeScreenAttackHotspotResponse, DescribeImportMachineInfoResponse, DescribeRiskDnsEventInfoResponse, DescribeSecurityEventStatResponse, DescribeAssetDatabaseListRequest, ModifyWebHookRuleStatusRequest, KeysLocalStorageResponse, DescribeAssetMachineListRequest, ExportBaselineRuleDetectListResponse, ExportBaselineWeakPasswordListRequest, DescribeAssetMachineTagTopRequest, DescribeAgentInstallationTokenRequest, DescribeVulDefencePluginDetailResponse, DescribeAssetDatabaseListResponse, ExportMalwaresRequest, DescribeProcessStatisticsRequest, SyncAssetScanResponse, CheckLogKafkaConnectionStateResponse, ExportBashPoliciesRequest, DescribeScreenBroadcastsResponse, CreateBaselineStrategyRequest, DescribeSecurityTrendsRequest, DescribeAttackVulTypeListResponse, DescribePrivilegeRulesResponse, DescribeReverseShellEventsResponse, DescribeAssetAppCountRequest, DescribeMaliciousRequestWhiteListRequest, DescribeBashEventsNewRequest, DescribeWebPageProtectStatResponse, DescribeAssetPortInfoListRequest, DescribeVulDefencePluginDetailRequest, ModifyLoginWhiteInfoRequest, DescribeExportMachinesRequest, DescribeAssetInfoResponse, DescribeAssetPortCountResponse, DescribeScreenMachinesRequest, DeleteMalwareScanTaskRequest, ExportBashPoliciesResponse, ModifyEventAttackStatusResponse, DescribeIgnoreRuleEffectHostListResponse, ExportProtectDirListRequest, CreateBaselineStrategyResponse, ExportAssetCoreModuleListResponse, ModifyMachineAutoClearConfigResponse, ModifyLogStorageConfigResponse, DescribeHistoryAccountsResponse, DescribeLogStorageConfigResponse, DescribeVulEmergentMsgResponse, DescribeLoginWhiteListRequest, DescribeAssetDatabaseCountRequest, ModifyWarningSettingRequest, ExportVulDetectionReportResponse, DescribeScanScheduleRequest, CheckFirstScanBaselineRequest, DescribeLicenseGeneralResponse, ExportAssetPortInfoListResponse, DescribeFastAnalysisResponse, DescribeAESKeyRequest, ModifyWebHookRuleResponse, DescribeAssetLoadInfoRequest, ExportBaselineWeakPasswordListResponse, RetryCreateSnapshotResponse, CreateBuyBindTaskResponse, ExportScanTaskDetailsRequest, ExportBaselineListResponse, DescribeBruteAttackRulesResponse, ModifyBanModeResponse, DescribeVulDefencePluginExceptionCountResponse, DeleteLicenseRecordResponse, DescribeVulDefenceOverviewRequest, DescribeCanNotSeparateMachineRequest, DescribeRiskDnsEventListResponse, DeleteBruteAttacksRequest, DescribeAssetCoreModuleListResponse, DescribeRansomDefenseStateResponse, DescribeFastAnalysisRequest, DescribeRiskBatchStatusRequest, DeleteMachineClearHistoryResponse, DescribeLicenseWhiteConfigResponse, ExportVulDetectionExcelResponse, ModifyWarningHostConfigResponse, DescribeVulCveIdInfoResponse, DescribeAlarmVertexIdResponse, CreateSearchTemplateRequest, DeleteTagsRequest, DescribeScanStateRequest, SyncBaselineDetectSummaryResponse, ExportFileTamperRulesResponse, DescribeAgentInstallCommandResponse, DescribeSafeInfoResponse, DescribeMachineListResponse, DescribeHostInfoResponse, ModifyWebHookPolicyStatusResponse, ExportRiskDnsPolicyListResponse, ModifyBruteAttackRulesRequest, DescribeScreenDefenseTrendsResponse, SyncBaselineDetectSummaryRequest, DescribeExpertServiceOrderListResponse, DeleteReverseShellRulesResponse, DescribeAssetPlanTaskListRequest, DescribePrivilegeEventInfoResponse, DescribeMachineLicenseDetailRequest, DescribeVersionCompareChartRequest, DescribeProtectDirRelatedServerRequest, TestWebHookRuleResponse, DescribeBaselineItemListResponse, DescribeMachinesSimpleRequest, StopBaselineDetectResponse, DescribeBanRegionsRequest, DescribeBashPoliciesResponse, DescribeBaselineAnalysisDataResponse, DeleteLicenseRecordAllResponse, ModifyBanModeRequest, DescribeBaselineDefaultStrategyListResponse, DeleteScanTaskResponse, ModifyFileTamperRuleStatusRequest, EditBashRulesRequest, ExportIgnoreBaselineRuleRequest, DescribeMachineRegionsResponse, ExportVulDefenceListRequest, DestroyOrderRequest, DescribeRiskDnsInfoRequest, DescribeBaselineRuleDetectListResponse, UpdateBaselineStrategyRequest, DescribeMachineLicenseDetailResponse, DescribeHostInfoRequest, DeleteBashEventsRequest, DescribeLicenseWhiteConfigRequest, DescribeFileTamperEventRuleInfoRequest, ExportIgnoreRuleEffectHostListRequest, ModifyLicenseOrderResponse, DescribeAttackEventInfoResponse, DescribeMachineSnapshotResponse, DescribeVulHostCountScanTimeResponse, DescribeAssetWebLocationCountRequest, DescribeAssetCoreModuleListRequest, DeleteLogExportResponse, ExportVulEffectHostListResponse, DeleteBanWhiteListResponse, DescribeBaselineStrategyDetailResponse, DescribeBaselineListRequest, DescribePublicProxyInstallCommandRequest, ExportJavaMemShellsResponse, DescribeProductStatusRequest, DescribeRiskProcessEventsResponse, DescribeScreenHostInvasionResponse, ExportBaselineFixListResponse, ExportBashEventsNewResponse, DescribeMachineDefenseCntRequest, ModifyBaselineWeakPasswordResponse, DescribeBaselineTopRequest, DescribeRiskDnsPolicyListRequest, DescribeAssetAppProcessListRequest, ScanTaskAgainRequest, ExportAssetWebAppListRequest, DescribeRecommendedProtectCpuResponse, DescribeLicenseBindListRequest, DeleteLicenseRecordAllRequest, DescribeAccountStatisticsRequest, ExportBruteAttacksResponse, DescribeBaselineHostDetectListResponse, DescribeAssetProcessInfoListResponse, ChangeStrategyEnableStatusRequest, ModifyLicenseBindsRequest, DescribeScanScheduleResponse, DescribeVulDefenceOverviewResponse, DescribeAssetWebAppListRequest, DescribeScreenEventsCntResponse, DescribePrivilegeEventInfoRequest, ModifyBashPolicyRequest, DescribeScreenEmergentMsgResponse, DeleteAllJavaMemShellsRequest, DescribeBaselineDetectOverviewRequest, DescribeLicenseRequest, DescribeLicenseGeneralRequest, DescribeAssetJarInfoRequest, DescribeMachinesResponse, DescribeMalwareWhiteListResponse, DescribeAssetWebLocationListRequest, DescribeAssetHostTotalCountRequest, CreateMaliciousRequestWhiteListResponse, DescribeVulLevelCountRequest, ScanVulRequest, DeleteBaselineWeakPasswordResponse, DeleteWebPageEventLogRequest, DescribeProVersionStatusRequest, DescribeBaselineListResponse, DescribeBaselineHostRiskTopResponse, DescribeBashEventsInfoResponse, DescribeJavaMemShellPluginInfoResponse, DescribeAlarmIncidentNodesResponse, DescribeExpertServiceOrderListRequest, DescribeFileTamperRulesRequest, DescribeJavaMemShellInfoResponse, ModifyRaspRulesResponse, DescribeRecommendedProtectCpuRequest, DescribeABTestConfigResponse, DescribeMachineInfoRequest, DescribeVulFixStatusResponse, DeleteMalwareWhiteListRequest, DescribeGeneralStatResponse, DescribeLogExportsRequest, DeleteMachineTagResponse, UpdateMachineTagsResponse, DescribeVulFixStatusRequest, UpdateMachineTagsRequest, DescribeBaselineItemDetectListResponse, DescribeScanTaskDetailsResponse, ModifyReverseShellRulesAggregationRequest, DescribeHostLoginListResponse, DescribePublicProxyInstallCommandResponse, DescribeAttackTopRequest, DescribeBaselineAnalysisDataRequest, ModifyRiskEventsStatusResponse, DescribeRaspRulesResponse, DescribeBaselineDetectListRequest, DescribeVulCveIdInfoRequest, ModifyJavaMemShellsStatusResponse, DescribeAssetWebLocationInfoRequest, DescribeAlarmVertexIdRequest, DescribeScreenGeneralStatResponse, DescribeAssetInitServiceListResponse, ModifyBaselinePolicyRequest, DescribeBaselineHostDetectListRequest, DescribeVulDefenceListRequest, EditTagsResponse, DescribeAssetWebFrameListResponse, DescribeRansomDefenseStrategyDetailRequest, ModifyRiskDnsPolicyRequest, DescribeWarningListResponse, ClearLocalStorageRequest, DescribeAssetUserListRequest, DeleteBaselineStrategyRequest, DescribeAssetSystemPackageListRequest, ModifyBruteAttackRulesResponse, DescribeVulHostTopRequest, DescribeBaselineItemIgnoreListResponse, DescribeAssetWebServiceInfoListRequest, CreateSearchLogRequest, DescribeSearchTemplatesRequest, CancelIgnoreVulRequest, ExportAssetUserListResponse } from "./cwp_models";
/**
 * cwp client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 获取资产管理端口列表
     */
    DescribeAssetPortInfoList(req: DescribeAssetPortInfoListRequest, cb?: (error: string, rep: DescribeAssetPortInfoListResponse) => void): Promise<DescribeAssetPortInfoListResponse>;
    /**
     * 获取配置的aeskey和aesiv
     */
    DescribeAESKey(req?: DescribeAESKeyRequest, cb?: (error: string, rep: DescribeAESKeyResponse) => void): Promise<DescribeAESKeyResponse>;
    /**
     * 批量修改防勒索策略状态
     */
    ModifyRansomDefenseStrategyStatus(req: ModifyRansomDefenseStrategyStatusRequest, cb?: (error: string, rep: ModifyRansomDefenseStrategyStatusResponse) => void): Promise<ModifyRansomDefenseStrategyStatusResponse>;
    /**
     * 查询防勒索策略列表
     */
    DescribeRansomDefenseStrategyList(req: DescribeRansomDefenseStrategyListRequest, cb?: (error: string, rep: DescribeRansomDefenseStrategyListResponse) => void): Promise<DescribeRansomDefenseStrategyListResponse>;
    /**
     * 导出Jar包列表
     */
    ExportAssetJarList(req: ExportAssetJarListRequest, cb?: (error: string, rep: ExportAssetJarListResponse) => void): Promise<ExportAssetJarListResponse>;
    /**
     * 获取爆破阻断模式
     */
    DescribeBanMode(req?: DescribeBanModeRequest, cb?: (error: string, rep: DescribeBanModeResponse) => void): Promise<DescribeBanModeResponse>;
    /**
     * 停止资产扫描任务
     */
    StopAssetScan(req: StopAssetScanRequest, cb?: (error: string, rep: StopAssetScanResponse) => void): Promise<StopAssetScanResponse>;
    /**
     * 获取本地存储键值列表
     */
    KeysLocalStorage(req?: KeysLocalStorageRequest, cb?: (error: string, rep: KeysLocalStorageResponse) => void): Promise<KeysLocalStorageResponse>;
    /**
     * 导出java内存马插件信息
     */
    ExportJavaMemShellPlugins(req: ExportJavaMemShellPluginsRequest, cb?: (error: string, rep: ExportJavaMemShellPluginsResponse) => void): Promise<ExportJavaMemShellPluginsResponse>;
    /**
     * 删除检索模板
     */
    DeleteSearchTemplate(req: DeleteSearchTemplateRequest, cb?: (error: string, rep: DeleteSearchTemplateResponse) => void): Promise<DeleteSearchTemplateResponse>;
    /**
     * 校验高危命令用户规则新增和编辑时的参数。
     */
    CheckBashRuleParams(req: CheckBashRuleParamsRequest, cb?: (error: string, rep: CheckBashRuleParamsResponse) => void): Promise<CheckBashRuleParamsResponse>;
    /**
     * DescribeScanTaskDetails 查询扫描任务详情 , 可以查询扫描进度信息/异常;
     */
    DescribeScanTaskDetails(req: DescribeScanTaskDetailsRequest, cb?: (error: string, rep: DescribeScanTaskDetailsResponse) => void): Promise<DescribeScanTaskDetailsResponse>;
    /**
     * 查询服务区关联目录详情
     */
    DescribeServerRelatedDirInfo(req: DescribeServerRelatedDirInfoRequest, cb?: (error: string, rep: DescribeServerRelatedDirInfoResponse) => void): Promise<DescribeServerRelatedDirInfoResponse>;
    /**
     * 导出漏洞防御列表
     */
    ExportVulDefenceList(req: ExportVulDefenceListRequest, cb?: (error: string, rep: ExportVulDefenceListResponse) => void): Promise<ExportVulDefenceListResponse>;
    /**
     * 根据taskid查询检测进度
     */
    DescribeScanSchedule(req: DescribeScanScheduleRequest, cb?: (error: string, rep: DescribeScanScheduleResponse) => void): Promise<DescribeScanScheduleResponse>;
    /**
     * 根据策略信息创建基线策略
     */
    CreateBaselineStrategy(req: CreateBaselineStrategyRequest, cb?: (error: string, rep: CreateBaselineStrategyResponse) => void): Promise<CreateBaselineStrategyResponse>;
    /**
     * 主机信息与标签信息查询
     */
    DescribeHostInfo(req: DescribeHostInfoRequest, cb?: (error: string, rep: DescribeHostInfoResponse) => void): Promise<DescribeHostInfoResponse>;
    /**
     * 查询批量导入机器信息
     */
    DescribeImportMachineInfo(req: DescribeImportMachineInfoRequest, cb?: (error: string, rep: DescribeImportMachineInfoResponse) => void): Promise<DescribeImportMachineInfoResponse>;
    /**
     * 获取基线规则列表
     */
    DescribeBaselineRuleList(req: DescribeBaselineRuleListRequest, cb?: (error: string, rep: DescribeBaselineRuleListResponse) => void): Promise<DescribeBaselineRuleListResponse>;
    /**
     * 大屏可视化风险资产top5（今日），统计今日风险资产
     */
    DescribeScreenRiskAssetsTop(req: DescribeScreenRiskAssetsTopRequest, cb?: (error: string, rep: DescribeScreenRiskAssetsTopResponse) => void): Promise<DescribeScreenRiskAssetsTopResponse>;
    /**
     * 获取所有软件应用数量
     */
    DescribeAssetAppCount(req: DescribeAssetAppCountRequest, cb?: (error: string, rep: DescribeAssetAppCountResponse) => void): Promise<DescribeAssetAppCountResponse>;
    /**
     * 接口返回TopN的风险服务器
     */
    DescribeBaselineHostTop(req: DescribeBaselineHostTopRequest, cb?: (error: string, rep: DescribeBaselineHostTopResponse) => void): Promise<DescribeBaselineHostTopResponse>;
    /**
     * 查询java内存马事件列表
     */
    DescribeJavaMemShellList(req: DescribeJavaMemShellListRequest, cb?: (error: string, rep: DescribeJavaMemShellListResponse) => void): Promise<DescribeJavaMemShellListResponse>;
    /**
     * 获取基线检测详情记录
     */
    DescribeBaselineDetectList(req: DescribeBaselineDetectListRequest, cb?: (error: string, rep: DescribeBaselineDetectListResponse) => void): Promise<DescribeBaselineDetectListResponse>;
    /**
     * 专家服务-旗舰重保列表
     */
    DescribeProtectNetList(req: DescribeProtectNetListRequest, cb?: (error: string, rep: DescribeProtectNetListResponse) => void): Promise<DescribeProtectNetListResponse>;
    /**
     * 获取反弹Shell规则列表
     */
    DescribeReverseShellRules(req: DescribeReverseShellRulesRequest, cb?: (error: string, rep: DescribeReverseShellRulesResponse) => void): Promise<DescribeReverseShellRulesResponse>;
    /**
     * 删除高危命令策略
     */
    DeleteBashPolicies(req: DeleteBashPoliciesRequest, cb?: (error: string, rep: DeleteBashPoliciesResponse) => void): Promise<DeleteBashPoliciesResponse>;
    /**
     * 获取漏洞防御概览信息，包括事件趋势及插件开启情况
     */
    DescribeVulDefenceOverview(req?: DescribeVulDefenceOverviewRequest, cb?: (error: string, rep: DescribeVulDefenceOverviewResponse) => void): Promise<DescribeVulDefenceOverviewResponse>;
    /**
     * 获取所有账号数量
     */
    DescribeAssetUserCount(req: DescribeAssetUserCountRequest, cb?: (error: string, rep: DescribeAssetUserCountResponse) => void): Promise<DescribeAssetUserCountResponse>;
    /**
     * 导出资产管理主机资源详细信息
     */
    ExportAssetMachineDetail(req: ExportAssetMachineDetailRequest, cb?: (error: string, rep: ExportAssetMachineDetailResponse) => void): Promise<ExportAssetMachineDetailResponse>;
    /**
     * 获取所有Web站点数量
     */
    DescribeAssetWebLocationCount(req: DescribeAssetWebLocationCountRequest, cb?: (error: string, rep: DescribeAssetWebLocationCountResponse) => void): Promise<DescribeAssetWebLocationCountResponse>;
    /**
     * 获取Web站点虚拟目录列表
     */
    DescribeAssetWebLocationPathList(req: DescribeAssetWebLocationPathListRequest, cb?: (error: string, rep: DescribeAssetWebLocationPathListResponse) => void): Promise<DescribeAssetWebLocationPathListResponse>;
    /**
     * 大屏可视化主机入侵详情
     */
    DescribeScreenHostInvasion(req: DescribeScreenHostInvasionRequest, cb?: (error: string, rep: DescribeScreenHostInvasionResponse) => void): Promise<DescribeScreenHostInvasionResponse>;
    /**
     * 查看产生事件时规则详情接口
     */
    DescribeFileTamperEventRuleInfo(req: DescribeFileTamperEventRuleInfoRequest, cb?: (error: string, rep: DescribeFileTamperEventRuleInfoResponse) => void): Promise<DescribeFileTamperEventRuleInfoResponse>;
    /**
     * 查询索引
     */
    DescribeLogIndex(req?: DescribeLogIndexRequest, cb?: (error: string, rep: DescribeLogIndexResponse) => void): Promise<DescribeLogIndexResponse>;
    /**
     * DestroyOrder  该接口可以对资源销毁.
     */
    DestroyOrder(req: DestroyOrderRequest, cb?: (error: string, rep: DestroyOrderResponse) => void): Promise<DestroyOrderResponse>;
    /**
     * 新增或更新告警接收人
     */
    ModifyWebHookReceiver(req: ModifyWebHookReceiverRequest, cb?: (error: string, rep: ModifyWebHookReceiverResponse) => void): Promise<ModifyWebHookReceiverResponse>;
    /**
     * 大屏可视化获取主机相关统计
     */
    DescribeScreenGeneralStat(req?: DescribeScreenGeneralStatRequest, cb?: (error: string, rep: DescribeScreenGeneralStatResponse) => void): Promise<DescribeScreenGeneralStatResponse>;
    /**
     * 查询高危命令事件详情(新)
     */
    DescribeBashEventsInfoNew(req: DescribeBashEventsInfoNewRequest, cb?: (error: string, rep: DescribeBashEventsInfoNewResponse) => void): Promise<DescribeBashEventsInfoNewResponse>;
    /**
     * 更改基线忽略规则
     */
    ModifyBaselineRuleIgnore(req: ModifyBaselineRuleIgnoreRequest, cb?: (error: string, rep: ModifyBaselineRuleIgnoreResponse) => void): Promise<ModifyBaselineRuleIgnoreResponse>;
    /**
     * 获取Web服务关联进程列表
     */
    DescribeAssetWebServiceProcessList(req: DescribeAssetWebServiceProcessListRequest, cb?: (error: string, rep: DescribeAssetWebServiceProcessListResponse) => void): Promise<DescribeAssetWebServiceProcessListResponse>;
    /**
     * 本接口 (ExportMaliciousRequests) 用于导出下载恶意请求文件。
     */
    ExportMaliciousRequests(req: ExportMaliciousRequestsRequest, cb?: (error: string, rep: ExportMaliciousRequestsResponse) => void): Promise<ExportMaliciousRequestsResponse>;
    /**
     * 获取系统负载、内存使用率、硬盘使用率情况
     */
    DescribeAssetLoadInfo(req?: DescribeAssetLoadInfoRequest, cb?: (error: string, rep: DescribeAssetLoadInfoResponse) => void): Promise<DescribeAssetLoadInfoResponse>;
    /**
     * 修复基线检测
     */
    FixBaselineDetect(req: FixBaselineDetectRequest, cb?: (error: string, rep: FixBaselineDetectResponse) => void): Promise<FixBaselineDetectResponse>;
    /**
     * 获取基线规则检测列表
     */
    DescribeBaselineRuleDetectList(req: DescribeBaselineRuleDetectListRequest, cb?: (error: string, rep: DescribeBaselineRuleDetectListResponse) => void): Promise<DescribeBaselineRuleDetectListResponse>;
    /**
     * 编辑漏洞防御最大cpu配置
     */
    ModifyRaspMaxCpu(req: ModifyRaspMaxCpuRequest, cb?: (error: string, rep: ModifyRaspMaxCpuResponse) => void): Promise<ModifyRaspMaxCpuResponse>;
    /**
     * 删除授权全部记录
     */
    DeleteLicenseRecordAll(req?: DeleteLicenseRecordAllRequest, cb?: (error: string, rep: DeleteLicenseRecordAllResponse) => void): Promise<DeleteLicenseRecordAllResponse>;
    /**
     * 获取概览统计数据。
     */
    DescribeOverviewStatistics(req?: DescribeOverviewStatisticsRequest, cb?: (error: string, rep: DescribeOverviewStatisticsResponse) => void): Promise<DescribeOverviewStatisticsResponse>;
    /**
     * 导出基线检测项
     */
    ExportBaselineItemDetectList(req: ExportBaselineItemDetectListRequest, cb?: (error: string, rep: ExportBaselineItemDetectListResponse) => void): Promise<ExportBaselineItemDetectListResponse>;
    /**
     * 更新合并后登录审计白名单信息（服务器列表数目应小于1000）
     */
    ModifyLoginWhiteRecord(req: ModifyLoginWhiteRecordRequest, cb?: (error: string, rep: ModifyLoginWhiteRecordResponse) => void): Promise<ModifyLoginWhiteRecordResponse>;
    /**
     * 根据任务id查询基线检测进度
     */
    DescribeBaselineScanSchedule(req: DescribeBaselineScanScheduleRequest, cb?: (error: string, rep: DescribeBaselineScanScheduleResponse) => void): Promise<DescribeBaselineScanScheduleResponse>;
    /**
     * 查询网络攻击设置
     */
    DescribeNetAttackSetting(req?: DescribeNetAttackSettingRequest, cb?: (error: string, rep: DescribeNetAttackSettingResponse) => void): Promise<DescribeNetAttackSettingResponse>;
    /**
     * 导出资产管理Web框架列表
     */
    ExportAssetWebFrameList(req: ExportAssetWebFrameListRequest, cb?: (error: string, rep: ExportAssetWebFrameListResponse) => void): Promise<ExportAssetWebFrameListResponse>;
    /**
     * 检验核心文件监控前端新增和编辑时的规则参数。
     */
    CheckFileTamperRule(req: CheckFileTamperRuleRequest, cb?: (error: string, rep: CheckFileTamperRuleResponse) => void): Promise<CheckFileTamperRuleResponse>;
    /**
     * 获取网络攻击白名单列表
     */
    DescribeNetAttackWhiteList(req: DescribeNetAttackWhiteListRequest, cb?: (error: string, rep: DescribeNetAttackWhiteListResponse) => void): Promise<DescribeNetAttackWhiteListResponse>;
    /**
     * 删除本地提权规则
     */
    DeletePrivilegeRules(req: DeletePrivilegeRulesRequest, cb?: (error: string, rep: DeletePrivilegeRulesResponse) => void): Promise<DeletePrivilegeRulesResponse>;
    /**
     * 切换高危命令策略状态
     */
    ModifyBashPolicyStatus(req: ModifyBashPolicyStatusRequest, cb?: (error: string, rep: ModifyBashPolicyStatusResponse) => void): Promise<ModifyBashPolicyStatusResponse>;
    /**
     * 删除恶意请求事件
     */
    DeleteRiskDnsEvent(req: DeleteRiskDnsEventRequest, cb?: (error: string, rep: DeleteRiskDnsEventResponse) => void): Promise<DeleteRiskDnsEventResponse>;
    /**
     * 设置本地存储数据
     */
    SetLocalStorageItem(req: SetLocalStorageItemRequest, cb?: (error: string, rep: SetLocalStorageItemResponse) => void): Promise<SetLocalStorageItemResponse>;
    /**
     * 本接口用于删除异地登录白名单规则。
     */
    DeleteLoginWhiteList(req: DeleteLoginWhiteListRequest, cb?: (error: string, rep: DeleteLoginWhiteListResponse) => void): Promise<DeleteLoginWhiteListResponse>;
    /**
     * 删除日志下载任务
     */
    DeleteLogExport(req: DeleteLogExportRequest, cb?: (error: string, rep: DeleteLogExportResponse) => void): Promise<DeleteLogExportResponse>;
    /**
     * 本接口 (DescribeExportMachines) 用于导出区域主机列表。
     */
    DescribeExportMachines(req: DescribeExportMachinesRequest, cb?: (error: string, rep: DescribeExportMachinesResponse) => void): Promise<DescribeExportMachinesResponse>;
    /**
     * 清理本地存储数据
     */
    ClearLocalStorage(req?: ClearLocalStorageRequest, cb?: (error: string, rep: ClearLocalStorageResponse) => void): Promise<ClearLocalStorageResponse>;
    /**
     * 获取所有Web框架数量
     */
    DescribeAssetWebFrameCount(req: DescribeAssetWebFrameCountRequest, cb?: (error: string, rep: DescribeAssetWebFrameCountResponse) => void): Promise<DescribeAssetWebFrameCountResponse>;
    /**
     * 根据基线策略id删除策略
     */
    DeleteBaselineStrategy(req: DeleteBaselineStrategyRequest, cb?: (error: string, rep: DeleteBaselineStrategyResponse) => void): Promise<DeleteBaselineStrategyResponse>;
    /**
     * 查询授权信息
     */
    DescribeLicense(req?: DescribeLicenseRequest, cb?: (error: string, rep: DescribeLicenseResponse) => void): Promise<DescribeLicenseResponse>;
    /**
     * 导出高危命令策略
     */
    ExportBashPolicies(req: ExportBashPoliciesRequest, cb?: (error: string, rep: ExportBashPoliciesResponse) => void): Promise<ExportBashPoliciesResponse>;
    /**
     * 本接口 (DescribeMachineLicenseDetail)查询机器授权信息
     */
    DescribeMachineLicenseDetail(req: DescribeMachineLicenseDetailRequest, cb?: (error: string, rep: DescribeMachineLicenseDetailResponse) => void): Promise<DescribeMachineLicenseDetailResponse>;
    /**
     * 编辑反弹Shell规则（支持多服务器选择）
     */
    ModifyReverseShellRulesAggregation(req: ModifyReverseShellRulesAggregationRequest, cb?: (error: string, rep: ModifyReverseShellRulesAggregationResponse) => void): Promise<ModifyReverseShellRulesAggregationResponse>;
    /**
     * 切换高危命令规则状态
     */
    SwitchBashRules(req: SwitchBashRulesRequest, cb?: (error: string, rep: SwitchBashRulesResponse) => void): Promise<SwitchBashRulesResponse>;
    /**
     * 获取账号列表
     */
    DescribeAssetUserList(req: DescribeAssetUserListRequest, cb?: (error: string, rep: DescribeAssetUserListResponse) => void): Promise<DescribeAssetUserListResponse>;
    /**
     * 获取Web站点详情
     */
    DescribeAssetWebLocationInfo(req: DescribeAssetWebLocationInfoRequest, cb?: (error: string, rep: DescribeAssetWebLocationInfoResponse) => void): Promise<DescribeAssetWebLocationInfoResponse>;
    /**
     * 查询给定主机java内存马插件信息
     */
    DescribeJavaMemShellPluginInfo(req: DescribeJavaMemShellPluginInfoRequest, cb?: (error: string, rep: DescribeJavaMemShellPluginInfoResponse) => void): Promise<DescribeJavaMemShellPluginInfoResponse>;
    /**
     * 获取密码破解列表
     */
    DescribeBruteAttackList(req: DescribeBruteAttackListRequest, cb?: (error: string, rep: DescribeBruteAttackListResponse) => void): Promise<DescribeBruteAttackListResponse>;
    /**
     * 查询资产管理数据库列表
     */
    DescribeAssetDatabaseList(req: DescribeAssetDatabaseListRequest, cb?: (error: string, rep: DescribeAssetDatabaseListResponse) => void): Promise<DescribeAssetDatabaseListResponse>;
    /**
     * 漏洞影响主机列表
     */
    DescribeVulEffectHostList(req: DescribeVulEffectHostListRequest, cb?: (error: string, rep: DescribeVulEffectHostListResponse) => void): Promise<DescribeVulEffectHostListResponse>;
    /**
     * 查询基线是否第一次检测
     */
    CheckFirstScanBaseline(req?: CheckFirstScanBaselineRequest, cb?: (error: string, rep: CheckFirstScanBaselineResponse) => void): Promise<CheckFirstScanBaselineResponse>;
    /**
     * 获取快速检索列表
     */
    DescribeSearchTemplates(req: DescribeSearchTemplatesRequest, cb?: (error: string, rep: DescribeSearchTemplatesResponse) => void): Promise<DescribeSearchTemplatesResponse>;
    /**
     * 导出资产管理Web服务列表
     */
    ExportAssetWebServiceInfoList(req: ExportAssetWebServiceInfoListRequest, cb?: (error: string, rep: ExportAssetWebServiceInfoListResponse) => void): Promise<ExportAssetWebServiceInfoListResponse>;
    /**
     * 本接口（SeparateMalwares）用于隔离木马。
     */
    SeparateMalwares(req: SeparateMalwaresRequest, cb?: (error: string, rep: SeparateMalwaresResponse) => void): Promise<SeparateMalwaresResponse>;
    /**
     * 删除网络攻击白名单
     */
    DeleteNetAttackWhiteList(req: DeleteNetAttackWhiteListRequest, cb?: (error: string, rep: DeleteNetAttackWhiteListResponse) => void): Promise<DeleteNetAttackWhiteListResponse>;
    /**
     * 获取日志下载任务列表
     */
    DescribeLogExports(req: DescribeLogExportsRequest, cb?: (error: string, rep: DescribeLogExportsResponse) => void): Promise<DescribeLogExportsResponse>;
    /**
     * 大屏可视化主机区域列表
     */
    DescribeScreenMachines(req: DescribeScreenMachinesRequest, cb?: (error: string, rep: DescribeScreenMachinesResponse) => void): Promise<DescribeScreenMachinesResponse>;
    /**
     * 大屏获取安全防护状态
     */
    DescribeScreenProtectionStat(req?: DescribeScreenProtectionStatRequest, cb?: (error: string, rep: DescribeScreenProtectionStatResponse) => void): Promise<DescribeScreenProtectionStatResponse>;
    /**
     * 获取高危命令列表(新)
     */
    DescribeBashEventsNew(req: DescribeBashEventsNewRequest, cb?: (error: string, rep: DescribeBashEventsNewResponse) => void): Promise<DescribeBashEventsNewResponse>;
    /**
     * 获取资产指纹页面的资源监控列表
     */
    DescribeAssetMachineList(req: DescribeAssetMachineListRequest, cb?: (error: string, rep: DescribeAssetMachineListResponse) => void): Promise<DescribeAssetMachineListResponse>;
    /**
     * 删除主机所有记录，目前只支持非腾讯云主机，且需要主机在离线状态
     */
    RemoveMachine(req: RemoveMachineRequest, cb?: (error: string, rep: RemoveMachineResponse) => void): Promise<RemoveMachineResponse>;
    /**
     * 获取主机所有资源数量
     */
    DescribeAssetHostTotalCount(req: DescribeAssetHostTotalCountRequest, cb?: (error: string, rep: DescribeAssetHostTotalCountResponse) => void): Promise<DescribeAssetHostTotalCountResponse>;
    /**
     * 获取Web站点列表
     */
    DescribeAssetWebLocationList(req: DescribeAssetWebLocationListRequest, cb?: (error: string, rep: DescribeAssetWebLocationListResponse) => void): Promise<DescribeAssetWebLocationListResponse>;
    /**
     * 查询恶意请求事件详情
     */
    DescribeRiskDnsEventInfo(req: DescribeRiskDnsEventInfoRequest, cb?: (error: string, rep: DescribeRiskDnsEventInfoResponse) => void): Promise<DescribeRiskDnsEventInfoResponse>;
    /**
     * 导出高危命令事件(新)
     */
    ExportBashEventsNew(req: ExportBashEventsNewRequest, cb?: (error: string, rep: ExportBashEventsNewResponse) => void): Promise<ExportBashEventsNewResponse>;
    /**
     * 查询入侵检测事件更新状态任务是否完成
     */
    DescribeRiskBatchStatus(req: DescribeRiskBatchStatusRequest, cb?: (error: string, rep: DescribeRiskBatchStatusResponse) => void): Promise<DescribeRiskBatchStatusResponse>;
    /**
     * 获取资产管理进程列表
     */
    DescribeAssetProcessInfoList(req: DescribeAssetProcessInfoListRequest, cb?: (error: string, rep: DescribeAssetProcessInfoListResponse) => void): Promise<DescribeAssetProcessInfoListResponse>;
    /**
     * 查询告警接收人列表
     */
    DescribeWebHookReceiver(req: DescribeWebHookReceiverRequest, cb?: (error: string, rep: DescribeWebHookReceiverResponse) => void): Promise<DescribeWebHookReceiverResponse>;
    /**
     * 用于设置新增主机自动开通专业防护配置。
     */
    ModifyAutoOpenProVersionConfig(req: ModifyAutoOpenProVersionConfigRequest, cb?: (error: string, rep: ModifyAutoOpenProVersionConfigResponse) => void): Promise<ModifyAutoOpenProVersionConfigResponse>;
    /**
     * 设置本地存储过期时间
     */
    SetLocalStorageExpire(req: SetLocalStorageExpireRequest, cb?: (error: string, rep: SetLocalStorageExpireResponse) => void): Promise<SetLocalStorageExpireResponse>;
    /**
     * 漏洞修护-查询可修护主机信息
     */
    DescribeCanFixVulMachine(req: DescribeCanFixVulMachineRequest, cb?: (error: string, rep: DescribeCanFixVulMachineResponse) => void): Promise<DescribeCanFixVulMachineResponse>;
    /**
     * 更改基线策略状态
     */
    ModifyBaselinePolicyState(req: ModifyBaselinePolicyStateRequest, cb?: (error: string, rep: ModifyBaselinePolicyStateResponse) => void): Promise<ModifyBaselinePolicyStateResponse>;
    /**
     * 测试企微机器人规则
     */
    TestWebHookRule(req: TestWebHookRuleRequest, cb?: (error: string, rep: TestWebHookRuleResponse) => void): Promise<TestWebHookRuleResponse>;
    /**
     * 入侵管理-终止扫描任务
     */
    DeleteMalwareScanTask(req?: DeleteMalwareScanTaskRequest, cb?: (error: string, rep: DeleteMalwareScanTaskResponse) => void): Promise<DeleteMalwareScanTaskResponse>;
    /**
     * 核心文件监控规则列表
     */
    DescribeFileTamperRules(req: DescribeFileTamperRulesRequest, cb?: (error: string, rep: DescribeFileTamperRulesResponse) => void): Promise<DescribeFileTamperRulesResponse>;
    /**
     * 查询主机高级防御事件数统计
     */
    DescribeMachineDefenseCnt(req: DescribeMachineDefenseCntRequest, cb?: (error: string, rep: DescribeMachineDefenseCntResponse) => void): Promise<DescribeMachineDefenseCntResponse>;
    /**
     * 用于获取专业版概览信息。
     */
    DescribeProVersionInfo(req?: DescribeProVersionInfoRequest, cb?: (error: string, rep: DescribeProVersionInfoResponse) => void): Promise<DescribeProVersionInfoResponse>;
    /**
     * 修改网络攻击设置
     */
    ModifyNetAttackSetting(req: ModifyNetAttackSettingRequest, cb?: (error: string, rep: ModifyNetAttackSettingResponse) => void): Promise<ModifyNetAttackSettingResponse>;
    /**
     * 删除阻断白名单列表
     */
    DeleteBanWhiteList(req: DeleteBanWhiteListRequest, cb?: (error: string, rep: DeleteBanWhiteListResponse) => void): Promise<DeleteBanWhiteListResponse>;
    /**
     * 查询网站防篡改概览信息
     */
    DescribeWebPageGeneralize(req?: DescribeWebPageGeneralizeRequest, cb?: (error: string, rep: DescribeWebPageGeneralizeResponse) => void): Promise<DescribeWebPageGeneralizeResponse>;
    /**
     * 获取本地提权事件列表
     */
    DescribePrivilegeEvents(req: DescribePrivilegeEventsRequest, cb?: (error: string, rep: DescribePrivilegeEventsResponse) => void): Promise<DescribePrivilegeEventsResponse>;
    /**
     * 本接口（DescribeMachineInfo）用于获取机器详细信息。
     */
    DescribeMachineInfo(req: DescribeMachineInfoRequest, cb?: (error: string, rep: DescribeMachineInfoResponse) => void): Promise<DescribeMachineInfoResponse>;
    /**
     * 获取各种类型资源Top5
     */
    DescribeAssetTypeTop(req?: DescribeAssetTypeTopRequest, cb?: (error: string, rep: DescribeAssetTypeTopResponse) => void): Promise<DescribeAssetTypeTopResponse>;
    /**
     * 获取漏洞防御白名单漏洞列表
     */
    DescribeRaspRuleVuls(req: DescribeRaspRuleVulsRequest, cb?: (error: string, rep: DescribeRaspRuleVulsResponse) => void): Promise<DescribeRaspRuleVulsResponse>;
    /**
     * 导出勒索防御策略绑定机器列表
     */
    ExportRansomDefenseStrategyMachines(req: ExportRansomDefenseStrategyMachinesRequest, cb?: (error: string, rep: ExportRansomDefenseStrategyMachinesResponse) => void): Promise<ExportRansomDefenseStrategyMachinesResponse>;
    /**
     * 获取日志检索容量使用统计
     */
    DescribeLogStorageStatistic(req?: DescribeLogStorageStatisticRequest, cb?: (error: string, rep: DescribeLogStorageStatisticResponse) => void): Promise<DescribeLogStorageStatisticResponse>;
    /**
     * 获取一键忽略受影响的检测项和主机信息
     */
    DescribeIgnoreHostAndItemConfig(req: DescribeIgnoreHostAndItemConfigRequest, cb?: (error: string, rep: DescribeIgnoreHostAndItemConfigResponse) => void): Promise<DescribeIgnoreHostAndItemConfigResponse>;
    /**
     * 查询已经忽略的检测项信息
     */
    DescribeIgnoreBaselineRule(req: DescribeIgnoreBaselineRuleRequest, cb?: (error: string, rep: DescribeIgnoreBaselineRuleResponse) => void): Promise<DescribeIgnoreBaselineRuleResponse>;
    /**
     * 查询日志投递kafka可选项列表
     */
    DescribeLogDeliveryKafkaOptions(req: DescribeLogDeliveryKafkaOptionsRequest, cb?: (error: string, rep: DescribeLogDeliveryKafkaOptionsResponse) => void): Promise<DescribeLogDeliveryKafkaOptionsResponse>;
    /**
     * 查询主机入侵检测事件统计
     */
    DescribeMachineRiskCnt(req: DescribeMachineRiskCntRequest, cb?: (error: string, rep: DescribeMachineRiskCntResponse) => void): Promise<DescribeMachineRiskCntResponse>;
    /**
     * 大屏可视化安全播报
     */
    DescribeScreenBroadcasts(req?: DescribeScreenBroadcastsRequest, cb?: (error: string, rep: DescribeScreenBroadcastsResponse) => void): Promise<DescribeScreenBroadcastsResponse>;
    /**
     * 该接口可以创建白名单订单
     */
    CreateWhiteListOrder(req: CreateWhiteListOrderRequest, cb?: (error: string, rep: CreateWhiteListOrderResponse) => void): Promise<CreateWhiteListOrderResponse>;
    /**
     * 设置中心-授权管理 对某个授权批量解绑机器
     */
    ModifyLicenseUnBinds(req: ModifyLicenseUnBindsRequest, cb?: (error: string, rep: ModifyLicenseUnBindsResponse) => void): Promise<ModifyLicenseUnBindsResponse>;
    /**
     * 获取版本对比信息
     */
    DescribeVersionCompareChart(req?: DescribeVersionCompareChartRequest, cb?: (error: string, rep: DescribeVersionCompareChartResponse) => void): Promise<DescribeVersionCompareChartResponse>;
    /**
     * 该接口可以对入侵检测-文件查杀扫描检测
     */
    CreateScanMalwareSetting(req: CreateScanMalwareSettingRequest, cb?: (error: string, rep: CreateScanMalwareSettingResponse) => void): Promise<CreateScanMalwareSettingResponse>;
    /**
     * 获取全网勒索态势
     */
    DescribeRansomDefenseTrend(req?: DescribeRansomDefenseTrendRequest, cb?: (error: string, rep: DescribeRansomDefenseTrendResponse) => void): Promise<DescribeRansomDefenseTrendResponse>;
    /**
     * 导出资产管理Web应用列表
     */
    ExportAssetWebAppList(req: ExportAssetWebAppListRequest, cb?: (error: string, rep: ExportAssetWebAppListResponse) => void): Promise<ExportAssetWebAppListResponse>;
    /**
     * 该接口可以获取设置中心-授权管理,某个授权下已绑定的授权机器列表
     */
    DescribeLicenseBindList(req: DescribeLicenseBindListRequest, cb?: (error: string, rep: DescribeLicenseBindListResponse) => void): Promise<DescribeLicenseBindListResponse>;
    /**
     * 获取内核模块详情
     */
    DescribeAssetCoreModuleInfo(req: DescribeAssetCoreModuleInfoRequest, cb?: (error: string, rep: DescribeAssetCoreModuleInfoResponse) => void): Promise<DescribeAssetCoreModuleInfoResponse>;
    /**
     * 获取主机相关统计
     */
    DescribeGeneralStat(req: DescribeGeneralStatRequest, cb?: (error: string, rep: DescribeGeneralStatResponse) => void): Promise<DescribeGeneralStatResponse>;
    /**
     * 开关java内存马插件
     */
    ModifyJavaMemShellPluginSwitch(req: ModifyJavaMemShellPluginSwitchRequest, cb?: (error: string, rep: ModifyJavaMemShellPluginSwitchResponse) => void): Promise<ModifyJavaMemShellPluginSwitchResponse>;
    /**
     * 获取资产管理数据库详情
     */
    DescribeAssetDatabaseInfo(req: DescribeAssetDatabaseInfoRequest, cb?: (error: string, rep: DescribeAssetDatabaseInfoResponse) => void): Promise<DescribeAssetDatabaseInfoResponse>;
    /**
     * 获取木马白名单列表
     */
    DescribeMalwareWhiteList(req: DescribeMalwareWhiteListRequest, cb?: (error: string, rep: DescribeMalwareWhiteListResponse) => void): Promise<DescribeMalwareWhiteListResponse>;
    /**
     * 查询定期检测的配置
     */
    DescribeScanVulSetting(req?: DescribeScanVulSettingRequest, cb?: (error: string, rep: DescribeScanVulSettingResponse) => void): Promise<DescribeScanVulSettingResponse>;
    /**
     * 创建木马白名单
     */
    CreateMalwareWhiteList(req: CreateMalwareWhiteListRequest, cb?: (error: string, rep: CreateMalwareWhiteListResponse) => void): Promise<CreateMalwareWhiteListResponse>;
    /**
     * 本接口（DeleteMachine）用于卸载主机安全客户端。
     */
    DeleteMachine(req: DeleteMachineRequest, cb?: (error: string, rep: DeleteMachineResponse) => void): Promise<DeleteMachineResponse>;
    /**
     * 核心文件事件更新
     */
    ModifyFileTamperEvents(req: ModifyFileTamperEventsRequest, cb?: (error: string, rep: ModifyFileTamperEventsResponse) => void): Promise<ModifyFileTamperEventsResponse>;
    /**
     * 查询安全播报文章信息
     */
    DescribeSecurityBroadcastInfo(req: DescribeSecurityBroadcastInfoRequest, cb?: (error: string, rep: DescribeSecurityBroadcastInfoResponse) => void): Promise<DescribeSecurityBroadcastInfoResponse>;
    /**
     * ScanTaskAgain  重新开始扫描任务，可以指定机器
     */
    ScanTaskAgain(req: ScanTaskAgainRequest, cb?: (error: string, rep: ScanTaskAgainResponse) => void): Promise<ScanTaskAgainResponse>;
    /**
     * 获取漏洞概览数据
     */
    DescribeVulOverview(req?: DescribeVulOverviewRequest, cb?: (error: string, rep: DescribeVulOverviewResponse) => void): Promise<DescribeVulOverviewResponse>;
    /**
     * 日志分析功能-获取日志类型，使用该接口返回的结果暂时可过滤的日志类型
     */
    DescribeLogType(req?: DescribeLogTypeRequest, cb?: (error: string, rep: DescribeLogTypeResponse) => void): Promise<DescribeLogTypeResponse>;
    /**
     * 网络攻击事件详情
     */
    DescribeAttackEventInfo(req: DescribeAttackEventInfoRequest, cb?: (error: string, rep: DescribeAttackEventInfoResponse) => void): Promise<DescribeAttackEventInfoResponse>;
    /**
     * 大屏可视化获取全网攻击热点
     */
    DescribeScreenAttackHotspot(req?: DescribeScreenAttackHotspotRequest, cb?: (error: string, rep: DescribeScreenAttackHotspotResponse) => void): Promise<DescribeScreenAttackHotspotResponse>;
    /**
     * 网站防篡改-删除事件记录
     */
    DeleteWebPageEventLog(req?: DeleteWebPageEventLogRequest, cb?: (error: string, rep: DeleteWebPageEventLogResponse) => void): Promise<DeleteWebPageEventLogResponse>;
    /**
     * 导出修复列表
     */
    ExportBaselineFixList(req: ExportBaselineFixListRequest, cb?: (error: string, rep: ExportBaselineFixListResponse) => void): Promise<ExportBaselineFixListResponse>;
    /**
     * 用于获取单台主机或所有主机是否开通专业版状态。
     */
    DescribeProVersionStatus(req: DescribeProVersionStatusRequest, cb?: (error: string, rep: DescribeProVersionStatusResponse) => void): Promise<DescribeProVersionStatusResponse>;
    /**
     * 本接口 (DescribeMachines) 用于获取区域主机列表。
     */
    DescribeMachines(req: DescribeMachinesRequest, cb?: (error: string, rep: DescribeMachinesResponse) => void): Promise<DescribeMachinesResponse>;
    /**
     * 获取基线检测主机列表
     */
    DescribeBaselineHostDetectList(req: DescribeBaselineHostDetectListRequest, cb?: (error: string, rep: DescribeBaselineHostDetectListResponse) => void): Promise<DescribeBaselineHostDetectListResponse>;
    /**
     * 查询资产管理Web服务列表
     */
    DescribeAssetWebServiceInfoList(req: DescribeAssetWebServiceInfoListRequest, cb?: (error: string, rep: DescribeAssetWebServiceInfoListResponse) => void): Promise<DescribeAssetWebServiceInfoListResponse>;
    /**
     * 获取所有进程数量
     */
    DescribeAssetProcessCount(req: DescribeAssetProcessCountRequest, cb?: (error: string, rep: DescribeAssetProcessCountResponse) => void): Promise<DescribeAssetProcessCountResponse>;
    /**
     * 删除服务器关联的标签
     */
    DeleteMachineTag(req: DeleteMachineTagRequest, cb?: (error: string, rep: DeleteMachineTagResponse) => void): Promise<DeleteMachineTagResponse>;
    /**
     * 获取漏洞态势信息
     */
    DescribeVulTrend(req?: DescribeVulTrendRequest, cb?: (error: string, rep: DescribeVulTrendResponse) => void): Promise<DescribeVulTrendResponse>;
    /**
     * 获取指定点属性信息
     */
    DescribeVertexDetail(req: DescribeVertexDetailRequest, cb?: (error: string, rep: DescribeVertexDetailResponse) => void): Promise<DescribeVertexDetailResponse>;
    /**
     * 修改告警策略开关
     */
    ModifyWebHookPolicyStatus(req: ModifyWebHookPolicyStatusRequest, cb?: (error: string, rep: ModifyWebHookPolicyStatusResponse) => void): Promise<ModifyWebHookPolicyStatusResponse>;
    /**
     * 导出检测项结果列表
     */
    ExportBaselineItemList(req: ExportBaselineItemListRequest, cb?: (error: string, rep: ExportBaselineItemListResponse) => void): Promise<ExportBaselineItemListResponse>;
    /**
     * 获取基线检测项TOP5
     */
    DescribeBaselineItemRiskTop(req: DescribeBaselineItemRiskTopRequest, cb?: (error: string, rep: DescribeBaselineItemRiskTopResponse) => void): Promise<DescribeBaselineItemRiskTopResponse>;
    /**
     * 导出资产管理应用列表
     */
    ExportAssetAppList(req: ExportAssetAppListRequest, cb?: (error: string, rep: ExportAssetAppListResponse) => void): Promise<ExportAssetAppListResponse>;
    /**
     * 导出弱口令配置列表
     */
    ExportBaselineWeakPasswordList(req: ExportBaselineWeakPasswordListRequest, cb?: (error: string, rep: ExportBaselineWeakPasswordListResponse) => void): Promise<ExportBaselineWeakPasswordListResponse>;
    /**
     * 查询一个用户下的基线策略信息
     */
    DescribeBaselineStrategyList(req: DescribeBaselineStrategyListRequest, cb?: (error: string, rep: DescribeBaselineStrategyListResponse) => void): Promise<DescribeBaselineStrategyListResponse>;
    /**
     * 删除本地存储数据
     */
    RemoveLocalStorageItem(req: RemoveLocalStorageItemRequest, cb?: (error: string, rep: RemoveLocalStorageItemResponse) => void): Promise<RemoveLocalStorageItemResponse>;
    /**
     * 导出资产管理端口列表
     */
    ExportAssetPortInfoList(req: ExportAssetPortInfoListRequest, cb?: (error: string, rep: ExportAssetPortInfoListResponse) => void): Promise<ExportAssetPortInfoListResponse>;
    /**
     * 导出资源监控列表
     */
    ExportAssetMachineList(req: ExportAssetMachineListRequest, cb?: (error: string, rep: ExportAssetMachineListResponse) => void): Promise<ExportAssetMachineListResponse>;
    /**
     * 获取日志存储量记录
     */
    DescribeLogStorageRecord(req?: DescribeLogStorageRecordRequest, cb?: (error: string, rep: DescribeLogStorageRecordResponse) => void): Promise<DescribeLogStorageRecordResponse>;
    /**
     * 校验高危命令用户规则新增和编辑时的参数。
     */
    CheckBashPolicyParams(req: CheckBashPolicyParamsRequest, cb?: (error: string, rep: CheckBashPolicyParamsResponse) => void): Promise<CheckBashPolicyParamsResponse>;
    /**
     * 修改网络攻击事件状态
     */
    ModifyEventAttackStatus(req: ModifyEventAttackStatusRequest, cb?: (error: string, rep: ModifyEventAttackStatusResponse) => void): Promise<ModifyEventAttackStatusResponse>;
    /**
     * 对旗舰版机器单次触发事件调查及告警回溯
     */
    CreateIncidentBacktracking(req: CreateIncidentBacktrackingRequest, cb?: (error: string, rep: CreateIncidentBacktrackingResponse) => void): Promise<CreateIncidentBacktrackingResponse>;
    /**
     * 根据Ids删除高危命令事件
     */
    DeleteBashEvents(req: DeleteBashEventsRequest, cb?: (error: string, rep: DeleteBashEventsResponse) => void): Promise<DeleteBashEventsResponse>;
    /**
     * 根据基线id查询基线影响主机列表
     */
    DescribeBaselineEffectHostList(req: DescribeBaselineEffectHostListRequest, cb?: (error: string, rep: DescribeBaselineEffectHostListResponse) => void): Promise<DescribeBaselineEffectHostListResponse>;
    /**
     * 本接口 (DeleteMaliciousRequests) 用于删除恶意请求记录。
     */
    DeleteMaliciousRequests(req: DeleteMaliciousRequestsRequest, cb?: (error: string, rep: DeleteMaliciousRequestsResponse) => void): Promise<DeleteMaliciousRequestsResponse>;
    /**
     * 用于统计专业版和基础版机器数。
     */
    DescribeVersionStatistics(req?: DescribeVersionStatisticsRequest, cb?: (error: string, rep: DescribeVersionStatisticsResponse) => void): Promise<DescribeVersionStatisticsResponse>;
    /**
     * 本接口（RecoverMalwares）用于批量恢复已经被隔离的木马文件。
     */
    RecoverMalwares(req: RecoverMalwaresRequest, cb?: (error: string, rep: RecoverMalwaresResponse) => void): Promise<RecoverMalwaresResponse>;
    /**
     * 删除反弹Shell规则
     */
    DeleteReverseShellRules(req: DeleteReverseShellRulesRequest, cb?: (error: string, rep: DeleteReverseShellRulesResponse) => void): Promise<DeleteReverseShellRulesResponse>;
    /**
     * 获取资产管理Web应用列表
     */
    DescribeAssetWebAppList(req: DescribeAssetWebAppListRequest, cb?: (error: string, rep: DescribeAssetWebAppListResponse) => void): Promise<DescribeAssetWebAppListResponse>;
    /**
     * 查询防护目录关联服务器列表信息
     */
    DescribeProtectDirRelatedServer(req: DescribeProtectDirRelatedServerRequest, cb?: (error: string, rep: DescribeProtectDirRelatedServerResponse) => void): Promise<DescribeProtectDirRelatedServerResponse>;
    /**
     * 导出基线主机检测
     */
    ExportBaselineHostDetectList(req: ExportBaselineHostDetectListRequest, cb?: (error: string, rep: ExportBaselineHostDetectListResponse) => void): Promise<ExportBaselineHostDetectListResponse>;
    /**
     * 删除防护网站
     */
    DeleteProtectDir(req: DeleteProtectDirRequest, cb?: (error: string, rep: DeleteProtectDirResponse) => void): Promise<DeleteProtectDirResponse>;
    /**
     * 本接口 (DeleteBruteAttacks) 用于删除暴力破解记录。
     */
    DeleteBruteAttacks(req: DeleteBruteAttacksRequest, cb?: (error: string, rep: DeleteBruteAttacksResponse) => void): Promise<DeleteBruteAttacksResponse>;
    /**
     * 删除告警接收人
     */
    DeleteWebHookReceiver(req: DeleteWebHookReceiverRequest, cb?: (error: string, rep: DeleteWebHookReceiverResponse) => void): Promise<DeleteWebHookReceiverResponse>;
    /**
     * 导出高危命令事件
     */
    ExportBashEvents(req: ExportBashEventsRequest, cb?: (error: string, rep: ExportBashEventsResponse) => void): Promise<ExportBashEventsResponse>;
    /**
     * 查询主机概览信息
     */
    DescribeMachineGeneral(req?: DescribeMachineGeneralRequest, cb?: (error: string, rep: DescribeMachineGeneralResponse) => void): Promise<DescribeMachineGeneralResponse>;
    /**
     * 获取恶意请求策略列表
     */
    DescribeRiskDnsPolicyList(req: DescribeRiskDnsPolicyListRequest, cb?: (error: string, rep: DescribeRiskDnsPolicyListResponse) => void): Promise<DescribeRiskDnsPolicyListResponse>;
    /**
     * 获取应急漏洞列表
     */
    DescribeEmergencyVulList(req: DescribeEmergencyVulListRequest, cb?: (error: string, rep: DescribeEmergencyVulListResponse) => void): Promise<DescribeEmergencyVulListResponse>;
    /**
     * 查询java内存马事件详细信息
     */
    DescribeJavaMemShellInfo(req: DescribeJavaMemShellInfoRequest, cb?: (error: string, rep: DescribeJavaMemShellInfoResponse) => void): Promise<DescribeJavaMemShellInfoResponse>;
    /**
     * 获取基线下载列表
     */
    DescribeBaselineDownloadList(req: DescribeBaselineDownloadListRequest, cb?: (error: string, rep: DescribeBaselineDownloadListResponse) => void): Promise<DescribeBaselineDownloadListResponse>;
    /**
     * 本接口（UntrustMalwares）用于取消信任木马文件。
     */
    UntrustMalwares(req: UntrustMalwaresRequest, cb?: (error: string, rep: UntrustMalwaresResponse) => void): Promise<UntrustMalwaresResponse>;
    /**
     * 新增或修改日志投递kafka接入配置
     */
    ModifyLogKafkaAccess(req: ModifyLogKafkaAccessRequest, cb?: (error: string, rep: ModifyLogKafkaAccessResponse) => void): Promise<ModifyLogKafkaAccessResponse>;
    /**
     * 获取本地提权规则列表
     */
    DescribePrivilegeRules(req: DescribePrivilegeRulesRequest, cb?: (error: string, rep: DescribePrivilegeRulesResponse) => void): Promise<DescribePrivilegeRulesResponse>;
    /**
     * 查询Jar包列表
     */
    DescribeAssetJarList(req: DescribeAssetJarListRequest, cb?: (error: string, rep: DescribeAssetJarListResponse) => void): Promise<DescribeAssetJarListResponse>;
    /**
     * 获取漏洞防御事件详情
     */
    DescribeDefenceEventDetail(req: DescribeDefenceEventDetailRequest, cb?: (error: string, rep: DescribeDefenceEventDetailResponse) => void): Promise<DescribeDefenceEventDetailResponse>;
    /**
     * 获取文件查杀概览信息
     */
    DescribeMalwareRiskOverview(req?: DescribeMalwareRiskOverviewRequest, cb?: (error: string, rep: DescribeMalwareRiskOverviewResponse) => void): Promise<DescribeMalwareRiskOverviewResponse>;
    /**
     * 设置阻断开关状态
     */
    ModifyBanStatus(req: ModifyBanStatusRequest, cb?: (error: string, rep: ModifyBanStatusResponse) => void): Promise<ModifyBanStatusResponse>;
    /**
     * 查询机器清理历史记录
     */
    DescribeMachineClearHistory(req: DescribeMachineClearHistoryRequest, cb?: (error: string, rep: DescribeMachineClearHistoryResponse) => void): Promise<DescribeMachineClearHistoryResponse>;
    /**
     * 关联机器标签列表
     */
    UpdateMachineTags(req: UpdateMachineTagsRequest, cb?: (error: string, rep: UpdateMachineTagsResponse) => void): Promise<UpdateMachineTagsResponse>;
    /**
     * 获取agent安装命令
     */
    DescribeAgentInstallCommand(req: DescribeAgentInstallCommandRequest, cb?: (error: string, rep: DescribeAgentInstallCommandResponse) => void): Promise<DescribeAgentInstallCommandResponse>;
    /**
     * 资产指纹启动扫描
     */
    ScanAsset(req: ScanAssetRequest, cb?: (error: string, rep: ScanAssetResponse) => void): Promise<ScanAssetResponse>;
    /**
     * 根据Ids删除反弹Shell事件
     */
    DeleteReverseShellEvents(req: DeleteReverseShellEventsRequest, cb?: (error: string, rep: DeleteReverseShellEventsResponse) => void): Promise<DeleteReverseShellEventsResponse>;
    /**
     * 根据任务id导出指定扫描任务详情
     */
    ExportScanTaskDetails(req: ExportScanTaskDetailsRequest, cb?: (error: string, rep: ExportScanTaskDetailsResponse) => void): Promise<ExportScanTaskDetailsResponse>;
    /**
     * 查询主机地域列表
     */
    DescribeMachineRegionList(req?: DescribeMachineRegionListRequest, cb?: (error: string, rep: DescribeMachineRegionListResponse) => void): Promise<DescribeMachineRegionListResponse>;
    /**
     * 修改网站防护设置
     */
    ModifyWebPageProtectSetting(req: ModifyWebPageProtectSettingRequest, cb?: (error: string, rep: ModifyWebPageProtectSettingResponse) => void): Promise<ModifyWebPageProtectSettingResponse>;
    /**
     * 导出资产管理启动服务列表
     */
    ExportAssetInitServiceList(req: ExportAssetInitServiceListRequest, cb?: (error: string, rep: ExportAssetInitServiceListResponse) => void): Promise<ExportAssetInitServiceListResponse>;
    /**
     * 更新登录审计白名单信息
     */
    ModifyLoginWhiteInfo(req: ModifyLoginWhiteInfoRequest, cb?: (error: string, rep: ModifyLoginWhiteInfoResponse) => void): Promise<ModifyLoginWhiteInfoResponse>;
    /**
     * 导出资产管理内核模块列表
     */
    ExportAssetCoreModuleList(req: ExportAssetCoreModuleListRequest, cb?: (error: string, rep: ExportAssetCoreModuleListResponse) => void): Promise<ExportAssetCoreModuleListResponse>;
    /**
     * 获取当前用户告警列表
     */
    DescribeWarningList(req?: DescribeWarningListRequest, cb?: (error: string, rep: DescribeWarningListResponse) => void): Promise<DescribeWarningListResponse>;
    /**
     * 获取基线忽略规则列表
     */
    DescribeBaselineRuleIgnoreList(req: DescribeBaselineRuleIgnoreListRequest, cb?: (error: string, rep: DescribeBaselineRuleIgnoreListResponse) => void): Promise<DescribeBaselineRuleIgnoreListResponse>;
    /**
     * 修改主机备注信息
     */
    ModifyMachineRemark(req: ModifyMachineRemarkRequest, cb?: (error: string, rep: ModifyMachineRemarkResponse) => void): Promise<ModifyMachineRemarkResponse>;
    /**
     * 查询基线基础信息列表
     */
    DescribeBaselineBasicInfo(req: DescribeBaselineBasicInfoRequest, cb?: (error: string, rep: DescribeBaselineBasicInfoResponse) => void): Promise<DescribeBaselineBasicInfoResponse>;
    /**
     * 获取机器地域列表
     */
    DescribeMachineRegions(req?: DescribeMachineRegionsRequest, cb?: (error: string, rep: DescribeMachineRegionsResponse) => void): Promise<DescribeMachineRegionsResponse>;
    /**
     * 获取当前漏洞防御插件设置
     */
    DescribeVulDefenceSetting(req?: DescribeVulDefenceSettingRequest, cb?: (error: string, rep: DescribeVulDefenceSettingResponse) => void): Promise<DescribeVulDefenceSettingResponse>;
    /**
     * 获取待处理漏洞数+影响主机数
     */
    DescribeVulHostCountScanTime(req?: DescribeVulHostCountScanTimeRequest, cb?: (error: string, rep: DescribeVulHostCountScanTimeResponse) => void): Promise<DescribeVulHostCountScanTimeResponse>;
    /**
     * 导出基线检测规则
     */
    ExportBaselineRuleDetectList(req: ExportBaselineRuleDetectListRequest, cb?: (error: string, rep: ExportBaselineRuleDetectListResponse) => void): Promise<ExportBaselineRuleDetectListResponse>;
    /**
     * 获取日志存储配置
     */
    DescribeLogStorageConfig(req?: DescribeLogStorageConfigRequest, cb?: (error: string, rep: DescribeLogStorageConfigResponse) => void): Promise<DescribeLogStorageConfigResponse>;
    /**
     * 删除机器清理记录
     */
    DeleteMachineClearHistory(req: DeleteMachineClearHistoryRequest, cb?: (error: string, rep: DeleteMachineClearHistoryResponse) => void): Promise<DeleteMachineClearHistoryResponse>;
    /**
     * 漏洞修护-查询主机创建的快照
     */
    DescribeMachineSnapshot(req: DescribeMachineSnapshotRequest, cb?: (error: string, rep: DescribeMachineSnapshotResponse) => void): Promise<DescribeMachineSnapshotResponse>;
    /**
     * 查询授权白名单的可用配置
     */
    DescribeLicenseWhiteConfig(req: DescribeLicenseWhiteConfigRequest, cb?: (error: string, rep: DescribeLicenseWhiteConfigResponse) => void): Promise<DescribeLicenseWhiteConfigResponse>;
    /**
     * 查询恶意请求白名单列表
     */
    DescribeMaliciousRequestWhiteList(req: DescribeMaliciousRequestWhiteListRequest, cb?: (error: string, rep: DescribeMaliciousRequestWhiteListResponse) => void): Promise<DescribeMaliciousRequestWhiteListResponse>;
    /**
     * 网络攻击top5数据列表
     */
    DescribeAttackTop(req: DescribeAttackTopRequest, cb?: (error: string, rep: DescribeAttackTopResponse) => void): Promise<DescribeAttackTopResponse>;
    /**
     * 根据基线策略id查询策略详情
     */
    DescribeBaselineStrategyDetail(req: DescribeBaselineStrategyDetailRequest, cb?: (error: string, rep: DescribeBaselineStrategyDetailResponse) => void): Promise<DescribeBaselineStrategyDetailResponse>;
    /**
     * 更改基线策略设置
     */
    ModifyBaselinePolicy(req: ModifyBaselinePolicyRequest, cb?: (error: string, rep: ModifyBaselinePolicyResponse) => void): Promise<ModifyBaselinePolicyResponse>;
    /**
     * 获取Jar包详情
     */
    DescribeAssetJarInfo(req: DescribeAssetJarInfoRequest, cb?: (error: string, rep: DescribeAssetJarInfoResponse) => void): Promise<DescribeAssetJarInfoResponse>;
    /**
     * 删除基线策略配置
     */
    DeleteBaselinePolicy(req: DeleteBaselinePolicyRequest, cb?: (error: string, rep: DeleteBaselinePolicyResponse) => void): Promise<DeleteBaselinePolicyResponse>;
    /**
     * DescribeScanTaskStatus 查询机器扫描状态列表用于过滤筛选
     */
    DescribeScanTaskStatus(req: DescribeScanTaskStatusRequest, cb?: (error: string, rep: DescribeScanTaskStatusResponse) => void): Promise<DescribeScanTaskStatusResponse>;
    /**
     * CreateLicenseOrder 该接口可以创建专业版/旗舰版订单
支持预付费后付费创建
后付费订单直接创建成功
预付费订单仅下单不支付,需要调用计费支付接口进行支付
     */
    CreateLicenseOrder(req: CreateLicenseOrderRequest, cb?: (error: string, rep: CreateLicenseOrderResponse) => void): Promise<CreateLicenseOrderResponse>;
    /**
     * 大屏可视化主机区域选项列表
     */
    DescribeScreenMachineRegions(req?: DescribeScreenMachineRegionsRequest, cb?: (error: string, rep: DescribeScreenMachineRegionsResponse) => void): Promise<DescribeScreenMachineRegionsResponse>;
    /**
     * 获取忽略规则主机列表
     */
    DescribeBaselineHostIgnoreList(req: DescribeBaselineHostIgnoreListRequest, cb?: (error: string, rep: DescribeBaselineHostIgnoreListResponse) => void): Promise<DescribeBaselineHostIgnoreListResponse>;
    /**
     * 新增或编辑标签
     */
    EditTags(req: EditTagsRequest, cb?: (error: string, rep: EditTagsResponse) => void): Promise<EditTagsResponse>;
    /**
     * 查询漏洞防御白名单
     */
    DescribeRaspRules(req: DescribeRaspRulesRequest, cb?: (error: string, rep: DescribeRaspRulesResponse) => void): Promise<DescribeRaspRulesResponse>;
    /**
     * 创建日志下载任务
     */
    CreateLogExport(req: CreateLogExportRequest, cb?: (error: string, rep: CreateLogExportResponse) => void): Promise<CreateLogExportResponse>;
    /**
     * 获取异地登录白名单合并后列表
     */
    DescribeLoginWhiteCombinedList(req: DescribeLoginWhiteCombinedListRequest, cb?: (error: string, rep: DescribeLoginWhiteCombinedListResponse) => void): Promise<DescribeLoginWhiteCombinedListResponse>;
    /**
     * 本接口 (ExportNonlocalLoginPlaces) 用于导出异地登录事件记录CSV文件。
     */
    ExportNonlocalLoginPlaces(req: ExportNonlocalLoginPlacesRequest, cb?: (error: string, rep: ExportNonlocalLoginPlacesResponse) => void): Promise<ExportNonlocalLoginPlacesResponse>;
    /**
     * 获取待处理风险文件数+影响服务器数+是否试用检测+最近检测时间
     */
    DescribeServersAndRiskAndFirstInfo(req?: DescribeServersAndRiskAndFirstInfoRequest, cb?: (error: string, rep: DescribeServersAndRiskAndFirstInfoResponse) => void): Promise<DescribeServersAndRiskAndFirstInfoResponse>;
    /**
     * 获取软件关联进程列表
     */
    DescribeAssetAppProcessList(req: DescribeAssetAppProcessListRequest, cb?: (error: string, rep: DescribeAssetAppProcessListResponse) => void): Promise<DescribeAssetAppProcessListResponse>;
    /**
     * 同步机器信息
     */
    SyncMachines(req: SyncMachinesRequest, cb?: (error: string, rep: SyncMachinesResponse) => void): Promise<SyncMachinesResponse>;
    /**
     * 获取反弹Shell列表
     */
    DescribeReverseShellEvents(req: DescribeReverseShellEventsRequest, cb?: (error: string, rep: DescribeReverseShellEventsResponse) => void): Promise<DescribeReverseShellEventsResponse>;
    /**
     * 查询定时扫描配置
     */
    DescribeMalwareTimingScanSetting(req?: DescribeMalwareTimingScanSettingRequest, cb?: (error: string, rep: DescribeMalwareTimingScanSettingResponse) => void): Promise<DescribeMalwareTimingScanSettingResponse>;
    /**
     * 删除恶意请求策略
     */
    DeleteRiskDnsPolicy(req: DeleteRiskDnsPolicyRequest, cb?: (error: string, rep: DeleteRiskDnsPolicyResponse) => void): Promise<DeleteRiskDnsPolicyResponse>;
    /**
     * 导出漏洞信息，包括影响主机列表，组件信息
     */
    ExportVulInfo(req: ExportVulInfoRequest, cb?: (error: string, rep: ExportVulInfoResponse) => void): Promise<ExportVulInfoResponse>;
    /**
     * 导出资产管理数据库列表
     */
    ExportAssetDatabaseList(req: ExportAssetDatabaseListRequest, cb?: (error: string, rep: ExportAssetDatabaseListResponse) => void): Promise<ExportAssetDatabaseListResponse>;
    /**
     * 导出核心文件监控规则
     */
    ExportFileTamperRules(req: ExportFileTamperRulesRequest, cb?: (error: string, rep: ExportFileTamperRulesResponse) => void): Promise<ExportFileTamperRulesResponse>;
    /**
     * 修改告警设置
     */
    ModifyWarningSetting(req: ModifyWarningSettingRequest, cb?: (error: string, rep: ModifyWarningSettingResponse) => void): Promise<ModifyWarningSettingResponse>;
    /**
     * 大屏可视化主机安全防护引擎介绍
     */
    DescribeScreenProtectionCnt(req?: DescribeScreenProtectionCntRequest, cb?: (error: string, rep: DescribeScreenProtectionCntResponse) => void): Promise<DescribeScreenProtectionCntResponse>;
    /**
     * 新增或修改高危命令策略
     */
    ModifyBashPolicy(req: ModifyBashPolicyRequest, cb?: (error: string, rep: ModifyBashPolicyResponse) => void): Promise<ModifyBashPolicyResponse>;
    /**
     * 查询安全通知信息
     */
    DescribeSafeInfo(req?: DescribeSafeInfoRequest, cb?: (error: string, rep: DescribeSafeInfoResponse) => void): Promise<DescribeSafeInfoResponse>;
    /**
     * 导出Web站点列表
     */
    ExportAssetWebLocationList(req: ExportAssetWebLocationListRequest, cb?: (error: string, rep: ExportAssetWebLocationListResponse) => void): Promise<ExportAssetWebLocationListResponse>;
    /**
     * 查询主机快照备份列表
     */
    DescribeRansomDefenseBackupList(req: DescribeRansomDefenseBackupListRequest, cb?: (error: string, rep: DescribeRansomDefenseBackupListResponse) => void): Promise<DescribeRansomDefenseBackupListResponse>;
    /**
     * 获取基线检测概览
     */
    DescribeBaselineDetectOverview(req: DescribeBaselineDetectOverviewRequest, cb?: (error: string, rep: DescribeBaselineDetectOverviewResponse) => void): Promise<DescribeBaselineDetectOverviewResponse>;
    /**
     * 获取基线检测项的列表
     */
    DescribeBaselineItemDetectList(req: DescribeBaselineItemDetectListRequest, cb?: (error: string, rep: DescribeBaselineItemDetectListResponse) => void): Promise<DescribeBaselineItemDetectListResponse>;
    /**
     * 本接口 (DescribeMachinesSimple) 用于获取主机列表。
     */
    DescribeMachinesSimple(req: DescribeMachinesSimpleRequest, cb?: (error: string, rep: DescribeMachinesSimpleResponse) => void): Promise<DescribeMachinesSimpleResponse>;
    /**
     * 获取策略详情
     */
    DescribeRansomDefenseStrategyDetail(req: DescribeRansomDefenseStrategyDetailRequest, cb?: (error: string, rep: DescribeRansomDefenseStrategyDetailResponse) => void): Promise<DescribeRansomDefenseStrategyDetailResponse>;
    /**
     * 导出本次漏洞检测Excel
     */
    ExportVulDetectionExcel(req: ExportVulDetectionExcelRequest, cb?: (error: string, rep: ExportVulDetectionExcelResponse) => void): Promise<ExportVulDetectionExcelResponse>;
    /**
     * 获取阻断地域
     */
    DescribeBanRegions(req: DescribeBanRegionsRequest, cb?: (error: string, rep: DescribeBanRegionsResponse) => void): Promise<DescribeBanRegionsResponse>;
    /**
     * 删除恶意请求白名单
     */
    DeleteMaliciousRequestWhiteList(req: DeleteMaliciousRequestWhiteListRequest, cb?: (error: string, rep: DeleteMaliciousRequestWhiteListResponse) => void): Promise<DeleteMaliciousRequestWhiteListResponse>;
    /**
     * 添加阻断白名单列表
     */
    CreateBanWhiteList(req: CreateBanWhiteListRequest, cb?: (error: string, rep: CreateBanWhiteListResponse) => void): Promise<CreateBanWhiteListResponse>;
    /**
     * 漏洞详情，带CVSS版本
     */
    DescribeVulInfoCvss(req: DescribeVulInfoCvssRequest, cb?: (error: string, rep: DescribeVulInfoCvssResponse) => void): Promise<DescribeVulInfoCvssResponse>;
    /**
     * 用于查询用户自定义配置
     */
    DescribeUsersConfig(req: DescribeUsersConfigRequest, cb?: (error: string, rep: DescribeUsersConfigResponse) => void): Promise<DescribeUsersConfigResponse>;
    /**
     * 修改日志投递状态信息
     */
    ModifyLogKafkaState(req: ModifyLogKafkaStateRequest, cb?: (error: string, rep: ModifyLogKafkaStateResponse) => void): Promise<ModifyLogKafkaStateResponse>;
    /**
     * 导出java内存马事件列表
     */
    ExportJavaMemShells(req: ExportJavaMemShellsRequest, cb?: (error: string, rep: ExportJavaMemShellsResponse) => void): Promise<ExportJavaMemShellsResponse>;
    /**
     * 获取基线弱口令列表
     */
    DescribeBaselineWeakPasswordList(req: DescribeBaselineWeakPasswordListRequest, cb?: (error: string, rep: DescribeBaselineWeakPasswordListResponse) => void): Promise<DescribeBaselineWeakPasswordListResponse>;
    /**
     * 查询推荐购买防护核数
     */
    DescribeRecommendedProtectCpu(req?: DescribeRecommendedProtectCpuRequest, cb?: (error: string, rep: DescribeRecommendedProtectCpuResponse) => void): Promise<DescribeRecommendedProtectCpuResponse>;
    /**
     * 获取基线检测项信息
     */
    DescribeBaselineItemInfo(req: DescribeBaselineItemInfoRequest, cb?: (error: string, rep: DescribeBaselineItemInfoResponse) => void): Promise<DescribeBaselineItemInfoResponse>;
    /**
     * 查询资产管理启动服务列表
     */
    DescribeAssetInitServiceList(req: DescribeAssetInitServiceListRequest, cb?: (error: string, rep: DescribeAssetInitServiceListResponse) => void): Promise<DescribeAssetInitServiceListResponse>;
    /**
     * 导出网络攻击事件
     */
    ExportAttackEvents(req: ExportAttackEventsRequest, cb?: (error: string, rep: ExportAttackEventsResponse) => void): Promise<ExportAttackEventsResponse>;
    /**
     * 获取基线修复列表
     */
    DescribeBaselineFixList(req: DescribeBaselineFixListRequest, cb?: (error: string, rep: DescribeBaselineFixListResponse) => void): Promise<DescribeBaselineFixListResponse>;
    /**
     * 查询告警机器范围配置
     */
    DescribeWarningHostConfig(req: DescribeWarningHostConfigRequest, cb?: (error: string, rep: DescribeWarningHostConfigResponse) => void): Promise<DescribeWarningHostConfigResponse>;
    /**
     * 修改机器清理配置
     */
    ModifyMachineAutoClearConfig(req: ModifyMachineAutoClearConfigRequest, cb?: (error: string, rep: ModifyMachineAutoClearConfigResponse) => void): Promise<ModifyMachineAutoClearConfigResponse>;
    /**
     * 漏洞管理-重新检测接口
     */
    ScanVulAgain(req: ScanVulAgainRequest, cb?: (error: string, rep: ScanVulAgainResponse) => void): Promise<ScanVulAgainResponse>;
    /**
     * 网站防篡改-查询网页防篡改服务器购买信息及服务器信息
     */
    DescribeWebPageServiceInfo(req?: DescribeWebPageServiceInfoRequest, cb?: (error: string, rep: DescribeWebPageServiceInfoResponse) => void): Promise<DescribeWebPageServiceInfoResponse>;
    /**
     * 获取漏洞防御事件列表
     */
    DescribeVulDefenceEvent(req: DescribeVulDefenceEventRequest, cb?: (error: string, rep: DescribeVulDefenceEventResponse) => void): Promise<DescribeVulDefenceEventResponse>;
    /**
     * 更改恶意请求策略状态
     */
    ModifyRiskDnsPolicyStatus(req: ModifyRiskDnsPolicyStatusRequest, cb?: (error: string, rep: ModifyRiskDnsPolicyStatusResponse) => void): Promise<ModifyRiskDnsPolicyStatusResponse>;
    /**
     * 获取漏洞列表数据
     */
    DescribeVulList(req: DescribeVulListRequest, cb?: (error: string, rep: DescribeVulListResponse) => void): Promise<DescribeVulListResponse>;
    /**
     * 修改企微机器人规则状态
     */
    ModifyWebHookRuleStatus(req: ModifyWebHookRuleStatusRequest, cb?: (error: string, rep: ModifyWebHookRuleStatusResponse) => void): Promise<ModifyWebHookRuleStatusResponse>;
    /**
     * 导出基线列表
     */
    ExportBaselineList(req: ExportBaselineListRequest, cb?: (error: string, rep: ExportBaselineListResponse) => void): Promise<ExportBaselineListResponse>;
    /**
     * 查询某个监控规则的详情
     */
    DescribeFileTamperRuleInfo(req: DescribeFileTamperRuleInfoRequest, cb?: (error: string, rep: DescribeFileTamperRuleInfoResponse) => void): Promise<DescribeFileTamperRuleInfoResponse>;
    /**
     * 基线检测与基线重新检测接口
     */
    ScanBaseline(req: ScanBaselineRequest, cb?: (error: string, rep: ScanBaselineResponse) => void): Promise<ScanBaselineResponse>;
    /**
     * 修改日志存储配置
     */
    ModifyLogStorageConfig(req: ModifyLogStorageConfigRequest, cb?: (error: string, rep: ModifyLogStorageConfigResponse) => void): Promise<ModifyLogStorageConfigResponse>;
    /**
     * 编辑、新增核心文件监控规则
     */
    ModifyFileTamperRule(req: ModifyFileTamperRuleRequest, cb?: (error: string, rep: ModifyFileTamperRuleResponse) => void): Promise<ModifyFileTamperRuleResponse>;
    /**
     * 获取木马文件下载地址
     */
    DescribeMalwareFile(req: DescribeMalwareFileRequest, cb?: (error: string, rep: DescribeMalwareFileResponse) => void): Promise<DescribeMalwareFileResponse>;
    /**
     * 获取恶意请求事件列表
     */
    DescribeRiskDnsEventList(req: DescribeRiskDnsEventListRequest, cb?: (error: string, rep: DescribeRiskDnsEventListResponse) => void): Promise<DescribeRiskDnsEventListResponse>;
    /**
     * 根据Ids删除本地提权
     */
    DeletePrivilegeEvents(req: DeletePrivilegeEventsRequest, cb?: (error: string, rep: DeletePrivilegeEventsResponse) => void): Promise<DeletePrivilegeEventsResponse>;
    /**
     * 查询告警策略
     */
    DescribeWebHookPolicy(req: DescribeWebHookPolicyRequest, cb?: (error: string, rep: DescribeWebHookPolicyResponse) => void): Promise<DescribeWebHookPolicyResponse>;
    /**
     * 修改防勒索事件状态
     */
    ModifyRansomDefenseEventsStatus(req: ModifyRansomDefenseEventsStatusRequest, cb?: (error: string, rep: ModifyRansomDefenseEventsStatusResponse) => void): Promise<ModifyRansomDefenseEventsStatusResponse>;
    /**
     * 查询资产管理计划任务列表
     */
    DescribeAssetPlanTaskList(req: DescribeAssetPlanTaskListRequest, cb?: (error: string, rep: DescribeAssetPlanTaskListResponse) => void): Promise<DescribeAssetPlanTaskListResponse>;
    /**
     * 按分页形式展示网络攻击检测事件列表
     */
    DescribeAttackEvents(req: DescribeAttackEventsRequest, cb?: (error: string, rep: DescribeAttackEventsResponse) => void): Promise<DescribeAttackEventsResponse>;
    /**
     * 获取资产管理Web应用插件列表
     */
    DescribeAssetWebAppPluginList(req: DescribeAssetWebAppPluginListRequest, cb?: (error: string, rep: DescribeAssetWebAppPluginListResponse) => void): Promise<DescribeAssetWebAppPluginListResponse>;
    /**
     * 查询合并后白名单机器列表
     */
    DescribeLoginWhiteHostList(req: DescribeLoginWhiteHostListRequest, cb?: (error: string, rep: DescribeLoginWhiteHostListResponse) => void): Promise<DescribeLoginWhiteHostListResponse>;
    /**
     * 获取历史搜索记录
     */
    DescribeSearchLogs(req?: DescribeSearchLogsRequest, cb?: (error: string, rep: DescribeSearchLogsResponse) => void): Promise<DescribeSearchLogsResponse>;
    /**
     * 根据策略id查询基线检测项TOP
     */
    DescribeBaselineTop(req: DescribeBaselineTopRequest, cb?: (error: string, rep: DescribeBaselineTopResponse) => void): Promise<DescribeBaselineTopResponse>;
    /**
     * 日志快速分析统计
     */
    DescribeFastAnalysis(req: DescribeFastAnalysisRequest, cb?: (error: string, rep: DescribeFastAnalysisResponse) => void): Promise<DescribeFastAnalysisResponse>;
    /**
     * 查询主机安全授权试用报告(仅限控制台申领的)
     */
    DescribeTrialReport(req?: DescribeTrialReportRequest, cb?: (error: string, rep: DescribeTrialReportResponse) => void): Promise<DescribeTrialReportResponse>;
    /**
     * 此接口（DescribeUsualLoginPlaces）用于查询常用登录地。
     */
    DescribeUsualLoginPlaces(req: DescribeUsualLoginPlacesRequest, cb?: (error: string, rep: DescribeUsualLoginPlacesResponse) => void): Promise<DescribeUsualLoginPlacesResponse>;
    /**
     * 导出核心文件事件
     */
    ExportFileTamperEvents(req: ExportFileTamperEventsRequest, cb?: (error: string, rep: ExportFileTamperEventsResponse) => void): Promise<ExportFileTamperEventsResponse>;
    /**
     * 批量添加异地登录白名单
     */
    AddLoginWhiteLists(req: AddLoginWhiteListsRequest, cb?: (error: string, rep: AddLoginWhiteListsResponse) => void): Promise<AddLoginWhiteListsResponse>;
    /**
     * 导出网页防篡改防护目录列表
     */
    ExportProtectDirList(req: ExportProtectDirListRequest, cb?: (error: string, rep: ExportProtectDirListResponse) => void): Promise<ExportProtectDirListResponse>;
    /**
     * 用于异步导出数据量大的日志文件
     */
    ExportTasks(req: ExportTasksRequest, cb?: (error: string, rep: ExportTasksResponse) => void): Promise<ExportTasksResponse>;
    /**
     * 根据检测项id导出忽略检测项影响主机列表
     */
    ExportIgnoreRuleEffectHostList(req: ExportIgnoreRuleEffectHostListRequest, cb?: (error: string, rep: ExportIgnoreRuleEffectHostListResponse) => void): Promise<ExportIgnoreRuleEffectHostListResponse>;
    /**
     * 查询恶意请求详情
     */
    DescribeRiskDnsInfo(req: DescribeRiskDnsInfoRequest, cb?: (error: string, rep: DescribeRiskDnsInfoResponse) => void): Promise<DescribeRiskDnsInfoResponse>;
    /**
     * 导出防勒索事件列表
     */
    ExportRansomDefenseEventsList(req: ExportRansomDefenseEventsListRequest, cb?: (error: string, rep: ExportRansomDefenseEventsListResponse) => void): Promise<ExportRansomDefenseEventsListResponse>;
    /**
     * 新增或修改企微机器人规则
     */
    ModifyWebHookRule(req: ModifyWebHookRuleRequest, cb?: (error: string, rep: ModifyWebHookRuleResponse) => void): Promise<ModifyWebHookRuleResponse>;
    /**
     * 获取当前异常插件数
     */
    DescribeVulDefencePluginExceptionCount(req?: DescribeVulDefencePluginExceptionCountRequest, cb?: (error: string, rep: DescribeVulDefencePluginExceptionCountResponse) => void): Promise<DescribeVulDefencePluginExceptionCountResponse>;
    /**
     * 根据检测项id与筛选条件查询忽略检测项影响主机列表信息
     */
    DescribeIgnoreRuleEffectHostList(req: DescribeIgnoreRuleEffectHostListRequest, cb?: (error: string, rep: DescribeIgnoreRuleEffectHostListResponse) => void): Promise<DescribeIgnoreRuleEffectHostListResponse>;
    /**
     * 查询应用列表
     */
    DescribeAssetAppList(req: DescribeAssetAppListRequest, cb?: (error: string, rep: DescribeAssetAppListResponse) => void): Promise<DescribeAssetAppListResponse>;
    /**
     * 获取kafka投递信息
     */
    DescribeLogKafkaDeliverInfo(req?: DescribeLogKafkaDeliverInfoRequest, cb?: (error: string, rep: DescribeLogKafkaDeliverInfoResponse) => void): Promise<DescribeLogKafkaDeliverInfoResponse>;
    /**
     * 获取ES字段聚合结果
     */
    DescribeESAggregations(req: DescribeESAggregationsRequest, cb?: (error: string, rep: DescribeESAggregationsResponse) => void): Promise<DescribeESAggregationsResponse>;
    /**
     * 添加历史搜索记录
     */
    CreateSearchLog(req: CreateSearchLogRequest, cb?: (error: string, rep: CreateSearchLogResponse) => void): Promise<CreateSearchLogResponse>;
    /**
     * 删除企微机器人规则
     */
    DeleteWebHookRule(req: DeleteWebHookRuleRequest, cb?: (error: string, rep: DeleteWebHookRuleResponse) => void): Promise<DeleteWebHookRuleResponse>;
    /**
     * 添加检索模板
     */
    CreateSearchTemplate(req: CreateSearchTemplateRequest, cb?: (error: string, rep: CreateSearchTemplateResponse) => void): Promise<CreateSearchTemplateResponse>;
    /**
     * 导出已忽略基线检测项信息
     */
    ExportIgnoreBaselineRule(req: ExportIgnoreBaselineRuleRequest, cb?: (error: string, rep: ExportIgnoreBaselineRuleResponse) => void): Promise<ExportIgnoreBaselineRuleResponse>;
    /**
     * 本地提权信息详情
     */
    DescribePrivilegeEventInfo(req: DescribePrivilegeEventInfoRequest, cb?: (error: string, rep: DescribePrivilegeEventInfoResponse) => void): Promise<DescribePrivilegeEventInfoResponse>;
    /**
     * 打开入侵检测-恶意文件检测,弹出风险预警内容
     */
    DescribeMalwareRiskWarning(req?: DescribeMalwareRiskWarningRequest, cb?: (error: string, rep: DescribeMalwareRiskWarningResponse) => void): Promise<DescribeMalwareRiskWarningResponse>;
    /**
     * 查询基线默认策略列表信息
     */
    DescribeBaselineDefaultStrategyList(req?: DescribeBaselineDefaultStrategyListRequest, cb?: (error: string, rep: DescribeBaselineDefaultStrategyListResponse) => void): Promise<DescribeBaselineDefaultStrategyListResponse>;
    /**
     * 提交漏洞修护
     */
    CreateVulFix(req: CreateVulFixRequest, cb?: (error: string, rep: CreateVulFixResponse) => void): Promise<CreateVulFixResponse>;
    /**
     * 获取指定标签关联的服务器信息
     */
    DescribeTagMachines(req: DescribeTagMachinesRequest, cb?: (error: string, rep: DescribeTagMachinesResponse) => void): Promise<DescribeTagMachinesResponse>;
    /**
     * 新增或修改本地提权规则（支持多服务器选择）
     */
    EditPrivilegeRules(req: EditPrivilegeRulesRequest, cb?: (error: string, rep: EditPrivilegeRulesResponse) => void): Promise<EditPrivilegeRulesResponse>;
    /**
     * 获取异常进程列表
     */
    DescribeRiskProcessEvents(req: DescribeRiskProcessEventsRequest, cb?: (error: string, rep: DescribeRiskProcessEventsResponse) => void): Promise<DescribeRiskProcessEventsResponse>;
    /**
     * 专家服务-可用订单详情
     */
    DescribeAvailableExpertServiceDetail(req?: DescribeAvailableExpertServiceDetailRequest, cb?: (error: string, rep: DescribeAvailableExpertServiceDetailResponse) => void): Promise<DescribeAvailableExpertServiceDetailResponse>;
    /**
     * 根据事件表名和id查询告警事件详情
     */
    DescribeEventByTable(req: DescribeEventByTableRequest, cb?: (error: string, rep: DescribeEventByTableResponse) => void): Promise<DescribeEventByTableResponse>;
    /**
     * 对授权管理-订单列表内已过期的订单进行删除.(删除后的订单不在统计范畴内)
     */
    DeleteLicenseRecord(req: DeleteLicenseRecordRequest, cb?: (error: string, rep: DeleteLicenseRecordResponse) => void): Promise<DeleteLicenseRecordResponse>;
    /**
     * 本接口 (DescribeSecurityDynamics) 用于获取安全事件动态消息数据。
     */
    DescribeSecurityDynamics(req: DescribeSecurityDynamicsRequest, cb?: (error: string, rep: DescribeSecurityDynamicsResponse) => void): Promise<DescribeSecurityDynamicsResponse>;
    /**
     * 获取安全事件统计
     */
    DescribeSecurityEventStat(req: DescribeSecurityEventStatRequest, cb?: (error: string, rep: DescribeSecurityEventStatResponse) => void): Promise<DescribeSecurityEventStatResponse>;
    /**
     * 获取所有数据库数量
     */
    DescribeAssetDatabaseCount(req: DescribeAssetDatabaseCountRequest, cb?: (error: string, rep: DescribeAssetDatabaseCountResponse) => void): Promise<DescribeAssetDatabaseCountResponse>;
    /**
     * 获取异地登录白名单列表
     */
    DescribeLoginWhiteList(req: DescribeLoginWhiteListRequest, cb?: (error: string, rep: DescribeLoginWhiteListResponse) => void): Promise<DescribeLoginWhiteListResponse>;
    /**
     * 获取日志直方图信息
     */
    DescribeLogHistogram(req: DescribeLogHistogramRequest, cb?: (error: string, rep: DescribeLogHistogramResponse) => void): Promise<DescribeLogHistogramResponse>;
    /**
     * 查询主机相关核心文件监控规则列表
     */
    DescribeMachineFileTamperRules(req: DescribeMachineFileTamperRulesRequest, cb?: (error: string, rep: DescribeMachineFileTamperRulesResponse) => void): Promise<DescribeMachineFileTamperRulesResponse>;
    /**
     * 编辑《主机安全-按量计费》授权订单
     */
    ModifyLicenseOrder(req: ModifyLicenseOrderRequest, cb?: (error: string, rep: ModifyLicenseOrderResponse) => void): Promise<ModifyLicenseOrderResponse>;
    /**
     * 获取公网接入代理安装命令
     */
    DescribePublicProxyInstallCommand(req: DescribePublicProxyInstallCommandRequest, cb?: (error: string, rep: DescribePublicProxyInstallCommandResponse) => void): Promise<DescribePublicProxyInstallCommandResponse>;
    /**
     * 漏洞数量等级分布统计
     */
    DescribeVulLevelCount(req: DescribeVulLevelCountRequest, cb?: (error: string, rep: DescribeVulLevelCountResponse) => void): Promise<DescribeVulLevelCountResponse>;
    /**
     * 导出账号列表
     */
    ExportAssetUserList(req: ExportAssetUserListRequest, cb?: (error: string, rep: ExportAssetUserListResponse) => void): Promise<ExportAssetUserListResponse>;
    /**
     * 用于网页防篡改获取区域主机列表。
     */
    DescribeMachineList(req: DescribeMachineListRequest, cb?: (error: string, rep: DescribeMachineListResponse) => void): Promise<DescribeMachineListResponse>;
    /**
     * 获取异常登录列表
     */
    DescribeHostLoginList(req: DescribeHostLoginListRequest, cb?: (error: string, rep: DescribeHostLoginListResponse) => void): Promise<DescribeHostLoginListResponse>;
    /**
     * 根据基线策略id更新策略信息
     */
    UpdateBaselineStrategy(req: UpdateBaselineStrategyRequest, cb?: (error: string, rep: UpdateBaselineStrategyResponse) => void): Promise<UpdateBaselineStrategyResponse>;
    /**
     * 产品变动切换到了\\n切换到 AddVulIgnoreRule / ModifyVulIgnoreRule  CancelVulIgnoreRule\\n相关接口

取消漏洞忽略
     */
    CancelIgnoreVul(req: CancelIgnoreVulRequest, cb?: (error: string, rep: CancelIgnoreVulResponse) => void): Promise<CancelIgnoreVulResponse>;
    /**
     * 入侵检测获取木马列表
     */
    DescribeMalWareList(req: DescribeMalWareListRequest, cb?: (error: string, rep: DescribeMalWareListResponse) => void): Promise<DescribeMalWareListResponse>;
    /**
     * 本接口 (DescribeOpenPortStatistics) 用于获取端口统计列表。
     */
    DescribeOpenPortStatistics(req: DescribeOpenPortStatisticsRequest, cb?: (error: string, rep: DescribeOpenPortStatisticsResponse) => void): Promise<DescribeOpenPortStatisticsResponse>;
    /**
     * 修改爆破阻断模式
     */
    ModifyBanMode(req: ModifyBanModeRequest, cb?: (error: string, rep: ModifyBanModeResponse) => void): Promise<ModifyBanModeResponse>;
    /**
     * 获取专线agent安装命令，包含token
     */
    DescribeDirectConnectInstallCommand(req: DescribeDirectConnectInstallCommandRequest, cb?: (error: string, rep: DescribeDirectConnectInstallCommandResponse) => void): Promise<DescribeDirectConnectInstallCommandResponse>;
    /**
     * 获取企微机器人规则列表
     */
    DescribeWebHookRules(req: DescribeWebHookRulesRequest, cb?: (error: string, rep: DescribeWebHookRulesResponse) => void): Promise<DescribeWebHookRulesResponse>;
    /**
     * 获取基线策略列表
     */
    DescribeBaselinePolicyList(req: DescribeBaselinePolicyListRequest, cb?: (error: string, rep: DescribeBaselinePolicyListResponse) => void): Promise<DescribeBaselinePolicyListResponse>;
    /**
     * 获取所有Web应用数量
     */
    DescribeAssetWebAppCount(req: DescribeAssetWebAppCountRequest, cb?: (error: string, rep: DescribeAssetWebAppCountResponse) => void): Promise<DescribeAssetWebAppCountResponse>;
    /**
     * 修改指定日志类别投递配置、开关
     */
    ModifyLogKafkaDeliverType(req: ModifyLogKafkaDeliverTypeRequest, cb?: (error: string, rep: ModifyLogKafkaDeliverTypeResponse) => void): Promise<ModifyLogKafkaDeliverTypeResponse>;
    /**
     * 查询高危命令事件详情
     */
    DescribeBashEventsInfo(req: DescribeBashEventsInfoRequest, cb?: (error: string, rep: DescribeBashEventsInfoResponse) => void): Promise<DescribeBashEventsInfoResponse>;
    /**
     * 查询告警点id列表
     */
    DescribeAlarmVertexId(req: DescribeAlarmVertexIdRequest, cb?: (error: string, rep: DescribeAlarmVertexIdResponse) => void): Promise<DescribeAlarmVertexIdResponse>;
    /**
     * CveId查询漏洞详情
     */
    DescribeVulCveIdInfo(req: DescribeVulCveIdInfoRequest, cb?: (error: string, rep: DescribeVulCveIdInfoResponse) => void): Promise<DescribeVulCveIdInfoResponse>;
    /**
     * 导出主机快照备份列表
     */
    ExportRansomDefenseBackupList(req: ExportRansomDefenseBackupListRequest, cb?: (error: string, rep: ExportRansomDefenseBackupListResponse) => void): Promise<ExportRansomDefenseBackupListResponse>;
    /**
     * 网络攻击数据统计
     */
    DescribeAttackStatistics(req?: DescribeAttackStatisticsRequest, cb?: (error: string, rep: DescribeAttackStatisticsResponse) => void): Promise<DescribeAttackStatisticsResponse>;
    /**
     * 获取主机账号Key列表
     */
    DescribeAssetUserKeyList(req: DescribeAssetUserKeyListRequest, cb?: (error: string, rep: DescribeAssetUserKeyListResponse) => void): Promise<DescribeAssetUserKeyListResponse>;
    /**
     * 查询木马扫描进度
     */
    DescribeScanMalwareSchedule(req?: DescribeScanMalwareScheduleRequest, cb?: (error: string, rep: DescribeScanMalwareScheduleResponse) => void): Promise<DescribeScanMalwareScheduleResponse>;
    /**
     * 编辑木马白名单
     */
    ModifyMalwareWhiteList(req: ModifyMalwareWhiteListRequest, cb?: (error: string, rep: ModifyMalwareWhiteListResponse) => void): Promise<ModifyMalwareWhiteListResponse>;
    /**
     * 获取企微机器人规则详情
     */
    DescribeWebHookRule(req: DescribeWebHookRuleRequest, cb?: (error: string, rep: DescribeWebHookRuleResponse) => void): Promise<DescribeWebHookRuleResponse>;
    /**
     * 修复失败时单独对某一个主机修复漏洞
     */
    RetryVulFix(req: RetryVulFixRequest, cb?: (error: string, rep: RetryVulFixResponse) => void): Promise<RetryVulFixResponse>;
    /**
     * 删除高危命令规则
     */
    DeleteBashRules(req: DeleteBashRulesRequest, cb?: (error: string, rep: DeleteBashRulesResponse) => void): Promise<DeleteBashRulesResponse>;
    /**
     * 添加漏洞防御白名单
     */
    ModifyRaspRules(req: ModifyRaspRulesRequest, cb?: (error: string, rep: ModifyRaspRulesResponse) => void): Promise<ModifyRaspRulesResponse>;
    /**
     * 快照创建失败时可以重试创建快照并且自动进行漏洞修复
     */
    RetryCreateSnapshot(req: RetryCreateSnapshotRequest, cb?: (error: string, rep: RetryCreateSnapshotResponse) => void): Promise<RetryCreateSnapshotResponse>;
    /**
     * 获取主机最近趋势情况
     */
    DescribeAssetRecentMachineInfo(req: DescribeAssetRecentMachineInfoRequest, cb?: (error: string, rep: DescribeAssetRecentMachineInfoResponse) => void): Promise<DescribeAssetRecentMachineInfoResponse>;
    /**
     * 漏洞影响组件列表
     */
    DescribeVulEffectModules(req: DescribeVulEffectModulesRequest, cb?: (error: string, rep: DescribeVulEffectModulesResponse) => void): Promise<DescribeVulEffectModulesResponse>;
    /**
     * 漏洞top统计
     */
    DescribeVulTop(req: DescribeVulTopRequest, cb?: (error: string, rep: DescribeVulTopResponse) => void): Promise<DescribeVulTopResponse>;
    /**
     * 获取爆破破解规则
     */
    DescribeBruteAttackRules(req?: DescribeBruteAttackRulesRequest, cb?: (error: string, rep: DescribeBruteAttackRulesResponse) => void): Promise<DescribeBruteAttackRulesResponse>;
    /**
     * 查询资产管理环境变量列表
     */
    DescribeAssetEnvList(req: DescribeAssetEnvListRequest, cb?: (error: string, rep: DescribeAssetEnvListResponse) => void): Promise<DescribeAssetEnvListResponse>;
    /**
     * 获取安全概览相关事件统计数据接口
     */
    DescribeSecurityEventsCnt(req?: DescribeSecurityEventsCntRequest, cb?: (error: string, rep: DescribeSecurityEventsCntResponse) => void): Promise<DescribeSecurityEventsCntResponse>;
    /**
     * 删除木马白名单
     */
    DeleteMalwareWhiteList(req: DeleteMalwareWhiteListRequest, cb?: (error: string, rep: DeleteMalwareWhiteListResponse) => void): Promise<DeleteMalwareWhiteListResponse>;
    /**
     * 同步基线检测进度概要
     */
    SyncBaselineDetectSummary(req: SyncBaselineDetectSummaryRequest, cb?: (error: string, rep: SyncBaselineDetectSummaryResponse) => void): Promise<SyncBaselineDetectSummaryResponse>;
    /**
     * 查询授权绑定任务的进度
     */
    DescribeLicenseBindSchedule(req: DescribeLicenseBindScheduleRequest, cb?: (error: string, rep: DescribeLicenseBindScheduleResponse) => void): Promise<DescribeLicenseBindScheduleResponse>;
    /**
     * 编辑网络攻击白名单
     */
    ModifyNetAttackWhiteList(req: ModifyNetAttackWhiteListRequest, cb?: (error: string, rep: ModifyNetAttackWhiteListResponse) => void): Promise<ModifyNetAttackWhiteListResponse>;
    /**
     * 网站防篡改-查询动态防护信息
     */
    DescribeWebPageProtectStat(req?: DescribeWebPageProtectStatRequest, cb?: (error: string, rep: DescribeWebPageProtectStatResponse) => void): Promise<DescribeWebPageProtectStatResponse>;
    /**
     * 本接口 (DescribeHistoryAccounts) 用于获取帐号变更历史列表数据。
     */
    DescribeHistoryAccounts(req: DescribeHistoryAccountsRequest, cb?: (error: string, rep: DescribeHistoryAccountsResponse) => void): Promise<DescribeHistoryAccountsResponse>;
    /**
     * 添加网站防护服务器
     */
    CreateProtectServer(req: CreateProtectServerRequest, cb?: (error: string, rep: CreateProtectServerResponse) => void): Promise<CreateProtectServerResponse>;
    /**
     * 查询基线列表信息
     */
    DescribeBaselineList(req: DescribeBaselineListRequest, cb?: (error: string, rep: DescribeBaselineListResponse) => void): Promise<DescribeBaselineListResponse>;
    /**
     * 获取资产管理主机资源详细信息
     */
    DescribeAssetMachineDetail(req: DescribeAssetMachineDetailRequest, cb?: (error: string, rep: DescribeAssetMachineDetailResponse) => void): Promise<DescribeAssetMachineDetailResponse>;
    /**
     * 获取服务器风险top列表
     */
    DescribeVulHostTop(req: DescribeVulHostTopRequest, cb?: (error: string, rep: DescribeVulHostTopResponse) => void): Promise<DescribeVulHostTopResponse>;
    /**
     * 对订单属性编辑
     */
    ModifyOrderAttribute(req: ModifyOrderAttributeRequest, cb?: (error: string, rep: ModifyOrderAttributeResponse) => void): Promise<ModifyOrderAttributeResponse>;
    /**
     * 获取用户当前灰度配置
     */
    DescribeABTestConfig(req: DescribeABTestConfigRequest, cb?: (error: string, rep: DescribeABTestConfigResponse) => void): Promise<DescribeABTestConfigResponse>;
    /**
     * 获取资产管理Web框架列表
     */
    DescribeAssetWebFrameList(req: DescribeAssetWebFrameListRequest, cb?: (error: string, rep: DescribeAssetWebFrameListResponse) => void): Promise<DescribeAssetWebFrameListResponse>;
    /**
     * 查询资产管理内核模块列表
     */
    DescribeAssetCoreModuleList(req: DescribeAssetCoreModuleListRequest, cb?: (error: string, rep: DescribeAssetCoreModuleListResponse) => void): Promise<DescribeAssetCoreModuleListResponse>;
    /**
     * 定时扫描设置
     */
    ModifyMalwareTimingScanSettings(req: ModifyMalwareTimingScanSettingsRequest, cb?: (error: string, rep: ModifyMalwareTimingScanSettingsResponse) => void): Promise<ModifyMalwareTimingScanSettingsResponse>;
    /**
     * 添加恶意请求白名单
     */
    CreateMaliciousRequestWhiteList(req: CreateMaliciousRequestWhiteListRequest, cb?: (error: string, rep: CreateMaliciousRequestWhiteListResponse) => void): Promise<CreateMaliciousRequestWhiteListResponse>;
    /**
     * 获取所有Web服务数量
     */
    DescribeAssetWebServiceCount(req: DescribeAssetWebServiceCountRequest, cb?: (error: string, rep: DescribeAssetWebServiceCountResponse) => void): Promise<DescribeAssetWebServiceCountResponse>;
    /**
     * 更改或新增弱口令
     */
    ModifyBaselineWeakPassword(req: ModifyBaselineWeakPasswordRequest, cb?: (error: string, rep: ModifyBaselineWeakPasswordResponse) => void): Promise<ModifyBaselineWeakPasswordResponse>;
    /**
     * 大屏可视化防趋势接口
     */
    DescribeScreenDefenseTrends(req: DescribeScreenDefenseTrendsRequest, cb?: (error: string, rep: DescribeScreenDefenseTrendsResponse) => void): Promise<DescribeScreenDefenseTrendsResponse>;
    /**
     * 设置高危命令事件状态
     */
    SetBashEventsStatus(req: SetBashEventsStatusRequest, cb?: (error: string, rep: SetBashEventsStatusResponse) => void): Promise<SetBashEventsStatusResponse>;
    /**
     * 根据策略id修改策略可用状态
     */
    ChangeStrategyEnableStatus(req: ChangeStrategyEnableStatusRequest, cb?: (error: string, rep: ChangeStrategyEnableStatusResponse) => void): Promise<ChangeStrategyEnableStatusResponse>;
    /**
     * 网页防篡改防护目录列表
     */
    DescribeProtectDirList(req: DescribeProtectDirListRequest, cb?: (error: string, rep: DescribeProtectDirListResponse) => void): Promise<DescribeProtectDirListResponse>;
    /**
     * 专家服务-专家服务订单列表
     */
    DescribeExpertServiceOrderList(req: DescribeExpertServiceOrderListRequest, cb?: (error: string, rep: DescribeExpertServiceOrderListResponse) => void): Promise<DescribeExpertServiceOrderListResponse>;
    /**
     * 检查日志投递kafka连通性
     */
    CheckLogKafkaConnectionState(req: CheckLogKafkaConnectionStateRequest, cb?: (error: string, rep: CheckLogKafkaConnectionStateResponse) => void): Promise<CheckLogKafkaConnectionStateResponse>;
    /**
     * 获取各主机漏洞防御插件状态
     */
    DescribeVulDefencePluginStatus(req: DescribeVulDefencePluginStatusRequest, cb?: (error: string, rep: DescribeVulDefencePluginStatusResponse) => void): Promise<DescribeVulDefencePluginStatusResponse>;
    /**
     * 获取所有主机标签
     */
    DescribeTags(req: DescribeTagsRequest, cb?: (error: string, rep: DescribeTagsResponse) => void): Promise<DescribeTagsResponse>;
    /**
     * 获取单台主机漏洞防御插件信息
     */
    DescribeVulDefencePluginDetail(req: DescribeVulDefencePluginDetailRequest, cb?: (error: string, rep: DescribeVulDefencePluginDetailResponse) => void): Promise<DescribeVulDefencePluginDetailResponse>;
    /**
     * 更改基线检测规则
     */
    ModifyBaselineRule(req: ModifyBaselineRuleRequest, cb?: (error: string, rep: ModifyBaselineRuleResponse) => void): Promise<ModifyBaselineRuleResponse>;
    /**
     * 网站防篡改防护设置开关
     */
    ModifyWebPageProtectSwitch(req: ModifyWebPageProtectSwitchRequest, cb?: (error: string, rep: ModifyWebPageProtectSwitchResponse) => void): Promise<ModifyWebPageProtectSwitchResponse>;
    /**
     * 修改漏洞防御事件状态（修复漏洞通过其他接口实现）
     */
    ModifyVulDefenceEventStatus(req: ModifyVulDefenceEventStatusRequest, cb?: (error: string, rep: ModifyVulDefenceEventStatusResponse) => void): Promise<ModifyVulDefenceEventStatusResponse>;
    /**
     * 获取本地存储数据
     */
    GetLocalStorageItem(req: GetLocalStorageItemRequest, cb?: (error: string, rep: GetLocalStorageItemResponse) => void): Promise<GetLocalStorageItemResponse>;
    /**
     * 导出备份详情列表
     */
    ExportRansomDefenseMachineList(req: ExportRansomDefenseMachineListRequest, cb?: (error: string, rep: ExportRansomDefenseMachineListResponse) => void): Promise<ExportRansomDefenseMachineListResponse>;
    /**
     * 查询防勒索事件列表
     */
    DescribeRansomDefenseEventsList(req: DescribeRansomDefenseEventsListRequest, cb?: (error: string, rep: DescribeRansomDefenseEventsListResponse) => void): Promise<DescribeRansomDefenseEventsListResponse>;
    /**
     * 导出资产管理进程列表
     */
    ExportAssetProcessInfoList(req: ExportAssetProcessInfoListRequest, cb?: (error: string, rep: ExportAssetProcessInfoListResponse) => void): Promise<ExportAssetProcessInfoListResponse>;
    /**
     * 获取主机绑定策略列表
     */
    DescribeRansomDefenseMachineStrategyInfo(req: DescribeRansomDefenseMachineStrategyInfoRequest, cb?: (error: string, rep: DescribeRansomDefenseMachineStrategyInfoResponse) => void): Promise<DescribeRansomDefenseMachineStrategyInfoResponse>;
    /**
     * 获取高危命令列表
     */
    DescribeBashEvents(req: DescribeBashEventsRequest, cb?: (error: string, rep: DescribeBashEventsResponse) => void): Promise<DescribeBashEventsResponse>;
    /**
     * 导出漏洞检测报告。
     */
    ExportVulDetectionReport(req: ExportVulDetectionReportRequest, cb?: (error: string, rep: ExportVulDetectionReportResponse) => void): Promise<ExportVulDetectionReportResponse>;
    /**
     * 修改告警机器范围配置
     */
    ModifyWarningHostConfig(req: ModifyWarningHostConfigRequest, cb?: (error: string, rep: ModifyWarningHostConfigResponse) => void): Promise<ModifyWarningHostConfigResponse>;
    /**
     * 获取用户所有授权订单信息
     */
    DescribeLicenseList(req: DescribeLicenseListRequest, cb?: (error: string, rep: DescribeLicenseListResponse) => void): Promise<DescribeLicenseListResponse>;
    /**
     * 检测基线
     */
    StartBaselineDetect(req: StartBaselineDetectRequest, cb?: (error: string, rep: StartBaselineDetectResponse) => void): Promise<StartBaselineDetectResponse>;
    /**
     * 删除标签
     */
    DeleteTags(req: DeleteTagsRequest, cb?: (error: string, rep: DeleteTagsResponse) => void): Promise<DeleteTagsResponse>;
    /**
     * 本接口(TrustMalwares)将被识别木马文件设为信任。
     */
    TrustMalwares(req: TrustMalwaresRequest, cb?: (error: string, rep: TrustMalwaresResponse) => void): Promise<TrustMalwaresResponse>;
    /**
     * 删除基线规则
     */
    DeleteBaselineRule(req: DeleteBaselineRuleRequest, cb?: (error: string, rep: DeleteBaselineRuleResponse) => void): Promise<DeleteBaselineRuleResponse>;
    /**
     * 查询指定告警接收人的关联策略使用信息
     */
    DescribeWebHookReceiverUsage(req: DescribeWebHookReceiverUsageRequest, cb?: (error: string, rep: DescribeWebHookReceiverUsageResponse) => void): Promise<DescribeWebHookReceiverUsageResponse>;
    /**
     * 更新恶意请求白名单
     */
    ModifyMaliciousRequestWhiteList(req: ModifyMaliciousRequestWhiteListRequest, cb?: (error: string, rep: ModifyMaliciousRequestWhiteListResponse) => void): Promise<ModifyMaliciousRequestWhiteListResponse>;
    /**
     * 删除基线忽略规则
     */
    DeleteBaselineRuleIgnore(req: DeleteBaselineRuleIgnoreRequest, cb?: (error: string, rep: DeleteBaselineRuleIgnoreResponse) => void): Promise<DeleteBaselineRuleIgnoreResponse>;
    /**
     * 根据检测项id或事件id批量忽略事件或取消忽略
     */
    ChangeRuleEventsIgnoreStatus(req: ChangeRuleEventsIgnoreStatusRequest, cb?: (error: string, rep: ChangeRuleEventsIgnoreStatusResponse) => void): Promise<ChangeRuleEventsIgnoreStatusResponse>;
    /**
     * 防勒索快照回滚
     */
    RansomDefenseRollback(req: RansomDefenseRollbackRequest, cb?: (error: string, rep: RansomDefenseRollbackResponse) => void): Promise<RansomDefenseRollbackResponse>;
    /**
     * 获取资产管理系统安装包列表
     */
    DescribeAssetSystemPackageList(req: DescribeAssetSystemPackageListRequest, cb?: (error: string, rep: DescribeAssetSystemPackageListResponse) => void): Promise<DescribeAssetSystemPackageListResponse>;
    /**
     * 安全播报列表页
     */
    DescribeSecurityBroadcasts(req: DescribeSecurityBroadcastsRequest, cb?: (error: string, rep: DescribeSecurityBroadcastsResponse) => void): Promise<DescribeSecurityBroadcastsResponse>;
    /**
     * 获取资产指纹类型列表
     */
    DescribeAssetTypes(req?: DescribeAssetTypesRequest, cb?: (error: string, rep: DescribeAssetTypesResponse) => void): Promise<DescribeAssetTypesResponse>;
    /**
     * 获取用户漏洞所有标签列表
     */
    DescribeVulLabels(req?: DescribeVulLabelsRequest, cb?: (error: string, rep: DescribeVulLabelsResponse) => void): Promise<DescribeVulLabelsResponse>;
    /**
     * 停止基线检测
     */
    StopBaselineDetect(req: StopBaselineDetectRequest, cb?: (error: string, rep: StopBaselineDetectResponse) => void): Promise<StopBaselineDetectResponse>;
    /**
     * 获取高危命令规则列表
     */
    DescribeBashRules(req: DescribeBashRulesRequest, cb?: (error: string, rep: DescribeBashRulesResponse) => void): Promise<DescribeBashRulesResponse>;
    /**
     * 获取主机磁盘分区列表
     */
    DescribeAssetDiskList(req: DescribeAssetDiskListRequest, cb?: (error: string, rep: DescribeAssetDiskListResponse) => void): Promise<DescribeAssetDiskListResponse>;
    /**
     * 修改漏洞防御插件设置
1）新增主机自动加入，scope为1，quuids为空
2）全量旗舰版不自动加入，scope为0，quuids为当前quuid列表，
3）给定quuid列表，scope为0，quuids为用户选择quuid
     */
    ModifyVulDefenceSetting(req: ModifyVulDefenceSettingRequest, cb?: (error: string, rep: ModifyVulDefenceSettingResponse) => void): Promise<ModifyVulDefenceSettingResponse>;
    /**
     * 导出漏洞防御事件
     */
    ExportVulDefenceEvent(req: ExportVulDefenceEventRequest, cb?: (error: string, rep: ExportVulDefenceEventResponse) => void): Promise<ExportVulDefenceEventResponse>;
    /**
     * 同步资产扫描信息
     */
    SyncAssetScan(req: SyncAssetScanRequest, cb?: (error: string, rep: SyncAssetScanResponse) => void): Promise<SyncAssetScanResponse>;
    /**
     * DescribeScanState 该接口能查询对应模块正在进行的扫描任务状态
     */
    DescribeScanState(req: DescribeScanStateRequest, cb?: (error: string, rep: DescribeScanStateResponse) => void): Promise<DescribeScanStateResponse>;
    /**
     * DeleteScanTask 该接口可以对指定类型的扫描任务进行停止扫描;
     */
    DeleteScanTask(req: DeleteScanTaskRequest, cb?: (error: string, rep: DeleteScanTaskResponse) => void): Promise<DeleteScanTaskResponse>;
    /**
     * 用于创建/修改用户自定义配置
     */
    ModifyUsersConfig(req: ModifyUsersConfigRequest, cb?: (error: string, rep: ModifyUsersConfigResponse) => void): Promise<ModifyUsersConfigResponse>;
    /**
     * 新增或修改告警策略
     */
    ModifyWebHookPolicy(req: ModifyWebHookPolicyRequest, cb?: (error: string, rep: ModifyWebHookPolicyResponse) => void): Promise<ModifyWebHookPolicyResponse>;
    /**
     * 删除全部java内存马事件
     */
    DeleteAllJavaMemShells(req: DeleteAllJavaMemShellsRequest, cb?: (error: string, rep: DeleteAllJavaMemShellsResponse) => void): Promise<DeleteAllJavaMemShellsResponse>;
    /**
     * 获取基线项检测结果列表
     */
    DescribeBaselineItemList(req: DescribeBaselineItemListRequest, cb?: (error: string, rep: DescribeBaselineItemListResponse) => void): Promise<DescribeBaselineItemListResponse>;
    /**
     * 获取高危命令策略列表
     */
    DescribeBashPolicies(req: DescribeBashPoliciesRequest, cb?: (error: string, rep: DescribeBashPoliciesResponse) => void): Promise<DescribeBashPoliciesResponse>;
    /**
     * 查询防勒索策略绑定机器列表
     */
    DescribeRansomDefenseStrategyMachines(req: DescribeRansomDefenseStrategyMachinesRequest, cb?: (error: string, rep: DescribeRansomDefenseStrategyMachinesResponse) => void): Promise<DescribeRansomDefenseStrategyMachinesResponse>;
    /**
     * 查询日志检索服务信息
     */
    DescribeHistoryService(req?: DescribeHistoryServiceRequest, cb?: (error: string, rep: DescribeHistoryServiceResponse) => void): Promise<DescribeHistoryServiceResponse>;
    /**
     * 漏洞管理模块，获取近日指定类型的漏洞数量和主机数量
     */
    DescribeVulCountByDates(req: DescribeVulCountByDatesRequest, cb?: (error: string, rep: DescribeVulCountByDatesResponse) => void): Promise<DescribeVulCountByDatesResponse>;
    /**
     * 导出漏洞影响主机列表
     */
    ExportVulEffectHostList(req: ExportVulEffectHostListRequest, cb?: (error: string, rep: ExportVulEffectHostListResponse) => void): Promise<ExportVulEffectHostListResponse>;
    /**
     * 获取漏洞库列表
     */
    DescribeVulStoreList(req: DescribeVulStoreListRequest, cb?: (error: string, rep: DescribeVulStoreListResponse) => void): Promise<DescribeVulStoreListResponse>;
    /**
     * 获取告警点所在事件的所有节点信息
     */
    DescribeAlarmIncidentNodes(req: DescribeAlarmIncidentNodesRequest, cb?: (error: string, rep: DescribeAlarmIncidentNodesResponse) => void): Promise<DescribeAlarmIncidentNodesResponse>;
    /**
     * 大屏可视化获取安全概览相关事件统计数据接口
     */
    DescribeScreenEventsCnt(req: DescribeScreenEventsCntRequest, cb?: (error: string, rep: DescribeScreenEventsCntResponse) => void): Promise<DescribeScreenEventsCntResponse>;
    /**
     * 获取所有资源数量：主机、账号、端口、进程、软件、数据库、Web应用、Web框架、Web服务、Web站点
     */
    DescribeAssetTotalCount(req?: DescribeAssetTotalCountRequest, cb?: (error: string, rep: DescribeAssetTotalCountResponse) => void): Promise<DescribeAssetTotalCountResponse>;
    /**
     * 获取全网热点漏洞
     */
    DescribeHotVulTop(req?: DescribeHotVulTopRequest, cb?: (error: string, rep: DescribeHotVulTopResponse) => void): Promise<DescribeHotVulTopResponse>;
    /**
     * 删除基线弱口令
     */
    DeleteBaselineWeakPassword(req: DeleteBaselineWeakPasswordRequest, cb?: (error: string, rep: DeleteBaselineWeakPasswordResponse) => void): Promise<DeleteBaselineWeakPasswordResponse>;
    /**
     * 本接口 (DeleteNonlocalLoginPlaces) 用于删除异地登录记录。
     */
    DeleteNonlocalLoginPlaces(req: DeleteNonlocalLoginPlacesRequest, cb?: (error: string, rep: DeleteNonlocalLoginPlacesResponse) => void): Promise<DeleteNonlocalLoginPlacesResponse>;
    /**
     * 导出资产管理系统安装包列表
     */
    ExportAssetSystemPackageList(req: ExportAssetSystemPackageListRequest, cb?: (error: string, rep: ExportAssetSystemPackageListResponse) => void): Promise<ExportAssetSystemPackageListResponse>;
    /**
     * 编辑反弹Shell规则（支持多服务器选择）
     */
    EditReverseShellRules(req: EditReverseShellRulesRequest, cb?: (error: string, rep: EditReverseShellRulesResponse) => void): Promise<EditReverseShellRulesResponse>;
    /**
     * 删除告警策略
     */
    DeleteWebHookPolicy(req: DeleteWebHookPolicyRequest, cb?: (error: string, rep: DeleteWebHookPolicyResponse) => void): Promise<DeleteWebHookPolicyResponse>;
    /**
     * 导出本地提权事件
     */
    ExportPrivilegeEvents(req: ExportPrivilegeEventsRequest, cb?: (error: string, rep: ExportPrivilegeEventsResponse) => void): Promise<ExportPrivilegeEventsResponse>;
    /**
     * 入侵检测所有事件的状态，包括：文件查杀，异常登录，密码破解，高危命令，反弹shell，本地提取
     */
    ModifyRiskEventsStatus(req: ModifyRiskEventsStatusRequest, cb?: (error: string, rep: ModifyRiskEventsStatusResponse) => void): Promise<ModifyRiskEventsStatusResponse>;
    /**
     * 获取主机账号详情
     */
    DescribeAssetUserInfo(req: DescribeAssetUserInfoRequest, cb?: (error: string, rep: DescribeAssetUserInfoResponse) => void): Promise<DescribeAssetUserInfoResponse>;
    /**
     * 删除漏洞防御白名单
     */
    DeleteRaspRules(req: DeleteRaspRulesRequest, cb?: (error: string, rep: DeleteRaspRulesResponse) => void): Promise<DeleteRaspRulesResponse>;
    /**
     * 本接口 (DeleteMalwares) 用于删除木马记录。
     */
    DeleteMalwares(req: DeleteMalwaresRequest, cb?: (error: string, rep: DeleteMalwaresResponse) => void): Promise<DeleteMalwaresResponse>;
    /**
     * 导出漏洞防御插件事件
     */
    ExportVulDefencePluginEvent(req: ExportVulDefencePluginEventRequest, cb?: (error: string, rep: ExportVulDefencePluginEventResponse) => void): Promise<ExportVulDefencePluginEventResponse>;
    /**
     * 定期扫描漏洞设置
     */
    ScanVulSetting(req: ScanVulSettingRequest, cb?: (error: string, rep: ScanVulSettingResponse) => void): Promise<ScanVulSettingResponse>;
    /**
     * 本接口 (DescribeAccountStatistics) 用于获取帐号统计列表数据。
     */
    DescribeAccountStatistics(req: DescribeAccountStatisticsRequest, cb?: (error: string, rep: DescribeAccountStatisticsResponse) => void): Promise<DescribeAccountStatisticsResponse>;
    /**
     * 导出异常进程事件
     */
    ExportRiskProcessEvents(req: ExportRiskProcessEventsRequest, cb?: (error: string, rep: ExportRiskProcessEventsResponse) => void): Promise<ExportRiskProcessEventsResponse>;
    /**
     * 获取漏洞管理模块指定类型的待处理漏洞数、主机数和非专业版主机数量
     */
    DescribeUndoVulCounts(req: DescribeUndoVulCountsRequest, cb?: (error: string, rep: DescribeUndoVulCountsResponse) => void): Promise<DescribeUndoVulCountsResponse>;
    /**
     * 获取木马不可隔离的主机
     */
    DescribeCanNotSeparateMachine(req: DescribeCanNotSeparateMachineRequest, cb?: (error: string, rep: DescribeCanNotSeparateMachineResponse) => void): Promise<DescribeCanNotSeparateMachineResponse>;
    /**
     * 本接口 (ExportBruteAttacks) 用于导出密码破解记录成CSV文件。
     */
    ExportBruteAttacks(req: ExportBruteAttacksRequest, cb?: (error: string, rep: ExportBruteAttacksResponse) => void): Promise<ExportBruteAttacksResponse>;
    /**
     * 导出资产管理环境变量列表
     */
    ExportAssetEnvList(req: ExportAssetEnvListRequest, cb?: (error: string, rep: ExportAssetEnvListResponse) => void): Promise<ExportAssetEnvListResponse>;
    /**
     * 创建/修改网站防护目录
     */
    ModifyWebPageProtectDir(req: ModifyWebPageProtectDirRequest, cb?: (error: string, rep: ModifyWebPageProtectDirResponse) => void): Promise<ModifyWebPageProtectDirResponse>;
    /**
     * 导出风险趋势
     */
    ExportSecurityTrends(req: ExportSecurityTrendsRequest, cb?: (error: string, rep: ExportSecurityTrendsResponse) => void): Promise<ExportSecurityTrendsResponse>;
    /**
     * 本接口 (ExportMalwares) 用于导出木马记录CSV文件。
     */
    ExportMalwares(req: ExportMalwaresRequest, cb?: (error: string, rep: ExportMalwaresResponse) => void): Promise<ExportMalwaresResponse>;
    /**
     * 核心文件规则状态更新，支持批量删除 关闭
     */
    ModifyFileTamperRuleStatus(req: ModifyFileTamperRuleStatusRequest, cb?: (error: string, rep: ModifyFileTamperRuleStatusResponse) => void): Promise<ModifyFileTamperRuleStatusResponse>;
    /**
     * 修改阻断白名单列表
     */
    ModifyBanWhiteList(req: ModifyBanWhiteListRequest, cb?: (error: string, rep: ModifyBanWhiteListResponse) => void): Promise<ModifyBanWhiteListResponse>;
    /**
     * 导出篡改事件列表
     */
    ExportWebPageEventList(req: ExportWebPageEventListRequest, cb?: (error: string, rep: ExportWebPageEventListResponse) => void): Promise<ExportWebPageEventListResponse>;
    /**
     * 获取基线服务器风险TOP5
     */
    DescribeBaselineHostRiskTop(req: DescribeBaselineHostRiskTopRequest, cb?: (error: string, rep: DescribeBaselineHostRiskTopResponse) => void): Promise<DescribeBaselineHostRiskTopResponse>;
    /**
     * 导出恶意请求事件列表
     */
    ExportRiskDnsEventList(req: ExportRiskDnsEventListRequest, cb?: (error: string, rep: ExportRiskDnsEventListResponse) => void): Promise<ExportRiskDnsEventListResponse>;
    /**
     * 导出授权列表对应的绑定信息
     */
    ExportLicenseDetail(req: ExportLicenseDetailRequest, cb?: (error: string, rep: ExportLicenseDetailResponse) => void): Promise<ExportLicenseDetailResponse>;
    /**
     * 专家服务-应急响应列表
     */
    DescribeEmergencyResponseList(req: DescribeEmergencyResponseListRequest, cb?: (error: string, rep: DescribeEmergencyResponseListResponse) => void): Promise<DescribeEmergencyResponseListResponse>;
    /**
     * 网络攻击趋势数据
     */
    DescribeAttackTrends(req: DescribeAttackTrendsRequest, cb?: (error: string, rep: DescribeAttackTrendsResponse) => void): Promise<DescribeAttackTrendsResponse>;
    /**
     * 创建或修改防勒索策略
     */
    CreateRansomDefenseStrategy(req: CreateRansomDefenseStrategyRequest, cb?: (error: string, rep: CreateRansomDefenseStrategyResponse) => void): Promise<CreateRansomDefenseStrategyResponse>;
    /**
     * 大屏可视化紧急通知
     */
    DescribeScreenEmergentMsg(req?: DescribeScreenEmergentMsgRequest, cb?: (error: string, rep: DescribeScreenEmergentMsgResponse) => void): Promise<DescribeScreenEmergentMsgResponse>;
    /**
     * 不再提醒爆破阻断提示弹窗
     */
    StopNoticeBanTips(req?: StopNoticeBanTipsRequest, cb?: (error: string, rep: StopNoticeBanTipsResponse) => void): Promise<StopNoticeBanTipsResponse>;
    /**
     * 根据基线id查询下属检测项信息
     */
    DescribeBaselineRule(req: DescribeBaselineRuleRequest, cb?: (error: string, rep: DescribeBaselineRuleResponse) => void): Promise<DescribeBaselineRuleResponse>;
    /**
     * 导出基线影响主机列表
     */
    ExportBaselineEffectHostList(req: ExportBaselineEffectHostListRequest, cb?: (error: string, rep: ExportBaselineEffectHostListResponse) => void): Promise<ExportBaselineEffectHostListResponse>;
    /**
     * 核心文件监控事件列表
     */
    DescribeFileTamperEvents(req: DescribeFileTamperEventsRequest, cb?: (error: string, rep: DescribeFileTamperEventsResponse) => void): Promise<DescribeFileTamperEventsResponse>;
    /**
     * 设置中心-授权管理 对某个授权批量绑定机器
     */
    ModifyLicenseBinds(req: ModifyLicenseBindsRequest, cb?: (error: string, rep: ModifyLicenseBindsResponse) => void): Promise<ModifyLicenseBindsResponse>;
    /**
     * 获取阻断按钮状态
     */
    DescribeBanStatus(req?: DescribeBanStatusRequest, cb?: (error: string, rep: DescribeBanStatusResponse) => void): Promise<DescribeBanStatusResponse>;
    /**
     * 查询漏洞防御列表
     */
    DescribeVulDefenceList(req: DescribeVulDefenceListRequest, cb?: (error: string, rep: DescribeVulDefenceListResponse) => void): Promise<DescribeVulDefenceListResponse>;
    /**
     * 本接口 (DescribeProcessStatistics) 用于获取进程统计列表数据。
     */
    DescribeProcessStatistics(req: DescribeProcessStatisticsRequest, cb?: (error: string, rep: DescribeProcessStatisticsResponse) => void): Promise<DescribeProcessStatisticsResponse>;
    /**
     * 查看恶意文件详情
     */
    DescribeMalwareInfo(req: DescribeMalwareInfoRequest, cb?: (error: string, rep: DescribeMalwareInfoResponse) => void): Promise<DescribeMalwareInfoResponse>;
    /**
     * 获取用户防勒索趋势
     */
    DescribeRansomDefenseState(req?: DescribeRansomDefenseStateRequest, cb?: (error: string, rep: DescribeRansomDefenseStateResponse) => void): Promise<DescribeRansomDefenseStateResponse>;
    /**
     * 导出恶意请求策略列表
     */
    ExportRiskDnsPolicyList(req: ExportRiskDnsPolicyListRequest, cb?: (error: string, rep: ExportRiskDnsPolicyListResponse) => void): Promise<ExportRiskDnsPolicyListResponse>;
    /**
     * 根据策略名查询策略是否存在
     */
    DescribeStrategyExist(req: DescribeStrategyExistRequest, cb?: (error: string, rep: DescribeStrategyExistResponse) => void): Promise<DescribeStrategyExistResponse>;
    /**
     * 新购授权自动绑定任务
     */
    CreateBuyBindTask(req: CreateBuyBindTaskRequest, cb?: (error: string, rep: CreateBuyBindTaskResponse) => void): Promise<CreateBuyBindTaskResponse>;
    /**
     * 查询日志
     */
    SearchLog(req: SearchLogRequest, cb?: (error: string, rep: SearchLogResponse) => void): Promise<SearchLogResponse>;
    /**
     * 查询主机关联文件监控规则数量
     */
    DescribeFileTamperRuleCount(req: DescribeFileTamperRuleCountRequest, cb?: (error: string, rep: DescribeFileTamperRuleCountResponse) => void): Promise<DescribeFileTamperRuleCountResponse>;
    /**
     * 查询java内存马插件列表
     */
    DescribeJavaMemShellPluginList(req: DescribeJavaMemShellPluginListRequest, cb?: (error: string, rep: DescribeJavaMemShellPluginListResponse) => void): Promise<DescribeJavaMemShellPluginListResponse>;
    /**
     * 创建应急漏洞扫描任务
     */
    CreateEmergencyVulScan(req: CreateEmergencyVulScanRequest, cb?: (error: string, rep: CreateEmergencyVulScanResponse) => void): Promise<CreateEmergencyVulScanResponse>;
    /**
     * 产品试用状态查询接口
     */
    DescribeProductStatus(req?: DescribeProductStatusRequest, cb?: (error: string, rep: DescribeProductStatusResponse) => void): Promise<DescribeProductStatusResponse>;
    /**
     * 反弹shell信息详情
     */
    DescribeReverseShellEventInfo(req: DescribeReverseShellEventInfoRequest, cb?: (error: string, rep: DescribeReverseShellEventInfoResponse) => void): Promise<DescribeReverseShellEventInfoResponse>;
    /**
     * 获取阻断白名单列表
     */
    DescribeBanWhiteList(req: DescribeBanWhiteListRequest, cb?: (error: string, rep: DescribeBanWhiteListResponse) => void): Promise<DescribeBanWhiteListResponse>;
    /**
     * 漏洞一键检测
     */
    ScanVul(req: ScanVulRequest, cb?: (error: string, rep: ScanVulResponse) => void): Promise<ScanVulResponse>;
    /**
     * 获取网络攻击威胁类型列表
     */
    DescribeAttackVulTypeList(req?: DescribeAttackVulTypeListRequest, cb?: (error: string, rep: DescribeAttackVulTypeListResponse) => void): Promise<DescribeAttackVulTypeListResponse>;
    /**
     * 查看漏洞防御最大cpu限制
     */
    DescribeRaspMaxCpu(req?: DescribeRaspMaxCpuRequest, cb?: (error: string, rep: DescribeRaspMaxCpuResponse) => void): Promise<DescribeRaspMaxCpuResponse>;
    /**
     * 根据基线id查询基线详情接口
     */
    DescribeBaselineDetail(req: DescribeBaselineDetailRequest, cb?: (error: string, rep: DescribeBaselineDetailResponse) => void): Promise<DescribeBaselineDetailResponse>;
    /**
     * 获取所有端口数量
     */
    DescribeAssetPortCount(req: DescribeAssetPortCountRequest, cb?: (error: string, rep: DescribeAssetPortCountResponse) => void): Promise<DescribeAssetPortCountResponse>;
    /**
     * 入侵检测，获取恶意请求列表
     */
    DescribeRiskDnsList(req: DescribeRiskDnsListRequest, cb?: (error: string, rep: DescribeRiskDnsListResponse) => void): Promise<DescribeRiskDnsListResponse>;
    /**
     * 获取病毒库及POC的更新信息
     */
    DescribeVdbAndPocInfo(req?: DescribeVdbAndPocInfoRequest, cb?: (error: string, rep: DescribeVdbAndPocInfoResponse) => void): Promise<DescribeVdbAndPocInfoResponse>;
    /**
     * 修改暴力破解规则
     */
    ModifyBruteAttackRules(req: ModifyBruteAttackRulesRequest, cb?: (error: string, rep: ModifyBruteAttackRulesResponse) => void): Promise<ModifyBruteAttackRulesResponse>;
    /**
     * 查询可筛选操作系统列表.
     */
    DescribeMachineOsList(req?: DescribeMachineOsListRequest, cb?: (error: string, rep: DescribeMachineOsListResponse) => void): Promise<DescribeMachineOsListResponse>;
    /**
     * 获取漏洞紧急通知
     */
    DescribeVulEmergentMsg(req?: DescribeVulEmergentMsgRequest, cb?: (error: string, rep: DescribeVulEmergentMsgResponse) => void): Promise<DescribeVulEmergentMsgResponse>;
    /**
     * 授权管理-授权概览信息
     */
    DescribeLicenseGeneral(req?: DescribeLicenseGeneralRequest, cb?: (error: string, rep: DescribeLicenseGeneralResponse) => void): Promise<DescribeLicenseGeneralResponse>;
    /**
     * 查询回滚任务列表
     */
    DescribeRansomDefenseRollBackTaskList(req: DescribeRansomDefenseRollBackTaskListRequest, cb?: (error: string, rep: DescribeRansomDefenseRollBackTaskListResponse) => void): Promise<DescribeRansomDefenseRollBackTaskListResponse>;
    /**
     * 导出主机最近趋势情况（最长最近90天）
     */
    ExportAssetRecentMachineInfo(req: ExportAssetRecentMachineInfoRequest, cb?: (error: string, rep: ExportAssetRecentMachineInfoResponse) => void): Promise<ExportAssetRecentMachineInfoResponse>;
    /**
     * 导出反弹Shell事件
     */
    ExportReverseShellEvents(req: ExportReverseShellEventsRequest, cb?: (error: string, rep: ExportReverseShellEventsResponse) => void): Promise<ExportReverseShellEventsResponse>;
    /**
     * 创建网络攻击白名单
     */
    CreateNetAttackWhiteList(req: CreateNetAttackWhiteListRequest, cb?: (error: string, rep: CreateNetAttackWhiteListResponse) => void): Promise<CreateNetAttackWhiteListResponse>;
    /**
     * 更改恶意请求策略
     */
    ModifyRiskDnsPolicy(req: ModifyRiskDnsPolicyRequest, cb?: (error: string, rep: ModifyRiskDnsPolicyResponse) => void): Promise<ModifyRiskDnsPolicyResponse>;
    /**
     * 专家服务-安全管家列表
     */
    DescribeExpertServiceList(req: DescribeExpertServiceListRequest, cb?: (error: string, rep: DescribeExpertServiceListResponse) => void): Promise<DescribeExpertServiceListResponse>;
    /**
     * 获取忽略规则项列表
     */
    DescribeBaselineItemIgnoreList(req: DescribeBaselineItemIgnoreListRequest, cb?: (error: string, rep: DescribeBaselineItemIgnoreListResponse) => void): Promise<DescribeBaselineItemIgnoreListResponse>;
    /**
     * 导出防勒索策略列表
     */
    ExportRansomDefenseStrategyList(req: ExportRansomDefenseStrategyListRequest, cb?: (error: string, rep: ExportRansomDefenseStrategyListResponse) => void): Promise<ExportRansomDefenseStrategyListResponse>;
    /**
     * 漏洞修护-查找主机漏洞修护进度
     */
    DescribeVulFixStatus(req: DescribeVulFixStatusRequest, cb?: (error: string, rep: DescribeVulFixStatusResponse) => void): Promise<DescribeVulFixStatusResponse>;
    /**
     * 获取客户端异常事件
     */
    DescribeClientException(req: DescribeClientExceptionRequest, cb?: (error: string, rep: DescribeClientExceptionResponse) => void): Promise<DescribeClientExceptionResponse>;
    /**
     * 获取基线分类列表
     */
    DescribeBaselineRuleCategoryList(req?: DescribeBaselineRuleCategoryListRequest, cb?: (error: string, rep: DescribeBaselineRuleCategoryListResponse) => void): Promise<DescribeBaselineRuleCategoryListResponse>;
    /**
     * 本接口 (DescribeSecurityTrends) 用于获取安全事件统计数据。
     */
    DescribeSecurityTrends(req: DescribeSecurityTrendsRequest, cb?: (error: string, rep: DescribeSecurityTrendsResponse) => void): Promise<DescribeSecurityTrendsResponse>;
    /**
     * 根据基线策略id查询基线策略数据概览统计
     */
    DescribeBaselineAnalysisData(req: DescribeBaselineAnalysisDataRequest, cb?: (error: string, rep: DescribeBaselineAnalysisDataResponse) => void): Promise<DescribeBaselineAnalysisDataResponse>;
    /**
     * 查询备份详情列表
     */
    DescribeRansomDefenseMachineList(req: DescribeRansomDefenseMachineListRequest, cb?: (error: string, rep: DescribeRansomDefenseMachineListResponse) => void): Promise<DescribeRansomDefenseMachineListResponse>;
    /**
     * 新增或修改高危命令规则
     */
    EditBashRules(req: EditBashRulesRequest, cb?: (error: string, rep: EditBashRulesResponse) => void): Promise<EditBashRulesResponse>;
    /**
     * 专家服务-安全管家月巡检报告下载
     */
    DescribeMonthInspectionReport(req: DescribeMonthInspectionReportRequest, cb?: (error: string, rep: DescribeMonthInspectionReportResponse) => void): Promise<DescribeMonthInspectionReportResponse>;
    /**
     * 获取资产数量： 主机数、账号数、端口数、进程数、软件数、数据库数、Web应用数、Web框架数、Web服务数、Web站点数
     */
    DescribeAssetInfo(req?: DescribeAssetInfoRequest, cb?: (error: string, rep: DescribeAssetInfoResponse) => void): Promise<DescribeAssetInfoResponse>;
    /**
     * 获取木马白名单受影响列表
     */
    DescribeMalwareWhiteListAffectList(req: DescribeMalwareWhiteListAffectListRequest, cb?: (error: string, rep: DescribeMalwareWhiteListAffectListResponse) => void): Promise<DescribeMalwareWhiteListAffectListResponse>;
    /**
     * 产品变动切换到了\\n切换到 AddVulIgnoreRule / ModifyVulIgnoreRule  CancelVulIgnoreRule\\n相关接口

本接口 (IgnoreImpactedHosts) 用于忽略漏洞。
     */
    IgnoreImpactedHosts(req: IgnoreImpactedHostsRequest, cb?: (error: string, rep: IgnoreImpactedHostsResponse) => void): Promise<IgnoreImpactedHostsResponse>;
    /**
     * 漏洞管理-导出漏洞列表
     */
    ExportVulList(req: ExportVulListRequest, cb?: (error: string, rep: ExportVulListResponse) => void): Promise<ExportVulListResponse>;
    /**
     * 修改java内存马事件状态
     */
    ModifyJavaMemShellsStatus(req: ModifyJavaMemShellsStatusRequest, cb?: (error: string, rep: ModifyJavaMemShellsStatusResponse) => void): Promise<ModifyJavaMemShellsStatusResponse>;
    /**
     * 导出资产管理计划任务列表
     */
    ExportAssetPlanTaskList(req: ExportAssetPlanTaskListRequest, cb?: (error: string, rep: ExportAssetPlanTaskListResponse) => void): Promise<ExportAssetPlanTaskListResponse>;
    /**
     * 查询篡改事件列表
     */
    DescribeWebPageEventList(req: DescribeWebPageEventListRequest, cb?: (error: string, rep: DescribeWebPageEventListResponse) => void): Promise<DescribeWebPageEventListResponse>;
    /**
     * 混合云安装agent token获取
     */
    DescribeAgentInstallationToken(req: DescribeAgentInstallationTokenRequest, cb?: (error: string, rep: DescribeAgentInstallationTokenResponse) => void): Promise<DescribeAgentInstallationTokenResponse>;
    /**
     * 获取主机标签Top5
     */
    DescribeAssetMachineTagTop(req?: DescribeAssetMachineTagTopRequest, cb?: (error: string, rep: DescribeAssetMachineTagTopResponse) => void): Promise<DescribeAssetMachineTagTopResponse>;
}
