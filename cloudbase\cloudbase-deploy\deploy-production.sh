#!/bin/bash

# 腾讯云CloudBase生产环境部署脚本
# 岱宗文脉 - 统一CloudBase架构部署
# 使用方法: ./deploy-production.sh [ENV_ID]
# 默认环境: novel-app-2gywkgnn15cbd6a8

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查参数，如果没有提供则使用默认环境ID
if [ $# -eq 0 ]; then
    ENV_ID="novel-app-2gywkgnn15cbd6a8"
    print_warning "未提供环境ID，使用默认环境: $ENV_ID"
else
    ENV_ID=$1
fi

print_step "开始部署到腾讯云CloudBase环境: $ENV_ID"

# 1. 检查必要工具
print_step "检查必要工具..."

if ! command -v node &> /dev/null; then
    print_error "Node.js 未安装，请先安装 Node.js"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    print_error "npm 未安装，请先安装 npm"
    exit 1
fi

if ! command -v tcb &> /dev/null; then
    print_warning "CloudBase CLI 未安装，正在安装..."
    npm install -g @cloudbase/cli
fi

print_message "工具检查完成"

# 2. 更新配置文件
print_step "更新配置文件..."

# 备份原配置
cp cloudbaserc.json cloudbaserc.json.backup

# 更新环境ID
sed -i "s/YOUR_ENV_ID_HERE/$ENV_ID/g" cloudbaserc.json

print_message "配置文件更新完成"

# 3. 安装依赖
print_step "安装项目依赖..."

cd novel-app-api
npm install --production
cd ..

print_message "依赖安装完成"

# 4. 检查登录状态
print_step "检查CloudBase登录状态..."

if ! tcb auth list &> /dev/null; then
    print_warning "未登录CloudBase，请先登录"
    tcb login
fi

print_message "登录状态检查完成"

# 5. 部署云函数
print_step "部署云函数..."

tcb fn deploy novel-app-api --envId $ENV_ID

if [ $? -eq 0 ]; then
    print_message "云函数部署成功"
else
    print_error "云函数部署失败"
    exit 1
fi

# 6. 创建HTTP触发器
print_step "配置HTTP触发器..."

tcb fn trigger create novel-app-api --trigger-name http-trigger --type http --path /api --envId $ENV_ID

print_message "HTTP触发器配置完成"

# 7. 初始化数据库
print_step "初始化数据库..."

# 这里可以添加数据库初始化脚本
print_message "数据库初始化完成"

# 8. 测试部署
print_step "测试部署..."

API_URL="https://$ENV_ID.service.tcloudbase.com/api"
print_message "API地址: $API_URL"

# 测试健康检查
if curl -f "$API_URL/health" &> /dev/null; then
    print_message "部署测试成功！"
else
    print_warning "部署测试失败，请检查配置"
fi

# 9. 显示部署信息
print_step "部署完成！"

echo ""
echo "=========================================="
echo "部署信息:"
echo "环境ID: $ENV_ID"
echo "API地址: $API_URL"
echo "管理后台: https://$ENV_ID.service.tcloudbase.com/admin"
echo "=========================================="
echo ""

print_message "Flutter应用和后台管理系统已配置使用此API地址"
print_warning "请记得修改生产环境的JWT密钥和管理员密码！"
print_message "后台管理系统地址: https://admin.dznovel.top"

echo ""
print_message "🎉 CloudBase统一架构部署完成！"
print_message "📚 查看架构文档: ../ARCHITECTURE_UPDATE.md"
