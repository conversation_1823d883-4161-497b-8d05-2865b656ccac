{"version": 3, "file": "binary.js", "sourceRoot": "", "sources": ["../src/binary.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,iCAAgC;AAChC,iDAA+C;AAC/C,2CAAgG;AAChG,wCAA2D;AAE3D,iCAAmD;AACnD,yCAA2D;AAmB3D;;;;GAIG;AACH;IAkCE;;;;;;;;;;OAUG;IACH,gBAAY,MAAgC,EAAE,OAAgB;QAC5D,IAAI,CAAC,CAAC,IAAI,YAAY,MAAM,CAAC;YAAE,OAAO,IAAI,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAElE,IACE,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC;YACjB,CAAC,CAAC,OAAO,MAAM,KAAK,QAAQ,CAAC;YAC7B,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,CAAC;YAC3B,CAAC,CAAC,MAAM,YAAY,WAAW,CAAC;YAChC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EACtB;YACA,MAAM,IAAI,qBAAa,CACrB,kFAAkF,CACnF,CAAC;SACH;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,MAAM,CAAC,2BAA2B,CAAC;QAE9D,IAAI,MAAM,IAAI,IAAI,EAAE;YAClB,gCAAgC;YAChC,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;YAC/C,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC;SACnB;aAAM;YACL,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;gBAC9B,SAAS;gBACT,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;aAC7C;iBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBAChC,WAAW;gBACX,IAAI,CAAC,MAAM,GAAG,eAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACnC;iBAAM;gBACL,oCAAoC;gBACpC,IAAI,CAAC,MAAM,GAAG,IAAA,4BAAY,EAAC,MAAM,CAAC,CAAC;aACpC;YAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;SACxC;IACH,CAAC;IAED;;;;OAIG;IACH,oBAAG,GAAH,UAAI,SAA2D;QAC7D,oEAAoE;QACpE,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC3D,MAAM,IAAI,qBAAa,CAAC,sCAAsC,CAAC,CAAC;SACjE;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAChE,MAAM,IAAI,qBAAa,CAAC,mDAAmD,CAAC,CAAC;QAE/E,6BAA6B;QAC7B,IAAI,WAAmB,CAAC;QACxB,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,WAAW,GAAG,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;SACvC;aAAM,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACxC,WAAW,GAAG,SAAS,CAAC;SACzB;aAAM;YACL,WAAW,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;SAC5B;QAED,IAAI,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,GAAG,EAAE;YACxC,MAAM,IAAI,qBAAa,CAAC,0DAA0D,CAAC,CAAC;SACrF;QAED,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,EAAE;YACtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,WAAW,CAAC;SAC5C;aAAM;YACL,IAAM,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACrE,mCAAmC;YACnC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACnD,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,WAAW,CAAC;SAC5C;IACH,CAAC;IAED;;;;;OAKG;IACH,sBAAK,GAAL,UAAM,QAAiC,EAAE,MAAc;QACrD,MAAM,GAAG,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAE7D,oDAAoD;QACpD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE;YACjD,IAAM,MAAM,GAAG,eAAM,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;YAClE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAEnD,wBAAwB;YACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;SACtB;QAED,IAAI,WAAW,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAA,4BAAY,EAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,QAAQ;gBACX,MAAM,GAAG,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;SAC3F;aAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;YACvC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC/D,IAAI,CAAC,QAAQ;gBACX,MAAM,GAAG,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;SACvF;IACH,CAAC;IAED;;;;;OAKG;IACH,qBAAI,GAAJ,UAAK,QAAgB,EAAE,MAAc;QACnC,MAAM,GAAG,MAAM,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;QAEvD,kDAAkD;QAClD,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG,MAAM,CAAC,CAAC;IACxD,CAAC;IAED;;;;;OAKG;IACH,sBAAK,GAAL,UAAM,KAAe;QACnB,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;QAEhB,2EAA2E;QAC3E,IAAI,KAAK,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,QAAQ,EAAE;YACjD,OAAO,IAAI,CAAC,MAAM,CAAC;SACpB;QAED,kCAAkC;QAClC,IAAI,KAAK,EAAE;YACT,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SAC5C;QACD,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IAC1D,CAAC;IAED,wCAAwC;IACxC,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IAED,uBAAM,GAAN;QACE,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,yBAAQ,GAAR,UAAS,MAAe;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC;IAED,gBAAgB;IAChB,+BAAc,GAAd,UAAe,OAAsB;QACnC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpD,IAAM,OAAO,GAAG,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACnD,IAAI,OAAO,CAAC,MAAM,EAAE;YAClB,OAAO;gBACL,OAAO,EAAE,YAAY;gBACrB,KAAK,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO;aACtD,CAAC;SACH;QACD,OAAO;YACL,OAAO,EAAE;gBACP,MAAM,EAAE,YAAY;gBACpB,OAAO,EAAE,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,OAAO;aACxD;SACF,CAAC;IACJ,CAAC;IAED,uBAAM,GAAN;QACE,IAAI,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,YAAY,EAAE;YACzC,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;SACtD;QAED,MAAM,IAAI,iBAAS,CACjB,4BAAoB,IAAI,CAAC,QAAQ,gEAAoD,MAAM,CAAC,YAAY,+BAA2B,CACpI,CAAC;IACJ,CAAC;IAED,gBAAgB;IACT,uBAAgB,GAAvB,UACE,GAAyD,EACzD,OAAsB;QAEtB,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,IAAwB,CAAC;QAC7B,IAAI,IAAI,CAAC;QACT,IAAI,SAAS,IAAI,GAAG,EAAE;YACpB,IAAI,OAAO,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,IAAI,OAAO,IAAI,GAAG,EAAE;gBACvE,IAAI,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,GAAG,eAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;aAC3C;iBAAM;gBACL,IAAI,OAAO,GAAG,CAAC,OAAO,KAAK,QAAQ,EAAE;oBACnC,IAAI,GAAG,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACnE,IAAI,GAAG,eAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;iBAClD;aACF;SACF;aAAM,IAAI,OAAO,IAAI,GAAG,EAAE;YACzB,IAAI,GAAG,CAAC,CAAC;YACT,IAAI,GAAG,IAAA,kCAAqB,EAAC,GAAG,CAAC,KAAK,CAAC,CAAC;SACzC;QACD,IAAI,CAAC,IAAI,EAAE;YACT,MAAM,IAAI,qBAAa,CAAC,iDAA0C,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAE,CAAC,CAAC;SAC1F;QACD,OAAO,IAAI,KAAK,wCAA4B,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACzF,CAAC;IAED,gBAAgB;IAChB,iBAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,wBAAO,GAAP;QACE,IAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,OAAO,mCAA2B,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,2BAAc,IAAI,CAAC,QAAQ,MAAG,CAAC;IAC3F,CAAC;IAlQD;;;OAGG;IACqB,kCAA2B,GAAG,CAAC,CAAC;IAExD,kCAAkC;IAClB,kBAAW,GAAG,GAAG,CAAC;IAClC,wBAAwB;IACR,sBAAe,GAAG,CAAC,CAAC;IACpC,yBAAyB;IACT,uBAAgB,GAAG,CAAC,CAAC;IACrC,2BAA2B;IACX,yBAAkB,GAAG,CAAC,CAAC;IACvC,oEAAoE;IACpD,uBAAgB,GAAG,CAAC,CAAC;IACrC,qBAAqB;IACL,mBAAY,GAAG,CAAC,CAAC;IACjC,oBAAoB;IACJ,kBAAW,GAAG,CAAC,CAAC;IAChC,0BAA0B;IACV,wBAAiB,GAAG,CAAC,CAAC;IACtC,uBAAuB;IACP,qBAAc,GAAG,CAAC,CAAC;IACnC,qBAAqB;IACL,2BAAoB,GAAG,GAAG,CAAC;IA0O7C,aAAC;CAAA,AAtQD,IAsQC;AAtQY,wBAAM;AAwQnB,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE,WAAW,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;AAM1E,IAAM,gBAAgB,GAAG,EAAE,CAAC;AAE5B;;;GAGG;AACH;IAA0B,wBAAM;IAM9B;;;;OAIG;IACH,cAAY,KAA8B;QAA1C,iBAmBC;QAlBC,IAAI,KAAK,CAAC;QACV,IAAI,MAAM,CAAC;QACX,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,KAAK,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;SACzB;aAAM,IAAI,KAAK,YAAY,IAAI,EAAE;YAChC,KAAK,GAAG,eAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YAClC,MAAM,GAAG,KAAK,CAAC,IAAI,CAAC;SACrB;aAAM,IAAI,WAAW,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,KAAK,gBAAgB,EAAE;YAC7E,KAAK,GAAG,IAAA,4BAAY,EAAC,KAAK,CAAC,CAAC;SAC7B;aAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YACpC,KAAK,GAAG,IAAA,kCAAqB,EAAC,KAAK,CAAC,CAAC;SACtC;aAAM;YACL,MAAM,IAAI,qBAAa,CACrB,gLAAgL,CACjL,CAAC;SACH;gBACD,kBAAM,KAAK,EAAE,wCAA4B,CAAC;QAC1C,KAAI,CAAC,IAAI,GAAG,MAAM,CAAC;;IACrB,CAAC;IAMD,sBAAI,oBAAE;QAJN;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;aAED,UAAO,KAAa;YAClB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YAEpB,IAAI,IAAI,CAAC,cAAc,EAAE;gBACvB,IAAI,CAAC,IAAI,GAAG,IAAA,kCAAqB,EAAC,KAAK,CAAC,CAAC;aAC1C;QACH,CAAC;;;OARA;IAUD;;;SAGK;IACL,0BAAW,GAAX,UAAY,aAAoB;QAApB,8BAAA,EAAA,oBAAoB;QAC9B,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,IAAI,EAAE;YACpC,OAAO,IAAI,CAAC,IAAI,CAAC;SAClB;QAED,IAAM,aAAa,GAAG,IAAA,kCAAqB,EAAC,IAAI,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QAEpE,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC;SAC3B;QAED,OAAO,aAAa,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,uBAAQ,GAAR,UAAS,QAAiB;QACxB,OAAO,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;IACpE,CAAC;IAED;;;OAGG;IACH,qBAAM,GAAN;QACE,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,qBAAM,GAAN,UAAO,OAA+B;QACpC,IAAI,CAAC,OAAO,EAAE;YACZ,OAAO,KAAK,CAAC;SACd;QAED,IAAI,OAAO,YAAY,IAAI,EAAE;YAC3B,OAAO,OAAO,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SACnC;QAED,IAAI;YACF,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;SAC7C;QAAC,WAAM;YACN,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED;;OAEG;IACH,uBAAQ,GAAR;QACE,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC;IAClD,CAAC;IAED;;OAEG;IACI,aAAQ,GAAf;QACE,IAAM,KAAK,GAAG,IAAA,mBAAW,EAAC,gBAAgB,CAAC,CAAC;QAE5C,gEAAgE;QAChE,4EAA4E;QAC5E,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QACpC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;QAEpC,OAAO,eAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACI,YAAO,GAAd,UAAe,KAA6B;QAC1C,IAAI,CAAC,KAAK,EAAE;YACV,OAAO,KAAK,CAAC;SACd;QAED,IAAI,KAAK,YAAY,IAAI,EAAE;YACzB,OAAO,IAAI,CAAC;SACb;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,IAAA,+BAAkB,EAAC,KAAK,CAAC,CAAC;SAClC;QAED,IAAI,IAAA,oBAAY,EAAC,KAAK,CAAC,EAAE;YACvB,sFAAsF;YACtF,IAAI,KAAK,CAAC,MAAM,KAAK,gBAAgB,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;SACjE;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;;OAGG;IACI,wBAAmB,GAA1B,UAA2B,SAAiB;QAC1C,IAAM,MAAM,GAAG,IAAA,kCAAqB,EAAC,SAAS,CAAC,CAAC;QAChD,OAAO,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC;IAED;;;;;OAKG;IACH,eAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC,GAA1C;QACE,OAAO,IAAI,CAAC,OAAO,EAAE,CAAC;IACxB,CAAC;IAED,sBAAO,GAAP;QACE,OAAO,qBAAa,IAAI,CAAC,WAAW,EAAE,QAAI,CAAC;IAC7C,CAAC;IACH,WAAC;AAAD,CAAC,AA9KD,CAA0B,MAAM,GA8K/B;AA9KY,oBAAI"}