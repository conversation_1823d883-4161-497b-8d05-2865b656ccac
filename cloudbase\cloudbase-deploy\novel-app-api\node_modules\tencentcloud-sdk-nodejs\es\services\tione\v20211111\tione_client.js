import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("tione.tencentcloudapi.com", "2021-11-11", clientConfig);
    }
    async DescribeTrainingModelVersion(req, cb) {
        return this.request("DescribeTrainingModelVersion", req, cb);
    }
    async DescribeModelService(req, cb) {
        return this.request("DescribeModelService", req, cb);
    }
    async DescribeBillingSpecs(req, cb) {
        return this.request("DescribeBillingSpecs", req, cb);
    }
    async CreateTrainingModel(req, cb) {
        return this.request("CreateTrainingModel", req, cb);
    }
    async DescribeBillingSpecsPrice(req, cb) {
        return this.request("DescribeBillingSpecsPrice", req, cb);
    }
    async DescribeModelServiceCallInfo(req, cb) {
        return this.request("DescribeModelServiceCallInfo", req, cb);
    }
    async DescribeTrainingTask(req, cb) {
        return this.request("DescribeTrainingTask", req, cb);
    }
    async DescribeEvents(req, cb) {
        return this.request("DescribeEvents", req, cb);
    }
    async DescribeBuildInImages(req, cb) {
        return this.request("DescribeBuildInImages", req, cb);
    }
    async StartNotebook(req, cb) {
        return this.request("StartNotebook", req, cb);
    }
    async DeleteDataset(req, cb) {
        return this.request("DeleteDataset", req, cb);
    }
    async CreateTrainingTask(req, cb) {
        return this.request("CreateTrainingTask", req, cb);
    }
    async ModifyModelServiceAuthorization(req, cb) {
        return this.request("ModifyModelServiceAuthorization", req, cb);
    }
    async DeleteNotebook(req, cb) {
        return this.request("DeleteNotebook", req, cb);
    }
    async DeleteModelServiceAuthToken(req, cb) {
        return this.request("DeleteModelServiceAuthToken", req, cb);
    }
    async DescribeTrainingTaskPods(req, cb) {
        return this.request("DescribeTrainingTaskPods", req, cb);
    }
    async DescribeModelServiceHotUpdated(req, cb) {
        return this.request("DescribeModelServiceHotUpdated", req, cb);
    }
    async DescribeModelAccelerateTask(req, cb) {
        return this.request("DescribeModelAccelerateTask", req, cb);
    }
    async ChatCompletion(req, cb) {
        return this.request("ChatCompletion", req, cb);
    }
    async ModifyNotebookTags(req, cb) {
        return this.request("ModifyNotebookTags", req, cb);
    }
    async DeleteTrainingModel(req, cb) {
        return this.request("DeleteTrainingModel", req, cb);
    }
    async CreateDataset(req, cb) {
        return this.request("CreateDataset", req, cb);
    }
    async CreateNotebook(req, cb) {
        return this.request("CreateNotebook", req, cb);
    }
    async StartTrainingTask(req, cb) {
        return this.request("StartTrainingTask", req, cb);
    }
    async DescribeModelServiceGroup(req, cb) {
        return this.request("DescribeModelServiceGroup", req, cb);
    }
    async DeleteModelServiceGroup(req, cb) {
        return this.request("DeleteModelServiceGroup", req, cb);
    }
    async PushTrainingMetrics(req, cb) {
        return this.request("PushTrainingMetrics", req, cb);
    }
    async StopTrainingTask(req, cb) {
        return this.request("StopTrainingTask", req, cb);
    }
    async DescribeTrainingModelVersions(req, cb) {
        return this.request("DescribeTrainingModelVersions", req, cb);
    }
    async ModifyModelServiceAuthToken(req, cb) {
        return this.request("ModifyModelServiceAuthToken", req, cb);
    }
    async CreateModelService(req, cb) {
        return this.request("CreateModelService", req, cb);
    }
    async CreatePresignedNotebookUrl(req, cb) {
        return this.request("CreatePresignedNotebookUrl", req, cb);
    }
    async DescribeLogs(req, cb) {
        return this.request("DescribeLogs", req, cb);
    }
    async DeleteModelService(req, cb) {
        return this.request("DeleteModelService", req, cb);
    }
    async DescribeDatasets(req, cb) {
        return this.request("DescribeDatasets", req, cb);
    }
    async CreateModelServiceAuthToken(req, cb) {
        return this.request("CreateModelServiceAuthToken", req, cb);
    }
    async DescribeBillingResourceInstanceRunningJobs(req, cb) {
        return this.request("DescribeBillingResourceInstanceRunningJobs", req, cb);
    }
    async DescribeTrainingTasks(req, cb) {
        return this.request("DescribeTrainingTasks", req, cb);
    }
    async ModifyModelService(req, cb) {
        return this.request("ModifyModelService", req, cb);
    }
    async DescribeNotebooks(req, cb) {
        return this.request("DescribeNotebooks", req, cb);
    }
    async DeleteTrainingTask(req, cb) {
        return this.request("DeleteTrainingTask", req, cb);
    }
    async DescribeModelAccelerateVersions(req, cb) {
        return this.request("DescribeModelAccelerateVersions", req, cb);
    }
    async StopModelAccelerateTask(req, cb) {
        return this.request("StopModelAccelerateTask", req, cb);
    }
    async DescribePlatformImages(req, cb) {
        return this.request("DescribePlatformImages", req, cb);
    }
    async DescribeBillingResourceGroup(req, cb) {
        return this.request("DescribeBillingResourceGroup", req, cb);
    }
    async DescribeNotebook(req, cb) {
        return this.request("DescribeNotebook", req, cb);
    }
    async DeleteTrainingModelVersion(req, cb) {
        return this.request("DeleteTrainingModelVersion", req, cb);
    }
    async DescribeBillingResourceGroups(req, cb) {
        return this.request("DescribeBillingResourceGroups", req, cb);
    }
    async DescribeInferTemplates(req, cb) {
        return this.request("DescribeInferTemplates", req, cb);
    }
    async DescribeModelServiceGroups(req, cb) {
        return this.request("DescribeModelServiceGroups", req, cb);
    }
    async StopNotebook(req, cb) {
        return this.request("StopNotebook", req, cb);
    }
}
