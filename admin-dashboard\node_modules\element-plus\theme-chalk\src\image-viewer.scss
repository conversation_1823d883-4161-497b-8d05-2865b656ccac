@use 'mixins/mixins' as *;
@use 'common/var' as *;

@mixin op-icon() {
  width: 44px;
  height: 44px;
  font-size: 24px;
  color: #fff;
  background-color: getCssVar('text-color', 'regular');
  border-color: #fff;
}

@include b(image-viewer) {
  @include e(wrapper) {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;

    &:focus {
      outline: none !important;
    }
  }

  @include e(btn) {
    position: absolute;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    opacity: 0.8;
    cursor: pointer;
    box-sizing: border-box;
    user-select: none;

    .#{$namespace}-icon {
      cursor: pointer;
    }
  }

  @include e(close) {
    top: 40px;
    right: 40px;
    width: 40px;
    height: 40px;
    font-size: 40px;
  }

  @include e(canvas) {
    position: static;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    user-select: none;
  }

  @include e(actions) {
    left: 50%;
    bottom: 30px;
    transform: translateX(-50%);
    height: 44px;
    padding: 0 23px;
    background-color: getCssVar('text-color', 'regular');
    border-color: #fff;
    border-radius: 22px;

    @include e(actions__inner) {
      width: 100%;
      height: 100%;
      cursor: default;
      font-size: 23px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: space-around;
      gap: 22px;
      padding: 0 6px;

      @include e(actions__divider) {
        margin: 0 -6px;
      }
    }
  }

  @include e(progress) {
    left: 50%;
    transform: translateX(-50%);
    cursor: default;
    color: #fff;
    bottom: 90px;
  }

  @include e(prev) {
    top: 50%;
    transform: translateY(-50%);
    left: 40px;
    @include op-icon();
  }

  @include e(next) {
    top: 50%;
    transform: translateY(-50%);
    right: 40px;
    text-indent: 2px;
    @include op-icon();
  }

  @include e(close) {
    @include op-icon();
  }

  @include e(mask) {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.5;
    background: #000;
  }
}

.viewer-fade-enter-active {
  animation: viewer-fade-in getCssVar('transition-duration');
}

.viewer-fade-leave-active {
  animation: viewer-fade-out getCssVar('transition-duration');
}

@keyframes viewer-fade-in {
  0% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}

@keyframes viewer-fade-out {
  0% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
  100% {
    transform: translate3d(0, -20px, 0);
    opacity: 0;
  }
}
