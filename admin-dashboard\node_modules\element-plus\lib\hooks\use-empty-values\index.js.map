{"version": 3, "file": "index.js", "sources": ["../../../../../packages/hooks/use-empty-values/index.ts"], "sourcesContent": ["import { computed, getCurrentInstance, inject, ref } from 'vue'\nimport { buildProps, debugWarn, isFunction } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, InjectionKey, Ref } from 'vue'\n\ntype EmptyValuesContext = ExtractPropTypes<typeof useEmptyValuesProps>\n\nexport const emptyValuesContextKey: InjectionKey<Ref<EmptyValuesContext>> =\n  Symbol('emptyValuesContextKey')\nexport const SCOPE = 'use-empty-values'\nexport const DEFAULT_EMPTY_VALUES = ['', undefined, null]\nexport const DEFAULT_VALUE_ON_CLEAR = undefined\n\nexport const useEmptyValuesProps = buildProps({\n  /**\n   * @description empty values supported by the component\n   */\n  emptyValues: Array,\n  /**\n   * @description return value when cleared, if you want to set `undefined`, use `() => undefined`\n   */\n  valueOnClear: {\n    type: [String, Number, Boolean, Function],\n    default: undefined,\n    validator: (val: any) => (isFunction(val) ? !val() : !val),\n  },\n} as const)\n\nexport const useEmptyValues = (\n  props: EmptyValuesContext,\n  defaultValue?: null | undefined\n) => {\n  const config = getCurrentInstance()\n    ? inject(emptyValuesContextKey, ref<EmptyValuesContext>({}))\n    : ref<EmptyValuesContext>({})\n\n  const emptyValues = computed(\n    () => props.emptyValues || config.value.emptyValues || DEFAULT_EMPTY_VALUES\n  )\n\n  const valueOnClear = computed(() => {\n    // function is used for undefined cause undefined can't be a value of prop\n    if (isFunction(props.valueOnClear)) {\n      return props.valueOnClear()\n    } else if (props.valueOnClear !== undefined) {\n      return props.valueOnClear\n    } else if (isFunction(config.value.valueOnClear)) {\n      return config.value.valueOnClear()\n    } else if (config.value.valueOnClear !== undefined) {\n      return config.value.valueOnClear\n    }\n    return defaultValue !== undefined ? defaultValue : DEFAULT_VALUE_ON_CLEAR\n  })\n\n  const isEmptyValue = (value: any) => {\n    return emptyValues.value.includes(value)\n  }\n\n  if (!emptyValues.value.includes(valueOnClear.value)) {\n    debugWarn(SCOPE, 'value-on-clear should be a value of empty-values')\n  }\n\n  return {\n    emptyValues,\n    valueOnClear,\n    isEmptyValue,\n  }\n}\n"], "names": ["buildProps", "isFunction", "getCurrentInstance", "inject", "ref", "computed", "debugWarn"], "mappings": ";;;;;;;;;AAEY,MAAC,qBAAqB,GAAG,MAAM,CAAC,uBAAuB,EAAE;AACzD,MAAC,KAAK,GAAG,mBAAmB;AAC5B,MAAC,oBAAoB,GAAG,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,EAAE;AAC3C,MAAC,sBAAsB,GAAG,KAAK,EAAE;AACjC,MAAC,mBAAmB,GAAGA,kBAAU,CAAC;AAC9C,EAAE,WAAW,EAAE,KAAK;AACpB,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC;AAC7C,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,IAAI,SAAS,EAAE,CAAC,GAAG,KAAKC,iBAAU,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG;AACvD,GAAG;AACH,CAAC,EAAE;AACS,MAAC,cAAc,GAAG,CAAC,KAAK,EAAE,YAAY,KAAK;AACvD,EAAE,MAAM,MAAM,GAAGC,sBAAkB,EAAE,GAAGC,UAAM,CAAC,qBAAqB,EAAEC,OAAG,CAAC,EAAE,CAAC,CAAC,GAAGA,OAAG,CAAC,EAAE,CAAC,CAAC;AACzF,EAAE,MAAM,WAAW,GAAGC,YAAQ,CAAC,MAAM,KAAK,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,oBAAoB,CAAC,CAAC;AAC5G,EAAE,MAAM,YAAY,GAAGA,YAAQ,CAAC,MAAM;AACtC,IAAI,IAAIJ,iBAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACxC,MAAM,OAAO,KAAK,CAAC,YAAY,EAAE,CAAC;AAClC,KAAK,MAAM,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,EAAE;AAC9C,MAAM,OAAO,KAAK,CAAC,YAAY,CAAC;AAChC,KAAK,MAAM,IAAIA,iBAAU,CAAC,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AACtD,MAAM,OAAO,MAAM,CAAC,KAAK,CAAC,YAAY,EAAE,CAAC;AACzC,KAAK,MAAM,IAAI,MAAM,CAAC,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,EAAE;AACrD,MAAM,OAAO,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC;AACvC,KAAK;AACL,IAAI,OAAO,YAAY,KAAK,KAAK,CAAC,GAAG,YAAY,GAAG,sBAAsB,CAAC;AAC3E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK;AAClC,IAAI,OAAO,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC7C,GAAG,CAAC;AACJ,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;AACvD,IAAIK,eAAS,CAAC,KAAK,EAAE,kDAAkD,CAAC,CAAC;AACzE,GAAG;AACH,EAAE,OAAO;AACT,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,YAAY;AAChB,GAAG,CAAC;AACJ;;;;;;;;;"}