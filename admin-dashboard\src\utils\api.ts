import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 获取API基础URL - 统一使用CloudBase云函数
const getApiBaseUrl = () => {
  // 优先使用环境变量
  if (import.meta.env.VITE_API_BASE_URL) {
    return import.meta.env.VITE_API_BASE_URL
  }

  // 开发环境使用本地模拟数据，生产环境使用CloudBase
  if (import.meta.env.DEV) {
    // 开发环境：使用本地模拟API
    return '/api'
  }

  // 生产环境：使用CloudBase云函数API
  return 'https://api.dznovel.top/api'
}

console.log('🔧 后台管理系统API配置:')
console.log('   环境:', import.meta.env.MODE)
console.log('   API地址:', getApiBaseUrl())

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 30000, // 增加超时时间到30秒
  headers: {
    'Content-Type': 'application/json'
  }
})

// 添加API路径参数的辅助函数
const addApiPathParam = (url: string): string => {
  // 如果是生产环境，需要添加_api_path查询参数
  if (import.meta.env.PROD || getApiBaseUrl().includes('api.dznovel.top')) {
    const separator = url.includes('?') ? '&' : '?'
    const apiPath = url.startsWith('/') ? url.substring(1) : url
    return `${url}${separator}_api_path=${apiPath}`
  }
  return url
}

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('admin_token')
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 为生产环境添加API路径参数
    if (config.url) {
      config.url = addApiPathParam(config.url)
    }

    console.log('🔍 API请求:', config.method?.toUpperCase(), config.url)

    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    console.error('API请求错误:', error)
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          ElMessage.error('未授权，请重新登录')
          localStorage.removeItem('admin_token')
          localStorage.removeItem('admin_user')
          window.location.href = '/login'
          break
        case 403:
          ElMessage.error('权限不足')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error(data?.message || '请求失败')
      }
    } else if (error.request) {
      ElMessage.error('网络连接失败，请检查网络')
    } else {
      ElMessage.error('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// API接口定义
export const authApi = {
  // 管理员登录
  adminLogin: (data: { username: string; password: string }) =>
    api.post('/admin/login', data),

  // 验证管理员token
  verifyAdminToken: () => api.get('/admin/verify'),

  // 管理员登出
  adminLogout: () => api.post('/admin/logout')
}

export const userApi = {
  // 获取用户列表
  getUsers: (params?: any) => api.get('/users', { params }),
  
  // 获取用户详情
  getUserById: (id: string) => api.get(`/users/${id}`),
  
  // 更新用户信息
  updateUser: (id: string, data: any) => api.put(`/users/${id}`, data),
  
  // 删除用户
  deleteUser: (id: string) => api.delete(`/users/${id}`),
  
  // 批量删除用户
  batchDeleteUsers: (ids: string[]) => api.post('/users/batch-delete', { ids }),
  
  // 获取用户统计
  getUserStats: () => api.get('/users/stats')
}

export const novelApi = {
  // 获取小说列表
  getNovels: (params?: any) => api.get('/novels', { params }),
  
  // 获取小说详情
  getNovelById: (id: string) => api.get(`/novels/${id}`),
  
  // 删除小说
  deleteNovel: (id: string) => api.delete(`/novels/${id}`),
  
  // 获取小说统计
  getNovelStats: () => api.get('/novels/stats')
}

export const memberApi = {
  // 获取会员码列表
  getMemberCodes: (params?: any) => api.get('/member-codes', { params }),

  // 生成会员码
  generateMemberCodes: (data: any) => api.post('/member-codes/generate', data),

  // 删除会员码
  deleteMemberCode: (id: string) => api.delete(`/member-codes/${id}`),

  // 获取会员统计
  getMemberStats: () => api.get('/members/stats')
}

export const packageApi = {
  // 获取套餐列表
  getPackages: () => api.get('/packages'),

  // 添加套餐
  createPackage: (data: any) => api.post('/packages', data),

  // 更新套餐
  updatePackage: (id: string, data: any) => api.put(`/packages/${id}`, data),

  // 删除套餐
  deletePackage: (id: string) => api.delete(`/packages/${id}`)
}

export const syncApi = {
  // 获取同步记录
  getSyncRecords: (params?: any) => api.get('/sync/records', { params }),

  // 获取同步统计
  getSyncStats: () => api.get('/sync/stats'),

  // 手动触发用户同步
  triggerUserSync: (data: { userId: string; syncContent: string[] }) =>
    api.post('/sync/trigger', data),

  // 获取同步详情
  getSyncDetail: (syncId: string) => api.get(`/sync/records/${syncId}`),

  // 重试失败的同步
  retrySyncRecord: (syncId: string) => api.post(`/sync/records/${syncId}/retry`),

  // 获取用户同步状态
  getUserSyncStatus: (userId: string) => api.get(`/sync/users/${userId}/status`),

  // 获取同步任务队列
  getSyncQueue: () => api.get('/sync/queue'),

  // 清理同步记录
  cleanSyncRecords: (params: { beforeDate?: string; status?: string }) =>
    api.delete('/sync/records/clean', { params }),

  // 导出同步记录
  exportSyncRecords: (params?: any) => api.get('/sync/records/export', { params }),

  // 获取同步配置
  getSyncConfig: () => api.get('/sync/config'),

  // 更新同步配置
  updateSyncConfig: (data: any) => api.put('/sync/config', data)
}

export const dashboardApi = {
  // 获取仪表板数据
  getDashboardData: () => api.get('/dashboard'),
  
  // 获取系统状态
  getSystemStatus: () => api.get('/system/status')
}

export { api }
