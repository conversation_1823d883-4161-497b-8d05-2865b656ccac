{"version": 3, "file": "ru.mjs", "sources": ["../../../../../packages/locale/lang/ru.ts"], "sourcesContent": ["export default {\n  name: 'ru',\n  el: {\n    breadcrumb: {\n      label: 'Хлебные крошки',\n    },\n    colorpicker: {\n      confirm: 'Ок',\n      clear: 'Очистить',\n    },\n    datepicker: {\n      now: 'Сейчас',\n      today: 'Сегодня',\n      cancel: 'Отмена',\n      clear: 'Очистить',\n      confirm: 'Ок',\n      selectDate: 'Выбрать дату',\n      selectTime: 'Выбрать время',\n      startDate: 'Дата начала',\n      startTime: 'Время начала',\n      endDate: 'Дата окончания',\n      endTime: 'Время окончания',\n      prevYear: 'Предыдущий год',\n      nextYear: 'Следующий год',\n      prevMonth: 'Предыдущий месяц',\n      nextMonth: 'Следующий месяц',\n      year: '',\n      month1: 'Январь',\n      month2: 'Февраль',\n      month3: 'Март',\n      month4: 'Апрель',\n      month5: 'Май',\n      month6: 'Июнь',\n      month7: 'Июль',\n      month8: 'Август',\n      month9: 'Сентябрь',\n      month10: 'Октябрь',\n      month11: 'Ноябрь',\n      month12: 'Декабрь',\n      week: 'неделя',\n      weeks: {\n        sun: 'Вс',\n        mon: 'Пн',\n        tue: 'Вт',\n        wed: 'Ср',\n        thu: 'Чт',\n        fri: 'Пт',\n        sat: 'Сб',\n      },\n      months: {\n        jan: 'Янв',\n        feb: 'Фев',\n        mar: 'Мар',\n        apr: 'Апр',\n        may: 'Май',\n        jun: 'Июн',\n        jul: 'Июл',\n        aug: 'Авг',\n        sep: 'Сен',\n        oct: 'Окт',\n        nov: 'Ноя',\n        dec: 'Дек',\n      },\n    },\n    select: {\n      loading: 'Загрузка',\n      noMatch: 'Совпадений не найдено',\n      noData: 'Нет данных',\n      placeholder: 'Выбрать',\n    },\n    mention: {\n      loading: 'Загрузка',\n    },\n    cascader: {\n      noMatch: 'Совпадений не найдено',\n      loading: 'Загрузка',\n      placeholder: 'Выбрать',\n      noData: 'Нет данных',\n    },\n    pagination: {\n      goto: 'Перейти',\n      pagesize: ' на странице',\n      total: 'Всего {total}',\n      pageClassifier: '',\n      page: 'Страница',\n      prev: 'Перейти на предыдущую страницу',\n      next: 'Перейти на следующую страницу',\n      currentPage: 'страница {pager}',\n      prevPages: 'Предыдущие {pager} страниц',\n      nextPages: 'Следующие {pager} страниц',\n    },\n    messagebox: {\n      title: 'Сообщение',\n      confirm: 'Ок',\n      cancel: 'Отмена',\n      error: 'Недопустимый ввод данных',\n    },\n    upload: {\n      deleteTip: 'Нажмите [Удалить] для удаления',\n      delete: 'Удалить',\n      preview: 'Превью',\n      continue: 'Продолжить',\n    },\n    table: {\n      emptyText: 'Нет данных',\n      confirmFilter: 'Ок',\n      resetFilter: 'Сбросить',\n      clearFilter: 'Все',\n      sumText: 'Сумма',\n    },\n    tour: {\n      next: 'Далее',\n      previous: 'Назад',\n      finish: 'Завершить',\n    },\n    tree: {\n      emptyText: 'Нет данных',\n    },\n    transfer: {\n      noMatch: 'Совпадений не найдено',\n      noData: 'Нет данных',\n      titles: ['Список 1', 'Список 2'],\n      filterPlaceholder: 'Введите ключевое слово',\n      noCheckedFormat: '{total} пунктов',\n      hasCheckedFormat: '{checked}/{total} выбрано',\n    },\n    image: {\n      error: 'ОШИБКА',\n    },\n    pageHeader: {\n      title: 'Назад',\n    },\n    popconfirm: {\n      confirmButtonText: 'Ок',\n      cancelButtonText: 'Отмена',\n    },\n    carousel: {\n      leftArrow: 'Слайдер стрелка влево',\n      rightArrow: 'Слайдер стрелка вправо',\n      indicator: 'Слайдер перейти на страницу под номером {index}',\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,iFAAiF;AAC9F,KAAK;AACL,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,KAAK,EAAE,kDAAkD;AAC/D,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,sCAAsC;AACjD,MAAM,KAAK,EAAE,4CAA4C;AACzD,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,kDAAkD;AAC/D,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,UAAU,EAAE,qEAAqE;AACvF,MAAM,UAAU,EAAE,2EAA2E;AAC7F,MAAM,SAAS,EAAE,+DAA+D;AAChF,MAAM,SAAS,EAAE,qEAAqE;AACtF,MAAM,OAAO,EAAE,iFAAiF;AAChG,MAAM,OAAO,EAAE,uFAAuF;AACtG,MAAM,QAAQ,EAAE,iFAAiF;AACjG,MAAM,QAAQ,EAAE,2EAA2E;AAC3F,MAAM,SAAS,EAAE,6FAA6F;AAC9G,MAAM,SAAS,EAAE,uFAAuF;AACxG,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,oBAAoB;AAClC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,0BAA0B;AACxC,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,MAAM,EAAE,kDAAkD;AAChE,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,OAAO,EAAE,4CAA4C;AAC3D,MAAM,IAAI,EAAE,sCAAsC;AAClD,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,QAAQ,GAAG,EAAE,cAAc;AAC3B,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,QAAQ,GAAG,EAAE,oBAAoB;AACjC,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,OAAO,EAAE,sHAAsH;AACrI,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,WAAW,EAAE,4CAA4C;AAC/D,KAAK;AACL,IAAI,OAAO,EAAE;AACb,MAAM,OAAO,EAAE,kDAAkD;AACjE,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sHAAsH;AACrI,MAAM,OAAO,EAAE,kDAAkD;AACjE,MAAM,WAAW,EAAE,4CAA4C;AAC/D,MAAM,MAAM,EAAE,yDAAyD;AACvE,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,4CAA4C;AACxD,MAAM,QAAQ,EAAE,gEAAgE;AAChF,MAAM,KAAK,EAAE,wCAAwC;AACrD,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,kDAAkD;AAC9D,MAAM,IAAI,EAAE,uKAAuK;AACnL,MAAM,IAAI,EAAE,iKAAiK;AAC7K,MAAM,WAAW,EAAE,0DAA0D;AAC7E,MAAM,SAAS,EAAE,iHAAiH;AAClI,MAAM,SAAS,EAAE,2GAA2G;AAC5H,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,wDAAwD;AACrE,MAAM,OAAO,EAAE,cAAc;AAC7B,MAAM,MAAM,EAAE,sCAAsC;AACpD,MAAM,KAAK,EAAE,wIAAwI;AACrJ,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,6JAA6J;AAC9K,MAAM,MAAM,EAAE,4CAA4C;AAC1D,MAAM,OAAO,EAAE,sCAAsC;AACrD,MAAM,QAAQ,EAAE,8DAA8D;AAC9E,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,yDAAyD;AAC1E,MAAM,aAAa,EAAE,cAAc;AACnC,MAAM,WAAW,EAAE,kDAAkD;AACrE,MAAM,WAAW,EAAE,oBAAoB;AACvC,MAAM,OAAO,EAAE,gCAAgC;AAC/C,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,IAAI,EAAE,gCAAgC;AAC5C,MAAM,QAAQ,EAAE,gCAAgC;AAChD,MAAM,MAAM,EAAE,wDAAwD;AACtE,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,yDAAyD;AAC1E,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,sHAAsH;AACrI,MAAM,MAAM,EAAE,yDAAyD;AACvE,MAAM,MAAM,EAAE,CAAC,wCAAwC,EAAE,wCAAwC,CAAC;AAClG,MAAM,iBAAiB,EAAE,4HAA4H;AACrJ,MAAM,eAAe,EAAE,oDAAoD;AAC3E,MAAM,gBAAgB,EAAE,8DAA8D;AACtF,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,sCAAsC;AACnD,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,gCAAgC;AAC7C,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,cAAc;AACvC,MAAM,gBAAgB,EAAE,sCAAsC;AAC9D,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,sHAAsH;AACvI,MAAM,UAAU,EAAE,4HAA4H;AAC9I,MAAM,SAAS,EAAE,2NAA2N;AAC5O,KAAK;AACL,GAAG;AACH,CAAC;;;;"}