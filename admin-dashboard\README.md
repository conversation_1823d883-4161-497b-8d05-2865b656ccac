# 小说应用后台管理系统

基于 Vue 3 + Element Plus + TypeScript 构建的现代化后台管理系统。

## 🚀 功能特性

### 📊 仪表板
- 系统概览和关键指标
- 用户增长趋势图表
- 小说创作统计
- 实时系统状态监控

### 👥 用户管理
- 用户列表查看和搜索
- 用户详情页面
- 会员状态管理
- 数据同步设置
- 批量操作支持

### 🎫 会员码管理
- 会员码生成和管理
- 批量生成会员码
- 使用状态跟踪
- 过期时间管理

### 📚 内容管理（开发中）
- 小说管理
- 角色管理
- 知识库管理

### 📈 数据分析（开发中）
- 业务数据分析
- 报表生成
- 趋势分析

## 🛠️ 技术栈

- **前端框架**: Vue 3 + Composition API
- **UI组件库**: Element Plus
- **开发语言**: TypeScript
- **路由管理**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **图表库**: ECharts + Vue-ECharts
- **构建工具**: Vite
- **包管理器**: npm

## 📦 安装和运行

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
cd admin-dashboard
npm install
```

### 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:8080

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🔐 登录信息

### 管理员账号
- 用户名: `admin`
- 密码: `admin123`

## 📁 项目结构

```
admin-dashboard/
├── public/                 # 静态资源
├── src/
│   ├── components/         # 公共组件
│   ├── layout/            # 布局组件
│   │   ├── index.vue      # 主布局
│   │   └── Sidebar.vue    # 侧边栏
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── stores/            # 状态管理
│   │   ├── app.ts         # 应用状态
│   │   └── auth.ts        # 认证状态
│   ├── styles/            # 样式文件
│   │   └── index.css      # 全局样式
│   ├── utils/             # 工具函数
│   │   └── api.ts         # API接口
│   ├── views/             # 页面组件
│   │   ├── Dashboard.vue  # 仪表板
│   │   ├── Login.vue      # 登录页
│   │   ├── Users/         # 用户管理
│   │   ├── MemberCodes/   # 会员码管理
│   │   └── ...
│   ├── App.vue            # 根组件
│   └── main.ts            # 入口文件
├── index.html             # HTML模板
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript配置
├── vite.config.ts         # Vite配置
└── README.md              # 项目说明
```

## 🔌 API接口

后台管理系统通过代理连接到 CloudBase 服务器：

### 管理员认证
- `POST /api/admin/login` - 管理员登录
- `POST /api/admin/logout` - 管理员登出
- `GET /api/admin/check-token` - 检查token有效性

### 仪表板
- `GET /api/dashboard` - 获取仪表板数据

### 用户管理
- `GET /api/users` - 获取用户列表
- `GET /api/users/:id` - 获取用户详情
- `PUT /api/users/:id` - 更新用户信息
- `DELETE /api/users/:id` - 删除用户

### 会员码管理
- `GET /api/member-codes` - 获取会员码列表
- `POST /api/member-codes/generate` - 生成会员码
- `DELETE /api/member-codes/:id` - 删除会员码

## 🎨 界面特性

### 响应式设计
- 支持桌面端和移动端
- 自适应布局
- 触摸友好的交互

### 主题支持
- 明亮主题
- 暗黑主题
- 主题切换动画

### 用户体验
- 流畅的页面切换动画
- 直观的数据可视化
- 友好的错误提示
- 加载状态指示

## 🔧 开发指南

### 添加新页面
1. 在 `src/views/` 下创建页面组件
2. 在 `src/router/index.ts` 中添加路由配置
3. 在侧边栏菜单中添加导航项

### 添加新API
1. 在 `src/utils/api.ts` 中定义API接口
2. 在页面组件中调用API
3. 处理加载状态和错误

### 样式规范
- 使用 Element Plus 的设计规范
- 保持一致的间距和颜色
- 响应式设计优先

## 🚀 部署

### 构建
```bash
npm run build
```

### 部署到静态服务器
将 `dist` 目录部署到任何静态文件服务器即可。

### 环境变量
- `VITE_API_URL`: API服务器地址（默认: `/api`）

## 📝 更新日志

### v1.0.0 (2025-01-21)
- ✅ 完成基础架构搭建
- ✅ 实现用户管理功能
- ✅ 实现会员码管理功能
- ✅ 完成仪表板页面
- ✅ 集成图表和数据可视化
- ✅ 响应式设计和主题切换

### 计划中的功能
- 📚 小说内容管理
- 📊 数据分析和报表
- 🔔 系统通知
- 📱 移动端优化
- 🌐 国际化支持

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
