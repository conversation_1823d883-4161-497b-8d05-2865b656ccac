"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.isValidEnvFormat = exports.isPageModuleName = exports.processReturn = exports.setThrowOnCode = exports.second = exports.isNonEmptyString = exports.E = exports.filterUndefined = exports.filterValue = exports.isAppId = exports.TcbError = void 0;
class TcbError extends Error {
    constructor(error) {
        super(error.message);
        this.code = error.code;
        this.message = error.message;
        this.requestId = error.requestId || '';
    }
}
exports.TcbError = TcbError;
function isAppId(appIdStr) {
    return /^[1-9][0-9]{4,64}$/gim.test(appIdStr);
}
exports.isAppId = isAppId;
function filterValue(o, value) {
    for (const key in o) {
        if (o[key] === value) {
            /* eslint-disable-next-line @typescript-eslint/no-dynamic-delete */
            delete o[key];
        }
    }
}
exports.filterValue = filterValue;
function filterUndefined(o) {
    filterValue(o, undefined);
}
exports.filterUndefined = filterUndefined;
function E(errObj) {
    return new TcbError(errObj);
}
exports.E = E;
function isNonEmptyString(str) {
    return typeof str === 'string' && str !== '';
}
exports.isNonEmptyString = isNonEmptyString;
function second() {
    // istanbul ignore next
    return Math.floor(new Date().getTime() / 1000);
}
exports.second = second;
// 兼容模式开关，兼容模式下，不抛出异常，直接返回
let throwOnCode = true;
function setThrowOnCode(value) {
    throwOnCode = value;
}
exports.setThrowOnCode = setThrowOnCode;
function processReturn(result) {
    if (!throwOnCode) {
        // 不抛报错，直接返回
        return result;
    }
    throw E(Object.assign({}, result));
}
exports.processReturn = processReturn;
/**
 * 是否是场景模块名
 *
 * $: 前缀，表示SaaS场景模块名，非实际环境ID，当前通过特殊环境ID标识
 *
 * @param envId
 * @returns
 */
function isPageModuleName(envId = '') {
    return typeof envId === 'string' && envId.startsWith('$:');
}
exports.isPageModuleName = isPageModuleName;
// 20 + 1 + 16, 限制长度 40
const kEnvRuleReg = /^[a-z0-9_-]{1,40}$/;
function isValidEnvFormat(env = '') {
    return typeof env === 'string' && kEnvRuleReg.test(env);
}
exports.isValidEnvFormat = isValidEnvFormat;
