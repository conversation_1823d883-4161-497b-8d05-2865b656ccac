// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'agent_message.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class AgentMessageAdapter extends TypeAdapter<AgentMessage> {
  @override
  final int typeId = 13;

  @override
  AgentMessage read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return AgentMessage(
      id: fields[0] as String,
      content: fields[1] as String,
      type: fields[2] as AgentMessageType,
      timestamp: fields[3] as DateTime,
      sessionId: fields[4] as String?,
      novelTitle: fields[5] as String?,
      chapterNumber: fields[6] as int?,
      editSuggestionIds: (fields[7] as List?)?.cast<String>(),
    );
  }

  @override
  void write(BinaryWriter writer, AgentMessage obj) {
    writer
      ..writeByte(8)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.content)
      ..writeByte(2)
      ..write(obj.type)
      ..writeByte(3)
      ..write(obj.timestamp)
      ..writeByte(4)
      ..write(obj.sessionId)
      ..writeByte(5)
      ..write(obj.novelTitle)
      ..writeByte(6)
      ..write(obj.chapterNumber)
      ..writeByte(7)
      ..write(obj.editSuggestionIds);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AgentMessageAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}

class AgentMessageTypeAdapter extends TypeAdapter<AgentMessageType> {
  @override
  final int typeId = 12;

  @override
  AgentMessageType read(BinaryReader reader) {
    switch (reader.readByte()) {
      case 0:
        return AgentMessageType.user;
      case 1:
        return AgentMessageType.agent;
      case 2:
        return AgentMessageType.system;
      case 3:
        return AgentMessageType.error;
      default:
        return AgentMessageType.user;
    }
  }

  @override
  void write(BinaryWriter writer, AgentMessageType obj) {
    switch (obj) {
      case AgentMessageType.user:
        writer.writeByte(0);
        break;
      case AgentMessageType.agent:
        writer.writeByte(1);
        break;
      case AgentMessageType.system:
        writer.writeByte(2);
        break;
      case AgentMessageType.error:
        writer.writeByte(3);
        break;
    }
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AgentMessageTypeAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
