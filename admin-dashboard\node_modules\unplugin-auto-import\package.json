{"name": "unplugin-auto-import", "type": "module", "version": "0.17.8", "description": "Register global imports on demand for Vite and Webpack", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/unplugin/unplugin-auto-import#readme", "repository": {"type": "git", "url": "git+https://github.com/unplugin/unplugin-auto-import.git"}, "bugs": {"url": "https://github.com/unplugin/unplugin-auto-import/issues"}, "keywords": ["unplugin", "vite", "astro", "webpack", "rollup", "rspack", "auto-import", "transform"], "sideEffects": false, "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}, "./nuxt": {"import": {"types": "./dist/nuxt.d.ts", "default": "./dist/nuxt.js"}, "require": {"types": "./dist/nuxt.d.cts", "default": "./dist/nuxt.cjs"}}, "./astro": {"import": {"types": "./dist/astro.d.ts", "default": "./dist/astro.js"}, "require": {"types": "./dist/astro.d.cts", "default": "./dist/astro.cjs"}}, "./rollup": {"import": {"types": "./dist/rollup.d.ts", "default": "./dist/rollup.js"}, "require": {"types": "./dist/rollup.d.cts", "default": "./dist/rollup.cjs"}}, "./types": {"import": {"types": "./dist/types.d.ts", "default": "./dist/types.js"}, "require": {"types": "./dist/types.d.cts", "default": "./dist/types.cjs"}}, "./vite": {"import": {"types": "./dist/vite.d.ts", "default": "./dist/vite.js"}, "require": {"types": "./dist/vite.d.cts", "default": "./dist/vite.cjs"}}, "./webpack": {"import": {"types": "./dist/webpack.d.ts", "default": "./dist/webpack.js"}, "require": {"types": "./dist/webpack.d.cts", "default": "./dist/webpack.cjs"}}, "./rspack": {"import": {"types": "./dist/rspack.d.ts", "default": "./dist/rspack.js"}, "require": {"types": "./dist/rspack.d.cts", "default": "./dist/rspack.cjs"}}, "./esbuild": {"import": {"types": "./dist/esbuild.d.ts", "default": "./dist/esbuild.js"}, "require": {"types": "./dist/esbuild.d.cts", "default": "./dist/esbuild.cjs"}}, "./*": "./*"}, "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./*"]}}, "files": ["*.d.ts", "dist"], "engines": {"node": ">=14"}, "peerDependencies": {"@nuxt/kit": "^3.2.2", "@vueuse/core": "*"}, "peerDependenciesMeta": {"@nuxt/kit": {"optional": true}, "@vueuse/core": {"optional": true}}, "dependencies": {"@antfu/utils": "^0.7.10", "@rollup/pluginutils": "^5.1.0", "fast-glob": "^3.3.2", "local-pkg": "^0.5.0", "magic-string": "^0.30.10", "minimatch": "^9.0.4", "unimport": "^3.7.2", "unplugin": "^1.11.0"}, "devDependencies": {"@antfu/eslint-config": "^2.22.0", "@antfu/ni": "^0.21.12", "@nuxt/kit": "^3.12.3", "@svgr/plugin-jsx": "^8.1.0", "@types/node": "^20.14.10", "@types/resolve": "^1.20.6", "@vueuse/metadata": "^10.11.0", "bumpp": "^9.4.1", "eslint": "^9.6.0", "esno": "^4.7.0", "publint": "^0.2.8", "rollup": "^4.18.1", "tsup": "^8.1.0", "typescript": "^5.5.3", "vite": "^5.3.3", "vitest": "^2.0.2", "webpack": "^5.92.1"}, "scripts": {"build": "tsup src/*.ts --format cjs,esm --dts --splitting --clean", "dev": "tsup src/*.ts --watch src", "lint": "eslint .", "lint:fix": "nr lint --fix", "typecheck": "tsc", "play": "npm -C playground run dev", "release": "bumpp && pnpm publish", "start": "esno src/index.ts", "test": "vitest", "test:run": "vitest run"}}