{"version": 3, "file": "tree-store.mjs", "sources": ["../../../../../../../packages/components/tree/src/model/tree-store.ts"], "sourcesContent": ["import { nextTick } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { NOOP, hasOwn, isObject, isPropAbsent } from '@element-plus/utils'\nimport Node from './node'\nimport { getNodeKey } from './util'\n\nimport type {\n  FilterNodeMethodFunction,\n  FilterValue,\n  LoadFunction,\n  TreeData,\n  TreeKey,\n  TreeNodeData,\n  TreeOptionProps,\n  TreeStoreNodesMap,\n  TreeStoreOptions,\n} from '../tree.type'\n\nexport default class TreeStore {\n  currentNode: Node | null\n  currentNodeKey: TreeKey | null\n  nodesMap: TreeStoreNodesMap\n  root!: Node\n  data!: TreeData\n  lazy = false\n  load?: LoadFunction\n  filterNodeMethod?: FilterNodeMethodFunction\n  key!: TreeKey\n  defaultCheckedKeys?: TreeKey[]\n  checkStrictly = false\n  defaultExpandedKeys?: TreeKey[]\n  autoExpandParent = false\n  defaultExpandAll = false\n  checkDescendants = false\n  props!: TreeOptionProps\n\n  constructor(options: TreeStoreOptions) {\n    this.currentNode = null\n    this.currentNodeKey = null\n\n    for (const option in options) {\n      if (hasOwn(options, option)) {\n        this[option] = options[option]\n      }\n    }\n\n    this.nodesMap = {}\n  }\n\n  initialize() {\n    this.root = new Node({\n      data: this.data,\n      store: this,\n    })\n    this.root.initialize()\n\n    if (this.lazy && this.load) {\n      const loadFn = this.load\n      loadFn(\n        this.root,\n        (data) => {\n          this.root.doCreateChildren(data)\n          this._initDefaultCheckedNodes()\n        },\n        NOOP\n      )\n    } else {\n      this._initDefaultCheckedNodes()\n    }\n  }\n\n  filter(value: FilterValue): void {\n    const filterNodeMethod = this.filterNodeMethod\n    const lazy = this.lazy\n    const traverse = async function (node: TreeStore | Node) {\n      const childNodes = (node as TreeStore).root\n        ? (node as TreeStore).root.childNodes\n        : (node as Node).childNodes\n\n      for (const [index, child] of childNodes.entries()) {\n        child.visible = !!filterNodeMethod?.call(\n          child,\n          value,\n          child.data,\n          child\n        )\n\n        if (index % 80 === 0 && index > 0) {\n          await nextTick()\n        }\n        await traverse(child)\n      }\n\n      if (!(node as Node).visible && childNodes.length) {\n        let allHidden = true\n        allHidden = !childNodes.some((child) => child.visible)\n\n        if ((node as TreeStore).root) {\n          ;(node as TreeStore).root.visible = allHidden === false\n        } else {\n          ;(node as Node).visible = allHidden === false\n        }\n      }\n      if (!value) return\n\n      if ((node as Node).visible && !(node as Node).isLeaf) {\n        if (!lazy || (node as Node).loaded) {\n          ;(node as Node).expand()\n        }\n      }\n    }\n\n    traverse(this)\n  }\n\n  setData(newVal: TreeData): void {\n    const instanceChanged = newVal !== this.root.data\n    if (instanceChanged) {\n      this.nodesMap = {}\n      this.root.setData(newVal)\n      this._initDefaultCheckedNodes()\n      this.setCurrentNodeKey(this.currentNodeKey)\n    } else {\n      this.root.updateChildren()\n    }\n  }\n\n  getNode(data: TreeKey | TreeNodeData | Node): Node {\n    if (data instanceof Node) return data\n    const key = isObject(data) ? getNodeKey(this.key, data) : data\n    return this.nodesMap[key] || null\n  }\n\n  insertBefore(\n    data: TreeNodeData,\n    refData: TreeKey | TreeNodeData | Node\n  ): void {\n    const refNode = this.getNode(refData)\n    refNode.parent?.insertBefore({ data }, refNode)\n  }\n\n  insertAfter(\n    data: TreeNodeData,\n    refData: TreeKey | TreeNodeData | Node\n  ): void {\n    const refNode = this.getNode(refData)\n    refNode.parent?.insertAfter({ data }, refNode)\n  }\n\n  remove(data: TreeNodeData | Node): void {\n    const node = this.getNode(data)\n\n    if (node && node.parent) {\n      if (node === this.currentNode) {\n        this.currentNode = null\n      }\n      node.parent.removeChild(node)\n    }\n  }\n\n  append(data: TreeNodeData, parentData: TreeNodeData | TreeKey | Node): void {\n    const parentNode = !isPropAbsent(parentData)\n      ? this.getNode(parentData)\n      : this.root\n\n    if (parentNode) {\n      parentNode.insertChild({ data })\n    }\n  }\n\n  _initDefaultCheckedNodes(): void {\n    const defaultCheckedKeys = this.defaultCheckedKeys || []\n    const nodesMap = this.nodesMap\n\n    defaultCheckedKeys.forEach((checkedKey) => {\n      const node = nodesMap[checkedKey]\n\n      if (node) {\n        node.setChecked(true, !this.checkStrictly)\n      }\n    })\n  }\n\n  _initDefaultCheckedNode(node: Node): void {\n    const defaultCheckedKeys = this.defaultCheckedKeys || []\n\n    if (!isNil(node.key) && defaultCheckedKeys.includes(node.key)) {\n      node.setChecked(true, !this.checkStrictly)\n    }\n  }\n\n  setDefaultCheckedKey(newVal: TreeKey[]): void {\n    if (newVal !== this.defaultCheckedKeys) {\n      this.defaultCheckedKeys = newVal\n      this._initDefaultCheckedNodes()\n    }\n  }\n\n  registerNode(node: Node): void {\n    const key = this.key\n    if (!node || !node.data) return\n\n    if (!key) {\n      this.nodesMap[node.id] = node\n    } else {\n      const nodeKey = node.key\n      if (!isNil(nodeKey)) this.nodesMap[nodeKey] = node\n    }\n  }\n\n  deregisterNode(node: Node): void {\n    const key = this.key\n    if (!key || !node || !node.data) return\n\n    node.childNodes.forEach((child) => {\n      this.deregisterNode(child)\n    })\n\n    delete this.nodesMap[node.key!]\n  }\n\n  getCheckedNodes(\n    leafOnly = false,\n    includeHalfChecked = false\n  ): TreeNodeData[] {\n    const checkedNodes: TreeNodeData[] = []\n    const traverse = function (node: TreeStore | Node) {\n      const childNodes = (node as TreeStore).root\n        ? (node as TreeStore).root.childNodes\n        : (node as Node).childNodes\n\n      childNodes.forEach((child) => {\n        if (\n          (child.checked || (includeHalfChecked && child.indeterminate)) &&\n          (!leafOnly || (leafOnly && child.isLeaf))\n        ) {\n          checkedNodes.push(child.data)\n        }\n\n        traverse(child)\n      })\n    }\n\n    traverse(this)\n\n    return checkedNodes\n  }\n\n  getCheckedKeys(leafOnly = false): TreeKey[] {\n    return this.getCheckedNodes(leafOnly).map((data) => (data || {})[this.key])\n  }\n\n  getHalfCheckedNodes(): TreeNodeData[] {\n    const nodes: TreeNodeData[] = []\n    const traverse = function (node: TreeStore | Node) {\n      const childNodes = (node as TreeStore).root\n        ? (node as TreeStore).root.childNodes\n        : (node as Node).childNodes\n\n      childNodes.forEach((child) => {\n        if (child.indeterminate) {\n          nodes.push(child.data)\n        }\n\n        traverse(child)\n      })\n    }\n\n    traverse(this)\n\n    return nodes\n  }\n\n  getHalfCheckedKeys(): TreeKey[] {\n    return this.getHalfCheckedNodes().map((data) => (data || {})[this.key])\n  }\n\n  _getAllNodes(): Node[] {\n    const allNodes: Node[] = []\n    const nodesMap = this.nodesMap\n    for (const nodeKey in nodesMap) {\n      if (hasOwn(nodesMap, nodeKey)) {\n        allNodes.push(nodesMap[nodeKey])\n      }\n    }\n\n    return allNodes\n  }\n\n  updateChildren(key: TreeKey, data: TreeData): void {\n    const node = this.nodesMap[key]\n    if (!node) return\n    const childNodes = node.childNodes\n    for (let i = childNodes.length - 1; i >= 0; i--) {\n      const child = childNodes[i]\n      this.remove(child.data)\n    }\n    for (let i = 0, j = data.length; i < j; i++) {\n      const child = data[i]\n      this.append(child, node.data)\n    }\n  }\n\n  _setCheckedKeys(\n    key: TreeKey,\n    leafOnly = false,\n    checkedKeys: { [key: string]: boolean }\n  ) {\n    const allNodes = this._getAllNodes().sort((a, b) => a.level - b.level)\n    const cache: Record<TreeKey, boolean> = Object.create(null)\n    const keys = Object.keys(checkedKeys)\n    allNodes.forEach((node) => node.setChecked(false, false))\n    const cacheCheckedChild = (node: Node) => {\n      node.childNodes.forEach((child) => {\n        cache[child.data[key]] = true\n        if (child.childNodes?.length) {\n          cacheCheckedChild(child)\n        }\n      })\n    }\n    for (let i = 0, j = allNodes.length; i < j; i++) {\n      const node = allNodes[i]\n      const nodeKey: string = node.data[key].toString()\n      const checked = keys.includes(nodeKey)\n      if (!checked) {\n        if (node.checked && !cache[nodeKey]) {\n          node.setChecked(false, false)\n        }\n        continue\n      }\n\n      if (node.childNodes.length) {\n        cacheCheckedChild(node)\n      }\n\n      if (node.isLeaf || this.checkStrictly) {\n        node.setChecked(true, false)\n        continue\n      }\n      node.setChecked(true, true)\n\n      if (leafOnly) {\n        node.setChecked(false, false)\n        const traverse = function (node: Node): void {\n          const childNodes = node.childNodes\n          childNodes.forEach((child) => {\n            if (!child.isLeaf) {\n              child.setChecked(false, false)\n            }\n            traverse(child)\n          })\n        }\n        traverse(node)\n      }\n    }\n  }\n\n  setCheckedNodes(array: Node[], leafOnly = false): void {\n    const key = this.key\n    const checkedKeys: Record<TreeKey, boolean> = {}\n    array.forEach((item) => {\n      checkedKeys[((item || {}) as any)[key]] = true\n    })\n\n    this._setCheckedKeys(key, leafOnly, checkedKeys)\n  }\n\n  setCheckedKeys(keys: TreeKey[], leafOnly = false): void {\n    this.defaultCheckedKeys = keys\n    const key = this.key\n    const checkedKeys: Record<TreeKey, boolean> = {}\n    keys.forEach((key) => {\n      checkedKeys[key] = true\n    })\n\n    this._setCheckedKeys(key, leafOnly, checkedKeys)\n  }\n\n  setDefaultExpandedKeys(keys: TreeKey[]) {\n    keys = keys || []\n    this.defaultExpandedKeys = keys\n    keys.forEach((key) => {\n      const node = this.getNode(key)\n      if (node) node.expand(null, this.autoExpandParent)\n    })\n  }\n\n  setChecked(\n    data: TreeKey | TreeNodeData,\n    checked: boolean,\n    deep: boolean\n  ): void {\n    const node = this.getNode(data)\n\n    if (node) {\n      node.setChecked(!!checked, deep)\n    }\n  }\n\n  getCurrentNode() {\n    return this.currentNode\n  }\n\n  setCurrentNode(currentNode: Node): void {\n    const prevCurrentNode = this.currentNode\n    if (prevCurrentNode) {\n      prevCurrentNode.isCurrent = false\n    }\n    this.currentNode = currentNode\n    this.currentNode.isCurrent = true\n  }\n\n  setUserCurrentNode(node: Node, shouldAutoExpandParent = true): void {\n    const key = (node as any)[this.key]\n    const currNode = this.nodesMap[key]\n    this.setCurrentNode(currNode)\n    if (\n      shouldAutoExpandParent &&\n      this.currentNode &&\n      this.currentNode.level > 1\n    ) {\n      this.currentNode.parent?.expand(null, true)\n    }\n  }\n\n  setCurrentNodeKey(key: TreeKey | null, shouldAutoExpandParent = true): void {\n    this.currentNodeKey = key\n    if (isPropAbsent(key)) {\n      this.currentNode && (this.currentNode.isCurrent = false)\n      this.currentNode = null\n      return\n    }\n    const node = this.getNode(key)\n    if (node) {\n      this.setCurrentNode(node)\n      if (\n        shouldAutoExpandParent &&\n        this.currentNode &&\n        this.currentNode.level > 1\n      ) {\n        this.currentNode.parent?.expand(null, true)\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAKe,MAAM,SAAS,CAAC;AAC/B,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;AACtB,IAAI,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;AAC/B,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAClC,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAClC,IAAI,IAAI,CAAC,gBAAgB,GAAG,KAAK,CAAC;AAClC,IAAI,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC5B,IAAI,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC;AAC/B,IAAI,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AAClC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;AACnC,QAAQ,IAAI,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,UAAU,GAAG;AACf,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC;AACzB,MAAM,IAAI,EAAE,IAAI,CAAC,IAAI;AACrB,MAAM,KAAK,EAAE,IAAI;AACjB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;AAC3B,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AAChC,MAAM,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;AAC/B,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,IAAI,KAAK;AAClC,QAAQ,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AACzC,QAAQ,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACxC,OAAO,EAAE,IAAI,CAAC,CAAC;AACf,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AACnD,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC3B,IAAI,MAAM,QAAQ,GAAG,eAAe,IAAI,EAAE;AAC1C,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC5E,MAAM,KAAK,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,UAAU,CAAC,OAAO,EAAE,EAAE;AACzD,QAAQ,KAAK,CAAC,OAAO,GAAG,CAAC,EAAE,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,CAAC;AACvH,QAAQ,IAAI,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;AAC3C,UAAU,MAAM,QAAQ,EAAE,CAAC;AAC3B,SAAS;AACT,QAAQ,MAAM,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC9B,OAAO;AACP,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,UAAU,CAAC,MAAM,EAAE;AAC9C,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC;AAC7B,QAAQ,SAAS,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;AAC/D,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE;AAEvB,UAAU,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,SAAS,KAAK,KAAK,CAAC;AAClD,SAAS,MAAM;AAEf,UAAU,IAAI,CAAC,OAAO,GAAG,SAAS,KAAK,KAAK,CAAC;AAC7C,SAAS;AACT,OAAO;AACP,MAAM,IAAI,CAAC,KAAK;AAChB,QAAQ,OAAO;AACf,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACxC,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AAElC,UAAU,IAAI,CAAC,MAAM,EAAE,CAAC;AACxB,SAAS;AACT,OAAO;AACP,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnB,GAAG;AACH,EAAE,OAAO,CAAC,MAAM,EAAE;AAClB,IAAI,MAAM,eAAe,GAAG,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACtD,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACzB,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAChC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACtC,MAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAClD,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC;AACjC,KAAK;AACL,GAAG;AACH,EAAE,OAAO,CAAC,IAAI,EAAE;AAChB,IAAI,IAAI,IAAI,YAAY,IAAI;AAC5B,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC;AACnE,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;AACtC,GAAG;AACH,EAAE,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE;AAC9B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1C,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE;AAC7B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1C,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,EAAE,OAAO,CAAC,CAAC;AAC/E,GAAG;AACH,EAAE,MAAM,CAAC,IAAI,EAAE;AACf,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;AAC7B,MAAM,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW,EAAE;AACrC,QAAQ,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAChC,OAAO;AACP,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,MAAM,CAAC,IAAI,EAAE,UAAU,EAAE;AAC3B,IAAI,MAAM,UAAU,GAAG,CAAC,YAAY,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;AACxF,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,UAAU,CAAC,WAAW,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH,EAAE,wBAAwB,GAAG;AAC7B,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC;AAC7D,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC,UAAU,KAAK;AAC/C,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;AACxC,MAAM,IAAI,IAAI,EAAE;AAChB,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACnD,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,uBAAuB,CAAC,IAAI,EAAE;AAChC,IAAI,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC;AAC7D,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;AACnE,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AACjD,KAAK;AACL,GAAG;AACH,EAAE,oBAAoB,CAAC,MAAM,EAAE;AAC/B,IAAI,IAAI,MAAM,KAAK,IAAI,CAAC,kBAAkB,EAAE;AAC5C,MAAM,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAC;AACvC,MAAM,IAAI,CAAC,wBAAwB,EAAE,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,YAAY,CAAC,IAAI,EAAE;AACrB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;AAC3B,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;AACpC,KAAK,MAAM;AACX,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC;AAC/B,MAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;AACzB,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,cAAc,CAAC,IAAI,EAAE;AACvB,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI;AACnC,MAAM,OAAO;AACb,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACvC,MAAM,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACjC,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnC,GAAG;AACH,EAAE,eAAe,CAAC,QAAQ,GAAG,KAAK,EAAE,kBAAkB,GAAG,KAAK,EAAE;AAChE,IAAI,MAAM,YAAY,GAAG,EAAE,CAAC;AAC5B,IAAI,MAAM,QAAQ,GAAG,SAAS,IAAI,EAAE;AACpC,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC5E,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACpC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,IAAI,kBAAkB,IAAI,KAAK,CAAC,aAAa,MAAM,CAAC,QAAQ,IAAI,QAAQ,IAAI,KAAK,CAAC,MAAM,CAAC,EAAE;AACrH,UAAU,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxB,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnB,IAAI,OAAO,YAAY,CAAC;AACxB,GAAG;AACH,EAAE,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAE;AACnC,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAChF,GAAG;AACH,EAAE,mBAAmB,GAAG;AACxB,IAAI,MAAM,KAAK,GAAG,EAAE,CAAC;AACrB,IAAI,MAAM,QAAQ,GAAG,SAAS,IAAI,EAAE;AACpC,MAAM,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AAC5E,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACpC,QAAQ,IAAI,KAAK,CAAC,aAAa,EAAE;AACjC,UAAU,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACjC,SAAS;AACT,QAAQ,QAAQ,CAAC,KAAK,CAAC,CAAC;AACxB,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;AACnB,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,kBAAkB,GAAG;AACvB,IAAI,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,EAAE,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5E,GAAG;AACH,EAAE,YAAY,GAAG;AACjB,IAAI,MAAM,QAAQ,GAAG,EAAE,CAAC;AACxB,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;AACnC,IAAI,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;AACpC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,EAAE;AACrC,QAAQ,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;AACzC,OAAO;AACP,KAAK;AACL,IAAI,OAAO,QAAQ,CAAC;AACpB,GAAG;AACH,EAAE,cAAc,CAAC,GAAG,EAAE,IAAI,EAAE;AAC5B,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,IAAI;AACb,MAAM,OAAO;AACb,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;AACvC,IAAI,KAAK,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,MAAM,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC9B,KAAK;AACL,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACjD,MAAM,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC5B,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK;AACL,GAAG;AACH,EAAE,eAAe,CAAC,GAAG,EAAE,QAAQ,GAAG,KAAK,EAAE,WAAW,EAAE;AACtD,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;AAC3E,IAAI,MAAM,KAAK,mBAAmB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACtD,IAAI,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAC1C,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9D,IAAI,MAAM,iBAAiB,GAAG,CAAC,IAAI,KAAK;AACxC,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACzC,QAAQ,IAAI,EAAE,CAAC;AACf,QAAQ,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AACtC,QAAQ,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE;AAClE,UAAU,iBAAiB,CAAC,KAAK,CAAC,CAAC;AACnC,SAAS;AACT,OAAO,CAAC,CAAC;AACT,KAAK,CAAC;AACN,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AACrD,MAAM,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC/B,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;AAChD,MAAM,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC7C,MAAM,IAAI,CAAC,OAAO,EAAE;AACpB,QAAQ,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE;AAC7C,UAAU,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACxC,SAAS;AACT,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE;AAClC,QAAQ,iBAAiB,CAAC,IAAI,CAAC,CAAC;AAChC,OAAO;AACP,MAAM,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,aAAa,EAAE;AAC7C,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AACrC,QAAQ,SAAS;AACjB,OAAO;AACP,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAClC,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACtC,QAAQ,MAAM,QAAQ,GAAG,SAAS,KAAK,EAAE;AACzC,UAAU,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC;AAC9C,UAAU,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;AACxC,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;AAC/B,cAAc,KAAK,CAAC,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AAC7C,aAAa;AACb,YAAY,QAAQ,CAAC,KAAK,CAAC,CAAC;AAC5B,WAAW,CAAC,CAAC;AACb,SAAS,CAAC;AACV,QAAQ,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvB,OAAO;AACP,KAAK;AACL,GAAG;AACH,EAAE,eAAe,CAAC,KAAK,EAAE,QAAQ,GAAG,KAAK,EAAE;AAC3C,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;AAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC5B,MAAM,WAAW,CAAC,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;AAC5C,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,cAAc,CAAC,IAAI,EAAE,QAAQ,GAAG,KAAK,EAAE;AACzC,IAAI,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;AACnC,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,IAAI,MAAM,WAAW,GAAG,EAAE,CAAC;AAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC3B,MAAM,WAAW,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC/B,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,sBAAsB,CAAC,IAAI,EAAE;AAC/B,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC;AACpC,IAAI,IAAI,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK;AAC1B,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACrC,MAAM,IAAI,IAAI;AACd,QAAQ,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;AACjD,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE;AAClC,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACpC,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACvC,KAAK;AACL,GAAG;AACH,EAAE,cAAc,GAAG;AACnB,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC;AAC5B,GAAG;AACH,EAAE,cAAc,CAAC,WAAW,EAAE;AAC9B,IAAI,MAAM,eAAe,GAAG,IAAI,CAAC,WAAW,CAAC;AAC7C,IAAI,IAAI,eAAe,EAAE;AACzB,MAAM,eAAe,CAAC,SAAS,GAAG,KAAK,CAAC;AACxC,KAAK;AACL,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;AACnC,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;AACtC,GAAG;AACH,EAAE,kBAAkB,CAAC,IAAI,EAAE,sBAAsB,GAAG,IAAI,EAAE;AAC1D,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC/B,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACxC,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;AAClC,IAAI,IAAI,sBAAsB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,EAAE;AAClF,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAC9E,KAAK;AACL,GAAG;AACH,EAAE,iBAAiB,CAAC,GAAG,EAAE,sBAAsB,GAAG,IAAI,EAAE;AACxD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,CAAC,cAAc,GAAG,GAAG,CAAC;AAC9B,IAAI,IAAI,YAAY,CAAC,GAAG,CAAC,EAAE;AAC3B,MAAM,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,KAAK,CAAC,CAAC;AAC/D,MAAM,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;AAC9B,MAAM,OAAO;AACb,KAAK;AACL,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,IAAI,IAAI,EAAE;AACd,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AAChC,MAAM,IAAI,sBAAsB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,CAAC,EAAE;AACpF,QAAQ,CAAC,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AAChF,OAAO;AACP,KAAK;AACL,GAAG;AACH;;;;"}