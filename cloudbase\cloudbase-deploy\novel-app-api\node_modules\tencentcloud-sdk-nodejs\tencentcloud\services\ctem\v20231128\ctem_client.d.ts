import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { DescribeFakeWechatOfficialsResponse, DescribeManagesRequest, DescribeFakeAppsRequest, DescribeJobRecordsResponse, DescribeAppsRequest, DescribeSensitiveInfosRequest, DescribeWechatAppletsResponse, DescribeCustomersRequest, DescribeFakeWebsitesResponse, DescribeNetDisksResponse, CreateJobRecordResponse, DescribeCustomersResponse, DescribeDomainsResponse, DescribePortsResponse, DescribeSensitiveInfosResponse, DescribeConfigsResponse, DescribeFakeWebsitesRequest, ModifyCustomerResponse, CreateJobRecordRequest, DescribeSuspiciousAssetsResponse, DescribeAssetsRequest, DescribeJobRecordDetailsRequest, DescribeSubDomainsRequest, DescribeVulsResponse, DescribeGithubsResponse, DescribeLeakageEmailsResponse, DescribeAssetsResponse, StopJobRecordRequest, DescribeEnterprisesRequest, DescribeLeakageCodesRequest, DescribePortsRequest, DescribeGithubsRequest, DescribeWechatOfficialAccountsResponse, DescribeWechatOfficialAccountsRequest, DescribeDarkWebsRequest, DescribeHttpsRequest, DescribeEnterprisesResponse, DescribeNetDisksRequest, ModifyCustomerRequest, DescribeSuspiciousAssetsRequest, DescribeWechatAppletsRequest, DescribeFakeAppsResponse, DescribeLeakageEmailsRequest, CreateCustomerResponse, DescribeManagesResponse, DescribeConfigsRequest, DescribeWeakPasswordsRequest, StopJobRecordResponse, DescribeFakeMiniProgramsRequest, CreateCustomerRequest, DescribeLeakageDatasRequest, DescribeJobRecordDetailsResponse, DescribeVulsRequest, DescribeFakeMiniProgramsResponse, DescribeSubDomainsResponse, DescribeHttpsResponse, DescribeLeakageDatasResponse, DescribeAppsResponse, DescribeWeakPasswordsResponse, DescribeFakeWechatOfficialsRequest, DescribeJobRecordsRequest, DescribeLeakageCodesResponse, DescribeDomainsRequest, DescribeDarkWebsResponse } from "./ctem_models";
/**
 * ctem client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 查看网盘泄露数据
     */
    DescribeNetDisks(req: DescribeNetDisksRequest, cb?: (error: string, rep: DescribeNetDisksResponse) => void): Promise<DescribeNetDisksResponse>;
    /**
     * 查看后台管理数据
     */
    DescribeManages(req: DescribeManagesRequest, cb?: (error: string, rep: DescribeManagesResponse) => void): Promise<DescribeManagesResponse>;
    /**
     * 获取数据泄露事件
     */
    DescribeLeakageDatas(req: DescribeLeakageDatasRequest, cb?: (error: string, rep: DescribeLeakageDatasResponse) => void): Promise<DescribeLeakageDatasResponse>;
    /**
     * 编辑企业
     */
    ModifyCustomer(req: ModifyCustomerRequest, cb?: (error: string, rep: ModifyCustomerResponse) => void): Promise<ModifyCustomerResponse>;
    /**
     * 查看主域名数据
     */
    DescribeDomains(req: DescribeDomainsRequest, cb?: (error: string, rep: DescribeDomainsResponse) => void): Promise<DescribeDomainsResponse>;
    /**
     * 查看漏洞数据
     */
    DescribeVuls(req: DescribeVulsRequest, cb?: (error: string, rep: DescribeVulsResponse) => void): Promise<DescribeVulsResponse>;
    /**
     * 查看目录爆破数据
     */
    DescribeConfigs(req: DescribeConfigsRequest, cb?: (error: string, rep: DescribeConfigsResponse) => void): Promise<DescribeConfigsResponse>;
    /**
     * 查看微信小程序
     */
    DescribeWechatApplets(req: DescribeWechatAppletsRequest, cb?: (error: string, rep: DescribeWechatAppletsResponse) => void): Promise<DescribeWechatAppletsResponse>;
    /**
     * 查询仿冒网站
     */
    DescribeFakeWebsites(req: DescribeFakeWebsitesRequest, cb?: (error: string, rep: DescribeFakeWebsitesResponse) => void): Promise<DescribeFakeWebsitesResponse>;
    /**
     * 查看敏感信息泄露数据
     */
    DescribeSensitiveInfos(req: DescribeSensitiveInfosRequest, cb?: (error: string, rep: DescribeSensitiveInfosResponse) => void): Promise<DescribeSensitiveInfosResponse>;
    /**
     * 查询仿冒小程序
     */
    DescribeFakeMiniPrograms(req: DescribeFakeMiniProgramsRequest, cb?: (error: string, rep: DescribeFakeMiniProgramsResponse) => void): Promise<DescribeFakeMiniProgramsResponse>;
    /**
     * 查看子域名数据
     */
    DescribeSubDomains(req: DescribeSubDomainsRequest, cb?: (error: string, rep: DescribeSubDomainsResponse) => void): Promise<DescribeSubDomainsResponse>;
    /**
     * 查询仿冒公众号
     */
    DescribeFakeWechatOfficials(req: DescribeFakeWechatOfficialsRequest, cb?: (error: string, rep: DescribeFakeWechatOfficialsResponse) => void): Promise<DescribeFakeWechatOfficialsResponse>;
    /**
     * 获取邮箱泄露数据
     */
    DescribeLeakageEmails(req: DescribeLeakageEmailsRequest, cb?: (error: string, rep: DescribeLeakageEmailsResponse) => void): Promise<DescribeLeakageEmailsResponse>;
    /**
     * 查询仿冒应用
     */
    DescribeFakeApps(req: DescribeFakeAppsRequest, cb?: (error: string, rep: DescribeFakeAppsResponse) => void): Promise<DescribeFakeAppsResponse>;
    /**
     * 查看移动端资产
     */
    DescribeApps(req: DescribeAppsRequest, cb?: (error: string, rep: DescribeAppsResponse) => void): Promise<DescribeAppsResponse>;
    /**
     * 查看企业列表
     */
    DescribeCustomers(req: DescribeCustomersRequest, cb?: (error: string, rep: DescribeCustomersResponse) => void): Promise<DescribeCustomersResponse>;
    /**
     * 查看链路详情
     */
    DescribeJobRecordDetails(req: DescribeJobRecordDetailsRequest, cb?: (error: string, rep: DescribeJobRecordDetailsResponse) => void): Promise<DescribeJobRecordDetailsResponse>;
    /**
     * 查看影子资产
     */
    DescribeSuspiciousAssets(req: DescribeSuspiciousAssetsRequest, cb?: (error: string, rep: DescribeSuspiciousAssetsResponse) => void): Promise<DescribeSuspiciousAssetsResponse>;
    /**
     * 查看Github泄露数据
     */
    DescribeGithubs(req: DescribeGithubsRequest, cb?: (error: string, rep: DescribeGithubsResponse) => void): Promise<DescribeGithubsResponse>;
    /**
     * 查看端口数据
     */
    DescribePorts(req: DescribePortsRequest, cb?: (error: string, rep: DescribePortsResponse) => void): Promise<DescribePortsResponse>;
    /**
     * 启动测绘
     */
    CreateJobRecord(req: CreateJobRecordRequest, cb?: (error: string, rep: CreateJobRecordResponse) => void): Promise<CreateJobRecordResponse>;
    /**
     * 查看http数据
     */
    DescribeHttps(req: DescribeHttpsRequest, cb?: (error: string, rep: DescribeHttpsResponse) => void): Promise<DescribeHttpsResponse>;
    /**
     * 查看主机资产
     */
    DescribeAssets(req: DescribeAssetsRequest, cb?: (error: string, rep: DescribeAssetsResponse) => void): Promise<DescribeAssetsResponse>;
    /**
     * 查看暗网数据
     */
    DescribeDarkWebs(req: DescribeDarkWebsRequest, cb?: (error: string, rep: DescribeDarkWebsResponse) => void): Promise<DescribeDarkWebsResponse>;
    /**
     * 获取代码泄露数据
     */
    DescribeLeakageCodes(req: DescribeLeakageCodesRequest, cb?: (error: string, rep: DescribeLeakageCodesResponse) => void): Promise<DescribeLeakageCodesResponse>;
    /**
     * 查看公众号数据
     */
    DescribeWechatOfficialAccounts(req: DescribeWechatOfficialAccountsRequest, cb?: (error: string, rep: DescribeWechatOfficialAccountsResponse) => void): Promise<DescribeWechatOfficialAccountsResponse>;
    /**
     * 查看企业架构数据
     */
    DescribeEnterprises(req: DescribeEnterprisesRequest, cb?: (error: string, rep: DescribeEnterprisesResponse) => void): Promise<DescribeEnterprisesResponse>;
    /**
     * 创建企业
     */
    CreateCustomer(req: CreateCustomerRequest, cb?: (error: string, rep: CreateCustomerResponse) => void): Promise<CreateCustomerResponse>;
    /**
     * 停止扫描
     */
    StopJobRecord(req: StopJobRecordRequest, cb?: (error: string, rep: StopJobRecordResponse) => void): Promise<StopJobRecordResponse>;
    /**
     * 查看任务运行记录列表
     */
    DescribeJobRecords(req: DescribeJobRecordsRequest, cb?: (error: string, rep: DescribeJobRecordsResponse) => void): Promise<DescribeJobRecordsResponse>;
    /**
     * 查看弱口令数据
     */
    DescribeWeakPasswords(req: DescribeWeakPasswordsRequest, cb?: (error: string, rep: DescribeWeakPasswordsResponse) => void): Promise<DescribeWeakPasswordsResponse>;
}
