// 测试COS API密钥是否正常工作
const tencentcloud = require('tencentcloud-sdk-nodejs');

// 使用您配置的密钥
const secretId = 'AKIDfGfsthlwvkLkwvlluVylRQDoGXcVCgx';
const secretKey = '98ygDXeZQ4WktcGhvynofKn4BtWTEocN';
const region = 'ap-shanghai';
const bucket = 'novel-1368800861';

async function testCOSCredentials() {
  try {
    console.log('🔑 开始测试COS API密钥...');
    
    // 测试STS服务
    const StsClient = tencentcloud.sts.v20180813.Client;
    
    const clientConfig = {
      credential: {
        secretId: secretId,
        secretKey: secretKey,
      },
      region: region,
      profile: {
        httpProfile: {
          endpoint: "sts.tencentcloudapi.com",
        },
      },
    };

    const client = new StsClient(clientConfig);

    // 构建测试策略
    const policy = {
      "version": "2.0",
      "statement": [
        {
          "effect": "allow",
          "action": [
            "cos:PutObject",
            "cos:PostObject"
          ],
          "resource": [
            `qcs::cos:${region}:uid/*:${bucket}/test/*`
          ]
        }
      ]
    };

    const params = {
      "Name": "test-credentials",
      "Policy": JSON.stringify(policy),
      "DurationSeconds": 3600,
    };

    console.log('📡 调用STS GetFederationToken...');
    const response = await client.GetFederationToken(params);

    if (response.Credentials) {
      console.log('✅ API密钥测试成功！');
      console.log('🔑 临时密钥ID:', response.Credentials.TmpSecretId);
      console.log('⏰ 过期时间:', response.Credentials.ExpiredTime);
      return true;
    } else {
      console.log('❌ 未获取到临时密钥');
      return false;
    }

  } catch (error) {
    console.log('❌ API密钥测试失败:', error.message);
    
    if (error.code === 'AuthFailure.SecretIdNotFound') {
      console.log('💡 错误原因: SecretId不存在或无效');
    } else if (error.code === 'AuthFailure.SignatureFailure') {
      console.log('💡 错误原因: SecretKey错误');
    } else if (error.code === 'UnauthorizedOperation') {
      console.log('💡 错误原因: API密钥没有STS权限');
    }
    
    return false;
  }
}

// 运行测试
testCOSCredentials().then(success => {
  if (success) {
    console.log('\n🎉 测试结果: API密钥配置正确，可以生成临时密钥');
    console.log('💡 建议: 重新部署CloudBase函数以应用新的环境变量');
  } else {
    console.log('\n❌ 测试结果: API密钥配置有问题');
    console.log('💡 建议: 检查密钥是否正确，以及是否有足够的权限');
  }
});
