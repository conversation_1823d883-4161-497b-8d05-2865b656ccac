import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("iss.tencentcloudapi.com", "2023-05-17", clientConfig);
    }
    async ListRecordRetrieveTasks(req, cb) {
        return this.request("ListRecordRetrieveTasks", req, cb);
    }
    async ListRecordPlans(req, cb) {
        return this.request("ListRecordPlans", req, cb);
    }
    async DescribeGatewayProtocol(req, cb) {
        return this.request("DescribeGatewayProtocol", req, cb);
    }
    async DescribeGatewayMonitor(req, cb) {
        return this.request("DescribeGatewayMonitor", req, cb);
    }
    async UpdateOrganization(req, cb) {
        return this.request("UpdateOrganization", req, cb);
    }
    async ListOrganizationChannels(req, cb) {
        return this.request("ListOrganizationChannels", req, cb);
    }
    async DescribeDomain(req, cb) {
        return this.request("DescribeDomain", req, cb);
    }
    async DeleteRecordBackupTemplate(req, cb) {
        return this.request("DeleteRecordBackupTemplate", req, cb);
    }
    async RefreshDeviceChannel(req, cb) {
        return this.request("RefreshDeviceChannel", req, cb);
    }
    async ControlDeviceSnapshot(req, cb) {
        return this.request("ControlDeviceSnapshot", req, cb);
    }
    async DescribeGateway(req, cb) {
        return this.request("DescribeGateway", req, cb);
    }
    async DeleteRecordPlan(req, cb) {
        return this.request("DeleteRecordPlan", req, cb);
    }
    async ControlDevicePTZ(req, cb) {
        return this.request("ControlDevicePTZ", req, cb);
    }
    async ListRecordBackupPlanDevices(req, cb) {
        return this.request("ListRecordBackupPlanDevices", req, cb);
    }
    async ListTasks(req, cb) {
        return this.request("ListTasks", req, cb);
    }
    async AddStreamAuth(req, cb) {
        return this.request("AddStreamAuth", req, cb);
    }
    async DeleteRecordBackupPlan(req, cb) {
        return this.request("DeleteRecordBackupPlan", req, cb);
    }
    async PlayRecord(req, cb) {
        return this.request("PlayRecord", req, cb);
    }
    async UpdateRecordTemplate(req, cb) {
        return this.request("UpdateRecordTemplate", req, cb);
    }
    async DeleteRecordTemplate(req, cb) {
        return this.request("DeleteRecordTemplate", req, cb);
    }
    async UpdateDeviceStatus(req, cb) {
        return this.request("UpdateDeviceStatus", req, cb);
    }
    async UpdateGateway(req, cb) {
        return this.request("UpdateGateway", req, cb);
    }
    async AddRecordRetrieveTask(req, cb) {
        return this.request("AddRecordRetrieveTask", req, cb);
    }
    async DescribeDomainRegion(req, cb) {
        return this.request("DescribeDomainRegion", req, cb);
    }
    async ListRecordBackupTemplates(req, cb) {
        return this.request("ListRecordBackupTemplates", req, cb);
    }
    async DescribeRecordBackupTemplate(req, cb) {
        return this.request("DescribeRecordBackupTemplate", req, cb);
    }
    async DescribeVideoBitRate(req, cb) {
        return this.request("DescribeVideoBitRate", req, cb);
    }
    async CreateVideoDownloadTask(req, cb) {
        return this.request("CreateVideoDownloadTask", req, cb);
    }
    async DescribeRecordBackupPlan(req, cb) {
        return this.request("DescribeRecordBackupPlan", req, cb);
    }
    async DescribeRecordRetrieveTask(req, cb) {
        return this.request("DescribeRecordRetrieveTask", req, cb);
    }
    async ListDeviceSnapshots(req, cb) {
        return this.request("ListDeviceSnapshots", req, cb);
    }
    async DescribeTask(req, cb) {
        return this.request("DescribeTask", req, cb);
    }
    async AddAITask(req, cb) {
        return this.request("AddAITask", req, cb);
    }
    async UpdateDeviceOrganization(req, cb) {
        return this.request("UpdateDeviceOrganization", req, cb);
    }
    async ListRecordPlanDevices(req, cb) {
        return this.request("ListRecordPlanDevices", req, cb);
    }
    async DescribeVideoDownloadUrl(req, cb) {
        return this.request("DescribeVideoDownloadUrl", req, cb);
    }
    async BatchOperateDevice(req, cb) {
        return this.request("BatchOperateDevice", req, cb);
    }
    async DescribeStreamAuth(req, cb) {
        return this.request("DescribeStreamAuth", req, cb);
    }
    async AddRecordTemplate(req, cb) {
        return this.request("AddRecordTemplate", req, cb);
    }
    async ListOrganizationChannelNumbers(req, cb) {
        return this.request("ListOrganizationChannelNumbers", req, cb);
    }
    async ListDevices(req, cb) {
        return this.request("ListDevices", req, cb);
    }
    async ListAITasks(req, cb) {
        return this.request("ListAITasks", req, cb);
    }
    async DescribeAITask(req, cb) {
        return this.request("DescribeAITask", req, cb);
    }
    async DescribeRecordTemplate(req, cb) {
        return this.request("DescribeRecordTemplate", req, cb);
    }
    async DescribeRecordFile(req, cb) {
        return this.request("DescribeRecordFile", req, cb);
    }
    async DeleteGateway(req, cb) {
        return this.request("DeleteGateway", req, cb);
    }
    async ControlRecord(req, cb) {
        return this.request("ControlRecord", req, cb);
    }
    async UpdateUserDevice(req, cb) {
        return this.request("UpdateUserDevice", req, cb);
    }
    async DescribeDeviceChannel(req, cb) {
        return this.request("DescribeDeviceChannel", req, cb);
    }
    async ListRecordBackupPlans(req, cb) {
        return this.request("ListRecordBackupPlans", req, cb);
    }
    async AddOrganization(req, cb) {
        return this.request("AddOrganization", req, cb);
    }
    async DescribeDevicePreset(req, cb) {
        return this.request("DescribeDevicePreset", req, cb);
    }
    async BatchDeleteVideoDownloadTask(req, cb) {
        return this.request("BatchDeleteVideoDownloadTask", req, cb);
    }
    async ListSubTasks(req, cb) {
        return this.request("ListSubTasks", req, cb);
    }
    async DescribeUserDevice(req, cb) {
        return this.request("DescribeUserDevice", req, cb);
    }
    async DescribeAITaskResult(req, cb) {
        return this.request("DescribeAITaskResult", req, cb);
    }
    async DescribeGBDeviceAddr(req, cb) {
        return this.request("DescribeGBDeviceAddr", req, cb);
    }
    async ListRecordPlanChannels(req, cb) {
        return this.request("ListRecordPlanChannels", req, cb);
    }
    async AddUserDevice(req, cb) {
        return this.request("AddUserDevice", req, cb);
    }
    async DeleteTask(req, cb) {
        return this.request("DeleteTask", req, cb);
    }
    async DeleteOrganization(req, cb) {
        return this.request("DeleteOrganization", req, cb);
    }
    async DescribeRecordPlaybackUrl(req, cb) {
        return this.request("DescribeRecordPlaybackUrl", req, cb);
    }
    async DescribeRecordPlan(req, cb) {
        return this.request("DescribeRecordPlan", req, cb);
    }
    async DescribeOrganization(req, cb) {
        return this.request("DescribeOrganization", req, cb);
    }
    async ControlDeviceStream(req, cb) {
        return this.request("ControlDeviceStream", req, cb);
    }
    async UpdateAITaskStatus(req, cb) {
        return this.request("UpdateAITaskStatus", req, cb);
    }
    async UpdateAITask(req, cb) {
        return this.request("UpdateAITask", req, cb);
    }
    async DeleteAITask(req, cb) {
        return this.request("DeleteAITask", req, cb);
    }
    async DescribeRecordSlice(req, cb) {
        return this.request("DescribeRecordSlice", req, cb);
    }
    async SetForbidPlayChannels(req, cb) {
        return this.request("SetForbidPlayChannels", req, cb);
    }
    async AddRecordBackupTemplate(req, cb) {
        return this.request("AddRecordBackupTemplate", req, cb);
    }
    async UpdateRecordPlan(req, cb) {
        return this.request("UpdateRecordPlan", req, cb);
    }
    async DeleteUserDevice(req, cb) {
        return this.request("DeleteUserDevice", req, cb);
    }
    async ListVideoDownloadTask(req, cb) {
        return this.request("ListVideoDownloadTask", req, cb);
    }
    async DescribeGatewayVersion(req, cb) {
        return this.request("DescribeGatewayVersion", req, cb);
    }
    async DescribeDeviceRegion(req, cb) {
        return this.request("DescribeDeviceRegion", req, cb);
    }
    async ListRecordTemplates(req, cb) {
        return this.request("ListRecordTemplates", req, cb);
    }
    async DescribeCNAME(req, cb) {
        return this.request("DescribeCNAME", req, cb);
    }
    async ListGateways(req, cb) {
        return this.request("ListGateways", req, cb);
    }
    async ControlDevicePreset(req, cb) {
        return this.request("ControlDevicePreset", req, cb);
    }
    async CallISAPI(req, cb) {
        return this.request("CallISAPI", req, cb);
    }
    async AddRecordPlan(req, cb) {
        return this.request("AddRecordPlan", req, cb);
    }
    async UpdateRecordBackupPlan(req, cb) {
        return this.request("UpdateRecordBackupPlan", req, cb);
    }
    async DeleteDomain(req, cb) {
        return this.request("DeleteDomain", req, cb);
    }
    async QueryForbidPlayChannelList(req, cb) {
        return this.request("QueryForbidPlayChannelList", req, cb);
    }
    async UpgradeGateway(req, cb) {
        return this.request("UpgradeGateway", req, cb);
    }
    async UpdateRecordBackupTemplate(req, cb) {
        return this.request("UpdateRecordBackupTemplate", req, cb);
    }
    async DeleteRecordRetrieveTask(req, cb) {
        return this.request("DeleteRecordRetrieveTask", req, cb);
    }
    async AddRecordBackupPlan(req, cb) {
        return this.request("AddRecordBackupPlan", req, cb);
    }
    async ControlRecordTimeline(req, cb) {
        return this.request("ControlRecordTimeline", req, cb);
    }
    async ListGatewayDevices(req, cb) {
        return this.request("ListGatewayDevices", req, cb);
    }
}
