{"version": 3, "file": "badge2.js", "sources": ["../../../../../../packages/components/badge/src/badge.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <slot />\n    <transition :name=\"`${ns.namespace.value}-zoom-in-center`\">\n      <sup\n        v-show=\"!hidden && (content || isDot || $slots.content)\"\n        :class=\"[\n          ns.e('content'),\n          ns.em('content', type),\n          ns.is('fixed', !!$slots.default),\n          ns.is('dot', isDot),\n          ns.is('hide-zero', !showZero && props.value === 0),\n          badgeClass,\n        ]\"\n        :style=\"style\"\n      >\n        <slot name=\"content\" :value=\"content\">\n          {{ content }}\n        </slot>\n      </sup>\n    </transition>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { addUnit, isNumber } from '@element-plus/utils'\nimport { badgeProps } from './badge'\n\nimport type { StyleValue } from 'vue'\n\ndefineOptions({\n  name: 'ElBadge',\n})\n\nconst props = defineProps(badgeProps)\n\nconst ns = useNamespace('badge')\n\nconst content = computed<string>(() => {\n  if (props.isDot) return ''\n  if (isNumber(props.value) && isNumber(props.max)) {\n    return props.max < props.value ? `${props.max}+` : `${props.value}`\n  }\n  return `${props.value}`\n})\n\nconst style = computed<StyleValue>(() => {\n  return [\n    {\n      backgroundColor: props.color,\n      marginRight: addUnit(-(props.offset?.[0] ?? 0)),\n      marginTop: addUnit(props.offset?.[1] ?? 0),\n    },\n    props.badgeStyle ?? {},\n  ]\n})\n\ndefineExpose({\n  /** @description badge content */\n  content,\n})\n</script>\n"], "names": ["useNamespace", "computed", "isNumber", "style", "addUnit"], "mappings": ";;;;;;;;;;;uCAgCc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,OAAO,CAAA,CAAA;AAE/B,IAAM,MAAA,OAAA,GAAUC,aAAiB,MAAM;AACrC,MAAI,IAAA,KAAA,CAAM;AACV,QAAA;AACE,MAAO,IAAAC,cAAA,CAAA,KAAY,CAAA,KAAA,CAAA,IAAcA,cAAA,CAAG,MAAM,GAAG,CAAA,EAAA;AAAoB,QACnE,OAAA,KAAA,CAAA,GAAA,GAAA,KAAA,CAAA,KAAA,GAAA,CAAA,EAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACA,OAAO;AAAc,MACtB,OAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAAC,OAAA,GAAAF,YAAA,CAAA,MAAA;AAAA,MACL,IAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA;AACyB,QAAA;AACuB,UAC9C,eAAmB,EAAA,KAAA,CAAA,KAAe;AAAO,UAC3C,WAAA,EAAAG,aAAA,CAAA,EAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA,CAAA;AAAA,UACA,wBAAoB,CAAC,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,KAAA,IAAA,GAAA,EAAA,GAAA,CAAA,CAAA;AAAA,SACvB;AAAA,QACD,CAAA,EAAA,GAAA,KAAA,CAAA,UAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA;AAED,OAAa,CAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEX,MAAA,CAAA;AAAA,MACD,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}