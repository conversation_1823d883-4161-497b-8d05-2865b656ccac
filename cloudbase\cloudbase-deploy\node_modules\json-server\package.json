{"name": "json-server", "version": "0.17.4", "description": "Get a full fake REST API with zero coding in less than 30 seconds", "main": "./lib/server/index.js", "bin": "./lib/cli/bin.js", "files": ["lib", "public"], "scripts": {"prepare": "husky install", "test": "npm run build && cross-env NODE_ENV=test jest", "start": "babel-node -- src/cli/bin db.json -r routes.json", "lint": "eslint . --ignore-path .gitignore", "fix": "npm run lint -- --fix", "build": "babel src -d lib", "toc": "markdown-toc -i README.md", "postversion": "git push && git push --tags", "prepublish": "npm test && npm run build"}, "dependencies": {"body-parser": "^1.19.0", "chalk": "^4.1.2", "compression": "^1.7.4", "connect-pause": "^0.1.1", "cors": "^2.8.5", "errorhandler": "^1.5.1", "express": "^4.17.1", "express-urlrewrite": "^1.4.0", "json-parse-helpfulerror": "^1.0.3", "lodash": "^4.17.21", "lodash-id": "^0.14.1", "lowdb": "^1.0.0", "method-override": "^3.0.0", "morgan": "^1.10.0", "nanoid": "^3.1.23", "please-upgrade-node": "^3.2.0", "pluralize": "^8.0.0", "server-destroy": "^1.0.1", "yargs": "^17.0.1"}, "devDependencies": {"@babel/cli": "^7.12.1", "@babel/core": "^7.12.3", "@babel/node": "^7.12.6", "@babel/preset-env": "^7.12.1", "cross-env": "^7.0.2", "eslint": "^7.13.0", "eslint-config-prettier": "^8.3.0", "eslint-config-standard": "^16.0.1", "eslint-plugin-import": "^2.22.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.1.0", "husky": "^6.0.0", "jest": "^26.6.3", "markdown-toc": "^1.2.0", "mkdirp": "^1.0.4", "npm-run-all": "^4.1.5", "os-tmpdir": "^2.0.0", "prettier": "^2.3.2", "rimraf": "^3.0.2", "server-ready": "^0.3.1", "standard": "^17.1.0", "supertest": "^6.0.1", "temp-write": "^4.0.0"}, "repository": {"type": "git", "url": "git://github.com/typicode/json-server.git"}, "keywords": ["JSON", "server", "fake", "REST", "API", "prototyping", "mock", "mocking", "test", "testing", "rest", "data", "dummy", "sandbox"], "author": "Typicode <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/typicode/json-server/issues"}, "homepage": "https://github.com/typicode/json-server", "engines": {"node": ">=12"}, "jest": {"testURL": "http://localhost/"}}