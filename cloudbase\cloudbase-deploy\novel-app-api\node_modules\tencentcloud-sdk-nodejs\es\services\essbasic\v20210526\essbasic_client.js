import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("essbasic.tencentcloudapi.com", "2021-05-26", clientConfig);
    }
    async ChannelCreateFlowSignReview(req, cb) {
        return this.request("ChannelCreateFlowSignReview", req, cb);
    }
    async ChannelModifyRole(req, cb) {
        return this.request("ChannelModifyRole", req, cb);
    }
    async CreateFlowGroupSignReview(req, cb) {
        return this.request("CreateFlowGroupSignReview", req, cb);
    }
    async ChannelCancelMultiFlowSignQRCode(req, cb) {
        return this.request("ChannelCancelMultiFlowSignQRCode", req, cb);
    }
    async DescribeExtendedServiceAuthInfo(req, cb) {
        return this.request("DescribeExtendedServiceAuthInfo", req, cb);
    }
    async DescribeBatchOrganizationRegistrationUrls(req, cb) {
        return this.request("DescribeBatchOrganizationRegistrationUrls", req, cb);
    }
    async DescribeUsage(req, cb) {
        return this.request("DescribeUsage", req, cb);
    }
    async ChannelDescribeSignFaceVideo(req, cb) {
        return this.request("ChannelDescribeSignFaceVideo", req, cb);
    }
    async ChannelDeleteRoleUsers(req, cb) {
        return this.request("ChannelDeleteRoleUsers", req, cb);
    }
    async CreateSignUrls(req, cb) {
        return this.request("CreateSignUrls", req, cb);
    }
    async ChannelCreateBoundFlows(req, cb) {
        return this.request("ChannelCreateBoundFlows", req, cb);
    }
    async CreatePartnerAutoSignAuthUrl(req, cb) {
        return this.request("CreatePartnerAutoSignAuthUrl", req, cb);
    }
    async ChannelCreateWebThemeConfig(req, cb) {
        return this.request("ChannelCreateWebThemeConfig", req, cb);
    }
    async ChannelCreateFlowReminds(req, cb) {
        return this.request("ChannelCreateFlowReminds", req, cb);
    }
    async ChannelBatchCancelFlows(req, cb) {
        return this.request("ChannelBatchCancelFlows", req, cb);
    }
    async ChannelCreateFlowApprovers(req, cb) {
        return this.request("ChannelCreateFlowApprovers", req, cb);
    }
    async CreatePersonAuthCertificateImage(req, cb) {
        return this.request("CreatePersonAuthCertificateImage", req, cb);
    }
    async CreateChannelFlowEvidenceReport(req, cb) {
        return this.request("CreateChannelFlowEvidenceReport", req, cb);
    }
    async ChannelCreatePreparedPersonalEsign(req, cb) {
        return this.request("ChannelCreatePreparedPersonalEsign", req, cb);
    }
    async ChannelCreateFlowByFiles(req, cb) {
        return this.request("ChannelCreateFlowByFiles", req, cb);
    }
    async CreateSealByImage(req, cb) {
        return this.request("CreateSealByImage", req, cb);
    }
    async DescribeBatchOrganizationRegistrationTasks(req, cb) {
        return this.request("DescribeBatchOrganizationRegistrationTasks", req, cb);
    }
    async ChannelCreateConvertTaskApi(req, cb) {
        return this.request("ChannelCreateConvertTaskApi", req, cb);
    }
    async ChannelCancelFlow(req, cb) {
        return this.request("ChannelCancelFlow", req, cb);
    }
    async CreateFlowsByTemplates(req, cb) {
        return this.request("CreateFlowsByTemplates", req, cb);
    }
    async ChannelCreateRole(req, cb) {
        return this.request("ChannelCreateRole", req, cb);
    }
    async DescribeChannelSealPolicyWorkflowUrl(req, cb) {
        return this.request("DescribeChannelSealPolicyWorkflowUrl", req, cb);
    }
    async ChannelCreateOrganizationModifyQrCode(req, cb) {
        return this.request("ChannelCreateOrganizationModifyQrCode", req, cb);
    }
    async ChannelCreateDynamicFlowApprover(req, cb) {
        return this.request("ChannelCreateDynamicFlowApprover", req, cb);
    }
    async CreateLegalSealQrCode(req, cb) {
        return this.request("CreateLegalSealQrCode", req, cb);
    }
    async ChannelVerifyPdf(req, cb) {
        return this.request("ChannelVerifyPdf", req, cb);
    }
    async GetDownloadFlowUrl(req, cb) {
        return this.request("GetDownloadFlowUrl", req, cb);
    }
    async ChannelDescribeOrganizationSeals(req, cb) {
        return this.request("ChannelDescribeOrganizationSeals", req, cb);
    }
    async DescribeExtendedServiceAuthDetail(req, cb) {
        return this.request("DescribeExtendedServiceAuthDetail", req, cb);
    }
    async CreateCloseOrganizationUrl(req, cb) {
        return this.request("CreateCloseOrganizationUrl", req, cb);
    }
    async PrepareFlows(req, cb) {
        return this.request("PrepareFlows", req, cb);
    }
    async ChannelDescribeRoles(req, cb) {
        return this.request("ChannelDescribeRoles", req, cb);
    }
    async DescribeResourceUrlsByFlows(req, cb) {
        return this.request("DescribeResourceUrlsByFlows", req, cb);
    }
    async DescribeChannelOrganizations(req, cb) {
        return this.request("DescribeChannelOrganizations", req, cb);
    }
    async CreateBatchInitOrganizationUrl(req, cb) {
        return this.request("CreateBatchInitOrganizationUrl", req, cb);
    }
    async ChannelDescribeAccountBillDetail(req, cb) {
        return this.request("ChannelDescribeAccountBillDetail", req, cb);
    }
    async ChannelDescribeFlowComponents(req, cb) {
        return this.request("ChannelDescribeFlowComponents", req, cb);
    }
    async ChannelDeleteSealPolicies(req, cb) {
        return this.request("ChannelDeleteSealPolicies", req, cb);
    }
    async ChannelCreateMultiFlowSignQRCode(req, cb) {
        return this.request("ChannelCreateMultiFlowSignQRCode", req, cb);
    }
    async OperateTemplate(req, cb) {
        return this.request("OperateTemplate", req, cb);
    }
    async ModifyExtendedService(req, cb) {
        return this.request("ModifyExtendedService", req, cb);
    }
    async CreateEmployeeChangeUrl(req, cb) {
        return this.request("CreateEmployeeChangeUrl", req, cb);
    }
    async CreateFlowBlockchainEvidenceUrl(req, cb) {
        return this.request("CreateFlowBlockchainEvidenceUrl", req, cb);
    }
    async CreateBatchOrganizationAuthorizationUrl(req, cb) {
        return this.request("CreateBatchOrganizationAuthorizationUrl", req, cb);
    }
    async ChannelCreatePrepareFlowGroup(req, cb) {
        return this.request("ChannelCreatePrepareFlowGroup", req, cb);
    }
    async ChannelCreateBatchQuickSignUrl(req, cb) {
        return this.request("ChannelCreateBatchQuickSignUrl", req, cb);
    }
    async ChannelDisableUserAutoSign(req, cb) {
        return this.request("ChannelDisableUserAutoSign", req, cb);
    }
    async CreateBatchOrganizationRegistrationTasks(req, cb) {
        return this.request("CreateBatchOrganizationRegistrationTasks", req, cb);
    }
    async CreateEmployeeQualificationSealQrCode(req, cb) {
        return this.request("CreateEmployeeQualificationSealQrCode", req, cb);
    }
    async ChannelGetTaskResultApi(req, cb) {
        return this.request("ChannelGetTaskResultApi", req, cb);
    }
    async ChannelCreateUserAutoSignSealUrl(req, cb) {
        return this.request("ChannelCreateUserAutoSignSealUrl", req, cb);
    }
    async CreateChannelSubOrganizationActive(req, cb) {
        return this.request("CreateChannelSubOrganizationActive", req, cb);
    }
    async ModifyPartnerAutoSignAuthUrl(req, cb) {
        return this.request("ModifyPartnerAutoSignAuthUrl", req, cb);
    }
    async DescribeTemplates(req, cb) {
        return this.request("DescribeTemplates", req, cb);
    }
    async ChannelCancelUserAutoSignEnableUrl(req, cb) {
        return this.request("ChannelCancelUserAutoSignEnableUrl", req, cb);
    }
    async UploadFiles(req, cb) {
        return this.request("UploadFiles", req, cb);
    }
    async CreateFlowForwards(req, cb) {
        return this.request("CreateFlowForwards", req, cb);
    }
    async ChannelCreateFlowSignUrl(req, cb) {
        return this.request("ChannelCreateFlowSignUrl", req, cb);
    }
    async CreateChannelOrganizationInfoChangeUrl(req, cb) {
        return this.request("CreateChannelOrganizationInfoChangeUrl", req, cb);
    }
    async DescribeUserFlowType(req, cb) {
        return this.request("DescribeUserFlowType", req, cb);
    }
    async ChannelCreateFlowGroupByFiles(req, cb) {
        return this.request("ChannelCreateFlowGroupByFiles", req, cb);
    }
    async CreateModifyAdminAuthorizationUrl(req, cb) {
        return this.request("CreateModifyAdminAuthorizationUrl", req, cb);
    }
    async DescribeFlowDetailInfo(req, cb) {
        return this.request("DescribeFlowDetailInfo", req, cb);
    }
    async ChannelDescribeBillUsageDetail(req, cb) {
        return this.request("ChannelDescribeBillUsageDetail", req, cb);
    }
    async DeleteOrganizationAuthorizations(req, cb) {
        return this.request("DeleteOrganizationAuthorizations", req, cb);
    }
    async ModifyFlowDeadline(req, cb) {
        return this.request("ModifyFlowDeadline", req, cb);
    }
    async ChannelCreateOrganizationBatchSignUrl(req, cb) {
        return this.request("ChannelCreateOrganizationBatchSignUrl", req, cb);
    }
    async ChannelCreateSealPolicy(req, cb) {
        return this.request("ChannelCreateSealPolicy", req, cb);
    }
    async DescribeCancelFlowsTask(req, cb) {
        return this.request("DescribeCancelFlowsTask", req, cb);
    }
    async CreateConsoleLoginUrl(req, cb) {
        return this.request("CreateConsoleLoginUrl", req, cb);
    }
    async ChannelCreatePrepareFlow(req, cb) {
        return this.request("ChannelCreatePrepareFlow", req, cb);
    }
    async ChannelCreateBatchCancelFlowUrl(req, cb) {
        return this.request("ChannelCreateBatchCancelFlowUrl", req, cb);
    }
    async ChannelDeleteRole(req, cb) {
        return this.request("ChannelDeleteRole", req, cb);
    }
    async SyncProxyOrganization(req, cb) {
        return this.request("SyncProxyOrganization", req, cb);
    }
    async ChannelUpdateSealStatus(req, cb) {
        return this.request("ChannelUpdateSealStatus", req, cb);
    }
    async ChannelCreateBatchSignUrl(req, cb) {
        return this.request("ChannelCreateBatchSignUrl", req, cb);
    }
    async ChannelRenewAutoSignLicense(req, cb) {
        return this.request("ChannelRenewAutoSignLicense", req, cb);
    }
    async ChannelDescribeUserAutoSignStatus(req, cb) {
        return this.request("ChannelDescribeUserAutoSignStatus", req, cb);
    }
    async ChannelCreateReleaseFlow(req, cb) {
        return this.request("ChannelCreateReleaseFlow", req, cb);
    }
    async ChannelDescribeEmployees(req, cb) {
        return this.request("ChannelDescribeEmployees", req, cb);
    }
    async ArchiveDynamicFlow(req, cb) {
        return this.request("ArchiveDynamicFlow", req, cb);
    }
    async CreateOrganizationAuthFile(req, cb) {
        return this.request("CreateOrganizationAuthFile", req, cb);
    }
    async ChannelCreateUserRoles(req, cb) {
        return this.request("ChannelCreateUserRoles", req, cb);
    }
    async ChannelCreateFlowGroupByTemplates(req, cb) {
        return this.request("ChannelCreateFlowGroupByTemplates", req, cb);
    }
    async DescribeChannelFlowEvidenceReport(req, cb) {
        return this.request("DescribeChannelFlowEvidenceReport", req, cb);
    }
    async OperateChannelTemplate(req, cb) {
        return this.request("OperateChannelTemplate", req, cb);
    }
    async SyncProxyOrganizationOperators(req, cb) {
        return this.request("SyncProxyOrganizationOperators", req, cb);
    }
    async ChannelCreateEmbedWebUrl(req, cb) {
        return this.request("ChannelCreateEmbedWebUrl", req, cb);
    }
    async ChannelCreateUserAutoSignEnableUrl(req, cb) {
        return this.request("ChannelCreateUserAutoSignEnableUrl", req, cb);
    }
}
