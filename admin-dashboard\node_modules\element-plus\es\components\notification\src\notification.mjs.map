{"version": 3, "file": "notification.mjs", "sources": ["../../../../../../packages/components/notification/src/notification.ts"], "sourcesContent": ["import { Close } from '@element-plus/icons-vue'\nimport { buildProps, definePropType, iconPropType } from '@element-plus/utils'\n\nimport type {\n  AppContext,\n  ExtractPropTypes,\n  VNode,\n  __ExtractPublicPropTypes,\n} from 'vue'\nimport type Notification from './notification.vue'\n\nexport const notificationTypes = [\n  'primary',\n  'success',\n  'info',\n  'warning',\n  'error',\n] as const\n\nexport const notificationProps = buildProps({\n  /**\n   * @description custom class name for Notification\n   */\n  customClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether `message` is treated as HTML string\n   */\n  dangerouslyUseHTMLString: Boolean,\n  /**\n   * @description duration before close. It will not automatically close if set 0\n   */\n  duration: {\n    type: Number,\n    default: 4500,\n  },\n  /**\n   * @description custom icon component. It will be overridden by `type`\n   */\n  icon: {\n    type: iconPropType,\n  },\n  /**\n   * @description notification dom id\n   */\n  id: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description description text\n   */\n  message: {\n    type: definePropType<string | VNode | (() => VNode)>([\n      String,\n      Object,\n      Function,\n    ]),\n    default: '',\n  },\n  /**\n   * @description offset from the top edge of the screen. Every Notification instance of the same moment should have the same offset\n   */\n  offset: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description callback function when notification clicked\n   */\n  onClick: {\n    type: definePropType<() => void>(Function),\n    default: () => undefined,\n  },\n  /**\n   * @description callback function when closed\n   */\n  onClose: {\n    type: definePropType<() => void>(Function),\n    required: true,\n  },\n  /**\n   * @description custom position\n   */\n  position: {\n    type: String,\n    values: ['top-right', 'top-left', 'bottom-right', 'bottom-left'],\n    default: 'top-right',\n  },\n  /**\n   * @description whether to show a close button\n   */\n  showClose: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description title\n   */\n  title: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description notification type\n   */\n  type: {\n    type: String,\n    values: [...notificationTypes, ''],\n    default: '',\n  },\n  /**\n   * @description initial zIndex\n   */\n  zIndex: Number,\n  /**\n   * @description custom close icon, default is Close\n   */\n  closeIcon: {\n    type: iconPropType,\n    default: Close,\n  },\n} as const)\nexport type NotificationProps = ExtractPropTypes<typeof notificationProps>\nexport type NotificationPropsPublic = __ExtractPublicPropTypes<\n  typeof notificationProps\n>\n\nexport const notificationEmits = {\n  destroy: () => true,\n}\nexport type NotificationEmits = typeof notificationEmits\n\nexport type NotificationInstance = InstanceType<typeof Notification> & unknown\n\nexport type NotificationOptions = Omit<NotificationProps, 'id' | 'onClose'> & {\n  /**\n   * @description set the root element for the notification, default to `document.body`\n   */\n  appendTo?: HTMLElement | string\n  /**\n   * @description callback function when closed\n   */\n  onClose?(vm: VNode): void\n}\nexport type NotificationOptionsTyped = Omit<NotificationOptions, 'type'>\n\nexport interface NotificationHandle {\n  close: () => void\n}\n\nexport type NotificationParams = Partial<NotificationOptions> | string | VNode\nexport type NotificationParamsTyped =\n  | Partial<NotificationOptionsTyped>\n  | string\n  | VNode\n\nexport interface NotifyFn {\n  (\n    options?: NotificationParams,\n    appContext?: null | AppContext\n  ): NotificationHandle\n  closeAll(): void\n  _context: AppContext | null\n}\n\nexport type NotifyTypedFn = (\n  options?: NotificationParamsTyped,\n  appContext?: null | AppContext\n) => NotificationHandle\n\nexport interface Notify extends NotifyFn {\n  primary: NotifyTypedFn\n  success: NotifyTypedFn\n  warning: NotifyTypedFn\n  error: NotifyTypedFn\n  info: NotifyTypedFn\n}\n\nexport interface NotificationQueueItem {\n  vm: VNode\n}\n\nexport type NotificationQueue = NotificationQueueItem[]\n"], "names": [], "mappings": ";;;;AAEY,MAAC,iBAAiB,GAAG;AACjC,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE;AACU,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,wBAAwB,EAAE,OAAO;AACnC,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,YAAY;AACtB,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC;AACzB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,KAAK,CAAC;AACN,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,MAAM,KAAK,CAAC;AACzB,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,QAAQ,EAAE,IAAI;AAClB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,aAAa,CAAC;AACpE,IAAI,OAAO,EAAE,WAAW;AACxB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,CAAC,GAAG,iBAAiB,EAAE,EAAE,CAAC;AACtC,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,CAAC,EAAE;AACS,MAAC,iBAAiB,GAAG;AACjC,EAAE,OAAO,EAAE,MAAM,IAAI;AACrB;;;;"}