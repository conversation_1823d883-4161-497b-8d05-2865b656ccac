import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("organization.tencentcloudapi.com", "2021-03-31", clientConfig);
    }
    async QuitOrganization(req, cb) {
        return this.request("QuitOrganization", req, cb);
    }
    async BindOrganizationPolicySubAccount(req, cb) {
        return this.request("BindOrganizationPolicySubAccount", req, cb);
    }
    async ListGroupMembers(req, cb) {
        return this.request("ListGroupMembers", req, cb);
    }
    async ListOrganizationIdentity(req, cb) {
        return this.request("ListOrganizationIdentity", req, cb);
    }
    async AddOrganizationNode(req, cb) {
        return this.request("AddOrganizationNode", req, cb);
    }
    async UpdateOrganizationMemberEmailBind(req, cb) {
        return this.request("UpdateOrganizationMemberEmailBind", req, cb);
    }
    async RemovePermissionPolicyFromRoleConfiguration(req, cb) {
        return this.request("RemovePermissionPolicyFromRoleConfiguration", req, cb);
    }
    async UpdateSCIMCredentialStatus(req, cb) {
        return this.request("UpdateSCIMCredentialStatus", req, cb);
    }
    async UpdateUser(req, cb) {
        return this.request("UpdateUser", req, cb);
    }
    async CreateOrgServiceAssign(req, cb) {
        return this.request("CreateOrgServiceAssign", req, cb);
    }
    async DescribeEffectivePolicy(req, cb) {
        return this.request("DescribeEffectivePolicy", req, cb);
    }
    async GetUser(req, cb) {
        return this.request("GetUser", req, cb);
    }
    async OpenIdentityCenter(req, cb) {
        return this.request("OpenIdentityCenter", req, cb);
    }
    async DeleteUser(req, cb) {
        return this.request("DeleteUser", req, cb);
    }
    async GetRoleConfiguration(req, cb) {
        return this.request("GetRoleConfiguration", req, cb);
    }
    async CreateOrganizationMember(req, cb) {
        return this.request("CreateOrganizationMember", req, cb);
    }
    async DescribeOrganizationMemberEmailBind(req, cb) {
        return this.request("DescribeOrganizationMemberEmailBind", req, cb);
    }
    async DeleteShareUnitResources(req, cb) {
        return this.request("DeleteShareUnitResources", req, cb);
    }
    async ListTasks(req, cb) {
        return this.request("ListTasks", req, cb);
    }
    async RemoveUserFromGroup(req, cb) {
        return this.request("RemoveUserFromGroup", req, cb);
    }
    async DescribeOrganizationFinancialByProduct(req, cb) {
        return this.request("DescribeOrganizationFinancialByProduct", req, cb);
    }
    async DescribeOrganizationNodes(req, cb) {
        return this.request("DescribeOrganizationNodes", req, cb);
    }
    async CreateUser(req, cb) {
        return this.request("CreateUser", req, cb);
    }
    async ListExternalSAMLIdPCertificates(req, cb) {
        return this.request("ListExternalSAMLIdPCertificates", req, cb);
    }
    async RemoveExternalSAMLIdPCertificate(req, cb) {
        return this.request("RemoveExternalSAMLIdPCertificate", req, cb);
    }
    async CheckAccountDelete(req, cb) {
        return this.request("CheckAccountDelete", req, cb);
    }
    async DescribeOrganizationMembers(req, cb) {
        return this.request("DescribeOrganizationMembers", req, cb);
    }
    async CreateRoleAssignment(req, cb) {
        return this.request("CreateRoleAssignment", req, cb);
    }
    async CreateRoleConfiguration(req, cb) {
        return this.request("CreateRoleConfiguration", req, cb);
    }
    async CancelOrganizationPolicySubAccount(req, cb) {
        return this.request("CancelOrganizationPolicySubAccount", req, cb);
    }
    async AcceptJoinShareUnitInvitation(req, cb) {
        return this.request("AcceptJoinShareUnitInvitation", req, cb);
    }
    async ListUsers(req, cb) {
        return this.request("ListUsers", req, cb);
    }
    async DescribePolicy(req, cb) {
        return this.request("DescribePolicy", req, cb);
    }
    async GetExternalSAMLIdentityProvider(req, cb) {
        return this.request("GetExternalSAMLIdentityProvider", req, cb);
    }
    async ClearExternalSAMLIdentityProvider(req, cb) {
        return this.request("ClearExternalSAMLIdentityProvider", req, cb);
    }
    async UpdateOrganizationIdentity(req, cb) {
        return this.request("UpdateOrganizationIdentity", req, cb);
    }
    async ProvisionRoleConfiguration(req, cb) {
        return this.request("ProvisionRoleConfiguration", req, cb);
    }
    async CancelOrganizationMemberAuthAccount(req, cb) {
        return this.request("CancelOrganizationMemberAuthAccount", req, cb);
    }
    async DescribeOrganizationFinancialByMember(req, cb) {
        return this.request("DescribeOrganizationFinancialByMember", req, cb);
    }
    async GetGroup(req, cb) {
        return this.request("GetGroup", req, cb);
    }
    async DeleteRoleAssignment(req, cb) {
        return this.request("DeleteRoleAssignment", req, cb);
    }
    async CreateOrganizationMembersPolicy(req, cb) {
        return this.request("CreateOrganizationMembersPolicy", req, cb);
    }
    async MoveOrganizationNodeMembers(req, cb) {
        return this.request("MoveOrganizationNodeMembers", req, cb);
    }
    async UpdateZone(req, cb) {
        return this.request("UpdateZone", req, cb);
    }
    async CreateOrganizationIdentity(req, cb) {
        return this.request("CreateOrganizationIdentity", req, cb);
    }
    async AddOrganizationMemberEmail(req, cb) {
        return this.request("AddOrganizationMemberEmail", req, cb);
    }
    async DeleteShareUnitMembers(req, cb) {
        return this.request("DeleteShareUnitMembers", req, cb);
    }
    async ListJoinedGroupsForUser(req, cb) {
        return this.request("ListJoinedGroupsForUser", req, cb);
    }
    async ListPermissionPoliciesInRoleConfiguration(req, cb) {
        return this.request("ListPermissionPoliciesInRoleConfiguration", req, cb);
    }
    async AddShareUnit(req, cb) {
        return this.request("AddShareUnit", req, cb);
    }
    async DescribeOrganizationMemberAuthAccounts(req, cb) {
        return this.request("DescribeOrganizationMemberAuthAccounts", req, cb);
    }
    async ListRoleConfigurationProvisionings(req, cb) {
        return this.request("ListRoleConfigurationProvisionings", req, cb);
    }
    async AddShareUnitMembers(req, cb) {
        return this.request("AddShareUnitMembers", req, cb);
    }
    async DescribePolicyConfig(req, cb) {
        return this.request("DescribePolicyConfig", req, cb);
    }
    async UpdatePolicy(req, cb) {
        return this.request("UpdatePolicy", req, cb);
    }
    async DescribeShareUnits(req, cb) {
        return this.request("DescribeShareUnits", req, cb);
    }
    async ListUserSyncProvisionings(req, cb) {
        return this.request("ListUserSyncProvisionings", req, cb);
    }
    async UpdateUserStatus(req, cb) {
        return this.request("UpdateUserStatus", req, cb);
    }
    async DisablePolicyType(req, cb) {
        return this.request("DisablePolicyType", req, cb);
    }
    async CreateUserSyncProvisioning(req, cb) {
        return this.request("CreateUserSyncProvisioning", req, cb);
    }
    async AddPermissionPolicyToRoleConfiguration(req, cb) {
        return this.request("AddPermissionPolicyToRoleConfiguration", req, cb);
    }
    async DeleteOrganizationMembersPolicy(req, cb) {
        return this.request("DeleteOrganizationMembersPolicy", req, cb);
    }
    async DeletePolicy(req, cb) {
        return this.request("DeletePolicy", req, cb);
    }
    async UpdateOrganizationMembersPolicy(req, cb) {
        return this.request("UpdateOrganizationMembersPolicy", req, cb);
    }
    async CreateGroup(req, cb) {
        return this.request("CreateGroup", req, cb);
    }
    async DescribeShareUnitMembers(req, cb) {
        return this.request("DescribeShareUnitMembers", req, cb);
    }
    async DismantleRoleConfiguration(req, cb) {
        return this.request("DismantleRoleConfiguration", req, cb);
    }
    async DeleteShareUnit(req, cb) {
        return this.request("DeleteShareUnit", req, cb);
    }
    async DeleteOrganizationMembers(req, cb) {
        return this.request("DeleteOrganizationMembers", req, cb);
    }
    async ListOrgServiceAssignMember(req, cb) {
        return this.request("ListOrgServiceAssignMember", req, cb);
    }
    async RejectJoinShareUnitInvitation(req, cb) {
        return this.request("RejectJoinShareUnitInvitation", req, cb);
    }
    async ListNonCompliantResource(req, cb) {
        return this.request("ListNonCompliantResource", req, cb);
    }
    async DeleteGroup(req, cb) {
        return this.request("DeleteGroup", req, cb);
    }
    async GetSCIMSynchronizationStatus(req, cb) {
        return this.request("GetSCIMSynchronizationStatus", req, cb);
    }
    async SetExternalSAMLIdentityProvider(req, cb) {
        return this.request("SetExternalSAMLIdentityProvider", req, cb);
    }
    async DeleteOrganization(req, cb) {
        return this.request("DeleteOrganization", req, cb);
    }
    async ListPoliciesForTarget(req, cb) {
        return this.request("ListPoliciesForTarget", req, cb);
    }
    async DeleteSCIMCredential(req, cb) {
        return this.request("DeleteSCIMCredential", req, cb);
    }
    async UpdateUserSyncProvisioning(req, cb) {
        return this.request("UpdateUserSyncProvisioning", req, cb);
    }
    async GetUserSyncProvisioning(req, cb) {
        return this.request("GetUserSyncProvisioning", req, cb);
    }
    async DescribeOrganization(req, cb) {
        return this.request("DescribeOrganization", req, cb);
    }
    async UpdateSCIMSynchronizationStatus(req, cb) {
        return this.request("UpdateSCIMSynchronizationStatus", req, cb);
    }
    async ListGroups(req, cb) {
        return this.request("ListGroups", req, cb);
    }
    async DescribeShareUnitResources(req, cb) {
        return this.request("DescribeShareUnitResources", req, cb);
    }
    async BindOrganizationMemberAuthAccount(req, cb) {
        return this.request("BindOrganizationMemberAuthAccount", req, cb);
    }
    async UpdateGroup(req, cb) {
        return this.request("UpdateGroup", req, cb);
    }
    async CreatePolicy(req, cb) {
        return this.request("CreatePolicy", req, cb);
    }
    async GetProvisioningTaskStatus(req, cb) {
        return this.request("GetProvisioningTaskStatus", req, cb);
    }
    async UpdateOrganizationNode(req, cb) {
        return this.request("UpdateOrganizationNode", req, cb);
    }
    async UpdateShareUnit(req, cb) {
        return this.request("UpdateShareUnit", req, cb);
    }
    async UpdateOrganizationMember(req, cb) {
        return this.request("UpdateOrganizationMember", req, cb);
    }
    async DescribeOrganizationAuthNode(req, cb) {
        return this.request("DescribeOrganizationAuthNode", req, cb);
    }
    async DescribeIdentityCenter(req, cb) {
        return this.request("DescribeIdentityCenter", req, cb);
    }
    async CreateSCIMCredential(req, cb) {
        return this.request("CreateSCIMCredential", req, cb);
    }
    async DescribeOrganizationMemberAuthIdentities(req, cb) {
        return this.request("DescribeOrganizationMemberAuthIdentities", req, cb);
    }
    async SendOrgMemberAccountBindEmail(req, cb) {
        return this.request("SendOrgMemberAccountBindEmail", req, cb);
    }
    async GetZoneStatistics(req, cb) {
        return this.request("GetZoneStatistics", req, cb);
    }
    async DetachPolicy(req, cb) {
        return this.request("DetachPolicy", req, cb);
    }
    async ListSCIMCredentials(req, cb) {
        return this.request("ListSCIMCredentials", req, cb);
    }
    async CreateOrganizationMemberAuthIdentity(req, cb) {
        return this.request("CreateOrganizationMemberAuthIdentity", req, cb);
    }
    async DescribeOrganizationFinancialByMonth(req, cb) {
        return this.request("DescribeOrganizationFinancialByMonth", req, cb);
    }
    async DeleteAccount(req, cb) {
        return this.request("DeleteAccount", req, cb);
    }
    async DeleteOrganizationNodes(req, cb) {
        return this.request("DeleteOrganizationNodes", req, cb);
    }
    async ListRoleAssignments(req, cb) {
        return this.request("ListRoleAssignments", req, cb);
    }
    async DescribeOrganizationMemberPolicies(req, cb) {
        return this.request("DescribeOrganizationMemberPolicies", req, cb);
    }
    async DeleteUserSyncProvisioning(req, cb) {
        return this.request("DeleteUserSyncProvisioning", req, cb);
    }
    async UpdateRoleConfiguration(req, cb) {
        return this.request("UpdateRoleConfiguration", req, cb);
    }
    async ListRoleConfigurations(req, cb) {
        return this.request("ListRoleConfigurations", req, cb);
    }
    async InviteOrganizationMember(req, cb) {
        return this.request("InviteOrganizationMember", req, cb);
    }
    async AddShareUnitResources(req, cb) {
        return this.request("AddShareUnitResources", req, cb);
    }
    async AttachPolicy(req, cb) {
        return this.request("AttachPolicy", req, cb);
    }
    async DescribeShareAreas(req, cb) {
        return this.request("DescribeShareAreas", req, cb);
    }
    async AddUserToGroup(req, cb) {
        return this.request("AddUserToGroup", req, cb);
    }
    async DeleteRoleConfiguration(req, cb) {
        return this.request("DeleteRoleConfiguration", req, cb);
    }
    async CreateOrganization(req, cb) {
        return this.request("CreateOrganization", req, cb);
    }
    async AddExternalSAMLIdPCertificate(req, cb) {
        return this.request("AddExternalSAMLIdPCertificate", req, cb);
    }
    async DeleteOrgServiceAssign(req, cb) {
        return this.request("DeleteOrgServiceAssign", req, cb);
    }
    async DeleteOrganizationIdentity(req, cb) {
        return this.request("DeleteOrganizationIdentity", req, cb);
    }
    async EnablePolicyType(req, cb) {
        return this.request("EnablePolicyType", req, cb);
    }
    async GetZoneSAMLServiceProviderInfo(req, cb) {
        return this.request("GetZoneSAMLServiceProviderInfo", req, cb);
    }
    async ListTargetsForPolicy(req, cb) {
        return this.request("ListTargetsForPolicy", req, cb);
    }
    async ListPolicies(req, cb) {
        return this.request("ListPolicies", req, cb);
    }
    async GetTaskStatus(req, cb) {
        return this.request("GetTaskStatus", req, cb);
    }
    async CreateOrganizationMemberPolicy(req, cb) {
        return this.request("CreateOrganizationMemberPolicy", req, cb);
    }
    async ListOrganizationService(req, cb) {
        return this.request("ListOrganizationService", req, cb);
    }
    async UpdateCustomPolicyForRoleConfiguration(req, cb) {
        return this.request("UpdateCustomPolicyForRoleConfiguration", req, cb);
    }
    async DeleteOrganizationMemberAuthIdentity(req, cb) {
        return this.request("DeleteOrganizationMemberAuthIdentity", req, cb);
    }
}
