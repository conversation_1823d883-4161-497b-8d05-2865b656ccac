<template>
  <div class="analytics-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">数据分析</h1>
      <p class="page-subtitle">业务数据分析和报表</p>
    </div>

    <!-- 时间范围选择 -->
    <div class="dashboard-card">
      <div class="time-selector">
        <el-radio-group v-model="timeRange" @change="handleTimeRangeChange">
          <el-radio-button label="7d">最近7天</el-radio-button>
          <el-radio-button label="30d">最近30天</el-radio-button>
          <el-radio-button label="90d">最近90天</el-radio-button>
          <el-radio-button label="1y">最近1年</el-radio-button>
        </el-radio-group>

        <el-date-picker
          v-model="customDateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleCustomDateChange"
          style="margin-left: 16px;"
        />
      </div>
    </div>

    <!-- 核心指标 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">核心指标概览</h3>
        <div class="period-info">{{ getPeriodText() }}</div>
      </div>

      <el-row :gutter="20">
        <el-col :xs="12" :sm="6" v-for="metric in coreMetrics" :key="metric.key">
          <div class="metric-card" :class="metric.trendClass">
            <div class="metric-icon">
              <el-icon :size="24" :color="metric.color">
                <component :is="metric.icon" />
              </el-icon>
            </div>
            <div class="metric-content">
              <div class="metric-value">{{ metric.value }}</div>
              <div class="metric-label">{{ metric.label }}</div>
              <div class="metric-trend">
                <el-icon size="12">
                  <component :is="metric.trendIcon" />
                </el-icon>
                {{ metric.trend }}
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表分析 -->
    <el-row :gutter="20">
      <!-- 用户增长趋势 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">用户增长趋势</h3>
            <el-button-group size="small">
              <el-button :type="userGrowthType === 'new' ? 'primary' : ''" @click="userGrowthType = 'new'">
                新增用户
              </el-button>
              <el-button :type="userGrowthType === 'active' ? 'primary' : ''" @click="userGrowthType = 'active'">
                活跃用户
              </el-button>
            </el-button-group>
          </div>
          <div class="chart-container">
            <v-chart :option="userGrowthOption" style="height: 300px;" />
          </div>
        </div>
      </el-col>

      <!-- 小说创作分析 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">小说创作分析</h3>
          </div>
          <div class="chart-container">
            <v-chart :option="novelCreationOption" style="height: 300px;" />
          </div>
        </div>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <!-- 会员转化漏斗 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">会员转化漏斗</h3>
          </div>
          <div class="chart-container">
            <v-chart :option="memberFunnelOption" style="height: 300px;" />
          </div>
        </div>
      </el-col>

      <!-- 小说类型分布 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">小说类型分布</h3>
          </div>
          <div class="chart-container">
            <v-chart :option="genreDistributionOption" style="height: 300px;" />
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-row :gutter="20">
      <!-- 热门小说排行 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">热门小说排行</h3>
            <el-button type="text" @click="$router.push('/novels')">查看全部</el-button>
          </div>

          <el-table :data="popularNovels" style="width: 100%">
            <el-table-column type="index" label="排名" width="60" />
            <el-table-column prop="title" label="小说标题" show-overflow-tooltip />
            <el-table-column label="字数" width="80">
              <template #default="{ row }">
                {{ formatWordCount(row.wordCount) }}
              </template>
            </el-table-column>
            <el-table-column label="热度" width="80">
              <template #default="{ row }">
                <el-progress
                  :percentage="row.popularity"
                  :stroke-width="6"
                  :show-text="false"
                  :color="getPopularityColor(row.popularity)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>

      <!-- 活跃用户排行 -->
      <el-col :xs="24" :lg="12">
        <div class="dashboard-card">
          <div class="card-header">
            <h3 class="card-title">活跃用户排行</h3>
            <el-button type="text" @click="$router.push('/users')">查看全部</el-button>
          </div>

          <el-table :data="activeUsers" style="width: 100%">
            <el-table-column type="index" label="排名" width="60" />
            <el-table-column prop="username" label="用户名" width="100" />
            <el-table-column label="会员状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.isMember ? 'success' : 'info'" size="small">
                  {{ row.isMember ? '会员' : '普通' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="创作数" width="80">
              <template #default="{ row }">
                {{ row.novelCount }}
              </template>
            </el-table-column>
            <el-table-column label="活跃度" width="100">
              <template #default="{ row }">
                <el-progress
                  :percentage="row.activityScore"
                  :stroke-width="6"
                  :show-text="false"
                  color="#52c41a"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>

    <!-- 数据报表 -->
    <div class="dashboard-card">
      <div class="card-header">
        <h3 class="card-title">数据报表</h3>
        <div class="report-actions">
          <el-button @click="generateReport" :loading="reportLoading">
            <el-icon><Document /></el-icon>
            生成报表
          </el-button>
          <el-button @click="exportData">
            <el-icon><Download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>

      <el-table :data="reportData" style="width: 100%">
        <el-table-column prop="date" label="日期" width="120" />
        <el-table-column prop="newUsers" label="新增用户" width="100" />
        <el-table-column prop="activeUsers" label="活跃用户" width="100" />
        <el-table-column prop="newNovels" label="新增小说" width="100" />
        <el-table-column prop="totalWords" label="新增字数" width="120">
          <template #default="{ row }">
            {{ formatWordCount(row.totalWords) }}
          </template>
        </el-table-column>
        <el-table-column prop="memberConversions" label="会员转化" width="100" />
        <el-table-column prop="syncCount" label="数据同步" width="100" />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import VChart from 'vue-echarts'
import { ElMessage } from 'element-plus'
import dayjs from 'dayjs'

const router = useRouter()

// 响应式数据
const timeRange = ref('30d')
const customDateRange = ref(null)
const userGrowthType = ref('new')
const reportLoading = ref(false)

// 核心指标数据
const coreMetrics = ref([
  {
    key: 'users',
    label: '总用户数',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'User',
    color: '#1890ff'
  },
  {
    key: 'novels',
    label: '小说总数',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'Reading',
    color: '#52c41a'
  },
  {
    key: 'words',
    label: '总字数',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'EditPen',
    color: '#faad14'
  },
  {
    key: 'members',
    label: '会员用户',
    value: '0',
    trend: '+0%',
    trendClass: 'trend-up',
    trendIcon: 'ArrowUp',
    icon: 'Postcard',
    color: '#f5222d'
  }
])

// 热门小说数据
const popularNovels = ref([])

// 活跃用户数据
const activeUsers = ref([])

// 报表数据
const reportData = ref([])

// 用户增长图表配置
const userGrowthOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  legend: {
    data: ['新增用户', '活跃用户']
  },
  xAxis: {
    type: 'category',
    data: ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20', '1/21']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '新增用户',
      type: 'line',
      data: [12, 15, 8, 18, 10, 12, 15],
      smooth: true,
      itemStyle: { color: '#1890ff' }
    },
    {
      name: '活跃用户',
      type: 'line',
      data: [65, 72, 68, 78, 69, 76, 89],
      smooth: true,
      itemStyle: { color: '#52c41a' }
    }
  ]
}))

// 小说创作图表配置
const novelCreationOption = computed(() => ({
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['1/15', '1/16', '1/17', '1/18', '1/19', '1/20', '1/21']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [2, 3, 1, 4, 2, 5, 3],
    type: 'bar',
    itemStyle: {
      color: '#faad14'
    }
  }]
}))

// 会员转化漏斗图配置
const memberFunnelOption = computed(() => ({
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b} : {c}%'
  },
  series: [{
    name: '会员转化',
    type: 'funnel',
    left: '10%',
    top: 60,
    bottom: 60,
    width: '80%',
    min: 0,
    max: 100,
    minSize: '0%',
    maxSize: '100%',
    sort: 'descending',
    gap: 2,
    label: {
      show: true,
      position: 'inside'
    },
    data: [
      { value: 100, name: '访问用户' },
      { value: 80, name: '注册用户' },
      { value: 60, name: '活跃用户' },
      { value: 40, name: '付费意向' },
      { value: 20, name: '会员用户' }
    ]
  }]
}))

// 小说类型分布图配置
const genreDistributionOption = computed(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [{
    type: 'pie',
    radius: '50%',
    data: [
      { value: 35, name: '都市' },
      { value: 25, name: '玄幻' },
      { value: 20, name: '科幻' },
      { value: 15, name: '历史' },
      { value: 5, name: '其他' }
    ],
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowOffsetX: 0,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}))

// 获取时间段文本
const getPeriodText = () => {
  const textMap = {
    '7d': '最近7天',
    '30d': '最近30天',
    '90d': '最近90天',
    '1y': '最近1年'
  }
  return textMap[timeRange.value] || '自定义时间段'
}

// 格式化字数
const formatWordCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + '万'
  }
  return count.toString()
}

// 获取热度颜色
const getPopularityColor = (popularity: number) => {
  if (popularity >= 90) return '#f5222d'
  if (popularity >= 70) return '#faad14'
  return '#52c41a'
}

// 处理时间范围变化
const handleTimeRangeChange = (value: string) => {
  customDateRange.value = null
  loadAnalyticsData()
}

// 处理自定义日期变化
const handleCustomDateChange = (dates: any) => {
  if (dates) {
    timeRange.value = 'custom'
    loadAnalyticsData()
  }
}

// 生成报表
const generateReport = async () => {
  reportLoading.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 2000))
    ElMessage.success('报表生成成功')
  } catch (error) {
    ElMessage.error('报表生成失败')
  } finally {
    reportLoading.value = false
  }
}

// 导出数据
const exportData = () => {
  ElMessage.info('导出功能开发中...')
}

// 加载分析数据
const loadAnalyticsData = async () => {
  try {
    // 加载仪表板数据获取基础统计
    const dashboardResponse = await fetch('/api/dashboard', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (dashboardResponse.ok) {
      const dashboardResult = await dashboardResponse.json()
      if (dashboardResult.success) {
        const data = dashboardResult.data

        // 更新核心指标
        coreMetrics.value[0].value = data.stats.totalUsers.toString()
        coreMetrics.value[1].value = data.stats.totalNovels.toString()
        coreMetrics.value[3].value = data.stats.memberUsers.toString()

        // 更新热门小说
        popularNovels.value = (data.popularNovels || []).map((novel, index) => ({
          title: novel.title,
          wordCount: novel.wordCount || 0,
          popularity: Math.max(50, 100 - index * 10) // 模拟热度
        }))

        // 更新活跃用户
        activeUsers.value = (data.recentUsers || []).map((user, index) => ({
          username: user.username,
          isMember: user.isMember || false,
          novelCount: Math.floor(Math.random() * 20) + 1, // 模拟小说数量
          activityScore: Math.max(60, 100 - index * 5) // 模拟活跃度
        }))
      }
    }

    // 加载小说统计获取字数信息
    const novelStatsResponse = await fetch('/api/novels/stats', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
      }
    })

    if (novelStatsResponse.ok) {
      const novelStatsResult = await novelStatsResponse.json()
      if (novelStatsResult.success) {
        const totalWords = novelStatsResult.data.totalWords || 0
        coreMetrics.value[2].value = totalWords >= 10000 ?
          (totalWords / 10000).toFixed(1) + '万' :
          totalWords.toString()
      }
    }

    // 生成模拟报表数据
    const today = new Date()
    const reportDays = 7
    const newReportData = []

    for (let i = reportDays - 1; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)

      newReportData.push({
        date: date.toISOString().split('T')[0],
        newUsers: Math.floor(Math.random() * 20) + 5,
        activeUsers: Math.floor(Math.random() * 50) + 30,
        newNovels: Math.floor(Math.random() * 8) + 1,
        totalWords: Math.floor(Math.random() * 20000) + 5000,
        memberConversions: Math.floor(Math.random() * 5),
        syncCount: Math.floor(Math.random() * 30) + 20
      })
    }

    reportData.value = newReportData

  } catch (error) {
    console.error('加载分析数据失败:', error)
    ElMessage.error('加载分析数据失败')
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadAnalyticsData()
})
</script>

<style scoped>
.analytics-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.time-selector {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.period-info {
  color: #8c8c8c;
  font-size: 14px;
}

.metric-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
  transition: all 0.3s;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.metric-content {
  flex: 1;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.metric-label {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.metric-trend {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.trend-up {
  color: #52c41a;
}

.trend-down {
  color: #ff4d4f;
}

.chart-container {
  width: 100%;
}

.report-actions {
  display: flex;
  gap: 12px;
}

/* 响应式 */
@media (max-width: 768px) {
  .time-selector {
    flex-direction: column;
    align-items: stretch;
  }

  .card-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .report-actions {
    width: 100%;
    justify-content: center;
  }

  .metric-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<style scoped>
.analytics-container {
  padding: 0;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 8px 0;
}

.page-subtitle {
  color: #8c8c8c;
  margin: 0;
}

.dashboard-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 60px 40px;
  text-align: center;
}

.coming-soon h2 {
  font-size: 24px;
  color: #262626;
  margin: 20px 0 12px 0;
}

.coming-soon p {
  color: #8c8c8c;
}
</style>
