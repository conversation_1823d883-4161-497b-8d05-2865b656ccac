"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sign = void 0;
const clone = require("clone");
const signer_1 = require("./signer");
const utils_1 = require("./utils");
__exportStar(require("./keyvalue"), exports);
__exportStar(require("./signer"), exports);
__exportStar(require("./utils.http"), exports);
__exportStar(require("./utils.lang"), exports);
__exportStar(require("./utils"), exports);
function sign(options) {
    const { secretId, secretKey, method, url, service, withSignedParams, isCloudApi } = options;
    // isCloudApi 为 true, 说明使用 cloud api v3, 返回签名值中不能携带 signedParams
    let validWithSignedParams = withSignedParams;
    if (isCloudApi === true && withSignedParams === true) {
        console.warn('isCloudApi 和 withSignedParams 参数同时为 true, withSignedParams 会自动转为 false');
        validWithSignedParams = false;
    }
    const signer = new signer_1.Signer({ secretId, secretKey }, service || 'tcb');
    const headers = clone(options.headers || {});
    const params = clone(options.params || {});
    const timestamp = options.timestamp || utils_1.second() - 1;
    const signatureInfo = signer.tc3sign(method, url, headers, params, timestamp, {
        withSignedParams: validWithSignedParams,
        isCloudApi,
    });
    return {
        authorization: signatureInfo.authorization,
        timestamp: signatureInfo.timestamp,
        multipart: signatureInfo.multipart,
    };
}
exports.sign = sign;
