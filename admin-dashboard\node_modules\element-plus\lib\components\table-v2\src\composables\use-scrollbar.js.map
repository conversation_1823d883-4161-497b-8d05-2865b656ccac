{"version": 3, "file": "use-scrollbar.js", "sources": ["../../../../../../../packages/components/table-v2/src/composables/use-scrollbar.ts"], "sourcesContent": ["import { ref, unref, watch } from 'vue'\n\nimport type { Ref } from 'vue'\nimport type { Alignment as ScrollStrategy } from '@element-plus/components/virtual-list'\nimport type { TableV2Props } from '../table'\nimport type { TableGridInstance } from '../table-grid'\n\nexport type ScrollPos = { scrollLeft: number; scrollTop: number }\ntype GridInstanceRef = Ref<TableGridInstance | undefined>\n\ntype UseScrollBarProps = {\n  mainTableRef: GridInstanceRef\n  leftTableRef: GridInstanceRef\n  rightTableRef: GridInstanceRef\n\n  onMaybeEndReached: () => void\n}\n\nexport type { ScrollStrategy }\n\nexport const useScrollbar = (\n  props: TableV2Props,\n  {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    onMaybeEndReached,\n  }: UseScrollBarProps\n) => {\n  const scrollPos = ref<ScrollPos>({ scrollLeft: 0, scrollTop: 0 })\n\n  function doScroll(params: ScrollPos) {\n    const { scrollTop } = params\n\n    mainTableRef.value?.scrollTo(params)\n    leftTableRef.value?.scrollToTop(scrollTop)\n    rightTableRef.value?.scrollToTop(scrollTop)\n  }\n\n  // methods\n  function scrollTo(params: ScrollPos) {\n    scrollPos.value = params\n\n    doScroll(params)\n  }\n\n  function scrollToTop(scrollTop: number) {\n    scrollPos.value.scrollTop = scrollTop\n\n    doScroll(unref(scrollPos))\n  }\n\n  function scrollToLeft(scrollLeft: number) {\n    scrollPos.value.scrollLeft = scrollLeft\n\n    mainTableRef.value?.scrollTo?.(unref(scrollPos))\n  }\n\n  function onScroll(params: ScrollPos) {\n    scrollTo(params)\n    props.onScroll?.(params)\n  }\n\n  function onVerticalScroll({ scrollTop }: ScrollPos) {\n    const { scrollTop: currentScrollTop } = unref(scrollPos)\n    if (scrollTop !== currentScrollTop) scrollToTop(scrollTop)\n  }\n\n  function scrollToRow(row: number, strategy: ScrollStrategy = 'auto') {\n    mainTableRef.value?.scrollToRow(row, strategy)\n  }\n\n  // When scrollTop changes, maybe reaching the bottom\n  watch(\n    () => unref(scrollPos).scrollTop,\n    (cur, prev) => {\n      if (cur > prev) onMaybeEndReached()\n    }\n  )\n\n  return {\n    scrollPos,\n\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll,\n  }\n}\n"], "names": ["ref", "unref", "watch"], "mappings": ";;;;;;AACY,MAAC,YAAY,GAAG,CAAC,KAAK,EAAE;AACpC,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,aAAa;AACf,EAAE,iBAAiB;AACnB,CAAC,KAAK;AACN,EAAE,MAAM,SAAS,GAAGA,OAAG,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;AACzD,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC5B,IAAI,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;AACnB,IAAI,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,CAAC;AACjC,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrE,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC3E,IAAI,CAAC,EAAE,GAAG,aAAa,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC;AAC5E,GAAG;AACH,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC5B,IAAI,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC;AAC7B,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrB,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,SAAS,EAAE;AAClC,IAAI,SAAS,CAAC,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;AAC1C,IAAI,QAAQ,CAACC,SAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAC/B,GAAG;AACH,EAAE,SAAS,YAAY,CAAC,UAAU,EAAE;AACpC,IAAI,IAAI,EAAE,EAAE,EAAE,CAAC;AACf,IAAI,SAAS,CAAC,KAAK,CAAC,UAAU,GAAG,UAAU,CAAC;AAC5C,IAAI,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,EAAEA,SAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AACrH,GAAG;AACH,EAAE,SAAS,QAAQ,CAAC,MAAM,EAAE;AAC5B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,QAAQ,CAAC,MAAM,CAAC,CAAC;AACrB,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AACpE,GAAG;AACH,EAAE,SAAS,gBAAgB,CAAC,EAAE,SAAS,EAAE,EAAE;AAC3C,IAAI,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAGA,SAAK,CAAC,SAAS,CAAC,CAAC;AAC7D,IAAI,IAAI,SAAS,KAAK,gBAAgB;AACtC,MAAM,WAAW,CAAC,SAAS,CAAC,CAAC;AAC7B,GAAG;AACH,EAAE,SAAS,WAAW,CAAC,GAAG,EAAE,QAAQ,GAAG,MAAM,EAAE;AAC/C,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,CAAC,EAAE,GAAG,YAAY,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AAC/E,GAAG;AACH,EAAEC,SAAK,CAAC,MAAMD,SAAK,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,CAAC,GAAG,EAAE,IAAI,KAAK;AACzD,IAAI,IAAI,GAAG,GAAG,IAAI;AAClB,MAAM,iBAAiB,EAAE,CAAC;AAC1B,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,SAAS;AACb,IAAI,QAAQ;AACZ,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,WAAW;AACf,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,GAAG,CAAC;AACJ;;;;"}