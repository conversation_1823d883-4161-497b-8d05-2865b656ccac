<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>岱宗文脉 - 欢迎</title>
    <link rel="stylesheet" href="https://cdn.staticfile.org/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.staticfile.org/tailwindcss/2.2.19/tailwind.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap">
    <style>
        :root {
            --primary-color: #2D5016; /* 深翠绿 */
            --primary-dark: #1B4332; /* 墨绿 */
            --primary-light: #4F7942; /* 竹绿 */
            --text-color: #1B4332; /* 墨绿文字 */
            --bg-color: #FFFDD0; /* 象牙白 */
            --secondary-bg: #F7E7CE; /* 香槟金 */
            --accent-color: #FFBF00; /* 琥珀黄作为点缀 */
        }

        .dark {
            --primary-color: #AADD44;
            --primary-dark: #8BC34A;
            --primary-light: #CDDC39;
            --text-color: #E1E1E1;
            --bg-color: #121212;
            --accent-color: #FFA726;
        }

        body {
            font-family: 'Noto Sans SC', Tahoma, Arial, Roboto, "Droid Sans", "Helvetica Neue", "Droid Sans Fallback", "Heiti SC", "Hiragino Sans GB", Simsun, sans-serif;
            color: var(--text-color);
            background-color: var(--bg-color);
            transition: background-color 0.3s ease, color 0.3s ease;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .hero-gradient {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
        }

        .logo-text {
            font-size: 3.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-dark), var(--accent-color));
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .btn-start {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%);
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .btn-start:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        }

        /* 微交互动画 */
        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .float-animation {
            animation: float 3s ease-in-out infinite;
        }

        .theme-toggle {
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .theme-toggle:hover {
            transform: rotate(30deg);
        }
    </style>
</head>
<body>
    <!-- 主内容区 -->
    <main class="flex-grow flex items-center justify-center py-8">
        <div class="container mx-auto px-4">
            <div class="flex flex-col md:flex-row items-center justify-center">
                <!-- Logo和标题 -->
                <div class="md:w-1/2 text-center md:text-left mb-10 md:mb-0">
                    <h1 class="logo-text mb-6">岱宗文脉</h1>
                    <p class="text-xl md:text-2xl mb-8">汲取泰山灵气，承载文脉传承</p>
                    <a href="index.html?direct=true" class="btn-start text-xl font-bold py-4 px-10 rounded-full inline-block shadow-lg text-white">
                        快速开始
                        <i class="fas fa-arrow-right ml-2"></i>
                    </a>
                </div>

                <!-- Logo图形 -->
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative float-animation">
                        <svg width="300" height="300" viewBox="0 0 300 300" class="drop-shadow-2xl">
                            <defs>
                                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" stop-color="#9ACD32" />
                                    <stop offset="100%" stop-color="#FF8C00" />
                                </linearGradient>
                            </defs>
                            <circle cx="150" cy="150" r="130" fill="white" />
                            <circle cx="150" cy="150" r="110" fill="url(#logoGradient)" />
                            <!-- 书本形状 -->
                            <path d="M120,100 L120,200 L180,200 C190,200 200,190 200,180 L200,120 C200,110 190,100 180,100 L120,100 Z" fill="white" />
                            <path d="M115,95 L115,205 L180,205 C195,205 205,195 205,180 L205,120 C205,105 195,95 180,95 L115,95 Z M110,90 L180,90 C200,90 210,100 210,120 L210,180 C210,200 200,210 180,210 L110,210 L110,90 Z" fill="#9ACD32" />
                            <!-- 羽毛笔 -->
                            <path d="M160,130 C160,130 180,110 200,130 C220,150 180,190 160,170 L160,130 Z" fill="#FF8C00" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 主题切换按钮 -->
    <div class="fixed top-4 right-4">
        <button id="themeToggle" class="theme-toggle p-2 rounded-full bg-gray-200 dark:bg-gray-700">
            <i class="fas fa-moon text-xl"></i>
        </button>
    </div>

    <script>
        // 主题切换功能
        const themeToggle = document.getElementById('themeToggle');
        const htmlElement = document.documentElement;
        const themeIcon = themeToggle.querySelector('i');

        // 检查系统偏好
        const prefersDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;

        // 检查本地存储
        const savedTheme = localStorage.getItem('theme');

        // 设置初始主题
        if (savedTheme) {
            htmlElement.className = savedTheme;
            updateThemeIcon(savedTheme === 'dark');
        } else if (prefersDarkMode) {
            htmlElement.className = 'dark';
            updateThemeIcon(true);
        }

        // 切换主题
        themeToggle.addEventListener('click', () => {
            const isDark = htmlElement.classList.contains('dark');
            htmlElement.className = isDark ? 'light' : 'dark';
            localStorage.setItem('theme', isDark ? 'light' : 'dark');
            updateThemeIcon(!isDark);
        });

        function updateThemeIcon(isDark) {
            themeIcon.className = isDark ? 'fas fa-sun text-xl' : 'fas fa-moon text-xl';
        }
    </script>
</body>
</html>
