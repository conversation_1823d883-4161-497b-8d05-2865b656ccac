import { CloudBaseInstance, ExtendedCloudBaseInstance, OrmClient } from './types';
import { generateHTTPClient } from './orm/http-orm-client';
export declare function initHTTPOverCallFunction(cloud: CloudBaseInstance, options?: {
    baseUrl?: string;
    sqlBaseUrl?: string;
}): ExtendedCloudBaseInstance;
export declare function init(cloud: CloudBaseInstance): ExtendedCloudBaseInstance;
export * from './types';
export { generateHTTPClient };
declare const _default: {
    init: typeof init;
    generateHTTPClient: (callFunction: import("./types").CallFunction, fetch: (options: import("@cloudbase/adapter-interface").IFetchOptions) => any, baseUrl: string, options?: {
        sqlBaseUrl?: string | undefined;
    } | undefined) => OrmClient;
};
export default _default;
