import{_ as H}from"./_plugin-vue_export-helper-CoKMZnro.js";/* empty css                        *//* empty css                   *//* empty css               *//* empty css                 *//* empty css                        *//* empty css                     *//* empty css                  *//* empty css                  */import{d as K,r as v,a as g,c as C,b as a,e as t,w as s,au as O,q as X,i as b,av as Z,g as $,l as j,m as J,a8 as Q,a2 as Y,a7 as h,ad as ee,W as te,F as le,G as ae,Z as se,_ as ne,$ as oe,h as k,t as _,H as S,X as de,M as ie,E as me,K as ue,U as T,v as D}from"./index-CAzH2L69.js";const re={class:"settings-container"},pe={class:"dashboard-card"},ye={class:"monitor-section"},xe={class:"status-card"},Ve={class:"status-content"},fe={class:"status-name"},ve={class:"status-value"},ge={class:"status-desc"},be={class:"monitor-section"},_e={class:"settings-actions"},we=K({__name:"index",setup(ce){const U=v("basic"),w=v(!1),o=g({systemName:"小说应用后台管理系统",systemDescription:"基于Vue 3构建的现代化小说应用管理平台",systemVersion:"1.0.0",maintenanceMode:!1,allowRegistration:!0,defaultMemberType:"none"}),d=g({autoSync:!0,syncInterval:30,maxSyncSize:100,retryCount:3,enableBackup:!0,backupRetentionDays:30}),i=g({codeValidityDays:365,codePrefix:"VIP",codeLength:10,expirationReminder:!0,reminderDays:7}),m=g({minPasswordLength:8,passwordComplexity:!0,maxLoginAttempts:5,lockoutDuration:15,tokenExpiration:24}),B=v([{name:"API服务",value:"正常运行",description:"响应时间: 45ms",status:"success",icon:"CircleCheckFilled"},{name:"数据库",value:"连接正常",description:"查询时间: 12ms",status:"success",icon:"CircleCheckFilled"},{name:"存储空间",value:"78% 已使用",description:"剩余: 2.1GB",status:"warning",icon:"WarningFilled"}]),P=v([{metric:"CPU使用率",current:"45%",average:"38%",peak:"67%",threshold:80,description:"服务器CPU使用情况"},{metric:"内存使用率",current:"62%",average:"58%",peak:"78%",threshold:85,description:"服务器内存使用情况"},{metric:"API响应时间",current:"45ms",average:"52ms",peak:"120ms",threshold:100,description:"API接口平均响应时间"},{metric:"并发用户数",current:"89",average:"76",peak:"156",threshold:200,description:"当前在线用户数量"}]),M=(p,e)=>{const r=parseFloat(p);return r>=e*.9?"danger":r>=e*.7?"warning":"success"},I=(p,e)=>{const r=parseFloat(p);return r>=e*.9?"警告":r>=e*.7?"注意":"正常"},L=async()=>{try{await T.confirm("确定要保存这些设置吗？","保存确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),w.value=!0,await new Promise(p=>setTimeout(p,2e3)),D.success("设置保存成功")}catch{}finally{w.value=!1}},R=async()=>{try{await T.confirm("确定要重置所有设置吗？","重置确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),D.success("设置已重置")}catch{}};return(p,e)=>{const r=J,n=j,y=Q,c=h,F=Y,f=$,V=Z,u=ee,N=me,z=de,A=te,x=ne,G=oe,W=se,q=O,E=X;return b(),C("div",re,[e[53]||(e[53]=a("div",{class:"page-header"},[a("h1",{class:"page-title"},"系统设置"),a("p",{class:"page-subtitle"},"系统配置和参数管理")],-1)),a("div",pe,[t(q,{modelValue:U.value,"onUpdate:modelValue":e[22]||(e[22]=l=>U.value=l),type:"card"},{default:s(()=>[t(V,{label:"基础设置",name:"basic"},{default:s(()=>[t(f,{model:o,"label-width":"150px"},{default:s(()=>[t(n,{label:"系统名称"},{default:s(()=>[t(r,{modelValue:o.systemName,"onUpdate:modelValue":e[0]||(e[0]=l=>o.systemName=l),style:{width:"300px"}},null,8,["modelValue"])]),_:1}),t(n,{label:"系统描述"},{default:s(()=>[t(r,{modelValue:o.systemDescription,"onUpdate:modelValue":e[1]||(e[1]=l=>o.systemDescription=l),type:"textarea",rows:3,style:{width:"500px"}},null,8,["modelValue"])]),_:1}),t(n,{label:"系统版本"},{default:s(()=>[t(r,{modelValue:o.systemVersion,"onUpdate:modelValue":e[2]||(e[2]=l=>o.systemVersion=l),style:{width:"200px"},readonly:""},null,8,["modelValue"])]),_:1}),t(n,{label:"维护模式"},{default:s(()=>[t(y,{modelValue:o.maintenanceMode,"onUpdate:modelValue":e[3]||(e[3]=l=>o.maintenanceMode=l),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),e[23]||(e[23]=a("div",{class:"setting-desc"},"开启后，普通用户将无法访问系统",-1))]),_:1,__:[23]}),t(n,{label:"用户注册"},{default:s(()=>[t(y,{modelValue:o.allowRegistration,"onUpdate:modelValue":e[4]||(e[4]=l=>o.allowRegistration=l),"active-text":"允许","inactive-text":"禁止"},null,8,["modelValue"]),e[24]||(e[24]=a("div",{class:"setting-desc"},"是否允许新用户注册",-1))]),_:1,__:[24]}),t(n,{label:"默认会员类型"},{default:s(()=>[t(F,{modelValue:o.defaultMemberType,"onUpdate:modelValue":e[5]||(e[5]=l=>o.defaultMemberType=l),style:{width:"200px"}},{default:s(()=>[t(c,{label:"普通用户",value:"none"}),t(c,{label:"月会员",value:"monthly"}),t(c,{label:"永久会员",value:"permanent"})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1}),t(V,{label:"数据同步",name:"sync"},{default:s(()=>[t(f,{model:d,"label-width":"150px"},{default:s(()=>[t(n,{label:"自动同步"},{default:s(()=>[t(y,{modelValue:d.autoSync,"onUpdate:modelValue":e[6]||(e[6]=l=>d.autoSync=l),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),e[25]||(e[25]=a("div",{class:"setting-desc"},"是否自动进行数据同步",-1))]),_:1,__:[25]}),t(n,{label:"同步间隔"},{default:s(()=>[t(u,{modelValue:d.syncInterval,"onUpdate:modelValue":e[7]||(e[7]=l=>d.syncInterval=l),min:1,max:60,style:{width:"200px"}},null,8,["modelValue"]),e[26]||(e[26]=a("span",{style:{"margin-left":"8px"}},"分钟",-1)),e[27]||(e[27]=a("div",{class:"setting-desc"},"自动同步的时间间隔",-1))]),_:1,__:[26,27]}),t(n,{label:"最大同步大小"},{default:s(()=>[t(u,{modelValue:d.maxSyncSize,"onUpdate:modelValue":e[8]||(e[8]=l=>d.maxSyncSize=l),min:1,max:1e3,style:{width:"200px"}},null,8,["modelValue"]),e[28]||(e[28]=a("span",{style:{"margin-left":"8px"}},"MB",-1)),e[29]||(e[29]=a("div",{class:"setting-desc"},"单次同步的最大数据量",-1))]),_:1,__:[28,29]}),t(n,{label:"同步重试次数"},{default:s(()=>[t(u,{modelValue:d.retryCount,"onUpdate:modelValue":e[9]||(e[9]=l=>d.retryCount=l),min:0,max:10,style:{width:"200px"}},null,8,["modelValue"]),e[30]||(e[30]=a("div",{class:"setting-desc"},"同步失败时的重试次数",-1))]),_:1,__:[30]}),t(n,{label:"数据备份"},{default:s(()=>[t(y,{modelValue:d.enableBackup,"onUpdate:modelValue":e[10]||(e[10]=l=>d.enableBackup=l),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),e[31]||(e[31]=a("div",{class:"setting-desc"},"是否启用数据自动备份",-1))]),_:1,__:[31]}),t(n,{label:"备份保留天数"},{default:s(()=>[t(u,{modelValue:d.backupRetentionDays,"onUpdate:modelValue":e[11]||(e[11]=l=>d.backupRetentionDays=l),min:1,max:365,style:{width:"200px"}},null,8,["modelValue"]),e[32]||(e[32]=a("span",{style:{"margin-left":"8px"}},"天",-1)),e[33]||(e[33]=a("div",{class:"setting-desc"},"备份文件的保留时间",-1))]),_:1,__:[32,33]})]),_:1},8,["model"])]),_:1}),t(V,{label:"会员设置",name:"member"},{default:s(()=>[t(f,{model:i,"label-width":"150px"},{default:s(()=>[t(n,{label:"会员码有效期"},{default:s(()=>[t(u,{modelValue:i.codeValidityDays,"onUpdate:modelValue":e[12]||(e[12]=l=>i.codeValidityDays=l),min:1,max:365,style:{width:"200px"}},null,8,["modelValue"]),e[34]||(e[34]=a("span",{style:{"margin-left":"8px"}},"天",-1)),e[35]||(e[35]=a("div",{class:"setting-desc"},"会员码的默认有效期",-1))]),_:1,__:[34,35]}),t(n,{label:"会员码前缀"},{default:s(()=>[t(r,{modelValue:i.codePrefix,"onUpdate:modelValue":e[13]||(e[13]=l=>i.codePrefix=l),style:{width:"200px"}},null,8,["modelValue"]),e[36]||(e[36]=a("div",{class:"setting-desc"},"生成会员码时的默认前缀",-1))]),_:1,__:[36]}),t(n,{label:"会员码长度"},{default:s(()=>[t(u,{modelValue:i.codeLength,"onUpdate:modelValue":e[14]||(e[14]=l=>i.codeLength=l),min:6,max:20,style:{width:"200px"}},null,8,["modelValue"]),e[37]||(e[37]=a("div",{class:"setting-desc"},"会员码的字符长度",-1))]),_:1,__:[37]}),t(n,{label:"到期提醒"},{default:s(()=>[t(y,{modelValue:i.expirationReminder,"onUpdate:modelValue":e[15]||(e[15]=l=>i.expirationReminder=l),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),e[38]||(e[38]=a("div",{class:"setting-desc"},"是否发送会员到期提醒",-1))]),_:1,__:[38]}),t(n,{label:"提醒提前天数"},{default:s(()=>[t(u,{modelValue:i.reminderDays,"onUpdate:modelValue":e[16]||(e[16]=l=>i.reminderDays=l),min:1,max:30,style:{width:"200px"}},null,8,["modelValue"]),e[39]||(e[39]=a("span",{style:{"margin-left":"8px"}},"天",-1)),e[40]||(e[40]=a("div",{class:"setting-desc"},"提前多少天发送到期提醒",-1))]),_:1,__:[39,40]})]),_:1},8,["model"])]),_:1}),t(V,{label:"安全设置",name:"security"},{default:s(()=>[t(f,{model:m,"label-width":"150px"},{default:s(()=>[t(n,{label:"密码最小长度"},{default:s(()=>[t(u,{modelValue:m.minPasswordLength,"onUpdate:modelValue":e[17]||(e[17]=l=>m.minPasswordLength=l),min:6,max:20,style:{width:"200px"}},null,8,["modelValue"]),e[41]||(e[41]=a("div",{class:"setting-desc"},"用户密码的最小长度要求",-1))]),_:1,__:[41]}),t(n,{label:"密码复杂度"},{default:s(()=>[t(y,{modelValue:m.passwordComplexity,"onUpdate:modelValue":e[18]||(e[18]=l=>m.passwordComplexity=l),"active-text":"开启","inactive-text":"关闭"},null,8,["modelValue"]),e[42]||(e[42]=a("div",{class:"setting-desc"},"是否要求密码包含数字、字母和特殊字符",-1))]),_:1,__:[42]}),t(n,{label:"登录失败限制"},{default:s(()=>[t(u,{modelValue:m.maxLoginAttempts,"onUpdate:modelValue":e[19]||(e[19]=l=>m.maxLoginAttempts=l),min:3,max:10,style:{width:"200px"}},null,8,["modelValue"]),e[43]||(e[43]=a("span",{style:{"margin-left":"8px"}},"次",-1)),e[44]||(e[44]=a("div",{class:"setting-desc"},"连续登录失败多少次后锁定账户",-1))]),_:1,__:[43,44]}),t(n,{label:"账户锁定时间"},{default:s(()=>[t(u,{modelValue:m.lockoutDuration,"onUpdate:modelValue":e[20]||(e[20]=l=>m.lockoutDuration=l),min:5,max:60,style:{width:"200px"}},null,8,["modelValue"]),e[45]||(e[45]=a("span",{style:{"margin-left":"8px"}},"分钟",-1)),e[46]||(e[46]=a("div",{class:"setting-desc"},"账户锁定的持续时间",-1))]),_:1,__:[45,46]}),t(n,{label:"Token有效期"},{default:s(()=>[t(u,{modelValue:m.tokenExpiration,"onUpdate:modelValue":e[21]||(e[21]=l=>m.tokenExpiration=l),min:1,max:24,style:{width:"200px"}},null,8,["modelValue"]),e[47]||(e[47]=a("span",{style:{"margin-left":"8px"}},"小时",-1)),e[48]||(e[48]=a("div",{class:"setting-desc"},"用户登录Token的有效期",-1))]),_:1,__:[47,48]})]),_:1},8,["model"])]),_:1}),t(V,{label:"系统监控",name:"monitor"},{default:s(()=>[a("div",ye,[e[49]||(e[49]=a("h4",null,"系统状态",-1)),t(A,{gutter:20},{default:s(()=>[(b(!0),C(le,null,ae(B.value,l=>(b(),S(z,{xs:24,sm:8,key:l.name},{default:s(()=>[a("div",xe,[a("div",{class:ie(["status-icon",l.status])},[t(N,{size:"24"},{default:s(()=>[(b(),S(ue(l.icon)))]),_:2},1024)],2),a("div",Ve,[a("div",fe,_(l.name),1),a("div",ve,_(l.value),1),a("div",ge,_(l.description),1)])])]),_:2},1024))),128))]),_:1})]),a("div",be,[e[50]||(e[50]=a("h4",null,"性能指标",-1)),t(W,{data:P.value,style:{width:"100%"}},{default:s(()=>[t(x,{prop:"metric",label:"指标",width:"200"}),t(x,{prop:"current",label:"当前值",width:"120"}),t(x,{prop:"average",label:"平均值",width:"120"}),t(x,{prop:"peak",label:"峰值",width:"120"}),t(x,{label:"状态",width:"100"},{default:s(({row:l})=>[t(G,{type:M(l.current,l.threshold),size:"small"},{default:s(()=>[k(_(I(l.current,l.threshold)),1)]),_:2},1032,["type"])]),_:1}),t(x,{prop:"description",label:"说明","show-overflow-tooltip":""})]),_:1},8,["data"])])]),_:1})]),_:1},8,["modelValue"]),a("div",_e,[t(E,{onClick:R},{default:s(()=>e[51]||(e[51]=[k("重置")])),_:1,__:[51]}),t(E,{type:"primary",onClick:L,loading:w.value},{default:s(()=>e[52]||(e[52]=[k(" 保存设置 ")])),_:1,__:[52]},8,["loading"])])])])}}}),Ie=H(we,[["__scopeId","data-v-a1101a78"]]);export{Ie as default};
