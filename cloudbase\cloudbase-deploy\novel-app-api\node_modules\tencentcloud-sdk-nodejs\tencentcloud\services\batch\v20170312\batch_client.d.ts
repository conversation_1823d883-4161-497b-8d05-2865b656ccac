import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { DeleteComputeEnvResponse, SubmitJobRequest, RetryJobsResponse, TerminateComputeNodeRequest, DescribeTaskLogsRequest, DescribeTaskTemplatesRequest, DescribeJobMonitorDataResponse, TerminateComputeNodesResponse, DescribeComputeEnvActivitiesResponse, DescribeComputeEnvResponse, TerminateJobRequest, DetachInstancesResponse, AttachInstancesResponse, TerminateComputeNodesRequest, DescribeComputeEnvActivitiesRequest, CreateTaskTemplateRequest, DeleteComputeEnvRequest, CreateComputeEnvRequest, DescribeComputeEnvCreateInfoResponse, TerminateJobResponse, DescribeComputeEnvCreateInfosRequest, DescribeComputeEnvRequest, DescribeTaskTemplatesResponse, DeleteJobRequest, DescribeTaskLogsResponse, DeleteTaskTemplatesRequest, DescribeJobResponse, DescribeComputeEnvCreateInfoRequest, ModifyTaskTemplateRequest, DeleteJobResponse, DetachInstancesRequest, DescribeJobMonitorDataRequest, DescribeJobSubmitInfoResponse, DescribeComputeEnvCreateInfosResponse, DescribeJobRequest, DescribeInstanceCategoriesResponse, ModifyTaskTemplateResponse, SubmitJobResponse, DescribeJobSubmitInfoRequest, CreateComputeEnvResponse, ModifyComputeEnvResponse, CreateTaskTemplateResponse, DescribeJobsRequest, DeleteTaskTemplatesResponse, DescribeCvmZoneInstanceConfigInfosRequest, TerminateTaskInstanceRequest, TerminateTaskInstanceResponse, RetryJobsRequest, DescribeAvailableCvmInstanceTypesResponse, DescribeTaskResponse, DescribeComputeEnvsResponse, DescribeTaskRequest, AttachInstancesRequest, DescribeComputeEnvsRequest, DescribeCvmZoneInstanceConfigInfosResponse, DescribeJobsResponse, TerminateComputeNodeResponse, DescribeAvailableCvmInstanceTypesRequest, DescribeInstanceCategoriesRequest, ModifyComputeEnvRequest } from "./batch_models";
/**
 * batch client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 查询作业任务实例的资源使用监控信息。当前只支持查询弹性节点任务并且Job未删除；暂不支持计算环境类任务；该接口只支持查询作业实例时间范围之内的资源使用情况。
     */
    DescribeJobMonitorData(req: DescribeJobMonitorDataRequest, cb?: (error: string, rep: DescribeJobMonitorDataResponse) => void): Promise<DescribeJobMonitorDataResponse>;
    /**
     * 用于查询计算环境的详细信息
     */
    DescribeComputeEnv(req: DescribeComputeEnvRequest, cb?: (error: string, rep: DescribeComputeEnvResponse) => void): Promise<DescribeComputeEnvResponse>;
    /**
     * 用于创建任务模板
     */
    CreateTaskTemplate(req: CreateTaskTemplateRequest, cb?: (error: string, rep: CreateTaskTemplateResponse) => void): Promise<CreateTaskTemplateResponse>;
    /**
     * 用于销毁计算节点。
对于状态为CREATED、CREATION_FAILED、RUNNING和ABNORMAL的节点，允许销毁处理。
     */
    TerminateComputeNode(req: TerminateComputeNodeRequest, cb?: (error: string, rep: TerminateComputeNodeResponse) => void): Promise<TerminateComputeNodeResponse>;
    /**
     * 用于查询若干个作业的概览信息
     */
    DescribeJobs(req: DescribeJobsRequest, cb?: (error: string, rep: DescribeJobsResponse) => void): Promise<DescribeJobsResponse>;
    /**
     * 查看可用的CVM机型配置信息
     */
    DescribeAvailableCvmInstanceTypes(req: DescribeAvailableCvmInstanceTypesRequest, cb?: (error: string, rep: DescribeAvailableCvmInstanceTypesResponse) => void): Promise<DescribeAvailableCvmInstanceTypesResponse>;
    /**
     * 此接口可将已存在实例添加到计算环境中。
实例需要满足如下条件：<br/>
1.实例不在批量计算系统中。<br/>
2.实例状态要求处于运行中。<br/>
3.支持预付费实例，按小时后付费实例，专享子机实例。不支持竞价实例。<br/>

此接口会将加入到计算环境中的实例重设UserData和重装操作系统。
     */
    AttachInstances(req: AttachInstancesRequest, cb?: (error: string, rep: AttachInstancesResponse) => void): Promise<AttachInstancesResponse>;
    /**
     * 用于创建计算环境
     */
    CreateComputeEnv(req: CreateComputeEnvRequest, cb?: (error: string, rep: CreateComputeEnvResponse) => void): Promise<CreateComputeEnvResponse>;
    /**
     * 用于删除计算环境
     */
    DeleteComputeEnv(req: DeleteComputeEnvRequest, cb?: (error: string, rep: DeleteComputeEnvResponse) => void): Promise<DeleteComputeEnvResponse>;
    /**
     * 将添加到计算环境中的实例从计算环境中移出。若是由批量计算自动创建的计算节点实例则不允许移出。
     */
    DetachInstances(req: DetachInstancesRequest, cb?: (error: string, rep: DetachInstancesResponse) => void): Promise<DetachInstancesResponse>;
    /**
     * 用于获取任务多个实例标准输出和标准错误日志。
     */
    DescribeTaskLogs(req: DescribeTaskLogsRequest, cb?: (error: string, rep: DescribeTaskLogsResponse) => void): Promise<DescribeTaskLogsResponse>;
    /**
     * 用于终止作业。
当作业处于“SUBMITTED”状态时，禁止终止操作；当作业处于“SUCCEED”状态时，终止操作不会生效。
终止作业是一个异步过程。整个终止过程的耗时和任务总数成正比。终止的效果相当于所含的所有任务实例进行[TerminateTaskInstance](https://cloud.tencent.com/document/product/599/15908)操作。具体效果和用法可参考[TerminateTaskInstance](https://cloud.tencent.com/document/product/599/15908)。
     */
    TerminateJob(req: TerminateJobRequest, cb?: (error: string, rep: TerminateJobResponse) => void): Promise<TerminateJobResponse>;
    /**
     * 用于查询指定任务的详细信息，包括任务内部的任务实例信息。
     */
    DescribeTask(req: DescribeTaskRequest, cb?: (error: string, rep: DescribeTaskResponse) => void): Promise<DescribeTaskResponse>;
    /**
     * 获取批量计算可用区机型配置信息
     */
    DescribeCvmZoneInstanceConfigInfos(req: DescribeCvmZoneInstanceConfigInfosRequest, cb?: (error: string, rep: DescribeCvmZoneInstanceConfigInfosResponse) => void): Promise<DescribeCvmZoneInstanceConfigInfosResponse>;
    /**
     * 用于查看一个作业的详细信息，包括内部任务（Task）和依赖（Dependence）信息。
     */
    DescribeJob(req: DescribeJobRequest, cb?: (error: string, rep: DescribeJobResponse) => void): Promise<DescribeJobResponse>;
    /**
     * 用于提交一个作业
     */
    SubmitJob(req: SubmitJobRequest, cb?: (error: string, rep: SubmitJobResponse) => void): Promise<SubmitJobResponse>;
    /**
     * 用于批量销毁计算节点，不允许重复销毁同一个节点。
     */
    TerminateComputeNodes(req: TerminateComputeNodesRequest, cb?: (error: string, rep: TerminateComputeNodesResponse) => void): Promise<TerminateComputeNodesResponse>;
    /**
     * 用于查询任务模板信息
     */
    DescribeTaskTemplates(req: DescribeTaskTemplatesRequest, cb?: (error: string, rep: DescribeTaskTemplatesResponse) => void): Promise<DescribeTaskTemplatesResponse>;
    /**
     * 目前对CVM现有实例族分类，每一类包含若干实例族。该接口用于查询实例分类信息。
     */
    DescribeInstanceCategories(req?: DescribeInstanceCategoriesRequest, cb?: (error: string, rep: DescribeInstanceCategoriesResponse) => void): Promise<DescribeInstanceCategoriesResponse>;
    /**
     * 用于删除任务模板信息
     */
    DeleteTaskTemplates(req: DeleteTaskTemplatesRequest, cb?: (error: string, rep: DeleteTaskTemplatesResponse) => void): Promise<DeleteTaskTemplatesResponse>;
    /**
     * 用于终止任务实例。
对于状态已经为“SUCCEED”和“FAILED”的任务实例，不做处理。
对于状态为“SUBMITTED”、“PENDING”、“RUNNABLE”的任务实例，状态将置为“FAILED”状态。
对于状态为“STARTING”、“RUNNING”、“FAILED_INTERRUPTED”的任务实例，区分两种情况：如果未显示指定计算环境，会先销毁CVM服务器，然后将状态置为“FAILED”，具有一定耗时；如果指定了计算环境EnvId，任务实例状态置为“FAILED”，并重启执行该任务的CVM服务器，具有一定的耗时。
对于状态为“FAILED_INTERRUPTED”的任务实例，终止操作实际成功之后，相关资源和配额才会释放。
     */
    TerminateTaskInstance(req: TerminateTaskInstanceRequest, cb?: (error: string, rep: TerminateTaskInstanceResponse) => void): Promise<TerminateTaskInstanceResponse>;
    /**
     * 用于修改计算环境属性
     */
    ModifyComputeEnv(req: ModifyComputeEnvRequest, cb?: (error: string, rep: ModifyComputeEnvResponse) => void): Promise<ModifyComputeEnvResponse>;
    /**
     * 用于查询指定作业的提交信息，其返回内容包括 JobId 和 SubmitJob 接口中作为输入参数的作业提交信息
     */
    DescribeJobSubmitInfo(req: DescribeJobSubmitInfoRequest, cb?: (error: string, rep: DescribeJobSubmitInfoResponse) => void): Promise<DescribeJobSubmitInfoResponse>;
    /**
     * 查看计算环境的创建信息。
     */
    DescribeComputeEnvCreateInfo(req: DescribeComputeEnvCreateInfoRequest, cb?: (error: string, rep: DescribeComputeEnvCreateInfoResponse) => void): Promise<DescribeComputeEnvCreateInfoResponse>;
    /**
     * 用于查询计算环境的活动信息
     */
    DescribeComputeEnvActivities(req: DescribeComputeEnvActivitiesRequest, cb?: (error: string, rep: DescribeComputeEnvActivitiesResponse) => void): Promise<DescribeComputeEnvActivitiesResponse>;
    /**
     * 用于查看计算环境创建信息列表，包括名称、描述、类型、环境参数、通知及期望节点数等。
     */
    DescribeComputeEnvCreateInfos(req: DescribeComputeEnvCreateInfosRequest, cb?: (error: string, rep: DescribeComputeEnvCreateInfosResponse) => void): Promise<DescribeComputeEnvCreateInfosResponse>;
    /**
     * 用于删除作业记录。
删除作业的效果相当于删除作业相关的所有信息。删除成功后，作业相关的所有信息都无法查询。
待删除的作业必须处于完结状态，且其内部包含的所有任务实例也必须处于完结状态，否则会禁止操作。完结状态，是指处于 SUCCEED 或 FAILED 状态。
     */
    DeleteJob(req: DeleteJobRequest, cb?: (error: string, rep: DeleteJobResponse) => void): Promise<DeleteJobResponse>;
    /**
     * 用于查看计算环境列表
     */
    DescribeComputeEnvs(req: DescribeComputeEnvsRequest, cb?: (error: string, rep: DescribeComputeEnvsResponse) => void): Promise<DescribeComputeEnvsResponse>;
    /**
     * 用于修改任务模板
     */
    ModifyTaskTemplate(req: ModifyTaskTemplateRequest, cb?: (error: string, rep: ModifyTaskTemplateResponse) => void): Promise<ModifyTaskTemplateResponse>;
    /**
     * 用于重试作业中失败的任务实例。
仅当作业处于“FAILED”状态，支持重试操作。重试操作成功后，作业会按照有向无环图中指定的任务依赖关系，依次重试各个任务中失败的任务实例。任务实例的历史信息将被重置，如同首次运行一样，参与后续的调度和执行。
     */
    RetryJobs(req: RetryJobsRequest, cb?: (error: string, rep: RetryJobsResponse) => void): Promise<RetryJobsResponse>;
}
