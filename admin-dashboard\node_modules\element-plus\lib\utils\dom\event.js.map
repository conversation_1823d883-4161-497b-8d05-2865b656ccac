{"version": 3, "file": "event.js", "sources": ["../../../../../packages/utils/dom/event.ts"], "sourcesContent": ["export const composeEventHandlers = <E>(\n  theirsHandler?: (event: E) => boolean | void,\n  oursHandler?: (event: E) => void,\n  { checkForDefaultPrevented = true } = {}\n) => {\n  const handleEvent = (event: E) => {\n    const shouldPrevent = theirsHandler?.(event)\n\n    if (checkForDefaultPrevented === false || !shouldPrevent) {\n      return oursHandler?.(event)\n    }\n  }\n  return handleEvent\n}\n\ntype WhenMouseHandler = (e: PointerEvent) => any\nexport const whenMouse = (handler: WhenMouseHandler): WhenMouseHandler => {\n  return (e: PointerEvent) =>\n    e.pointerType === 'mouse' ? handler(e) : undefined\n}\n"], "names": [], "mappings": ";;;;AAAY,MAAC,oBAAoB,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE,wBAAwB,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK;AAC9G,EAAE,MAAM,WAAW,GAAG,CAAC,KAAK,KAAK;AACjC,IAAI,MAAM,aAAa,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AAChF,IAAI,IAAI,wBAAwB,KAAK,KAAK,IAAI,CAAC,aAAa,EAAE;AAC9D,MAAM,OAAO,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/D,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO,WAAW,CAAC;AACrB,EAAE;AACU,MAAC,SAAS,GAAG,CAAC,OAAO,KAAK;AACtC,EAAE,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,WAAW,KAAK,OAAO,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAChE;;;;;"}