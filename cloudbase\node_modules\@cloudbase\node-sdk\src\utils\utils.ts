interface IErrorInfo {
  requestId?: string
  code?: string
  message?: string
}

export class TcbError extends Error {
  public readonly code: string
  public readonly message: string
  public readonly requestId: string

  public constructor(error: IErrorInfo) {
    super(error.message)
    this.code = error.code
    this.message = error.message
    this.requestId = error.requestId || ''
  }
}

export function isAppId(appIdStr: string) {
  return /^[1-9][0-9]{4,64}$/gim.test(appIdStr)
}

export function filterValue(o, value) {
  for (const key in o) {
    if (o[key] === value) {
      /* eslint-disable-next-line @typescript-eslint/no-dynamic-delete */
      delete o[key]
    }
  }
}

export function filterUndefined(o) {
  filterValue(o, undefined)
}

export function E(errObj: IErrorInfo) {
  return new TcbError(errObj)
}

export function isNonEmptyString(str: string) {
  return typeof str === 'string' && str !== ''
}

export function second(): number {
  // istanbul ignore next
  return Math.floor(new Date().getTime() / 1000)
}

// 兼容模式开关，兼容模式下，不抛出异常，直接返回
let throwOnCode = true

export function setThrowOnCode(value: boolean) {
  throwOnCode = value
}

export function processReturn(result: any) {
  if (!throwOnCode) {
    // 不抛报错，直接返回
    return result
  }

  throw E({ ...result })
}

/**
 * 是否是场景模块名
 *
 * $: 前缀，表示SaaS场景模块名，非实际环境ID，当前通过特殊环境ID标识
 *
 * @param envId
 * @returns
 */
export function isPageModuleName(envId = '') {
  return typeof envId === 'string' && envId.startsWith('$:')
}

// 20 + 1 + 16, 限制长度 40
const kEnvRuleReg = /^[a-z0-9_-]{1,40}$/

export function isValidEnvFormat(env = '') {
  return typeof env === 'string' && kEnvRuleReg.test(env)
}
