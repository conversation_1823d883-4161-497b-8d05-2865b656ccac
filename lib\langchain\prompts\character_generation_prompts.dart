/// 角色生成专用提示词模板
class CharacterGenerationPrompts {
  /// 简化版角色生成提示词
  static String buildSimpleCharacterPrompt({
    required String title,
    required String genre,
    required String background,
    required int characterCount,
    List<String>? preferredTypes,
  }) {
    return '''
你是一位专业的小说角色设计师。请为以下小说创建$characterCount个角色：

【小说信息】
- 标题：$title
- 类型：$genre
- 背景：$background

${preferredTypes != null && preferredTypes.isNotEmpty ? '【角色类型要求】\n${preferredTypes.map((t) => '- $t').join('\n')}' : ''}

【重要】请直接返回JSON数组格式，不要添加任何解释文字或代码块标记。

每个角色必须包含以下字段：
- name: 角色姓名
- gender: 性别
- age: 年龄
- bodyDescription: 外貌描述
- personalityTraits: 性格特点
- background: 背景故事

可选字段：
- race: 种族
- specialAbilities: 特殊能力

示例输出：
[{"name":"张三","gender":"男","age":"25岁","bodyDescription":"身材高大，黑发黑眸","personalityTraits":"勇敢正直，有些冲动","background":"出身平民家庭，立志成为英雄","race":"人类","specialAbilities":"剑术精湛"}]

请确保：
1. 角色符合小说类型和背景
2. 每个角色都有独特个性
3. JSON格式正确，可直接解析
4. 不要添加```json```标记或其他文字
''';
  }

  /// 系统提示词
  static const String systemPrompt = 
      "你是专业的小说角色设计师。请严格按照用户要求的JSON格式输出角色信息，不要添加任何解释文字或代码块标记。";

  /// 错误恢复提示词
  static String buildErrorRecoveryPrompt({
    required String title,
    required String genre,
    required int characterCount,
  }) {
    return '''
请为小说《$title》（类型：$genre）创建$characterCount个简单角色。

直接返回JSON数组，格式：
[{"name":"角色名","gender":"性别","age":"年龄","bodyDescription":"外貌","personalityTraits":"性格","background":"背景"}]

不要添加任何其他文字。
''';
  }

  /// 验证JSON格式的正则表达式
  static final RegExp jsonArrayRegex = RegExp(r'\[[\s\S]*?\]');
  static final RegExp jsonCodeBlockRegex = RegExp(r'```(?:json)?\s*([\s\S]*?)\s*```');
  
  /// 提取JSON的方法
  static String? extractJsonFromResponse(String response) {
    // 1. 直接检查是否为JSON数组
    if (response.trim().startsWith('[') && response.trim().endsWith(']')) {
      try {
        // 简单验证JSON格式
        final trimmed = response.trim();
        if (trimmed.contains('"name"') && trimmed.contains('"gender"')) {
          return trimmed;
        }
      } catch (e) {
        // 继续尝试其他方法
      }
    }

    // 2. 尝试提取代码块
    final codeBlockMatch = jsonCodeBlockRegex.firstMatch(response);
    if (codeBlockMatch != null) {
      final extracted = codeBlockMatch.group(1)?.trim();
      if (extracted != null && extracted.startsWith('[') && extracted.endsWith(']')) {
        return extracted;
      }
    }

    // 3. 尝试提取JSON数组
    final arrayMatch = jsonArrayRegex.firstMatch(response);
    if (arrayMatch != null) {
      final extracted = arrayMatch.group(0)?.trim();
      if (extracted != null && extracted.contains('"name"')) {
        return extracted;
      }
    }

    return null;
  }

  /// 创建错误角色JSON
  static String createErrorCharacterJson(String errorMessage, {int count = 1}) {
    final characters = List.generate(count, (index) => {
      'name': '生成失败${count > 1 ? '${index + 1}' : ''}',
      'gender': '未知',
      'age': '未知',
      'bodyDescription': '角色生成失败',
      'personalityTraits': errorMessage,
      'background': '请检查网络连接和API配置后重试',
    });
    
    return characters.map((char) => 
      '{"name":"${char['name']}","gender":"${char['gender']}","age":"${char['age']}","bodyDescription":"${char['bodyDescription']}","personalityTraits":"${char['personalityTraits']}","background":"${char['background']}"}'
    ).join(',').let((json) => '[$json]');
  }
}

extension on String {
  T let<T>(T Function(String) transform) => transform(this);
}
