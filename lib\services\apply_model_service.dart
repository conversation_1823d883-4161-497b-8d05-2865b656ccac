import 'package:get/get.dart';
import 'dart:convert';
import '../controllers/api_config_controller.dart';
import '../services/ai_service.dart';
import '../models/novel.dart';

/// Apply模型服务
/// 专门用于精确应用编辑建议到文本内容
/// 类似Cursor IDE的Apply机制
class ApplyModelService {
  final ApiConfigController _apiConfig = Get.find<ApiConfigController>();
  late final AIService _aiService;

  ApplyModelService() {
    _aiService = AIService(_apiConfig);
  }

  /// 应用编辑建议到章节内容
  Future<ApplyResult> applyEdit({
    required EditSuggestion suggestion,
    required Chapter chapter,
  }) async {
    try {
      final applyModel = _apiConfig.applyModel.value;
      if (applyModel == null) {
        return ApplyResult(
          success: false,
          error: 'Apply模型未配置',
        );
      }

      // 构建Apply指令
      final applyInstruction = _buildApplyInstruction(suggestion, chapter);

      // 临时保存当前模型ID
      final originalModelId = _apiConfig.selectedModelId.value;

      // 获取优化的Apply模型配置
      final optimizedApplyModel = _apiConfig.getOptimizedApplyModelConfig(applyModel);

      print('🔧 Apply模型开始工作');
      print('   原始模型: $originalModelId');
      print('   Apply模型: ${applyModel.name}');
      print('   优化配置: 温度=${optimizedApplyModel.temperature}, Token=${optimizedApplyModel.maxTokens}');

      // 临时切换到Apply模型
      _apiConfig.updateSelectedModel(applyModel.name);

      try {
        // 调用Apply模型（使用优化配置）
        print('🚀 开始调用Apply模型生成文本...');
        final response = await _aiService.generateText(
          systemPrompt: _getApplySystemPrompt(),
          userPrompt: applyInstruction,
          temperature: optimizedApplyModel.temperature,
          maxTokens: optimizedApplyModel.maxTokens,
        );

        print('✅ Apply模型响应完成，响应长度: ${response.length}');

        // 解析Apply结果
        final result = await _parseApplyResult(response, chapter);

        print('🎯 Apply结果: ${result.success ? "成功" : "失败"}');
        if (!result.success) {
          print('   错误: ${result.error}');
        }

        return result;
      } finally {
        // 恢复原始模型配置
        print('🔄 恢复原始模型: $originalModelId');
        _apiConfig.updateSelectedModel(originalModelId);
      }

    } catch (e) {
      return ApplyResult(
        success: false,
        error: 'Apply模型执行失败: $e',
      );
    }
  }

  /// 批量应用多个编辑建议
  Future<BatchApplyResult> batchApplyEdits({
    required List<EditSuggestion> suggestions,
    required Chapter chapter,
  }) async {
    final results = <ApplyResult>[];
    var currentChapter = chapter;
    var successCount = 0;

    for (final suggestion in suggestions) {
      final result = await applyEdit(
        suggestion: suggestion,
        chapter: currentChapter,
      );
      
      results.add(result);
      
      if (result.success && result.modifiedContent != null) {
        // 更新章节内容为修改后的内容
        currentChapter = Chapter(
          number: currentChapter.number,
          title: currentChapter.title,
          content: result.modifiedContent!,
        );
        successCount++;
      }
    }

    return BatchApplyResult(
      results: results,
      successCount: successCount,
      totalCount: suggestions.length,
      finalContent: currentChapter.content,
    );
  }



  /// 构建Apply指令
  String _buildApplyInstruction(EditSuggestion suggestion, Chapter chapter) {
    final lines = chapter.content.split('\n');
    
    // 获取上下文（修改位置前后的内容）
    final contextBefore = _getContextLines(lines, suggestion.startLine - 3, suggestion.startLine - 1);
    final contextAfter = _getContextLines(lines, suggestion.endLine + 1, suggestion.endLine + 3);
    final targetLines = _getContextLines(lines, suggestion.startLine, suggestion.endLine);

    return '''
编辑类型: ${suggestion.type.name}
编辑描述: ${suggestion.description}

章节信息:
- 标题: ${chapter.title}
- 总行数: ${lines.length}

修改位置: 第${suggestion.startLine + 1}行 到 第${suggestion.endLine + 1}行

上下文（修改前）:
${contextBefore.isNotEmpty ? contextBefore.join('\n') : '（文档开头）'}

当前内容:
${targetLines.join('\n')}

上下文（修改后）:
${contextAfter.isNotEmpty ? contextAfter.join('\n') : '（文档结尾）'}

建议的新内容:
${suggestion.suggestedText}

请精确执行这个编辑操作，返回修改后的完整章节内容。
''';
  }

  /// 获取指定范围的行
  List<String> _getContextLines(List<String> lines, int start, int end) {
    if (start < 0 || end >= lines.length || start > end) {
      return [];
    }
    return lines.sublist(start, end + 1);
  }

  /// 获取Apply系统提示词
  String _getApplySystemPrompt() {
    return '''
你是一个精确的文本编辑器，专门负责将编辑建议准确应用到文本内容中。

你的任务：
1. 理解用户提供的编辑指令和上下文
2. 精确执行指定的编辑操作（替换、插入、删除）
3. 确保修改后的内容保持原有的格式和风格
4. 返回修改后的完整章节内容

编辑规则：
- REPLACE: 用新内容替换指定行范围的内容
- INSERT: 在指定位置插入新内容
- DELETE: 删除指定行范围的内容

注意事项：
- 必须保持文本的原有格式（段落、缩进等）
- 确保修改后的内容语法正确、逻辑连贯
- 只修改指定的部分，其他内容保持不变
- 返回完整的章节内容，不要添加任何解释或标记

输出格式：
直接返回修改后的完整章节内容，不要包含任何其他文字。
''';
  }

  /// 解析Apply结果
  Future<ApplyResult> _parseApplyResult(String response, Chapter originalChapter) async {
    try {
      // 清理响应内容
      final cleanedResponse = response.trim();
      
      // 验证修改后的内容
      if (cleanedResponse.isEmpty) {
        return ApplyResult(
          success: false,
          error: 'Apply模型返回空内容',
        );
      }

      // 基本验证：检查内容长度是否合理
      final originalLength = originalChapter.content.length;
      final newLength = cleanedResponse.length;
      
      // 如果新内容长度变化过大，可能有问题
      if (newLength < originalLength * 0.1 || newLength > originalLength * 10) {
        return ApplyResult(
          success: false,
          error: '修改后的内容长度异常（原长度: $originalLength, 新长度: $newLength）',
        );
      }

      return ApplyResult(
        success: true,
        modifiedContent: cleanedResponse,
        originalContent: originalChapter.content,
      );

    } catch (e) {
      return ApplyResult(
        success: false,
        error: '解析Apply结果失败: $e',
      );
    }
  }
}

/// Apply结果
class ApplyResult {
  final bool success;
  final String? modifiedContent;
  final String? originalContent;
  final String? error;

  ApplyResult({
    required this.success,
    this.modifiedContent,
    this.originalContent,
    this.error,
  });
}

/// 批量Apply结果
class BatchApplyResult {
  final List<ApplyResult> results;
  final int successCount;
  final int totalCount;
  final String finalContent;

  BatchApplyResult({
    required this.results,
    required this.successCount,
    required this.totalCount,
    required this.finalContent,
  });

  bool get allSuccess => successCount == totalCount;
  double get successRate => totalCount > 0 ? successCount / totalCount : 0.0;
}

/// 编辑建议（临时定义，避免循环导入）
class EditSuggestion {
  final String id;
  final String description;
  final String originalText;
  final String suggestedText;
  final int startLine;
  final int endLine;
  final EditType type;

  EditSuggestion({
    required this.id,
    required this.description,
    required this.originalText,
    required this.suggestedText,
    required this.startLine,
    required this.endLine,
    required this.type,
  });
}

enum EditType {
  replace,
  insert,
  delete,
}
