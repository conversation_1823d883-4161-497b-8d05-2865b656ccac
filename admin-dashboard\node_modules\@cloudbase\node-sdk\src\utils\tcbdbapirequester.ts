import * as tcbapicaller from './tcbapirequester'
import { ICloudBaseConfig, ICustomReqOpts } from '../../types'

export class TcbDBApiHttpRequester {
  private readonly config: ICloudBaseConfig

  public constructor(config: ICloudBaseConfig) {
    this.config = config
  }

  /**
     * 发送请求
     *
     * @param dbParams   - 数据库请求参数
     * @param opts  - 可选配置项
     */
  public async send(api: string, data: any, opts?: ICustomReqOpts): Promise<any> {
    const params = { ...data, action: api }

    return await tcbapicaller.request({
      config: this.config,
      params,
      method: 'post',
      opts,
      headers: {
        'content-type': 'application/json'
      }
    })
  }
}
