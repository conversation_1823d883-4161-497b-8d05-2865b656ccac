{"version": 3, "file": "header-cell.js", "sources": ["../../../../../../packages/components/table-v2/src/header-cell.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { classType, column } from './common'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\n\nexport const tableV2HeaderCell = buildProps({\n  class: classType,\n  columnIndex: Number,\n  column,\n})\n\nexport type TableV2HeaderCell = ExtractPropTypes<typeof tableV2HeaderCell>\nexport type TableV2HeaderCellPublic = __ExtractPublicPropTypes<\n  typeof tableV2HeaderCell\n>\n"], "names": ["buildProps", "classType", "column"], "mappings": ";;;;;;;AAEY,MAAC,iBAAiB,GAAGA,kBAAU,CAAC;AAC5C,EAAE,KAAK,EAAEC,gBAAS;AAClB,EAAE,WAAW,EAAE,MAAM;AACrB,UAAEC,aAAM;AACR,CAAC;;;;"}