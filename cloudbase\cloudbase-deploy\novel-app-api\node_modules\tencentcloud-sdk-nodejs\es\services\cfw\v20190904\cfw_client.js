import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cfw.tencentcloudapi.com", "2019-09-04", clientConfig);
    }
    async RemoveVpcAcRule(req, cb) {
        return this.request("RemoveVpcAcRule", req, cb);
    }
    async DeleteBlockIgnoreRuleNew(req, cb) {
        return this.request("DeleteBlockIgnoreRuleNew", req, cb);
    }
    async DescribeVpcFwGroupSwitch(req, cb) {
        return this.request("DescribeVpcFwGroupSwitch", req, cb);
    }
    async ModifyEWRuleStatus(req, cb) {
        return this.request("ModifyEWRuleStatus", req, cb);
    }
    async StopSecurityGroupRuleDispatch(req, cb) {
        return this.request("StopSecurityGroupRuleDispatch", req, cb);
    }
    async ModifyResourceGroup(req, cb) {
        return this.request("ModifyResourceGroup", req, cb);
    }
    async DescribeResourceGroupNew(req, cb) {
        return this.request("DescribeResourceGroupNew", req, cb);
    }
    async ModifyVpcFwGroup(req, cb) {
        return this.request("ModifyVpcFwGroup", req, cb);
    }
    async ModifyAssetSync(req, cb) {
        return this.request("ModifyAssetSync", req, cb);
    }
    async DeleteVpcFwGroup(req, cb) {
        return this.request("DeleteVpcFwGroup", req, cb);
    }
    async AddVpcAcRule(req, cb) {
        return this.request("AddVpcAcRule", req, cb);
    }
    async AddEnterpriseSecurityGroupRules(req, cb) {
        return this.request("AddEnterpriseSecurityGroupRules", req, cb);
    }
    async DescribeTableStatus(req, cb) {
        return this.request("DescribeTableStatus", req, cb);
    }
    async ModifySecurityGroupSequenceRules(req, cb) {
        return this.request("ModifySecurityGroupSequenceRules", req, cb);
    }
    async RemoveEnterpriseSecurityGroupRule(req, cb) {
        return this.request("RemoveEnterpriseSecurityGroupRule", req, cb);
    }
    async ModifyNatSequenceRules(req, cb) {
        return this.request("ModifyNatSequenceRules", req, cb);
    }
    async DescribeUnHandleEventTabList(req, cb) {
        return this.request("DescribeUnHandleEventTabList", req, cb);
    }
    async CreateNatFwInstance(req, cb) {
        return this.request("CreateNatFwInstance", req, cb);
    }
    async ModifyBlockTop(req, cb) {
        return this.request("ModifyBlockTop", req, cb);
    }
    async DeleteRemoteAccessDomain(req, cb) {
        return this.request("DeleteRemoteAccessDomain", req, cb);
    }
    async RemoveAclRule(req, cb) {
        return this.request("RemoveAclRule", req, cb);
    }
    async DeleteNatFwInstance(req, cb) {
        return this.request("DeleteNatFwInstance", req, cb);
    }
    async CreateBlockIgnoreRuleList(req, cb) {
        return this.request("CreateBlockIgnoreRuleList", req, cb);
    }
    async ModifyBlockIgnoreRuleNew(req, cb) {
        return this.request("ModifyBlockIgnoreRuleNew", req, cb);
    }
    async ModifyEdgeIpSwitch(req, cb) {
        return this.request("ModifyEdgeIpSwitch", req, cb);
    }
    async DescribeBlockByIpTimesList(req, cb) {
        return this.request("DescribeBlockByIpTimesList", req, cb);
    }
    async DescribeNatFwDnatRule(req, cb) {
        return this.request("DescribeNatFwDnatRule", req, cb);
    }
    async DescribeEnterpriseSGRuleProgress(req, cb) {
        return this.request("DescribeEnterpriseSGRuleProgress", req, cb);
    }
    async DescribeNatFwInfoCount(req, cb) {
        return this.request("DescribeNatFwInfoCount", req, cb);
    }
    async DescribeNatFwVpcDnsLst(req, cb) {
        return this.request("DescribeNatFwVpcDnsLst", req, cb);
    }
    async ModifyBlockIgnoreRule(req, cb) {
        return this.request("ModifyBlockIgnoreRule", req, cb);
    }
    async DescribeAclRule(req, cb) {
        return this.request("DescribeAclRule", req, cb);
    }
    async ModifySecurityGroupRule(req, cb) {
        return this.request("ModifySecurityGroupRule", req, cb);
    }
    async DeleteAllAccessControlRule(req, cb) {
        return this.request("DeleteAllAccessControlRule", req, cb);
    }
    async DescribeSecurityGroupList(req, cb) {
        return this.request("DescribeSecurityGroupList", req, cb);
    }
    async ModifyVpcAcRule(req, cb) {
        return this.request("ModifyVpcAcRule", req, cb);
    }
    async DescribeAssetSync(req, cb) {
        return this.request("DescribeAssetSync", req, cb);
    }
    async CreateSecurityGroupRules(req, cb) {
        return this.request("CreateSecurityGroupRules", req, cb);
    }
    async DescribeDefenseSwitch(req, cb) {
        return this.request("DescribeDefenseSwitch", req, cb);
    }
    async DescribeFwGroupInstanceInfo(req, cb) {
        return this.request("DescribeFwGroupInstanceInfo", req, cb);
    }
    async ModifyNatFwVpcDnsSwitch(req, cb) {
        return this.request("ModifyNatFwVpcDnsSwitch", req, cb);
    }
    async ModifySecurityGroupItemRuleStatus(req, cb) {
        return this.request("ModifySecurityGroupItemRuleStatus", req, cb);
    }
    async DescribeNatFwInstanceWithRegion(req, cb) {
        return this.request("DescribeNatFwInstanceWithRegion", req, cb);
    }
    async DeleteIdsWhiteRule(req, cb) {
        return this.request("DeleteIdsWhiteRule", req, cb);
    }
    async CreateBlockIgnoreRuleNew(req, cb) {
        return this.request("CreateBlockIgnoreRuleNew", req, cb);
    }
    async DescribeSourceAsset(req, cb) {
        return this.request("DescribeSourceAsset", req, cb);
    }
    async DeleteResourceGroup(req, cb) {
        return this.request("DeleteResourceGroup", req, cb);
    }
    async RemoveAcRule(req, cb) {
        return this.request("RemoveAcRule", req, cb);
    }
    async ModifyVpcFwSequenceRules(req, cb) {
        return this.request("ModifyVpcFwSequenceRules", req, cb);
    }
    async CreateAlertCenterOmit(req, cb) {
        return this.request("CreateAlertCenterOmit", req, cb);
    }
    async CreateIdsWhiteRule(req, cb) {
        return this.request("CreateIdsWhiteRule", req, cb);
    }
    async DescribeVpcAcRule(req, cb) {
        return this.request("DescribeVpcAcRule", req, cb);
    }
    async ModifyEnterpriseSecurityGroupRule(req, cb) {
        return this.request("ModifyEnterpriseSecurityGroupRule", req, cb);
    }
    async AddAclRule(req, cb) {
        return this.request("AddAclRule", req, cb);
    }
    async ModifyNatAcRule(req, cb) {
        return this.request("ModifyNatAcRule", req, cb);
    }
    async DeleteSecurityGroupRule(req, cb) {
        return this.request("DeleteSecurityGroupRule", req, cb);
    }
    async ModifyAddressTemplate(req, cb) {
        return this.request("ModifyAddressTemplate", req, cb);
    }
    async DescribeSwitchLists(req, cb) {
        return this.request("DescribeSwitchLists", req, cb);
    }
    async ModifyTableStatus(req, cb) {
        return this.request("ModifyTableStatus", req, cb);
    }
    async CreateAlertCenterIsolate(req, cb) {
        return this.request("CreateAlertCenterIsolate", req, cb);
    }
    async ModifyBlockIgnoreList(req, cb) {
        return this.request("ModifyBlockIgnoreList", req, cb);
    }
    async CreateChooseVpcs(req, cb) {
        return this.request("CreateChooseVpcs", req, cb);
    }
    async ModifyAssetScan(req, cb) {
        return this.request("ModifyAssetScan", req, cb);
    }
    async DeleteAddressTemplate(req, cb) {
        return this.request("DeleteAddressTemplate", req, cb);
    }
    async ModifySequenceRules(req, cb) {
        return this.request("ModifySequenceRules", req, cb);
    }
    async ModifySequenceAclRules(req, cb) {
        return this.request("ModifySequenceAclRules", req, cb);
    }
    async DescribeRuleOverview(req, cb) {
        return this.request("DescribeRuleOverview", req, cb);
    }
    async DescribeIPStatusList(req, cb) {
        return this.request("DescribeIPStatusList", req, cb);
    }
    async DescribeCfwInsStatus(req, cb) {
        return this.request("DescribeCfwInsStatus", req, cb);
    }
    async DescribeEnterpriseSecurityGroupRule(req, cb) {
        return this.request("DescribeEnterpriseSecurityGroupRule", req, cb);
    }
    async ModifyFwGroupSwitch(req, cb) {
        return this.request("ModifyFwGroupSwitch", req, cb);
    }
    async DescribeLogs(req, cb) {
        return this.request("DescribeLogs", req, cb);
    }
    async SetNatFwDnatRule(req, cb) {
        return this.request("SetNatFwDnatRule", req, cb);
    }
    async ModifyAllRuleStatus(req, cb) {
        return this.request("ModifyAllRuleStatus", req, cb);
    }
    async DeleteBlockIgnoreRuleList(req, cb) {
        return this.request("DeleteBlockIgnoreRuleList", req, cb);
    }
    async SetNatFwEip(req, cb) {
        return this.request("SetNatFwEip", req, cb);
    }
    async CreateAlertCenterRule(req, cb) {
        return this.request("CreateAlertCenterRule", req, cb);
    }
    async ModifyAclRule(req, cb) {
        return this.request("ModifyAclRule", req, cb);
    }
    async DescribeEnterpriseSecurityGroupRuleList(req, cb) {
        return this.request("DescribeEnterpriseSecurityGroupRuleList", req, cb);
    }
    async DescribeCfwEips(req, cb) {
        return this.request("DescribeCfwEips", req, cb);
    }
    async DescribeBlockIgnoreList(req, cb) {
        return this.request("DescribeBlockIgnoreList", req, cb);
    }
    async RemoveNatAcRule(req, cb) {
        return this.request("RemoveNatAcRule", req, cb);
    }
    async DescribeAcLists(req, cb) {
        return this.request("DescribeAcLists", req, cb);
    }
    async DescribeLogStorageStatistic(req, cb) {
        return this.request("DescribeLogStorageStatistic", req, cb);
    }
    async ExpandCfwVertical(req, cb) {
        return this.request("ExpandCfwVertical", req, cb);
    }
    async CreateVpcFwGroup(req, cb) {
        return this.request("CreateVpcFwGroup", req, cb);
    }
    async ModifyAllPublicIPSwitchStatus(req, cb) {
        return this.request("ModifyAllPublicIPSwitchStatus", req, cb);
    }
    async DescribeAssociatedInstanceList(req, cb) {
        return this.request("DescribeAssociatedInstanceList", req, cb);
    }
    async ModifyNatInstance(req, cb) {
        return this.request("ModifyNatInstance", req, cb);
    }
    async DeleteAcRule(req, cb) {
        return this.request("DeleteAcRule", req, cb);
    }
    async DescribeNatAcRule(req, cb) {
        return this.request("DescribeNatAcRule", req, cb);
    }
    async DescribeFwSyncStatus(req, cb) {
        return this.request("DescribeFwSyncStatus", req, cb);
    }
    async DescribeNatFwInstance(req, cb) {
        return this.request("DescribeNatFwInstance", req, cb);
    }
    async CreateDatabaseWhiteListRules(req, cb) {
        return this.request("CreateDatabaseWhiteListRules", req, cb);
    }
    async CreateNatFwInstanceWithDomain(req, cb) {
        return this.request("CreateNatFwInstanceWithDomain", req, cb);
    }
    async CreateAcRules(req, cb) {
        return this.request("CreateAcRules", req, cb);
    }
    async ModifyEnterpriseSecurityDispatchStatus(req, cb) {
        return this.request("ModifyEnterpriseSecurityDispatchStatus", req, cb);
    }
    async DescribeTLogInfo(req, cb) {
        return this.request("DescribeTLogInfo", req, cb);
    }
    async ModifyRunSyncAsset(req, cb) {
        return this.request("ModifyRunSyncAsset", req, cb);
    }
    async DescribeGuideScanInfo(req, cb) {
        return this.request("DescribeGuideScanInfo", req, cb);
    }
    async DescribeAddressTemplateList(req, cb) {
        return this.request("DescribeAddressTemplateList", req, cb);
    }
    async DescribeNatFwInstancesInfo(req, cb) {
        return this.request("DescribeNatFwInstancesInfo", req, cb);
    }
    async ModifyNatFwReSelect(req, cb) {
        return this.request("ModifyNatFwReSelect", req, cb);
    }
    async ModifyStorageSetting(req, cb) {
        return this.request("ModifyStorageSetting", req, cb);
    }
    async SearchLog(req, cb) {
        return this.request("SearchLog", req, cb);
    }
    async DescribeResourceGroup(req, cb) {
        return this.request("DescribeResourceGroup", req, cb);
    }
    async SyncFwOperate(req, cb) {
        return this.request("SyncFwOperate", req, cb);
    }
    async ModifyAcRule(req, cb) {
        return this.request("ModifyAcRule", req, cb);
    }
    async DescribeFwEdgeIps(req, cb) {
        return this.request("DescribeFwEdgeIps", req, cb);
    }
    async ModifyNatFwSwitch(req, cb) {
        return this.request("ModifyNatFwSwitch", req, cb);
    }
    async DescribeTLogIpList(req, cb) {
        return this.request("DescribeTLogIpList", req, cb);
    }
    async CreateAddressTemplate(req, cb) {
        return this.request("CreateAddressTemplate", req, cb);
    }
    async AddNatAcRule(req, cb) {
        return this.request("AddNatAcRule", req, cb);
    }
    async DescribeBlockStaticList(req, cb) {
        return this.request("DescribeBlockStaticList", req, cb);
    }
    async DescribeIdsWhiteRule(req, cb) {
        return this.request("DescribeIdsWhiteRule", req, cb);
    }
}
