import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cme.tencentcloudapi.com", "2019-10-29", clientConfig);
    }
    async DescribeTasks(req, cb) {
        return this.request("DescribeTasks", req, cb);
    }
    async DescribeTeams(req, cb) {
        return this.request("DescribeTeams", req, cb);
    }
    async DescribeVideoEncodingPresets(req, cb) {
        return this.request("DescribeVideoEncodingPresets", req, cb);
    }
    async ExportVideoEditProject(req, cb) {
        return this.request("ExportVideoEditProject", req, cb);
    }
    async DescribeSharedSpace(req, cb) {
        return this.request("DescribeSharedSpace", req, cb);
    }
    async GenerateVideoSegmentationSchemeByAi(req, cb) {
        return this.request("GenerateVideoSegmentationSchemeByAi", req, cb);
    }
    async ExportVideoByVideoSegmentationData(req, cb) {
        return this.request("ExportVideoByVideoSegmentationData", req, cb);
    }
    async GrantResourceAuthorization(req, cb) {
        return this.request("GrantResourceAuthorization", req, cb);
    }
    async SearchMaterial(req, cb) {
        return this.request("SearchMaterial", req, cb);
    }
    async ParseEvent(req, cb) {
        return this.request("ParseEvent", req, cb);
    }
    async DescribeJoinTeams(req, cb) {
        return this.request("DescribeJoinTeams", req, cb);
    }
    async DescribeResourceAuthorization(req, cb) {
        return this.request("DescribeResourceAuthorization", req, cb);
    }
    async ImportMaterial(req, cb) {
        return this.request("ImportMaterial", req, cb);
    }
    async ExportVideoByEditorTrackData(req, cb) {
        return this.request("ExportVideoByEditorTrackData", req, cb);
    }
    async DescribeClass(req, cb) {
        return this.request("DescribeClass", req, cb);
    }
    async DescribeTaskDetail(req, cb) {
        return this.request("DescribeTaskDetail", req, cb);
    }
    async CreateVideoEncodingPreset(req, cb) {
        return this.request("CreateVideoEncodingPreset", req, cb);
    }
    async ModifyTeam(req, cb) {
        return this.request("ModifyTeam", req, cb);
    }
    async DeleteMaterial(req, cb) {
        return this.request("DeleteMaterial", req, cb);
    }
    async ModifyMaterial(req, cb) {
        return this.request("ModifyMaterial", req, cb);
    }
    async DeleteTeam(req, cb) {
        return this.request("DeleteTeam", req, cb);
    }
    async AddTeamMember(req, cb) {
        return this.request("AddTeamMember", req, cb);
    }
    async MoveClass(req, cb) {
        return this.request("MoveClass", req, cb);
    }
    async CreateLink(req, cb) {
        return this.request("CreateLink", req, cb);
    }
    async ModifyTeamMember(req, cb) {
        return this.request("ModifyTeamMember", req, cb);
    }
    async ModifyVideoEncodingPreset(req, cb) {
        return this.request("ModifyVideoEncodingPreset", req, cb);
    }
    async DeleteVideoEncodingPreset(req, cb) {
        return this.request("DeleteVideoEncodingPreset", req, cb);
    }
    async DeleteTeamMembers(req, cb) {
        return this.request("DeleteTeamMembers", req, cb);
    }
    async DeleteLoginStatus(req, cb) {
        return this.request("DeleteLoginStatus", req, cb);
    }
    async DescribeProjects(req, cb) {
        return this.request("DescribeProjects", req, cb);
    }
    async DescribeLoginStatus(req, cb) {
        return this.request("DescribeLoginStatus", req, cb);
    }
    async HandleMediaCastProject(req, cb) {
        return this.request("HandleMediaCastProject", req, cb);
    }
    async DescribeMaterials(req, cb) {
        return this.request("DescribeMaterials", req, cb);
    }
    async HandleStreamConnectProject(req, cb) {
        return this.request("HandleStreamConnectProject", req, cb);
    }
    async DescribeAccounts(req, cb) {
        return this.request("DescribeAccounts", req, cb);
    }
    async DescribePlatforms(req, cb) {
        return this.request("DescribePlatforms", req, cb);
    }
    async CreateTeam(req, cb) {
        return this.request("CreateTeam", req, cb);
    }
    async ModifyProject(req, cb) {
        return this.request("ModifyProject", req, cb);
    }
    async CreateClass(req, cb) {
        return this.request("CreateClass", req, cb);
    }
    async RevokeResourceAuthorization(req, cb) {
        return this.request("RevokeResourceAuthorization", req, cb);
    }
    async ImportMediaToProject(req, cb) {
        return this.request("ImportMediaToProject", req, cb);
    }
    async ListMedia(req, cb) {
        return this.request("ListMedia", req, cb);
    }
    async CreateProject(req, cb) {
        return this.request("CreateProject", req, cb);
    }
    async ExportVideoByTemplate(req, cb) {
        return this.request("ExportVideoByTemplate", req, cb);
    }
    async MoveResource(req, cb) {
        return this.request("MoveResource", req, cb);
    }
    async DeleteClass(req, cb) {
        return this.request("DeleteClass", req, cb);
    }
    async DeleteProject(req, cb) {
        return this.request("DeleteProject", req, cb);
    }
    async FlattenListMedia(req, cb) {
        return this.request("FlattenListMedia", req, cb);
    }
    async CopyProject(req, cb) {
        return this.request("CopyProject", req, cb);
    }
    async DescribeTeamMembers(req, cb) {
        return this.request("DescribeTeamMembers", req, cb);
    }
}
