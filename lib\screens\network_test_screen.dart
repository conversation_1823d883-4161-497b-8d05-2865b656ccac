import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/utils/network_test.dart';
import 'package:novel_app/controllers/api_config_controller.dart';

class NetworkTestScreen extends StatefulWidget {
  const NetworkTestScreen({Key? key}) : super(key: key);

  @override
  State<NetworkTestScreen> createState() => _NetworkTestScreenState();
}

class _NetworkTestScreenState extends State<NetworkTestScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _testResults;
  final _apiKeyController = TextEditingController();
  bool _isApiKeyVisible = false;

  @override
  void initState() {
    super.initState();
    // 尝试从当前配置中获取API密钥
    try {
      final controller = Get.find<ApiConfigController>();
      final currentModel = controller.getCurrentModel();
      if (currentModel.apiFormat == 'Google API' &&
          currentModel.apiKey.isNotEmpty) {
        _apiKeyController.text = currentModel.apiKey;
      }
    } catch (e) {
      // 如果获取失败，保持空白
    }
  }

  @override
  void dispose() {
    _apiKeyController.dispose();
    super.dispose();
  }

  Future<void> _runNetworkTest() async {
    setState(() {
      _isLoading = true;
      _testResults = null;
    });

    try {
      // 运行网络环境测试
      final envResults = await NetworkTest.testNetworkEnvironment();

      // 如果有API密钥，测试Google API
      Map<String, dynamic>? googleApiResults;
      if (_apiKeyController.text.isNotEmpty) {
        googleApiResults = await NetworkTest.testGoogleApiConnection(
          apiKey: _apiKeyController.text,
          timeout: const Duration(seconds: 30),
        );
      }

      setState(() {
        _testResults = {
          'environment': envResults,
          if (googleApiResults != null) 'googleApi': googleApiResults,
        };
      });

      // 打印详细报告到控制台
      NetworkTest.printNetworkReport(_testResults!);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('测试失败: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildTestResultCard(String title, Map<String, dynamic> result) {
    final isSuccess = result['success'] == true;
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: Icon(
          isSuccess ? Icons.check_circle : Icons.error,
          color: isSuccess ? Colors.green : Colors.red,
        ),
        title: Text(title),
        subtitle: Text(result['message'] ?? '无消息'),
        trailing: result.containsKey('responseTime')
            ? Text(result['responseTime'].toString())
            : null,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('网络连接测试'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '网络连接诊断',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '此工具将测试您的网络连接，包括Google API的可达性。'
                      '如果您有Google API密钥，请在下方输入以进行完整测试。',
                    ),
                    const SizedBox(height: 16),
                    TextField(
                      controller: _apiKeyController,
                      obscureText: !_isApiKeyVisible,
                      decoration: InputDecoration(
                        labelText: 'Google API密钥（可选）',
                        hintText: '输入您的Google API密钥进行完整测试',
                        border: const OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(
                            _isApiKeyVisible
                                ? Icons.visibility
                                : Icons.visibility_off,
                          ),
                          onPressed: () {
                            setState(() {
                              _isApiKeyVisible = !_isApiKeyVisible;
                            });
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _runNetworkTest,
                        child: _isLoading
                            ? const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                        strokeWidth: 2),
                                  ),
                                  SizedBox(width: 8),
                                  Text('测试中...'),
                                ],
                              )
                            : const Text('开始测试'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            if (_testResults != null) ...[
              const Text(
                '测试结果',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Expanded(
                child: ListView(
                  children: [
                    if (_testResults!.containsKey('environment')) ...[
                      const Text(
                        '网络环境测试',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ..._buildEnvironmentResults(_testResults!['environment']),
                      const SizedBox(height: 16),
                    ],
                    if (_testResults!.containsKey('googleApi')) ...[
                      const Text(
                        'Google API测试',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      ..._buildGoogleApiResults(_testResults!['googleApi']),
                    ],
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildEnvironmentResults(Map<String, dynamic> envResults) {
    final widgets = <Widget>[];

    if (envResults.containsKey('google')) {
      widgets.add(_buildTestResultCard('Google连接', envResults['google']));
    }

    if (envResults.containsKey('googleApi')) {
      widgets
          .add(_buildTestResultCard('Google API域名', envResults['googleApi']));
    }

    if (envResults.containsKey('dnsResolution')) {
      widgets.add(_buildTestResultCard('DNS解析', envResults['dnsResolution']));
    }

    if (envResults.containsKey('systemProxy')) {
      final proxy = envResults['systemProxy'];
      widgets.add(Card(
        margin: const EdgeInsets.symmetric(vertical: 4),
        child: ListTile(
          leading: Icon(
            proxy['hasProxy'] ? Icons.vpn_lock : Icons.vpn_lock_outlined,
            color: proxy['hasProxy'] ? Colors.blue : Colors.grey,
          ),
          title: const Text('系统代理'),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(proxy['message'] ?? '无信息'),
              if (proxy['detectedProxies'] != null &&
                  proxy['detectedProxies'].isNotEmpty) ...[
                const SizedBox(height: 4),
                const Text('检测到的代理:',
                    style:
                        TextStyle(fontWeight: FontWeight.bold, fontSize: 12)),
                ...proxy['detectedProxies'].map<Widget>((proxyInfo) =>
                    Text('• $proxyInfo', style: const TextStyle(fontSize: 11))),
              ],
              if (proxy['workingProxies'] != null &&
                  proxy['workingProxies'].isNotEmpty) ...[
                const SizedBox(height: 4),
                const Text('可用代理:',
                    style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                        color: Colors.green)),
                ...proxy['workingProxies'].map<Widget>((proxyInfo) => Text(
                    '• $proxyInfo',
                    style: const TextStyle(fontSize: 11, color: Colors.green))),
              ],
            ],
          ),
          isThreeLine: proxy['detectedProxies'] != null &&
              proxy['detectedProxies'].isNotEmpty,
        ),
      ));
    }

    return widgets;
  }

  List<Widget> _buildGoogleApiResults(Map<String, dynamic> googleApiResults) {
    final widgets = <Widget>[];

    if (googleApiResults.containsKey('basicConnection')) {
      widgets.add(
          _buildTestResultCard('基本连接', googleApiResults['basicConnection']));
    }

    if (googleApiResults.containsKey('apiCall')) {
      final apiCall = googleApiResults['apiCall'];
      widgets.add(Card(
        margin: const EdgeInsets.symmetric(vertical: 4),
        child: ListTile(
          leading: Icon(
            apiCall['success'] ? Icons.check_circle : Icons.error,
            color: apiCall['success'] ? Colors.green : Colors.red,
          ),
          title: const Text('API调用测试'),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(apiCall['message'] ?? '无消息'),
              if (apiCall['success'] && apiCall['generatedText'] != null)
                Text(
                  '生成内容: ${apiCall['generatedText']}',
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
            ],
          ),
          isThreeLine: apiCall['success'] && apiCall['generatedText'] != null,
        ),
      ));
    }

    return widgets;
  }
}
