import { AbstractClient } from "../../../common/abstract_client";
import { ClientConfig } from "../../../common/interface";
import { ResetDeviceAccountPasswordResponse, DescribeOperationTaskResponse, ModifyResourceResponse, BindDeviceAccountPasswordRequest, DeleteAclsRequest, ModifyOperationTaskResponse, SearchSessionRequest, ModifyOperationTaskRequest, DescribeDeviceGroupsResponse, DeleteCmdTemplatesResponse, DeleteDevicesResponse, CreateChangePwdTaskResponse, DescribeLoginEventRequest, BindDeviceAccountPasswordResponse, DescribeChangePwdTaskDetailRequest, ResetDeviceAccountPasswordRequest, DescribeAccessWhiteListRulesResponse, AddUserGroupMembersRequest, CreateResourceRequest, DescribeDevicesResponse, ResetUserResponse, CreateAccessWhiteListRuleResponse, DeleteUserGroupMembersRequest, RunChangePwdTaskRequest, DescribeChangePwdTaskDetailResponse, DeleteUserGroupsResponse, ModifyDeviceRequest, SearchFileRequest, CreateAccessWhiteListRuleRequest, SearchCommandRequest, ImportExternalDeviceRequest, DescribeResourcesRequest, BindDeviceResourceResponse, ResetDeviceAccountPrivateKeyResponse, SetLDAPSyncFlagResponse, DeleteUserGroupMembersResponse, DescribeLDAPUnitSetRequest, SearchSubtaskResultByIdResponse, ModifyOAuthSettingRequest, CreateUserGroupRequest, ReplaySessionRequest, BindDeviceAccountPrivateKeyResponse, SearchCommandBySidRequest, CreateDeviceGroupResponse, DescribeUserGroupMembersRequest, AccessDevicesRequest, DescribeAccessWhiteListRulesRequest, ModifyUserGroupResponse, CreateUserResponse, DeleteOperationTasksRequest, DescribeAssetSyncStatusResponse, ModifyChangePwdTaskResponse, DescribeUserGroupsRequest, CreateAclRequest, DescribeLoginEventResponse, SearchSubtaskResultByIdRequest, CreateDeviceGroupRequest, RunOperationTaskResponse, DeleteDeviceGroupMembersRequest, SearchCommandResponse, DescribeChangePwdTaskResponse, CreateOperationTaskResponse, CreateAclResponse, DescribeAclsResponse, ModifyUserRequest, CreateCmdTemplateResponse, DeleteOperationTasksResponse, ModifyAclRequest, SearchAuditLogRequest, CreateResourceResponse, ModifyResourceRequest, DeleteUsersResponse, SearchCommandBySidResponse, DescribeResourcesResponse, DescribeUsersRequest, DeployResourceRequest, SearchSessionResponse, CreateCmdTemplateRequest, ModifyCmdTemplateResponse, ResetDeviceAccountPrivateKeyRequest, DescribeDeviceGroupsRequest, CreateAssetSyncJobResponse, BindDeviceAccountPrivateKeyRequest, ModifyLDAPSettingRequest, ModifyChangePwdTaskRequest, CreateAssetSyncJobRequest, UnlockUserResponse, DescribeAssetSyncStatusRequest, AccessDevicesResponse, DeleteUsersRequest, DeleteDeviceAccountsRequest, SearchTaskResultRequest, DeleteDeviceGroupMembersResponse, ModifyDeviceGroupResponse, ModifyOAuthSettingResponse, DescribeOperationTaskRequest, DescribeLDAPUnitSetResponse, DescribeUserGroupMembersResponse, ImportExternalDeviceResponse, SearchSessionCommandResponse, DeleteAccessWhiteListRulesRequest, CheckLDAPConnectionRequest, ResetUserRequest, DeleteUserGroupsRequest, CreateDeviceAccountResponse, ModifyDeviceGroupRequest, DescribeUsersResponse, DeployResourceResponse, CreateUserRequest, DescribeChangePwdTaskRequest, SearchFileBySidResponse, DescribeOperationEventRequest, SetLDAPSyncFlagRequest, ModifyDeviceResponse, ModifyUserResponse, ModifyCmdTemplateRequest, CreateOperationTaskRequest, DescribeCmdTemplatesResponse, UnlockUserRequest, CreateDeviceAccountRequest, AddDeviceGroupMembersResponse, DeleteCmdTemplatesRequest, DescribeUserGroupsResponse, AddUserGroupMembersResponse, DescribeDeviceAccountsRequest, DeleteChangePwdTaskRequest, ModifyUserGroupRequest, CreateChangePwdTaskRequest, SearchTaskResultResponse, DeleteChangePwdTaskResponse, CreateUserGroupResponse, DeleteDevicesRequest, DescribeDeviceAccountsResponse, DescribeDomainsRequest, DescribeCmdTemplatesRequest, ModifyAclResponse, DescribeAclsRequest, DeleteDeviceGroupsRequest, DescribeDeviceGroupMembersResponse, SearchAuditLogResponse, RunChangePwdTaskResponse, BindDeviceResourceRequest, CheckLDAPConnectionResponse, RunOperationTaskRequest, DescribeDevicesRequest, SearchSessionCommandRequest, SearchFileBySidRequest, DescribeOperationEventResponse, DescribeDomainsResponse, ModifyLDAPSettingResponse, ReplaySessionResponse, DeleteAclsResponse, DescribeDeviceGroupMembersRequest, DeleteDeviceGroupsResponse, AddDeviceGroupMembersRequest, DeleteDeviceAccountsResponse, DeleteAccessWhiteListRulesResponse, SearchFileResponse } from "./bh_models";
/**
 * bh client
 * @class
 */
export declare class Client extends AbstractClient {
    constructor(clientConfig: ClientConfig);
    /**
     * 查询操作日志
     */
    DescribeOperationEvent(req: DescribeOperationEventRequest, cb?: (error: string, rep: DescribeOperationEventResponse) => void): Promise<DescribeOperationEventResponse>;
    /**
     * 查询资产同步状态
     */
    DescribeAssetSyncStatus(req: DescribeAssetSyncStatusRequest, cb?: (error: string, rep: DescribeAssetSyncStatusResponse) => void): Promise<DescribeAssetSyncStatusResponse>;
    /**
     * 设置OAuth认证参数
     */
    ModifyOAuthSetting(req: ModifyOAuthSettingRequest, cb?: (error: string, rep: ModifyOAuthSettingResponse) => void): Promise<ModifyOAuthSettingResponse>;
    /**
     * 删除访问白名单规则
     */
    DeleteAccessWhiteListRules(req: DeleteAccessWhiteListRulesRequest, cb?: (error: string, rep: DeleteAccessWhiteListRulesResponse) => void): Promise<DeleteAccessWhiteListRulesResponse>;
    /**
     * 查询登录日志
     */
    DescribeLoginEvent(req: DescribeLoginEventRequest, cb?: (error: string, rep: DescribeLoginEventResponse) => void): Promise<DescribeLoginEventResponse>;
    /**
     * 新建资产组
     */
    CreateDeviceGroup(req: CreateDeviceGroupRequest, cb?: (error: string, rep: CreateDeviceGroupResponse) => void): Promise<CreateDeviceGroupResponse>;
    /**
     * 查询主机账号列表
     */
    DescribeDeviceAccounts(req: DescribeDeviceAccountsRequest, cb?: (error: string, rep: DescribeDeviceAccountsResponse) => void): Promise<DescribeDeviceAccountsResponse>;
    /**
     * 测试LDAP连接
     */
    CheckLDAPConnection(req: CheckLDAPConnectionRequest, cb?: (error: string, rep: CheckLDAPConnectionResponse) => void): Promise<CheckLDAPConnectionResponse>;
    /**
     * 重置用户
     */
    ResetUser(req: ResetUserRequest, cb?: (error: string, rep: ResetUserResponse) => void): Promise<ResetUserResponse>;
    /**
     * 创建运维任务
     */
    CreateOperationTask(req: CreateOperationTaskRequest, cb?: (error: string, rep: CreateOperationTaskResponse) => void): Promise<CreateOperationTaskResponse>;
    /**
     * 创建修改密码任务
     */
    CreateChangePwdTask(req: CreateChangePwdTaskRequest, cb?: (error: string, rep: CreateChangePwdTaskResponse) => void): Promise<CreateChangePwdTaskResponse>;
    /**
     * 设置LDAP 立即同步标记
     */
    SetLDAPSyncFlag(req?: SetLDAPSyncFlagRequest, cb?: (error: string, rep: SetLDAPSyncFlagResponse) => void): Promise<SetLDAPSyncFlagResponse>;
    /**
     * 查询用户组列表
     */
    DescribeUserGroups(req: DescribeUserGroupsRequest, cb?: (error: string, rep: DescribeUserGroupsResponse) => void): Promise<DescribeUserGroupsResponse>;
    /**
     * 查询网络域
     */
    DescribeDomains(req: DescribeDomainsRequest, cb?: (error: string, rep: DescribeDomainsResponse) => void): Promise<DescribeDomainsResponse>;
    /**
     * 开通服务，初始化资源，只针对新购资源
     */
    DeployResource(req: DeployResourceRequest, cb?: (error: string, rep: DeployResourceResponse) => void): Promise<DeployResourceResponse>;
    /**
     * 修改资产信息
     */
    ModifyDevice(req: ModifyDeviceRequest, cb?: (error: string, rep: ModifyDeviceResponse) => void): Promise<ModifyDeviceResponse>;
    /**
     * 修改资产绑定的堡垒机服务
     */
    BindDeviceResource(req: BindDeviceResourceRequest, cb?: (error: string, rep: BindDeviceResourceResponse) => void): Promise<BindDeviceResourceResponse>;
    /**
     * 绑定主机账号密码
     */
    BindDeviceAccountPassword(req: BindDeviceAccountPasswordRequest, cb?: (error: string, rep: BindDeviceAccountPasswordResponse) => void): Promise<BindDeviceAccountPasswordResponse>;
    /**
     * 获取LDAP ou 列表
     */
    DescribeLDAPUnitSet(req: DescribeLDAPUnitSetRequest, cb?: (error: string, rep: DescribeLDAPUnitSetResponse) => void): Promise<DescribeLDAPUnitSetResponse>;
    /**
     * 修改高危命令模板
     */
    ModifyCmdTemplate(req: ModifyCmdTemplateRequest, cb?: (error: string, rep: ModifyCmdTemplateResponse) => void): Promise<ModifyCmdTemplateResponse>;
    /**
     * 文件传输检索
     */
    SearchFile(req: SearchFileRequest, cb?: (error: string, rep: SearchFileResponse) => void): Promise<SearchFileResponse>;
    /**
     * 搜索会话
     */
    SearchSession(req: SearchSessionRequest, cb?: (error: string, rep: SearchSessionResponse) => void): Promise<SearchSessionResponse>;
    /**
     * 删除访问权限
     */
    DeleteAcls(req: DeleteAclsRequest, cb?: (error: string, rep: DeleteAclsResponse) => void): Promise<DeleteAclsResponse>;
    /**
     * 外部客户访问资产
     */
    AccessDevices(req: AccessDevicesRequest, cb?: (error: string, rep: AccessDevicesResponse) => void): Promise<AccessDevicesResponse>;
    /**
     * 查询用户购买的堡垒机服务信息，包括资源ID、授权点数、VPC、过期时间等。
     */
    DescribeResources(req: DescribeResourcesRequest, cb?: (error: string, rep: DescribeResourcesResponse) => void): Promise<DescribeResourcesResponse>;
    /**
     * 删除用户组成员
     */
    DeleteUserGroupMembers(req: DeleteUserGroupMembersRequest, cb?: (error: string, rep: DeleteUserGroupMembersResponse) => void): Promise<DeleteUserGroupMembersResponse>;
    /**
     * 更新修改密码任务
     */
    ModifyChangePwdTask(req: ModifyChangePwdTaskRequest, cb?: (error: string, rep: ModifyChangePwdTaskResponse) => void): Promise<ModifyChangePwdTaskResponse>;
    /**
     * 创建手工资产同步任务
     */
    CreateAssetSyncJob(req: CreateAssetSyncJobRequest, cb?: (error: string, rep: CreateAssetSyncJobResponse) => void): Promise<CreateAssetSyncJobResponse>;
    /**
     * 查询访问权限列表
     */
    DescribeAcls(req: DescribeAclsRequest, cb?: (error: string, rep: DescribeAclsResponse) => void): Promise<DescribeAclsResponse>;
    /**
     * 执行改密任务
     */
    RunChangePwdTask(req: RunChangePwdTaskRequest, cb?: (error: string, rep: RunChangePwdTaskResponse) => void): Promise<RunChangePwdTaskResponse>;
    /**
     * 清除设备账号绑定密码
     */
    ResetDeviceAccountPassword(req: ResetDeviceAccountPasswordRequest, cb?: (error: string, rep: ResetDeviceAccountPasswordResponse) => void): Promise<ResetDeviceAccountPasswordResponse>;
    /**
     * 修改用户组
     */
    ModifyUserGroup(req: ModifyUserGroupRequest, cb?: (error: string, rep: ModifyUserGroupResponse) => void): Promise<ModifyUserGroupResponse>;
    /**
     * 删除资产组
     */
    DeleteDeviceGroups(req: DeleteDeviceGroupsRequest, cb?: (error: string, rep: DeleteDeviceGroupsResponse) => void): Promise<DeleteDeviceGroupsResponse>;
    /**
     * 删除运维任务
     */
    DeleteOperationTasks(req: DeleteOperationTasksRequest, cb?: (error: string, rep: DeleteOperationTasksResponse) => void): Promise<DeleteOperationTasksResponse>;
    /**
     * 修改访问权限
     */
    ModifyAcl(req: ModifyAclRequest, cb?: (error: string, rep: ModifyAclResponse) => void): Promise<ModifyAclResponse>;
    /**
     * 删除用户
     */
    DeleteUsers(req: DeleteUsersRequest, cb?: (error: string, rep: DeleteUsersResponse) => void): Promise<DeleteUsersResponse>;
    /**
     * 新建访问权限
     */
    CreateAcl(req: CreateAclRequest, cb?: (error: string, rep: CreateAclResponse) => void): Promise<CreateAclResponse>;
    /**
     * 根据会话Id搜索Command
     */
    SearchCommandBySid(req: SearchCommandBySidRequest, cb?: (error: string, rep: SearchCommandBySidResponse) => void): Promise<SearchCommandBySidResponse>;
    /**
     * 解锁用户
     */
    UnlockUser(req: UnlockUserRequest, cb?: (error: string, rep: UnlockUserResponse) => void): Promise<UnlockUserResponse>;
    /**
     * 搜索文件传输会话下文件操作列表
     */
    SearchFileBySid(req: SearchFileBySidRequest, cb?: (error: string, rep: SearchFileBySidResponse) => void): Promise<SearchFileBySidResponse>;
    /**
     * 清除设备账号绑定的密钥
     */
    ResetDeviceAccountPrivateKey(req: ResetDeviceAccountPrivateKeyRequest, cb?: (error: string, rep: ResetDeviceAccountPrivateKeyResponse) => void): Promise<ResetDeviceAccountPrivateKeyResponse>;
    /**
     * 添加访问白名单规则
     */
    CreateAccessWhiteListRule(req: CreateAccessWhiteListRuleRequest, cb?: (error: string, rep: CreateAccessWhiteListRuleResponse) => void): Promise<CreateAccessWhiteListRuleResponse>;
    /**
     * 新建主机账号
     */
    CreateDeviceAccount(req: CreateDeviceAccountRequest, cb?: (error: string, rep: CreateDeviceAccountResponse) => void): Promise<CreateDeviceAccountResponse>;
    /**
     * 查询访问白名单规则列表
     */
    DescribeAccessWhiteListRules(req: DescribeAccessWhiteListRulesRequest, cb?: (error: string, rep: DescribeAccessWhiteListRulesResponse) => void): Promise<DescribeAccessWhiteListRulesResponse>;
    /**
     * 查询改密任务详情
     */
    DescribeChangePwdTaskDetail(req: DescribeChangePwdTaskDetailRequest, cb?: (error: string, rep: DescribeChangePwdTaskDetailResponse) => void): Promise<DescribeChangePwdTaskDetailResponse>;
    /**
     * 删除资产组成员
     */
    DeleteDeviceGroupMembers(req: DeleteDeviceGroupMembersRequest, cb?: (error: string, rep: DeleteDeviceGroupMembersResponse) => void): Promise<DeleteDeviceGroupMembersResponse>;
    /**
     * 删除改密任务
     */
    DeleteChangePwdTask(req: DeleteChangePwdTaskRequest, cb?: (error: string, rep: DeleteChangePwdTaskResponse) => void): Promise<DeleteChangePwdTaskResponse>;
    /**
     * 删除主机
     */
    DeleteDevices(req: DeleteDevicesRequest, cb?: (error: string, rep: DeleteDevicesResponse) => void): Promise<DeleteDevicesResponse>;
    /**
     * 修改运维任务
     */
    ModifyOperationTask(req: ModifyOperationTaskRequest, cb?: (error: string, rep: ModifyOperationTaskResponse) => void): Promise<ModifyOperationTaskResponse>;
    /**
     * 修改LDAP配置信息
     */
    ModifyLDAPSetting(req: ModifyLDAPSettingRequest, cb?: (error: string, rep: ModifyLDAPSettingResponse) => void): Promise<ModifyLDAPSettingResponse>;
    /**
     * 删除主机账号
     */
    DeleteDeviceAccounts(req: DeleteDeviceAccountsRequest, cb?: (error: string, rep: DeleteDeviceAccountsResponse) => void): Promise<DeleteDeviceAccountsResponse>;
    /**
     * 删除用户组
     */
    DeleteUserGroups(req: DeleteUserGroupsRequest, cb?: (error: string, rep: DeleteUserGroupsResponse) => void): Promise<DeleteUserGroupsResponse>;
    /**
     * 执行运维任务
     */
    RunOperationTask(req: RunOperationTaskRequest, cb?: (error: string, rep: RunOperationTaskResponse) => void): Promise<RunOperationTaskResponse>;
    /**
     * 新建高危命令模板
     */
    CreateCmdTemplate(req: CreateCmdTemplateRequest, cb?: (error: string, rep: CreateCmdTemplateResponse) => void): Promise<CreateCmdTemplateResponse>;
    /**
     * 资源变配
     */
    ModifyResource(req: ModifyResourceRequest, cb?: (error: string, rep: ModifyResourceResponse) => void): Promise<ModifyResourceResponse>;
    /**
     * 搜索审计日志
     */
    SearchAuditLog(req: SearchAuditLogRequest, cb?: (error: string, rep: SearchAuditLogResponse) => void): Promise<SearchAuditLogResponse>;
    /**
     * 获取运维任务列表
     */
    DescribeOperationTask(req: DescribeOperationTaskRequest, cb?: (error: string, rep: DescribeOperationTaskResponse) => void): Promise<DescribeOperationTaskResponse>;
    /**
     * 修改用户信息
     */
    ModifyUser(req: ModifyUserRequest, cb?: (error: string, rep: ModifyUserResponse) => void): Promise<ModifyUserResponse>;
    /**
     * 查询命令模板列表
     */
    DescribeCmdTemplates(req: DescribeCmdTemplatesRequest, cb?: (error: string, rep: DescribeCmdTemplatesResponse) => void): Promise<DescribeCmdTemplatesResponse>;
    /**
     * 删除高危命令模板
     */
    DeleteCmdTemplates(req: DeleteCmdTemplatesRequest, cb?: (error: string, rep: DeleteCmdTemplatesResponse) => void): Promise<DeleteCmdTemplatesResponse>;
    /**
     * 添加用户组成员
     */
    AddUserGroupMembers(req: AddUserGroupMembersRequest, cb?: (error: string, rep: AddUserGroupMembersResponse) => void): Promise<AddUserGroupMembersResponse>;
    /**
     * 查询资产组列表
     */
    DescribeDeviceGroups(req: DescribeDeviceGroupsRequest, cb?: (error: string, rep: DescribeDeviceGroupsResponse) => void): Promise<DescribeDeviceGroupsResponse>;
    /**
     * 查询用户列表
     */
    DescribeUsers(req: DescribeUsersRequest, cb?: (error: string, rep: DescribeUsersResponse) => void): Promise<DescribeUsersResponse>;
    /**
     * 查询资产列表
     */
    DescribeDevices(req: DescribeDevicesRequest, cb?: (error: string, rep: DescribeDevicesResponse) => void): Promise<DescribeDevicesResponse>;
    /**
     * 查询用户组成员列表
     */
    DescribeUserGroupMembers(req: DescribeUserGroupMembersRequest, cb?: (error: string, rep: DescribeUserGroupMembersResponse) => void): Promise<DescribeUserGroupMembersResponse>;
    /**
     * 会话回放
     */
    ReplaySession(req: ReplaySessionRequest, cb?: (error: string, rep: ReplaySessionResponse) => void): Promise<ReplaySessionResponse>;
    /**
     * 查询运维子任务执行结果
     */
    SearchSubtaskResultById(req: SearchSubtaskResultByIdRequest, cb?: (error: string, rep: SearchSubtaskResultByIdResponse) => void): Promise<SearchSubtaskResultByIdResponse>;
    /**
     * 查询资产组成员列表
     */
    DescribeDeviceGroupMembers(req: DescribeDeviceGroupMembersRequest, cb?: (error: string, rep: DescribeDeviceGroupMembersResponse) => void): Promise<DescribeDeviceGroupMembersResponse>;
    /**
     * 导入外部资产信息
     */
    ImportExternalDevice(req: ImportExternalDeviceRequest, cb?: (error: string, rep: ImportExternalDeviceResponse) => void): Promise<ImportExternalDeviceResponse>;
    /**
     * 查询改密任务列表
     */
    DescribeChangePwdTask(req: DescribeChangePwdTaskRequest, cb?: (error: string, rep: DescribeChangePwdTaskResponse) => void): Promise<DescribeChangePwdTaskResponse>;
    /**
     * 添加资产组成员
     */
    AddDeviceGroupMembers(req: AddDeviceGroupMembersRequest, cb?: (error: string, rep: AddDeviceGroupMembersResponse) => void): Promise<AddDeviceGroupMembersResponse>;
    /**
     * 新建用户组
     */
    CreateUserGroup(req: CreateUserGroupRequest, cb?: (error: string, rep: CreateUserGroupResponse) => void): Promise<CreateUserGroupResponse>;
    /**
     * 创建堡垒机实例
     */
    CreateResource(req: CreateResourceRequest, cb?: (error: string, rep: CreateResourceResponse) => void): Promise<CreateResourceResponse>;
    /**
     * 新建用户
     */
    CreateUser(req: CreateUserRequest, cb?: (error: string, rep: CreateUserResponse) => void): Promise<CreateUserResponse>;
    /**
     * 搜索运维任务执行结果
     */
    SearchTaskResult(req: SearchTaskResultRequest, cb?: (error: string, rep: SearchTaskResultResponse) => void): Promise<SearchTaskResultResponse>;
    /**
     * 绑定主机账号私钥
     */
    BindDeviceAccountPrivateKey(req: BindDeviceAccountPrivateKeyRequest, cb?: (error: string, rep: BindDeviceAccountPrivateKeyResponse) => void): Promise<BindDeviceAccountPrivateKeyResponse>;
    /**
     * 修改资产组
     */
    ModifyDeviceGroup(req: ModifyDeviceGroupRequest, cb?: (error: string, rep: ModifyDeviceGroupResponse) => void): Promise<ModifyDeviceGroupResponse>;
    /**
     * 命令执行检索
     */
    SearchCommand(req: SearchCommandRequest, cb?: (error: string, rep: SearchCommandResponse) => void): Promise<SearchCommandResponse>;
    /**
     * 命令检索
     */
    SearchSessionCommand(req: SearchSessionCommandRequest, cb?: (error: string, rep: SearchSessionCommandResponse) => void): Promise<SearchSessionCommandResponse>;
}
