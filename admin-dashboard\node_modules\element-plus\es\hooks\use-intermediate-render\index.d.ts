import type { Ref } from 'vue';
export type UseDelayedRenderProps = {
    indicator: Ref<boolean>;
    intermediateIndicator: Ref<boolean>;
    shouldSetIntermediate?: (step: 'show' | 'hide') => boolean;
    beforeShow?: () => void;
    beforeHide?: () => void;
    afterShow?: () => void;
    afterHide?: () => void;
};
export declare const useDelayedRender: ({ indicator, intermediateIndicator, shouldSetIntermediate, beforeShow, afterShow, afterHide, beforeHide, }: UseDelayedRenderProps) => void;
