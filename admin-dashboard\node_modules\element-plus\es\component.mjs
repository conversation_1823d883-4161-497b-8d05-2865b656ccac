import { ElAffix } from './components/affix/index.mjs';
import { El<PERSON>lert } from './components/alert/index.mjs';
import { ElAutocomplete } from './components/autocomplete/index.mjs';
import { ElAvatar } from './components/avatar/index.mjs';
import { ElBacktop } from './components/backtop/index.mjs';
import { ElBadge } from './components/badge/index.mjs';
import { ElBreadcrumb, ElBreadcrumbItem } from './components/breadcrumb/index.mjs';
import { ElButton, ElButtonGroup } from './components/button/index.mjs';
import { ElCalendar } from './components/calendar/index.mjs';
import { ElCard } from './components/card/index.mjs';
import { ElCarousel, ElCarouselItem } from './components/carousel/index.mjs';
import { ElCascader } from './components/cascader/index.mjs';
import { ElCascaderPanel } from './components/cascader-panel/index.mjs';
import { ElCheckTag } from './components/check-tag/index.mjs';
import { ElCheckbox, ElCheckboxButton, ElCheckboxGroup } from './components/checkbox/index.mjs';
import { ElCol } from './components/col/index.mjs';
import { ElCollapse, ElCollapseItem } from './components/collapse/index.mjs';
import { ElCollapseTransition } from './components/collapse-transition/index.mjs';
import { ElColorPicker } from './components/color-picker/index.mjs';
import { ElConfigProvider } from './components/config-provider/index.mjs';
import { ElContainer, ElAside, ElFooter, ElHeader, ElMain } from './components/container/index.mjs';
import { ElDatePicker } from './components/date-picker/index.mjs';
import { ElDescriptions, ElDescriptionsItem } from './components/descriptions/index.mjs';
import { ElDialog } from './components/dialog/index.mjs';
import { ElDivider } from './components/divider/index.mjs';
import { ElDrawer } from './components/drawer/index.mjs';
import { ElDropdown, ElDropdownItem, ElDropdownMenu } from './components/dropdown/index.mjs';
import { ElEmpty } from './components/empty/index.mjs';
import { ElForm, ElFormItem } from './components/form/index.mjs';
import { ElIcon } from './components/icon/index.mjs';
import { ElImage } from './components/image/index.mjs';
import { ElImageViewer } from './components/image-viewer/index.mjs';
import { ElInput } from './components/input/index.mjs';
import { ElInputNumber } from './components/input-number/index.mjs';
import { ElInputTag } from './components/input-tag/index.mjs';
import { ElLink } from './components/link/index.mjs';
import { ElMenu, ElMenuItem, ElMenuItemGroup, ElSubMenu } from './components/menu/index.mjs';
import { ElPageHeader } from './components/page-header/index.mjs';
import { ElPagination } from './components/pagination/index.mjs';
import { ElPopconfirm } from './components/popconfirm/index.mjs';
import { ElPopover } from './components/popover/index.mjs';
import { ElPopper } from './components/popper/index.mjs';
import { ElProgress } from './components/progress/index.mjs';
import { ElRadio, ElRadioButton, ElRadioGroup } from './components/radio/index.mjs';
import { ElRate } from './components/rate/index.mjs';
import { ElResult } from './components/result/index.mjs';
import { ElRow } from './components/row/index.mjs';
import { ElScrollbar } from './components/scrollbar/index.mjs';
import { ElSelect, ElOption, ElOptionGroup } from './components/select/index.mjs';
import { ElSelectV2 } from './components/select-v2/index.mjs';
import { ElSkeleton, ElSkeletonItem } from './components/skeleton/index.mjs';
import { ElSlider } from './components/slider/index.mjs';
import { ElSpace } from './components/space/index.mjs';
import { ElStatistic } from './components/statistic/index.mjs';
import { ElCountdown } from './components/countdown/index.mjs';
import { ElSteps, ElStep } from './components/steps/index.mjs';
import { ElSwitch } from './components/switch/index.mjs';
import { ElTable, ElTableColumn } from './components/table/index.mjs';
import { ElAutoResizer, ElTableV2 } from './components/table-v2/index.mjs';
import { ElTabs, ElTabPane } from './components/tabs/index.mjs';
import { ElTag } from './components/tag/index.mjs';
import { ElText } from './components/text/index.mjs';
import { ElTimePicker } from './components/time-picker/index.mjs';
import { ElTimeSelect } from './components/time-select/index.mjs';
import { ElTimeline, ElTimelineItem } from './components/timeline/index.mjs';
import { ElTooltip } from './components/tooltip/index.mjs';
import { ElTooltipV2 } from './components/tooltip-v2/index.mjs';
import { ElTransfer } from './components/transfer/index.mjs';
import { ElTree } from './components/tree/index.mjs';
import { ElTreeSelect } from './components/tree-select/index.mjs';
import { ElTreeV2 } from './components/tree-v2/index.mjs';
import { ElUpload } from './components/upload/index.mjs';
import { ElWatermark } from './components/watermark/index.mjs';
import { ElTour, ElTourStep } from './components/tour/index.mjs';
import { ElAnchor, ElAnchorLink } from './components/anchor/index.mjs';
import { ElSegmented } from './components/segmented/index.mjs';
import { ElMention } from './components/mention/index.mjs';
import { ElSplitter, ElSplitterPanel } from './components/splitter/index.mjs';

var Components = [
  ElAffix,
  ElAlert,
  ElAutocomplete,
  ElAutoResizer,
  ElAvatar,
  ElBacktop,
  ElBadge,
  ElBreadcrumb,
  ElBreadcrumbItem,
  ElButton,
  ElButtonGroup,
  ElCalendar,
  ElCard,
  ElCarousel,
  ElCarouselItem,
  ElCascader,
  ElCascaderPanel,
  ElCheckTag,
  ElCheckbox,
  ElCheckboxButton,
  ElCheckboxGroup,
  ElCol,
  ElCollapse,
  ElCollapseItem,
  ElCollapseTransition,
  ElColorPicker,
  ElConfigProvider,
  ElContainer,
  ElAside,
  ElFooter,
  ElHeader,
  ElMain,
  ElDatePicker,
  ElDescriptions,
  ElDescriptionsItem,
  ElDialog,
  ElDivider,
  ElDrawer,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElEmpty,
  ElForm,
  ElFormItem,
  ElIcon,
  ElImage,
  ElImageViewer,
  ElInput,
  ElInputNumber,
  ElInputTag,
  ElLink,
  ElMenu,
  ElMenuItem,
  ElMenuItemGroup,
  ElSubMenu,
  ElPageHeader,
  ElPagination,
  ElPopconfirm,
  ElPopover,
  ElPopper,
  ElProgress,
  ElRadio,
  ElRadioButton,
  ElRadioGroup,
  ElRate,
  ElResult,
  ElRow,
  ElScrollbar,
  ElSelect,
  ElOption,
  ElOptionGroup,
  ElSelectV2,
  ElSkeleton,
  ElSkeletonItem,
  ElSlider,
  ElSpace,
  ElStatistic,
  ElCountdown,
  ElSteps,
  ElStep,
  ElSwitch,
  ElTable,
  ElTableColumn,
  ElTableV2,
  ElTabs,
  ElTabPane,
  ElTag,
  ElText,
  ElTimePicker,
  ElTimeSelect,
  ElTimeline,
  ElTimelineItem,
  ElTooltip,
  ElTooltipV2,
  ElTransfer,
  ElTree,
  ElTreeSelect,
  ElTreeV2,
  ElUpload,
  ElWatermark,
  ElTour,
  ElTourStep,
  ElAnchor,
  ElAnchorLink,
  ElSegmented,
  ElMention,
  ElSplitter,
  ElSplitterPanel
];

export { Components as default };
//# sourceMappingURL=component.mjs.map
