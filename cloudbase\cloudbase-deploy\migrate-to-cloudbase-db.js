const fs = require('fs');
const path = require('path');
const tcb = require('@cloudbase/node-sdk');

// 配置信息
const ENV_ID = process.env.TCB_ENV || 'novel-app-2gywkgnn15cbd6a8';
const DB_FILE = path.join(__dirname, 'novel-app-api', 'db.json');

// 初始化CloudBase
const app = tcb.init({
  env: ENV_ID
});

const db = app.database();

// 颜色输出
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 加载本地数据
function loadLocalData() {
  try {
    const data = fs.readFileSync(DB_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    log(`加载本地数据失败: ${error.message}`, 'red');
    return null;
  }
}

// 迁移用户数据
async function migrateUsers(users) {
  log('开始迁移用户数据...', 'blue');
  
  const collection = db.collection('users');
  let successCount = 0;
  let errorCount = 0;

  for (const user of users) {
    try {
      // 检查用户是否已存在
      const existingUser = await collection.where({
        username: user.username
      }).get();

      if (existingUser.data.length === 0) {
        // 添加时间戳字段
        const userData = {
          ...user,
          createdAt: user.createdAt || new Date().toISOString(),
          updatedAt: user.updatedAt || new Date().toISOString()
        };

        await collection.add(userData);
        successCount++;
        log(`✓ 用户 ${user.username} 迁移成功`, 'green');
      } else {
        log(`- 用户 ${user.username} 已存在，跳过`, 'yellow');
      }
    } catch (error) {
      errorCount++;
      log(`✗ 用户 ${user.username} 迁移失败: ${error.message}`, 'red');
    }
  }

  log(`用户数据迁移完成: 成功 ${successCount}, 失败 ${errorCount}`, 'blue');
}

// 迁移小说数据
async function migrateNovels(novels) {
  log('开始迁移小说数据...', 'blue');
  
  const collection = db.collection('novels');
  let successCount = 0;
  let errorCount = 0;

  for (const novel of novels) {
    try {
      // 检查小说是否已存在
      const existingNovel = await collection.where({
        id: novel.id
      }).get();

      if (existingNovel.data.length === 0) {
        // 添加时间戳和索引字段
        const novelData = {
          ...novel,
          createdAt: novel.createdAt || new Date().toISOString(),
          updatedAt: novel.updatedAt || new Date().toISOString(),
          wordCount: novel.wordCount || 0,
          chapterCount: novel.chapters ? novel.chapters.length : 0
        };

        await collection.add(novelData);
        successCount++;
        log(`✓ 小说 ${novel.title} 迁移成功`, 'green');
      } else {
        log(`- 小说 ${novel.title} 已存在，跳过`, 'yellow');
      }
    } catch (error) {
      errorCount++;
      log(`✗ 小说 ${novel.title} 迁移失败: ${error.message}`, 'red');
    }
  }

  log(`小说数据迁移完成: 成功 ${successCount}, 失败 ${errorCount}`, 'blue');
}

// 迁移会员码数据
async function migrateMemberCodes(memberCodes) {
  log('开始迁移会员码数据...', 'blue');
  
  const collection = db.collection('memberCodes');
  let successCount = 0;
  let errorCount = 0;

  for (const code of memberCodes) {
    try {
      // 检查会员码是否已存在
      const existingCode = await collection.where({
        code: code.code
      }).get();

      if (existingCode.data.length === 0) {
        // 添加时间戳字段
        const codeData = {
          ...code,
          createdAt: code.createdAt || new Date().toISOString(),
          updatedAt: code.updatedAt || new Date().toISOString()
        };

        await collection.add(codeData);
        successCount++;
        log(`✓ 会员码 ${code.code} 迁移成功`, 'green');
      } else {
        log(`- 会员码 ${code.code} 已存在，跳过`, 'yellow');
      }
    } catch (error) {
      errorCount++;
      log(`✗ 会员码 ${code.code} 迁移失败: ${error.message}`, 'red');
    }
  }

  log(`会员码数据迁移完成: 成功 ${successCount}, 失败 ${errorCount}`, 'blue');
}

// 创建数据库索引
async function createIndexes() {
  log('创建数据库索引...', 'blue');

  try {
    // 用户集合索引
    await db.collection('users').createIndex({
      keys: [{ username: 1 }],
      options: { unique: true }
    });

    await db.collection('users').createIndex({
      keys: [{ phoneNumber: 1 }],
      options: { unique: true }
    });

    // 小说集合索引
    await db.collection('novels').createIndex({
      keys: [{ userId: 1, createdAt: -1 }]
    });

    // 会员码集合索引
    await db.collection('memberCodes').createIndex({
      keys: [{ code: 1 }],
      options: { unique: true }
    });

    log('数据库索引创建完成', 'green');
  } catch (error) {
    log(`创建索引失败: ${error.message}`, 'red');
  }
}

// 主迁移函数
async function migrate() {
  log('=== CloudBase数据库迁移工具 ===', 'blue');
  log(`目标环境: ${ENV_ID}`, 'blue');

  // 检查环境ID
  if (ENV_ID === 'your-env-id-here') {
    log('请先设置正确的环境ID!', 'red');
    log('使用方法: TCB_ENV=your-actual-env-id node migrate-to-cloudbase-db.js', 'yellow');
    process.exit(1);
  }

  log('使用环境ID: novel-app-2gywkgnn15cbd6a8', 'green');

  // 加载本地数据
  const localData = loadLocalData();
  if (!localData) {
    log('无法加载本地数据，迁移终止', 'red');
    process.exit(1);
  }

  log(`本地数据统计:`, 'blue');
  log(`- 用户: ${localData.users?.length || 0} 个`, 'blue');
  log(`- 小说: ${localData.novels?.length || 0} 个`, 'blue');
  log(`- 会员码: ${localData.memberCodes?.length || 0} 个`, 'blue');

  try {
    // 创建索引
    await createIndexes();

    // 迁移数据
    if (localData.users && localData.users.length > 0) {
      await migrateUsers(localData.users);
    }

    if (localData.novels && localData.novels.length > 0) {
      await migrateNovels(localData.novels);
    }

    if (localData.memberCodes && localData.memberCodes.length > 0) {
      await migrateMemberCodes(localData.memberCodes);
    }

    log('=== 数据迁移完成 ===', 'green');
    log('请在CloudBase控制台验证数据是否正确迁移', 'yellow');

  } catch (error) {
    log(`迁移过程中发生错误: ${error.message}`, 'red');
    process.exit(1);
  }
}

// 执行迁移
if (require.main === module) {
  migrate().catch(error => {
    log(`迁移失败: ${error.message}`, 'red');
    process.exit(1);
  });
}

module.exports = { migrate };
