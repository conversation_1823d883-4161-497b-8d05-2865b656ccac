{"version": 3, "file": "easings.js", "sources": ["../../../../packages/utils/easings.ts"], "sourcesContent": ["export function easeInOutCubic(t: number, b: number, c: number, d: number) {\n  const cc = c - b\n  t /= d / 2\n  if (t < 1) {\n    return (cc / 2) * t * t * t + b\n  }\n  return (cc / 2) * ((t -= 2) * t * t + 2) + b\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;AAC3C,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACnB,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACb,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE;AACb,IAAI,OAAO,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC7C;;;;"}