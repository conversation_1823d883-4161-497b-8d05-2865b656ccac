import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:novel_app/screens/character_generator_screen.dart';
import 'package:novel_app/screens/background_generator_screen.dart';
import 'package:novel_app/screens/knowledge_base_screen.dart';
import 'package:novel_app/screens/debug/conversation_debug_screen.dart';
import 'package:novel_app/screens/chat_history_list_screen.dart';
import 'package:novel_app/screens/tools/ai_agent_novel_screen.dart';
import 'package:novel_app/screens/tools/ai_agent_demo_screen.dart';
import 'package:novel_app/screens/tools/ai_agent_help_screen.dart';

import 'package:novel_app/services/announcement_service.dart';

class ToolsScreen extends StatelessWidget {
  const ToolsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('工具广场'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: const Icon(Icons.record_voice_over),
                  title: const Text('文本转语音'),
                  subtitle: const Text('将小说转换为语音'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.toNamed('/tts'),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.person_add),
                  title: const Text('角色生成器'),
                  subtitle: const Text('自动生成小说角色'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.to(() => const CharacterGeneratorScreen()),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.landscape),
                  title: const Text('背景生成器'),
                  subtitle: const Text('自动生成故事背景和世界观'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.to(() => const BackgroundGeneratorScreen()),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.auto_awesome, color: Colors.purple),
                  title: const Text('AI智能体小说生成'),
                  subtitle: const Text('多智能体协作自主创作百万字级爽文'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.to(() => const AIAgentNovelScreen()),
                ),
                ListTile(
                  leading: const Icon(Icons.play_circle_outline, color: Colors.orange),
                  title: const Text('AI智能体演示'),
                  subtitle: const Text('体验多智能体协作创作流程（演示版）'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.to(() => const AIAgentDemoScreen()),
                ),
                ListTile(
                  leading: const Icon(Icons.help_outline, color: Colors.blue),
                  title: const Text('AI智能体使用说明'),
                  subtitle: const Text('详细了解系统功能和使用方法'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.to(() => const AIAgentHelpScreen()),
                ),
                ListTile(
                  leading: const Icon(Icons.settings, color: Colors.grey),
                  title: const Text('AI智能体设置'),
                  subtitle: const Text('配置API密钥和服务器设置'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.toNamed('/settings'),
                ),
                ListTile(
                  leading: const Icon(Icons.edit_document, color: Colors.purple),
                  title: const Text('AI文件编辑器'),
                  subtitle: const Text('像Cursor一样的AI文件编辑功能'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.toNamed('/ai_file_editor_demo'),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.book),
                  title: const Text('知识库管理'),
                  subtitle: const Text('管理创作参考资料和知识'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.to(() => KnowledgeBaseScreen()),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.chat),
                  title: const Text('历史对话'),
                  subtitle: const Text('查看和继续小说创作对话'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => Get.to(() => const ChatHistoryListScreen()),
                ),
                const Divider(),
                ListTile(
                  leading: const Icon(Icons.lock),
                  title: const Text('会话历史调试'),
                  subtitle: const Text('查看和管理AI对话历史记录'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () => _showPasswordDialog(context),
                ),
                const Divider(),
                // 版本更新选项仅在非Web平台显示
                if (!kIsWeb)
                  ListTile(
                    leading: const Icon(Icons.system_update),
                    title: const Text('版本更新'),
                    subtitle: const Text('检查应用更新并下载最新版本'),
                    trailing: const Icon(Icons.arrow_forward_ios),
                    onTap: () => Get.toNamed('/update'),
                  ),

                const Divider(),
                ListTile(
                  leading: const Icon(Icons.announcement),
                  title: const Text('刷新公告'),
                  subtitle: const Text('从服务器获取最新公告'),
                  trailing: const Icon(Icons.refresh),
                  onTap: () {
                    final announcementService = Get.find<AnnouncementService>();
                    announcementService.refreshAnnouncement();
                  },
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showPasswordDialog(BuildContext context) {
    final TextEditingController passwordController = TextEditingController();
    final RxBool isPasswordIncorrect = false.obs;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.lock, color: Colors.orange),
            SizedBox(width: 10),
            Text('需要密码'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('会话历史调试功能受密码保护，请输入密码继续'),
            const SizedBox(height: 20),
            Obx(() => TextField(
                  controller: passwordController,
                  decoration: InputDecoration(
                    labelText: '密码',
                    border: const OutlineInputBorder(),
                    prefixIcon: const Icon(Icons.vpn_key),
                    errorText: isPasswordIncorrect.value ? '密码错误，请重试' : null,
                  ),
                  obscureText: true,
                  keyboardType: TextInputType.number,
                )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              if (passwordController.text == '147258') {
                Navigator.pop(context);
                Get.to(() => const ConversationDebugScreen());
              } else {
                isPasswordIncorrect.value = true;
              }
            },
            child: const Text('确认'),
          ),
        ],
      ),
    );
  }
}
