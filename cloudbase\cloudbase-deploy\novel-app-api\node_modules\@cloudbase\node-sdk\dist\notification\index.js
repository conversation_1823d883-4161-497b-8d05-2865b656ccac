"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendNotification = void 0;
const functions_1 = require("../functions");
/**
   * SDK推送消息接口
   * @param params
   * notifyId: 通知策略Id
   * data: 通知策略下的模板变量对应值
   * receivers: 待通知的用户名
   * url: 点击消息卡片打开的链接
   * @returns
   */
async function sendNotification(cloudbase, params, opts) {
    return await (0, functions_1.callFunction)(cloudbase, {
        name: 'lowcode-datasource',
        data: {
            methodName: 'callWedaApi',
            params: {
                action: 'PushNotifyMsg',
                data: {
                    NotifyId: params.notifyId,
                    Data: JSON.stringify(params.data),
                    NotifyUsers: undefined,
                    Url: params.url
                }
            },
            mode: 'c'
        }
    }, opts);
}
exports.sendNotification = sendNotification;
