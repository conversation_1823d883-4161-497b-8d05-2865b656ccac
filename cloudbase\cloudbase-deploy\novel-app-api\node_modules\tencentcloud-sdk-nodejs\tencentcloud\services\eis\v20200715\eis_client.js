"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Client = void 0;
/* eslint-disable @typescript-eslint/no-unused-vars */
/*
 * Copyright (c) 2018 Tencent. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
const abstract_client_1 = require("../../../common/abstract_client");
/**
 * eis client
 * @class
 */
class Client extends abstract_client_1.AbstractClient {
    constructor(clientConfig) {
        super("eis.tencentcloudapi.com", "2020-07-15", clientConfig);
    }
    /**
     * 获取连接器操作列表
     */
    async ListEisConnectorOperations(req, cb) {
        return this.request("ListEisConnectorOperations", req, cb);
    }
    /**
     * 连接器列表
     */
    async ListEisConnectors(req, cb) {
        return this.request("ListEisConnectors", req, cb);
    }
    /**
     * 获取连接器配置参数
     */
    async DescribeEisConnectorConfig(req, cb) {
        return this.request("DescribeEisConnectorConfig", req, cb);
    }
}
exports.Client = Client;
