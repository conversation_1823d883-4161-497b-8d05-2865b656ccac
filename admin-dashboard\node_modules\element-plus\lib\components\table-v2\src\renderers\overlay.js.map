{"version": 3, "file": "overlay.js", "sources": ["../../../../../../../packages/components/table-v2/src/renderers/overlay.tsx"], "sourcesContent": ["import type { SimpleFunctionalComponent } from '../types'\n\nconst Overlay: SimpleFunctionalComponent = (props, { slots }) => {\n  return (\n    <div class={props.class} style={props.style}>\n      {slots.default?.()}\n    </div>\n  )\n}\n\nOverlay.displayName = 'ElTableV2Overlay'\n\nexport default Overlay\n"], "names": ["slots", "_createVNode", "props", "class", "style", "default", "Overlay"], "mappings": ";;;;;;;AAEA,EAAA,KAAwC;AAAaA,CAAAA,KAAAA;AAAF,EAAc,IAAA,EAAA,CAAA;AAC/D,EAAA,OAAAC,eAAA,CAAA,KAAA,EAAA;IAAA,OACcC,EAAAA,KAAK,CAACC,KADpB;AAAA,IAAA,OAAA,EACkCD,KAAK,CAACE,KAAAA;GACnCJ,EAAAA,CAAAA,CAAAA,EAAAA,GAAMK,KAAAA,CAAAA,OAFX,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAKD,CAND,CAAA;;AAQAC,gBAAA,OAAsB;;;;"}