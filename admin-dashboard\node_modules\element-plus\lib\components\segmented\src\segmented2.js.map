{"version": 3, "file": "segmented2.js", "sources": ["../../../../../../packages/components/segmented/src/segmented.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"options.length\"\n    :id=\"inputId\"\n    ref=\"segmentedRef\"\n    :class=\"segmentedCls\"\n    role=\"radiogroup\"\n    :aria-label=\"!isLabeledByFormItem ? ariaLabel || 'segmented' : undefined\"\n    :aria-labelledby=\"isLabeledByFormItem ? formItem!.labelId : undefined\"\n  >\n    <div :class=\"[ns.e('group'), ns.m(props.direction)]\">\n      <div :style=\"selectedStyle\" :class=\"selectedCls\" />\n      <label\n        v-for=\"(item, index) in options\"\n        :key=\"index\"\n        :class=\"getItemCls(item)\"\n      >\n        <input\n          :class=\"ns.e('item-input')\"\n          type=\"radio\"\n          :name=\"name\"\n          :disabled=\"getDisabled(item)\"\n          :checked=\"getSelected(item)\"\n          @change=\"handleChange(item)\"\n        />\n        <div :class=\"ns.e('item-label')\">\n          <slot :item=\"item\">{{ getLabel(item) }}</slot>\n        </div>\n      </label>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, reactive, ref, watch } from 'vue'\nimport { useActiveElement, useResizeObserver } from '@vueuse/core'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { debugWarn, isObject } from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { defaultProps, segmentedEmits, segmentedProps } from './segmented'\n\nimport type { Option } from './types'\n\ndefineOptions({\n  name: 'ElSegmented',\n})\n\nconst props = defineProps(segmentedProps)\nconst emit = defineEmits(segmentedEmits)\n\nconst ns = useNamespace('segmented')\nconst segmentedId = useId()\nconst segmentedSize = useFormSize()\nconst _disabled = useFormDisabled()\nconst { formItem } = useFormItem()\nconst { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst segmentedRef = ref<HTMLElement | null>(null)\nconst activeElement = useActiveElement()\n\nconst state = reactive({\n  isInit: false,\n  width: 0,\n  height: 0,\n  translateX: 0,\n  translateY: 0,\n  focusVisible: false,\n})\n\nconst handleChange = (item: Option) => {\n  const value = getValue(item)\n  emit(UPDATE_MODEL_EVENT, value)\n  emit(CHANGE_EVENT, value)\n}\n\nconst aliasProps = computed(() => ({ ...defaultProps, ...props.props }))\n\nconst getValue = (item: Option) => {\n  return isObject(item) ? item[aliasProps.value.value] : item\n}\n\nconst getLabel = (item: Option) => {\n  return isObject(item) ? item[aliasProps.value.label] : item\n}\n\nconst getDisabled = (item: Option | undefined) => {\n  return !!(\n    _disabled.value ||\n    (isObject(item) ? item[aliasProps.value.disabled] : false)\n  )\n}\n\nconst getSelected = (item: Option) => {\n  return props.modelValue === getValue(item)\n}\n\nconst getOption = (value: any) => {\n  return props.options.find((item) => getValue(item) === value)\n}\n\nconst getItemCls = (item: Option) => {\n  return [\n    ns.e('item'),\n    ns.is('selected', getSelected(item)),\n    ns.is('disabled', getDisabled(item)),\n  ]\n}\n\nconst updateSelect = () => {\n  if (!segmentedRef.value) return\n  const selectedItem = segmentedRef.value.querySelector(\n    '.is-selected'\n  ) as HTMLElement\n  const selectedItemInput = segmentedRef.value.querySelector(\n    '.is-selected input'\n  ) as HTMLElement\n  if (!selectedItem || !selectedItemInput) {\n    state.width = 0\n    state.height = 0\n    state.translateX = 0\n    state.translateY = 0\n    state.focusVisible = false\n    return\n  }\n  const rect = selectedItem.getBoundingClientRect()\n  state.isInit = true\n  if (props.direction === 'vertical') {\n    state.height = rect.height\n    state.translateY = selectedItem.offsetTop\n  } else {\n    state.width = rect.width\n    state.translateX = selectedItem.offsetLeft\n  }\n  try {\n    // This will failed in test\n    state.focusVisible = selectedItemInput.matches(':focus-visible')\n  } catch {}\n}\n\nconst segmentedCls = computed(() => [\n  ns.b(),\n  ns.m(segmentedSize.value),\n  ns.is('block', props.block),\n])\n\nconst selectedStyle = computed(() => ({\n  width: props.direction === 'vertical' ? '100%' : `${state.width}px`,\n  height: props.direction === 'vertical' ? `${state.height}px` : '100%',\n  transform:\n    props.direction === 'vertical'\n      ? `translateY(${state.translateY}px)`\n      : `translateX(${state.translateX}px)`,\n  display: state.isInit ? 'block' : 'none',\n}))\n\nconst selectedCls = computed(() => [\n  ns.e('item-selected'),\n  ns.is('disabled', getDisabled(getOption(props.modelValue))),\n  ns.is('focus-visible', state.focusVisible),\n])\n\nconst name = computed(() => {\n  return props.name || segmentedId.value\n})\n\nuseResizeObserver(segmentedRef, updateSelect)\n\nwatch(activeElement, updateSelect)\n\nwatch(\n  () => props.modelValue,\n  () => {\n    updateSelect()\n    if (props.validateEvent) {\n      formItem?.validate?.('change').catch((err) => debugWarn(err))\n    }\n  },\n  {\n    flush: 'post',\n  }\n)\n</script>\n"], "names": ["useNamespace", "useId", "useFormSize", "useFormDisabled", "useFormItem", "useFormItemInputId", "ref", "useActiveElement", "reactive", "UPDATE_MODEL_EVENT", "CHANGE_EVENT", "computed", "defaultProps", "isObject", "useResizeObserver", "watch", "debugWarn", "_openBlock", "_createElementBlock", "_unref"], "mappings": ";;;;;;;;;;;;;;;;uCAiDc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAKA,mBAAa,WAAW,CAAA,CAAA;AACnC,IAAA,MAAM,cAAcC,aAAM,EAAA,CAAA;AAC1B,IAAA,MAAM,gBAAgBC,8BAAY,EAAA,CAAA;AAClC,IAAA,MAAM,YAAYC,kCAAgB,EAAA,CAAA;AAClC,IAAM,MAAA,EAAE,QAAS,EAAA,GAAIC,uBAAY,EAAA,CAAA;AACjC,IAAA,MAAM,EAAE,OAAA,EAAS,mBAAoB,EAAA,GAAIC,+BAAmB,KAAO,EAAA;AAAA,MACjE,eAAiB,EAAA,QAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAeC,QAAwB,IAAI,CAAA,CAAA;AACjD,IAAA,MAAM,gBAAgBC,qBAAiB,EAAA,CAAA;AAEvC,IAAA,MAAM,QAAQC,YAAS,CAAA;AAAA,MACrB,MAAQ,EAAA,KAAA;AAAA,MACR,KAAO,EAAA,CAAA;AAAA,MACP,MAAQ,EAAA,CAAA;AAAA,MACR,UAAY,EAAA,CAAA;AAAA,MACZ,UAAY,EAAA,CAAA;AAAA,MACZ,YAAc,EAAA,KAAA;AAAA,KACf,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,CAAC,IAAiB,KAAA;AACrC,MAAM,MAAA,KAAA,GAAQ,SAAS,IAAI,CAAA,CAAA;AAC3B,MAAA,IAAA,CAAKC,0BAAoB,KAAK,CAAA,CAAA;AAC9B,MAAA,IAAA,CAAKC,oBAAc,KAAK,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAM,MAAA,UAAA,GAAaC,aAAS,OAAO,EAAE,GAAGC,sBAAc,EAAA,GAAG,KAAM,CAAA,KAAA,EAAQ,CAAA,CAAA,CAAA;AAEvE,IAAM,MAAA,QAAA,GAAW,CAAC,IAAiB,KAAA;AACjC,MAAA,OAAOC,gBAAS,IAAI,CAAA,GAAI,KAAK,UAAW,CAAA,KAAA,CAAM,KAAK,CAAI,GAAA,IAAA,CAAA;AAAA,KACzD,CAAA;AAEA,IAAM,MAAA,QAAA,GAAW,CAAC,IAAiB,KAAA;AACjC,MAAA,OAAOA,gBAAS,IAAI,CAAA,GAAI,KAAK,UAAW,CAAA,KAAA,CAAM,KAAK,CAAI,GAAA,IAAA,CAAA;AAAA,KACzD,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAAC,IAA6B,KAAA;AAChD,MAAO,OAAA,CAAC,EACN,SAAA,CAAU,KACT,KAAAA,eAAA,CAAS,IAAI,CAAA,GAAI,IAAK,CAAA,UAAA,CAAW,KAAM,CAAA,QAAQ,CAAI,GAAA,KAAA,CAAA,CAAA,CAAA;AAAA,KAExD,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,CAAC,IAAiB,KAAA;AACpC,MAAO,OAAA,KAAA,CAAM,UAAe,KAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AAAA,KAC3C,CAAA;AAEA,IAAM,MAAA,SAAA,GAAY,CAAC,KAAe,KAAA;AAChC,MAAO,OAAA,KAAA,CAAM,QAAQ,IAAK,CAAA,CAAC,SAAS,QAAS,CAAA,IAAI,MAAM,KAAK,CAAA,CAAA;AAAA,KAC9D,CAAA;AAEA,IAAM,MAAA,UAAA,GAAa,CAAC,IAAiB,KAAA;AACnC,MAAO,OAAA;AAAA,QACL,EAAA,CAAG,EAAE,MAAM,CAAA;AAAA,QACX,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,WAAA,CAAY,IAAI,CAAC,CAAA;AAAA,QACnC,EAAG,CAAA,EAAA,CAAG,UAAY,EAAA,WAAA,CAAY,IAAI,CAAC,CAAA;AAAA,OACrC,CAAA;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAI,IAAA,CAAC,aAAa,KAAO;AACzB,QAAM,OAAA;AAAkC,MACtC,MAAA,YAAA,GAAA,YAAA,CAAA,KAAA,CAAA,aAAA,CAAA,cAAA,CAAA,CAAA;AAAA,MACF,MAAA,iBAAA,GAAA,YAAA,CAAA,KAAA,CAAA,aAAA,CAAA,oBAAA,CAAA,CAAA;AACA,MAAM,IAAA,CAAA,YAAA,IAAA,CAAA,iBAAiC,EAAM;AAAA,QAC3C,KAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAAA,QACF,KAAA,CAAA,MAAA,GAAA,CAAA,CAAA;AACA,QAAI,KAAiB,CAAA,UAAA,GAAA,CAAC,CAAmB;AACvC,QAAA,KAAA,CAAM,UAAQ,GAAA,CAAA,CAAA;AACd,QAAA,KAAA,CAAM,YAAS,GAAA,KAAA,CAAA;AACf,QAAA,OAAmB;AACnB,OAAA;AACA,MAAA,MAAA,IAAqB,GAAA,YAAA,CAAA,qBAAA,EAAA,CAAA;AACrB,MAAA,KAAA,CAAA,MAAA,GAAA,IAAA,CAAA;AAAA,MACF,IAAA,KAAA,CAAA,SAAA,KAAA,UAAA,EAAA;AACA,QAAM,KAAA,CAAA,oBAA0C,CAAA;AAChD,QAAA,KAAe,CAAA,UAAA,GAAA,YAAA,CAAA,SAAA,CAAA;AACf,OAAI,MAAA;AACF,QAAA,KAAA,CAAM,YAAc,CAAA,KAAA,CAAA;AACpB,QAAA,KAAA,CAAM,aAAa,YAAa,CAAA,UAAA,CAAA;AAAA,OAC3B;AACL,MAAA,IAAA;AACA,QAAA,KAAA,CAAM,eAA0B,iBAAA,CAAA,OAAA,CAAA,gBAAA,CAAA,CAAA;AAAA,OAClC,CAAA,OAAA,CAAA,EAAA;AACA,OAAI;AAEF,KAAM,CAAA;AAAyD,IAAA,MACzD,YAAA,GAAAF,YAAA,CAAA,MAAA;AAAA,MAAC,EAAA,CAAA,CAAA,EAAA;AAAA,MACX,EAAA,CAAA,CAAA,CAAA,aAAA,CAAA,KAAA,CAAA;AAEA,MAAM,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA;AAA8B,KAAA,CAClC;AAAK,IACL,MAAK,aAAA,GAAmBA,YAAA,CAAA,OAAA;AAAA,MACxB,KAAG,EAAY,KAAA,CAAA,SAAW,KAAA,UAAA,GAAA,MAAA,GAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,MAC3B,MAAA,EAAA,KAAA,CAAA,SAAA,KAAA,UAAA,GAAA,CAAA,EAAA,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA,GAAA,MAAA;AAED,MAAM,SAAA,EAAA,KAAA,CAAA,cAAgC,UAAA,GAAA,CAAA,WAAA,EAAA,KAAA,CAAA,UAAA,CAAA,GAAA,CAAA,GAAA,CAAA,WAAA,EAAA,KAAA,CAAA,UAAA,CAAA,GAAA,CAAA;AAAA,MACpC,OAAO,EAAM,KAAA,CAAA,MAAA,GAAA,gBAA2B;AAAuB,KAAA,CAC/D;AAA+D,IAC/D,MAAA,WACQ,GAAAA,YAAA,CAAA,MACF;AAC8B,MACpC,EAAA,CAAA,CAAA,CAAA,eAAe,CAAA;AAAmB,MAClC,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,WAAA,CAAA,SAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAEF,MAAM,EAAA,CAAA,EAAA,CAAA,sBAA6B,CAAA,YAAA,CAAA;AAAA,KACjC,CAAA,CAAA;AAAoB,IACpB,UAAkB,GAAAA,YAAA,CAAA,MAAA;AAAwC,MAC1D,OAAuB,KAAA,CAAA,IAAA,IAAA,WAAkB,CAAA,KAAA,CAAA;AAAA,KAC1C,CAAA,CAAA;AAED,IAAMG,mCAAsB,EAAA,YAAA,CAAA,CAAA;AAC1B,IAAOC,SAAA,CAAA,2BAA0B,CAAA,CAAA;AAAA,IACnCA,SAAC,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,MAAA;AAED,MAAA,IAAA,EAAA,CAAA;AAEA,MAAA;AAEA,MAAA,IAAA,KAAA,CAAA,aAAA,EAAA;AAAA,cACc,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAAC,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,OACN;AACJ,KAAa,EAAA;AACb,MAAA,KAAA,QAAyB;AACvB,KAAU,CAAA,CAAA;AAAkD,IAC9D,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MACF,OAAA,IAAA,CAAA,OAAA,CAAA,MAAA,IAAAC,aAAA,EAAA,EAAAC,sBAAA,CAAA,KAAA,EAAA;AAAA,QACA,GAAA,EAAA,CAAA;AAAA,QACE,EAAO,EAAAC,SAAA,CAAA,OAAA,CAAA;AAAA,QACT,OAAA,EAAA,cAAA;AAAA,QACF,GAAA,EAAA,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}