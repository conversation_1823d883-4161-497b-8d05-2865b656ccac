import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("waf.tencentcloudapi.com", "2018-01-25", clientConfig);
    }
    async DescribeAttackType(req, cb) {
        return this.request("DescribeAttackType", req, cb);
    }
    async DescribeDomainDetailsClb(req, cb) {
        return this.request("DescribeDomainDetailsClb", req, cb);
    }
    async DescribeDomains(req, cb) {
        return this.request("DescribeDomains", req, cb);
    }
    async DescribeAccessIndex(req, cb) {
        return this.request("DescribeAccessIndex", req, cb);
    }
    async GetAttackTotalCount(req, cb) {
        return this.request("GetAttackTotalCount", req, cb);
    }
    async ImportIpAccessControl(req, cb) {
        return this.request("ImportIpAccessControl", req, cb);
    }
    async AddCustomWhiteRule(req, cb) {
        return this.request("AddCustomWhiteRule", req, cb);
    }
    async DeleteIpAccessControl(req, cb) {
        return this.request("DeleteIpAccessControl", req, cb);
    }
    async ModifyUserSignatureRule(req, cb) {
        return this.request("ModifyUserSignatureRule", req, cb);
    }
    async DescribeAreaBanAreas(req, cb) {
        return this.request("DescribeAreaBanAreas", req, cb);
    }
    async ModifyCustomRule(req, cb) {
        return this.request("ModifyCustomRule", req, cb);
    }
    async ModifyHostFlowMode(req, cb) {
        return this.request("ModifyHostFlowMode", req, cb);
    }
    async ModifyInstanceQpsLimit(req, cb) {
        return this.request("ModifyInstanceQpsLimit", req, cb);
    }
    async DescribeApiListVersionTwo(req, cb) {
        return this.request("DescribeApiListVersionTwo", req, cb);
    }
    async DescribeSpartaProtectionInfo(req, cb) {
        return this.request("DescribeSpartaProtectionInfo", req, cb);
    }
    async DescribeAutoDenyIP(req, cb) {
        return this.request("DescribeAutoDenyIP", req, cb);
    }
    async DeleteCustomWhiteRule(req, cb) {
        return this.request("DeleteCustomWhiteRule", req, cb);
    }
    async GetAttackDownloadRecords(req, cb) {
        return this.request("GetAttackDownloadRecords", req, cb);
    }
    async DescribeApiDetail(req, cb) {
        return this.request("DescribeApiDetail", req, cb);
    }
    async DescribeScanIp(req, cb) {
        return this.request("DescribeScanIp", req, cb);
    }
    async CreatePostCKafkaFlow(req, cb) {
        return this.request("CreatePostCKafkaFlow", req, cb);
    }
    async DeleteCustomRule(req, cb) {
        return this.request("DeleteCustomRule", req, cb);
    }
    async ModifyAntiFakeUrl(req, cb) {
        return this.request("ModifyAntiFakeUrl", req, cb);
    }
    async ModifySpartaProtection(req, cb) {
        return this.request("ModifySpartaProtection", req, cb);
    }
    async ModifyInstanceElasticMode(req, cb) {
        return this.request("ModifyInstanceElasticMode", req, cb);
    }
    async ModifyApiAnalyzeStatus(req, cb) {
        return this.request("ModifyApiAnalyzeStatus", req, cb);
    }
    async ModifyWafAutoDenyRules(req, cb) {
        return this.request("ModifyWafAutoDenyRules", req, cb);
    }
    async DescribeUserDomainInfo(req, cb) {
        return this.request("DescribeUserDomainInfo", req, cb);
    }
    async DescribeProtectionModes(req, cb) {
        return this.request("DescribeProtectionModes", req, cb);
    }
    async DescribeAntiInfoLeakageRules(req, cb) {
        return this.request("DescribeAntiInfoLeakageRules", req, cb);
    }
    async SearchAccessLog(req, cb) {
        return this.request("SearchAccessLog", req, cb);
    }
    async DescribeWafThreatenIntelligence(req, cb) {
        return this.request("DescribeWafThreatenIntelligence", req, cb);
    }
    async DescribeBotSceneUCBRule(req, cb) {
        return this.request("DescribeBotSceneUCBRule", req, cb);
    }
    async DescribeIpHitItems(req, cb) {
        return this.request("DescribeIpHitItems", req, cb);
    }
    async DescribeHistogram(req, cb) {
        return this.request("DescribeHistogram", req, cb);
    }
    async DescribeWebshellStatus(req, cb) {
        return this.request("DescribeWebshellStatus", req, cb);
    }
    async GenerateDealsAndPayNew(req, cb) {
        return this.request("GenerateDealsAndPayNew", req, cb);
    }
    async DeleteAntiInfoLeakRule(req, cb) {
        return this.request("DeleteAntiInfoLeakRule", req, cb);
    }
    async CreateAreaBanRule(req, cb) {
        return this.request("CreateAreaBanRule", req, cb);
    }
    async ModifyAreaBanAreas(req, cb) {
        return this.request("ModifyAreaBanAreas", req, cb);
    }
    async CreateDeals(req, cb) {
        return this.request("CreateDeals", req, cb);
    }
    async DescribeAreaBanSupportAreas(req, cb) {
        return this.request("DescribeAreaBanSupportAreas", req, cb);
    }
    async ModifyInstanceAttackLogPost(req, cb) {
        return this.request("ModifyInstanceAttackLogPost", req, cb);
    }
    async DescribeRuleLimit(req, cb) {
        return this.request("DescribeRuleLimit", req, cb);
    }
    async AddAntiInfoLeakRules(req, cb) {
        return this.request("AddAntiInfoLeakRules", req, cb);
    }
    async ModifyHostStatus(req, cb) {
        return this.request("ModifyHostStatus", req, cb);
    }
    async ModifyBotSceneUCBRule(req, cb) {
        return this.request("ModifyBotSceneUCBRule", req, cb);
    }
    async DescribeModuleStatus(req, cb) {
        return this.request("DescribeModuleStatus", req, cb);
    }
    async UpsertIpAccessControl(req, cb) {
        return this.request("UpsertIpAccessControl", req, cb);
    }
    async CreatePostCLSFlow(req, cb) {
        return this.request("CreatePostCLSFlow", req, cb);
    }
    async GetAttackHistogram(req, cb) {
        return this.request("GetAttackHistogram", req, cb);
    }
    async ModifyDomainsCLSStatus(req, cb) {
        return this.request("ModifyDomainsCLSStatus", req, cb);
    }
    async DescribeUserSignatureRuleV2(req, cb) {
        return this.request("DescribeUserSignatureRuleV2", req, cb);
    }
    async DescribePostCKafkaFlows(req, cb) {
        return this.request("DescribePostCKafkaFlows", req, cb);
    }
    async DescribeVipInfo(req, cb) {
        return this.request("DescribeVipInfo", req, cb);
    }
    async DeleteHost(req, cb) {
        return this.request("DeleteHost", req, cb);
    }
    async DescribeAccessFastAnalysis(req, cb) {
        return this.request("DescribeAccessFastAnalysis", req, cb);
    }
    async DescribePeakPoints(req, cb) {
        return this.request("DescribePeakPoints", req, cb);
    }
    async ModifyCustomWhiteRule(req, cb) {
        return this.request("ModifyCustomWhiteRule", req, cb);
    }
    async DescribeCertificateVerifyResult(req, cb) {
        return this.request("DescribeCertificateVerifyResult", req, cb);
    }
    async DeleteIpAccessControlV2(req, cb) {
        return this.request("DeleteIpAccessControlV2", req, cb);
    }
    async SwitchElasticMode(req, cb) {
        return this.request("SwitchElasticMode", req, cb);
    }
    async ModifyInstanceRenewFlag(req, cb) {
        return this.request("ModifyInstanceRenewFlag", req, cb);
    }
    async DescribeDomainDetailsSaas(req, cb) {
        return this.request("DescribeDomainDetailsSaas", req, cb);
    }
    async DescribeCCRule(req, cb) {
        return this.request("DescribeCCRule", req, cb);
    }
    async DescribePeakValue(req, cb) {
        return this.request("DescribePeakValue", req, cb);
    }
    async ModifyWafThreatenIntelligence(req, cb) {
        return this.request("ModifyWafThreatenIntelligence", req, cb);
    }
    async ModifySpartaProtectionMode(req, cb) {
        return this.request("ModifySpartaProtectionMode", req, cb);
    }
    async DeleteAttackDownloadRecord(req, cb) {
        return this.request("DeleteAttackDownloadRecord", req, cb);
    }
    async DescribeCustomRuleList(req, cb) {
        return this.request("DescribeCustomRuleList", req, cb);
    }
    async DescribeAttackOverview(req, cb) {
        return this.request("DescribeAttackOverview", req, cb);
    }
    async DescribeAttackWhiteRule(req, cb) {
        return this.request("DescribeAttackWhiteRule", req, cb);
    }
    async DescribeHosts(req, cb) {
        return this.request("DescribeHosts", req, cb);
    }
    async AddSpartaProtection(req, cb) {
        return this.request("AddSpartaProtection", req, cb);
    }
    async DescribePolicyStatus(req, cb) {
        return this.request("DescribePolicyStatus", req, cb);
    }
    async DescribeSession(req, cb) {
        return this.request("DescribeSession", req, cb);
    }
    async DescribeTopAttackDomain(req, cb) {
        return this.request("DescribeTopAttackDomain", req, cb);
    }
    async ModifyBotSceneStatus(req, cb) {
        return this.request("ModifyBotSceneStatus", req, cb);
    }
    async ModifyHost(req, cb) {
        return this.request("ModifyHost", req, cb);
    }
    async DescribeUserSignatureClass(req, cb) {
        return this.request("DescribeUserSignatureClass", req, cb);
    }
    async ModifyBotStatus(req, cb) {
        return this.request("ModifyBotStatus", req, cb);
    }
    async ModifyProtectionStatus(req, cb) {
        return this.request("ModifyProtectionStatus", req, cb);
    }
    async DescribeCiphersDetail(req, cb) {
        return this.request("DescribeCiphersDetail", req, cb);
    }
    async ModifyAreaBanStatus(req, cb) {
        return this.request("ModifyAreaBanStatus", req, cb);
    }
    async ModifyIpAccessControl(req, cb) {
        return this.request("ModifyIpAccessControl", req, cb);
    }
    async DescribeAntiFakeRules(req, cb) {
        return this.request("DescribeAntiFakeRules", req, cb);
    }
    async DescribeFindDomainList(req, cb) {
        return this.request("DescribeFindDomainList", req, cb);
    }
    async DescribePostCLSFlows(req, cb) {
        return this.request("DescribePostCLSFlows", req, cb);
    }
    async ModifyAntiFakeUrlStatus(req, cb) {
        return this.request("ModifyAntiFakeUrlStatus", req, cb);
    }
    async DescribeAccessHistogram(req, cb) {
        return this.request("DescribeAccessHistogram", req, cb);
    }
    async DescribeTlsVersion(req, cb) {
        return this.request("DescribeTlsVersion", req, cb);
    }
    async ModifyAntiInfoLeakRules(req, cb) {
        return this.request("ModifyAntiInfoLeakRules", req, cb);
    }
    async CreateIpAccessControl(req, cb) {
        return this.request("CreateIpAccessControl", req, cb);
    }
    async AddAntiFakeUrl(req, cb) {
        return this.request("AddAntiFakeUrl", req, cb);
    }
    async DescribeFlowTrend(req, cb) {
        return this.request("DescribeFlowTrend", req, cb);
    }
    async DescribeObjects(req, cb) {
        return this.request("DescribeObjects", req, cb);
    }
    async ModifyDomainWhiteRule(req, cb) {
        return this.request("ModifyDomainWhiteRule", req, cb);
    }
    async ModifyCustomRuleStatus(req, cb) {
        return this.request("ModifyCustomRuleStatus", req, cb);
    }
    async DeleteAttackWhiteRule(req, cb) {
        return this.request("DeleteAttackWhiteRule", req, cb);
    }
    async DescribeIpAccessControl(req, cb) {
        return this.request("DescribeIpAccessControl", req, cb);
    }
    async DescribeBotSceneOverview(req, cb) {
        return this.request("DescribeBotSceneOverview", req, cb);
    }
    async AddAttackWhiteRule(req, cb) {
        return this.request("AddAttackWhiteRule", req, cb);
    }
    async AddAreaBanAreas(req, cb) {
        return this.request("AddAreaBanAreas", req, cb);
    }
    async DescribeDomainCountInfo(req, cb) {
        return this.request("DescribeDomainCountInfo", req, cb);
    }
    async DeleteSpartaProtection(req, cb) {
        return this.request("DeleteSpartaProtection", req, cb);
    }
    async SwitchDomainRules(req, cb) {
        return this.request("SwitchDomainRules", req, cb);
    }
    async UpsertCCAutoStatus(req, cb) {
        return this.request("UpsertCCAutoStatus", req, cb);
    }
    async DestroyPostCLSFlow(req, cb) {
        return this.request("DestroyPostCLSFlow", req, cb);
    }
    async DescribeBatchIpAccessControl(req, cb) {
        return this.request("DescribeBatchIpAccessControl", req, cb);
    }
    async DescribeWafAutoDenyRules(req, cb) {
        return this.request("DescribeWafAutoDenyRules", req, cb);
    }
    async DeleteSession(req, cb) {
        return this.request("DeleteSession", req, cb);
    }
    async DescribeDomainWhiteRules(req, cb) {
        return this.request("DescribeDomainWhiteRules", req, cb);
    }
    async ModifyWebshellStatus(req, cb) {
        return this.request("ModifyWebshellStatus", req, cb);
    }
    async ModifyAntiInfoLeakRuleStatus(req, cb) {
        return this.request("ModifyAntiInfoLeakRuleStatus", req, cb);
    }
    async DestroyPostCKafkaFlow(req, cb) {
        return this.request("DestroyPostCKafkaFlow", req, cb);
    }
    async PostAttackDownloadTask(req, cb) {
        return this.request("PostAttackDownloadTask", req, cb);
    }
    async DescribeCustomWhiteRule(req, cb) {
        return this.request("DescribeCustomWhiteRule", req, cb);
    }
    async DescribeHost(req, cb) {
        return this.request("DescribeHost", req, cb);
    }
    async ModifyUserSignatureRuleV2(req, cb) {
        return this.request("ModifyUserSignatureRuleV2", req, cb);
    }
    async DescribeAccessExports(req, cb) {
        return this.request("DescribeAccessExports", req, cb);
    }
    async DescribeHostLimit(req, cb) {
        return this.request("DescribeHostLimit", req, cb);
    }
    async GetInstanceQpsLimit(req, cb) {
        return this.request("GetInstanceQpsLimit", req, cb);
    }
    async DescribeAreaBanRule(req, cb) {
        return this.request("DescribeAreaBanRule", req, cb);
    }
    async DeleteAccessExport(req, cb) {
        return this.request("DeleteAccessExport", req, cb);
    }
    async DeleteDomainWhiteRules(req, cb) {
        return this.request("DeleteDomainWhiteRules", req, cb);
    }
    async DescribeDomainVerifyResult(req, cb) {
        return this.request("DescribeDomainVerifyResult", req, cb);
    }
    async CreateAccessExport(req, cb) {
        return this.request("CreateAccessExport", req, cb);
    }
    async SearchAttackLog(req, cb) {
        return this.request("SearchAttackLog", req, cb);
    }
    async DescribeInstances(req, cb) {
        return this.request("DescribeInstances", req, cb);
    }
    async ModifyAttackWhiteRule(req, cb) {
        return this.request("ModifyAttackWhiteRule", req, cb);
    }
    async AddCustomRule(req, cb) {
        return this.request("AddCustomRule", req, cb);
    }
    async DescribeUserLevel(req, cb) {
        return this.request("DescribeUserLevel", req, cb);
    }
    async ModifyGenerateDeals(req, cb) {
        return this.request("ModifyGenerateDeals", req, cb);
    }
    async ModifyInstanceName(req, cb) {
        return this.request("ModifyInstanceName", req, cb);
    }
    async ModifyModuleStatus(req, cb) {
        return this.request("ModifyModuleStatus", req, cb);
    }
    async DescribeUserCdcClbWafRegions(req, cb) {
        return this.request("DescribeUserCdcClbWafRegions", req, cb);
    }
    async ModifyAreaBanRule(req, cb) {
        return this.request("ModifyAreaBanRule", req, cb);
    }
    async DescribeWafAutoDenyStatus(req, cb) {
        return this.request("DescribeWafAutoDenyStatus", req, cb);
    }
    async DescribeBotSceneList(req, cb) {
        return this.request("DescribeBotSceneList", req, cb);
    }
    async ModifyCustomWhiteRuleStatus(req, cb) {
        return this.request("ModifyCustomWhiteRuleStatus", req, cb);
    }
    async DescribePorts(req, cb) {
        return this.request("DescribePorts", req, cb);
    }
    async ModifyHostMode(req, cb) {
        return this.request("ModifyHostMode", req, cb);
    }
    async ModifyDomainPostAction(req, cb) {
        return this.request("ModifyDomainPostAction", req, cb);
    }
    async DescribeUserClbWafRegions(req, cb) {
        return this.request("DescribeUserClbWafRegions", req, cb);
    }
    async UpsertCCRule(req, cb) {
        return this.request("UpsertCCRule", req, cb);
    }
    async ModifyObject(req, cb) {
        return this.request("ModifyObject", req, cb);
    }
    async DeleteAntiFakeUrl(req, cb) {
        return this.request("DeleteAntiFakeUrl", req, cb);
    }
    async DescribeCCRuleList(req, cb) {
        return this.request("DescribeCCRuleList", req, cb);
    }
    async DeleteCCRule(req, cb) {
        return this.request("DeleteCCRule", req, cb);
    }
    async CreateHost(req, cb) {
        return this.request("CreateHost", req, cb);
    }
    async DeleteBotSceneUCBRule(req, cb) {
        return this.request("DeleteBotSceneUCBRule", req, cb);
    }
    async UpsertSession(req, cb) {
        return this.request("UpsertSession", req, cb);
    }
    async DescribeCCAutoStatus(req, cb) {
        return this.request("DescribeCCAutoStatus", req, cb);
    }
    async DescribeUserSignatureRule(req, cb) {
        return this.request("DescribeUserSignatureRule", req, cb);
    }
    async BatchOperateUserSignatureRules(req, cb) {
        return this.request("BatchOperateUserSignatureRules", req, cb);
    }
    async FreshAntiFakeUrl(req, cb) {
        return this.request("FreshAntiFakeUrl", req, cb);
    }
    async DescribeDomainRules(req, cb) {
        return this.request("DescribeDomainRules", req, cb);
    }
    async ModifyApiSecEventChange(req, cb) {
        return this.request("ModifyApiSecEventChange", req, cb);
    }
    async RefreshAccessCheckResult(req, cb) {
        return this.request("RefreshAccessCheckResult", req, cb);
    }
    async ModifyUserSignatureClass(req, cb) {
        return this.request("ModifyUserSignatureClass", req, cb);
    }
    async AddDomainWhiteRule(req, cb) {
        return this.request("AddDomainWhiteRule", req, cb);
    }
    async ModifyDomainIpv6Status(req, cb) {
        return this.request("ModifyDomainIpv6Status", req, cb);
    }
    async UpdateProtectionModes(req, cb) {
        return this.request("UpdateProtectionModes", req, cb);
    }
    async ModifyUserLevel(req, cb) {
        return this.request("ModifyUserLevel", req, cb);
    }
}
