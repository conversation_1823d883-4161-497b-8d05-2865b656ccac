import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import '../config/api_config.dart';

/// CloudBase真实数据同步服务
/// 使用CloudBase云函数API实现真正的数据同步
class CloudBaseSyncService {
  static CloudBaseSyncService? _instance;
  static CloudBaseSyncService get instance {
    // 强制重新创建实例以确保使用最新配置
    _instance = CloudBaseSyncService._();
    return _instance!;
  }

  CloudBaseSyncService._() {
    print('🔧 CloudBaseSyncService 初始化');
    print('🔧 配置的baseUrl: ${ApiConfig.baseUrl}');
    print('🔧 配置的envId: $_envId');
    print('🔧 当前时间: ${DateTime.now()}');
  }

  // CloudBase配置 - 使用ApiConfig动态配置
  String get _baseUrl => ApiConfig.baseUrl;
  final String _envId = 'novel-app-2gywkgnn15cbd6a8';

  // 调试：确保使用正确的URL
  String get debugBaseUrl {
    print('🔍 DEBUG: _baseUrl = $_baseUrl');
    return _baseUrl;
  }

  /// 上传数据到CloudBase - 使用智能分块策略
  Future<bool> uploadData(Map<String, dynamic> data, String authToken) async {
    try {
      print('🚀 开始智能CloudBase数据上传...');

      // 分析数据大小
      final dataString = jsonEncode(data);
      final dataSizeKB = dataString.length / 1024;
      print('📊 数据大小: ${dataSizeKB.toStringAsFixed(2)} KB');

      // 使用智能分块上传策略，避免传输限制
      return await _uploadWithSmartChunking(data, authToken);

    } catch (e) {
      print('❌ CloudBase数据上传异常: $e');
      return false;
    }
  }

  /// 智能分块上传策略
  Future<bool> _uploadWithSmartChunking(Map<String, dynamic> data, String authToken) async {
    try {
      print('📦 使用智能分块上传策略...');

      // 按数据类型分块上传
      final chunks = _createDataChunks(data);
      print('📊 分为 ${chunks.length} 个数据块');

      // 逐个上传数据块
      for (int i = 0; i < chunks.length; i++) {
        final chunk = chunks[i];
        print('📤 上传数据块 ${i + 1}/${chunks.length}: ${chunk['type']}');

        final success = await _uploadChunk(chunk, authToken, i + 1, chunks.length);
        if (!success) {
          print('❌ 数据块 ${i + 1} 上传失败');
          return false;
        }

        print('✅ 数据块 ${i + 1} 上传成功');

        // 块间延迟，避免请求过于频繁
        if (i < chunks.length - 1) {
          await Future.delayed(Duration(milliseconds: 500));
        }
      }

      print('🎉 所有数据块上传完成！');
      return true;

    } catch (e) {
      print('❌ 智能分块上传异常: $e');
      return false;
    }
  }

  /// 创建数据块
  List<Map<String, dynamic>> _createDataChunks(Map<String, dynamic> data) {
    final chunks = <Map<String, dynamic>>[];

    // 1. 用户设置块（最小，优先上传）
    if (data['userSettings'] != null) {
      chunks.add({
        'type': 'userSettings',
        'data': {'userSettings': data['userSettings']},
        'timestamp': DateTime.now().toIso8601String(),
      });
    }

    // 2. 知识库文档块
    if (data['knowledgeDocuments'] != null) {
      final docs = data['knowledgeDocuments'] as List;
      if (docs.isNotEmpty) {
        chunks.add({
          'type': 'knowledgeDocuments',
          'data': {'knowledgeDocuments': docs},
          'timestamp': DateTime.now().toIso8601String(),
        });
      }
    }

    // 3. 小说数据块（按小说分块，每块最多5本）
    if (data['novels'] != null) {
      final novels = data['novels'] as List;
      if (novels.isNotEmpty) {
        final batchSize = 5; // 每批最多5本小说
        for (int i = 0; i < novels.length; i += batchSize) {
          final end = (i + batchSize < novels.length) ? i + batchSize : novels.length;
          final novelBatch = novels.sublist(i, end);

          chunks.add({
            'type': 'novels_batch_${(i ~/ batchSize) + 1}',
            'data': {'novels': novelBatch},
            'timestamp': DateTime.now().toIso8601String(),
            'batchInfo': {
              'batchNumber': (i ~/ batchSize) + 1,
              'totalBatches': (novels.length / batchSize).ceil(),
              'startIndex': i,
              'endIndex': end - 1,
            }
          });
        }
      }
    }

    // 4. 角色卡片块（如果存在）
    if (data['characterCards'] != null) {
      chunks.add({
        'type': 'characterCards',
        'data': {'characterCards': data['characterCards']},
        'timestamp': DateTime.now().toIso8601String(),
      });
    }

    // 5. 角色类型块（如果存在）
    if (data['characterTypes'] != null) {
      chunks.add({
        'type': 'characterTypes',
        'data': {'characterTypes': data['characterTypes']},
        'timestamp': DateTime.now().toIso8601String(),
      });
    }

    return chunks;
  }

  /// 上传单个数据块
  Future<bool> _uploadChunk(Map<String, dynamic> chunk, String authToken, int chunkIndex, int totalChunks) async {
    try {
      final chunkString = jsonEncode(chunk['data']);
      final chunkSizeKB = chunkString.length / 1024;
      print('   📊 块大小: ${chunkSizeKB.toStringAsFixed(2)} KB');

      final response = await http.post(
        Uri.parse('$_baseUrl/sync/upload?_api_path=sync/upload'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'data': chunk['data'],
          'timestamp': chunk['timestamp'],
          'envId': _envId,
          'chunkInfo': {
            'type': chunk['type'],
            'index': chunkIndex,
            'total': totalChunks,
            'batchInfo': chunk['batchInfo'], // 小说批次信息
          }
        }),
      ).timeout(Duration(seconds: 30)); // 30秒超时

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          return true;
        } else {
          print('   ❌ 服务器返回错误: ${responseData['message']}');
          return false;
        }
      } else {
        print('   ❌ HTTP错误: ${response.statusCode}');
        print('   📄 响应内容: ${response.body}');
        return false;
      }

    } catch (e) {
      print('   ❌ 上传块异常: $e');
      return false;
    }
  }

  /// 直接上传数据（保留作为备用方法）
  Future<bool> _uploadDirectly(Map<String, dynamic> data, String authToken) async {
    try {
      print('📤 开始直接上传到CloudBase...');
      print('🔍 上传URL: $_baseUrl/sync/upload');
      print('🔍 认证Token: ${authToken.length > 20 ? '${authToken.substring(0, 20)}...' : authToken}');

      final response = await http.post(
        Uri.parse('$_baseUrl/sync/upload'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
        body: jsonEncode({
          'data': data,
          'timestamp': DateTime.now().toIso8601String(),
          'envId': _envId,
        }),
      ).timeout(Duration(seconds: 60));

      print('🔍 上传响应状态: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          print('✅ CloudBase数据上传成功');
          print('   数据大小: ${(jsonEncode(data).length / 1024).toStringAsFixed(2)} KB');
          return true;
        } else {
          print('❌ CloudBase API返回错误: ${responseData['message']}');
          return false;
        }
      } else {
        print('❌ CloudBase上传失败: ${response.statusCode}');
        print('   响应内容: ${response.body}');
        return false;
      }

    } catch (e) {
      print('❌ CloudBase直接上传异常: $e');
      return false;
    }
  }

  /// 分批上传数据
  Future<bool> _uploadInBatches(Map<String, dynamic> data, String authToken) async {
    try {
      print('📦 开始分批上传到CloudBase...');

      // 提取小说数据进行分批
      final novels = data['novels'] as List? ?? [];
      if (novels.isEmpty) {
        print('❌ 没有小说数据需要上传');
        return false;
      }

      // 按每批3本小说分批（确保每批小于5MB）
      const batchSize = 3;
      final totalBatches = (novels.length / batchSize).ceil();
      
      print('📦 分为 $totalBatches 批上传（每批$batchSize本小说）');

      for (int i = 0; i < totalBatches; i++) {
        final startIndex = i * batchSize;
        final endIndex = (startIndex + batchSize < novels.length) ? startIndex + batchSize : novels.length;
        final batchNovels = novels.sublist(startIndex, endIndex);
        
        final batchData = {
          'novels': batchNovels,
          'batchInfo': {
            'batchIndex': i + 1,
            'totalBatches': totalBatches,
            'isLastBatch': i == totalBatches - 1,
          },
          'timestamp': DateTime.now().toIso8601String(),
        };

        // 如果是最后一批，包含其他数据
        if (i == totalBatches - 1) {
          batchData['knowledgeDocuments'] = data['knowledgeDocuments'] ?? [];
          batchData['characterCards'] = data['characterCards'] ?? [];
          batchData['characterTypes'] = data['characterTypes'] ?? [];
          batchData['userSettings'] = data['userSettings'] ?? {};
        }

        print('📤 上传批次 ${i + 1}/$totalBatches (${batchNovels.length}本小说)...');
        
        final success = await _uploadDirectly(batchData, authToken);
        if (!success) {
          print('❌ 批次 ${i + 1} 上传失败');
          return false;
        }

        print('✅ 批次 ${i + 1} 上传成功');
        
        // 批次间延迟
        if (i < totalBatches - 1) {
          await Future.delayed(Duration(milliseconds: 1000));
        }
      }

      print('🎉 所有批次上传完成！');
      return true;

    } catch (e) {
      print('❌ 分批上传异常: $e');
      return false;
    }
  }

  /// 从CloudBase下载数据
  Future<Map<String, dynamic>?> downloadData(String authToken) async {
    try {
      print('📥 开始从CloudBase下载数据...');

      final response = await http.get(
        Uri.parse('$_baseUrl/sync/download'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $authToken',
        },
      ).timeout(Duration(seconds: 30));

      print('🔍 下载响应状态: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        if (responseData['success'] == true) {
          print('✅ CloudBase数据下载成功');
          final data = responseData['data'] as Map<String, dynamic>?;
          if (data != null) {
            print('   下载数据大小: ${(response.body.length / 1024).toStringAsFixed(2)} KB');
            return data;
          } else {
            print('ℹ️ 云端暂无数据');
            return null;
          }
        } else {
          print('❌ CloudBase API返回错误: ${responseData['message']}');
          return null;
        }
      } else {
        print('❌ CloudBase下载失败: ${response.statusCode}');
        print('   响应内容: ${response.body}');
        return null;
      }

    } catch (e) {
      print('❌ CloudBase下载异常: $e');
      return null;
    }
  }

  /// 检查CloudBase连接状态
  Future<bool> checkConnection() async {
    try {
      print('🔍 检查CloudBase连接状态...');
      print('🔍 连接地址: $_baseUrl/health?_api_path=health');

      final response = await http.get(
        Uri.parse('$_baseUrl/health?_api_path=health'),
        headers: {
          'Content-Type': 'application/json',
        },
      ).timeout(Duration(seconds: 10));

      print('🔍 响应状态码: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        print('📊 服务器响应: $data');
        // 检查正确的状态字段
        if (data['status'] == 'healthy') {
          print('✅ CloudBase连接正常');
          print('📊 服务器环境: ${data['environment']}');
          print('📊 服务器消息: ${data['message']}');
          return true;
        }
      }

      print('❌ CloudBase连接异常: 状态码 ${response.statusCode}');
      print('📄 响应内容: ${response.body}');
      return false;

    } catch (e) {
      print('❌ CloudBase连接检查失败: $e');
      print('🔍 请检查服务器是否运行在 $_baseUrl');
      return false;
    }
  }

  /// 显示成功提示
  void showSuccessMessage(String message) {
    Get.snackbar(
      '同步成功',
      message,
      backgroundColor: Get.theme.primaryColor.withOpacity(0.1),
      colorText: Get.theme.primaryColor,
      duration: Duration(seconds: 3),
    );
  }

  /// 显示错误提示
  void showErrorMessage(String message) {
    Get.snackbar(
      '同步失败',
      message,
      backgroundColor: Get.theme.colorScheme.error.withOpacity(0.1),
      colorText: Get.theme.colorScheme.error,
      duration: Duration(seconds: 5),
    );
  }
}
