import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("mps.tencentcloudapi.com", "2019-06-12", clientConfig);
    }
    async DescribeAsrHotwords(req, cb) {
        return this.request("DescribeAsrHotwords", req, cb);
    }
    async CreateSnapshotByTimeOffsetTemplate(req, cb) {
        return this.request("CreateSnapshotByTimeOffsetTemplate", req, cb);
    }
    async DescribeStreamLinkFlowRealtimeStatus(req, cb) {
        return this.request("DescribeStreamLinkFlowRealtimeStatus", req, cb);
    }
    async CreateAnimatedGraphicsTemplate(req, cb) {
        return this.request("CreateAnimatedGraphicsTemplate", req, cb);
    }
    async ModifySmartSubtitleTemplate(req, cb) {
        return this.request("ModifySmartSubtitleTemplate", req, cb);
    }
    async DescribeBatchTaskDetail(req, cb) {
        return this.request("DescribeBatchTaskDetail", req, cb);
    }
    async ModifyLiveRecordTemplate(req, cb) {
        return this.request("ModifyLiveRecordTemplate", req, cb);
    }
    async EditMedia(req, cb) {
        return this.request("EditMedia", req, cb);
    }
    async DescribeStreamLinkEvent(req, cb) {
        return this.request("DescribeStreamLinkEvent", req, cb);
    }
    async DeleteAnimatedGraphicsTemplate(req, cb) {
        return this.request("DeleteAnimatedGraphicsTemplate", req, cb);
    }
    async DescribeAIAnalysisTemplates(req, cb) {
        return this.request("DescribeAIAnalysisTemplates", req, cb);
    }
    async DeleteSmartSubtitleTemplate(req, cb) {
        return this.request("DeleteSmartSubtitleTemplate", req, cb);
    }
    async ParseLiveStreamProcessNotification(req, cb) {
        return this.request("ParseLiveStreamProcessNotification", req, cb);
    }
    async DeleteTranscodeTemplate(req, cb) {
        return this.request("DeleteTranscodeTemplate", req, cb);
    }
    async DescribeTaskDetail(req, cb) {
        return this.request("DescribeTaskDetail", req, cb);
    }
    async DescribeWordSamples(req, cb) {
        return this.request("DescribeWordSamples", req, cb);
    }
    async ProcessMedia(req, cb) {
        return this.request("ProcessMedia", req, cb);
    }
    async ModifyAIAnalysisTemplate(req, cb) {
        return this.request("ModifyAIAnalysisTemplate", req, cb);
    }
    async DeleteAdaptiveDynamicStreamingTemplate(req, cb) {
        return this.request("DeleteAdaptiveDynamicStreamingTemplate", req, cb);
    }
    async CreateAdaptiveDynamicStreamingTemplate(req, cb) {
        return this.request("CreateAdaptiveDynamicStreamingTemplate", req, cb);
    }
    async DisableWorkflow(req, cb) {
        return this.request("DisableWorkflow", req, cb);
    }
    async DescribeSampleSnapshotTemplates(req, cb) {
        return this.request("DescribeSampleSnapshotTemplates", req, cb);
    }
    async DescribeStreamLinkFlowStatistics(req, cb) {
        return this.request("DescribeStreamLinkFlowStatistics", req, cb);
    }
    async BatchDeleteStreamLinkFlow(req, cb) {
        return this.request("BatchDeleteStreamLinkFlow", req, cb);
    }
    async DescribeLiveRecordTemplates(req, cb) {
        return this.request("DescribeLiveRecordTemplates", req, cb);
    }
    async DeleteAsrHotwords(req, cb) {
        return this.request("DeleteAsrHotwords", req, cb);
    }
    async EnableWorkflow(req, cb) {
        return this.request("EnableWorkflow", req, cb);
    }
    async RecognizeMediaForZhiXue(req, cb) {
        return this.request("RecognizeMediaForZhiXue", req, cb);
    }
    async DescribeAsrHotwordsList(req, cb) {
        return this.request("DescribeAsrHotwordsList", req, cb);
    }
    async ModifyStreamLinkFlow(req, cb) {
        return this.request("ModifyStreamLinkFlow", req, cb);
    }
    async DescribeTasks(req, cb) {
        return this.request("DescribeTasks", req, cb);
    }
    async CreateWordSamples(req, cb) {
        return this.request("CreateWordSamples", req, cb);
    }
    async CreateTranscodeTemplate(req, cb) {
        return this.request("CreateTranscodeTemplate", req, cb);
    }
    async CreateStreamLinkFlow(req, cb) {
        return this.request("CreateStreamLinkFlow", req, cb);
    }
    async ExecuteFunction(req, cb) {
        return this.request("ExecuteFunction", req, cb);
    }
    async BatchProcessMedia(req, cb) {
        return this.request("BatchProcessMedia", req, cb);
    }
    async DescribeStreamLinkFlows(req, cb) {
        return this.request("DescribeStreamLinkFlows", req, cb);
    }
    async ModifyAnimatedGraphicsTemplate(req, cb) {
        return this.request("ModifyAnimatedGraphicsTemplate", req, cb);
    }
    async DescribeImageTaskDetail(req, cb) {
        return this.request("DescribeImageTaskDetail", req, cb);
    }
    async DeleteStreamLinkOutput(req, cb) {
        return this.request("DeleteStreamLinkOutput", req, cb);
    }
    async CreateContentReviewTemplate(req, cb) {
        return this.request("CreateContentReviewTemplate", req, cb);
    }
    async DescribeGroupAttachFlowsById(req, cb) {
        return this.request("DescribeGroupAttachFlowsById", req, cb);
    }
    async CreateSampleSnapshotTemplate(req, cb) {
        return this.request("CreateSampleSnapshotTemplate", req, cb);
    }
    async DeleteAIAnalysisTemplate(req, cb) {
        return this.request("DeleteAIAnalysisTemplate", req, cb);
    }
    async ModifySchedule(req, cb) {
        return this.request("ModifySchedule", req, cb);
    }
    async DescribeMediaMetaData(req, cb) {
        return this.request("DescribeMediaMetaData", req, cb);
    }
    async DescribeVideoSearchTaskDetail(req, cb) {
        return this.request("DescribeVideoSearchTaskDetail", req, cb);
    }
    async ModifySampleSnapshotTemplate(req, cb) {
        return this.request("ModifySampleSnapshotTemplate", req, cb);
    }
    async ResetWorkflow(req, cb) {
        return this.request("ResetWorkflow", req, cb);
    }
    async DeleteQualityControlTemplate(req, cb) {
        return this.request("DeleteQualityControlTemplate", req, cb);
    }
    async DeleteWorkflow(req, cb) {
        return this.request("DeleteWorkflow", req, cb);
    }
    async CreateMediaEvaluation(req, cb) {
        return this.request("CreateMediaEvaluation", req, cb);
    }
    async CreateQualityControlTemplate(req, cb) {
        return this.request("CreateQualityControlTemplate", req, cb);
    }
    async ModifyImageSpriteTemplate(req, cb) {
        return this.request("ModifyImageSpriteTemplate", req, cb);
    }
    async DescribeTranscodeTemplates(req, cb) {
        return this.request("DescribeTranscodeTemplates", req, cb);
    }
    async ModifyStreamLinkSecurityGroup(req, cb) {
        return this.request("ModifyStreamLinkSecurityGroup", req, cb);
    }
    async DeleteStreamLinkFlow(req, cb) {
        return this.request("DeleteStreamLinkFlow", req, cb);
    }
    async StartStreamLinkFlow(req, cb) {
        return this.request("StartStreamLinkFlow", req, cb);
    }
    async ProcessImage(req, cb) {
        return this.request("ProcessImage", req, cb);
    }
    async ModifyStreamLinkInput(req, cb) {
        return this.request("ModifyStreamLinkInput", req, cb);
    }
    async CreateSmartSubtitleTemplate(req, cb) {
        return this.request("CreateSmartSubtitleTemplate", req, cb);
    }
    async DescribeStreamLinkRegions(req, cb) {
        return this.request("DescribeStreamLinkRegions", req, cb);
    }
    async DescribeSchedules(req, cb) {
        return this.request("DescribeSchedules", req, cb);
    }
    async ModifyPersonSample(req, cb) {
        return this.request("ModifyPersonSample", req, cb);
    }
    async CreateLiveRecordTemplate(req, cb) {
        return this.request("CreateLiveRecordTemplate", req, cb);
    }
    async DescribeStreamLinkSecurityGroups(req, cb) {
        return this.request("DescribeStreamLinkSecurityGroups", req, cb);
    }
    async DeleteStreamLinkSecurityGroup(req, cb) {
        return this.request("DeleteStreamLinkSecurityGroup", req, cb);
    }
    async CreateAIAnalysisTemplate(req, cb) {
        return this.request("CreateAIAnalysisTemplate", req, cb);
    }
    async ModifyQualityControlTemplate(req, cb) {
        return this.request("ModifyQualityControlTemplate", req, cb);
    }
    async StopStreamLinkFlow(req, cb) {
        return this.request("StopStreamLinkFlow", req, cb);
    }
    async DescribeStreamLinkEventAttachedFlows(req, cb) {
        return this.request("DescribeStreamLinkEventAttachedFlows", req, cb);
    }
    async DescribeSmartSubtitleTemplates(req, cb) {
        return this.request("DescribeSmartSubtitleTemplates", req, cb);
    }
    async ModifyStreamLinkOutputInfo(req, cb) {
        return this.request("ModifyStreamLinkOutputInfo", req, cb);
    }
    async DescribeSnapshotByTimeOffsetTemplates(req, cb) {
        return this.request("DescribeSnapshotByTimeOffsetTemplates", req, cb);
    }
    async ModifyTranscodeTemplate(req, cb) {
        return this.request("ModifyTranscodeTemplate", req, cb);
    }
    async DescribeContentReviewTemplates(req, cb) {
        return this.request("DescribeContentReviewTemplates", req, cb);
    }
    async DescribeStreamLinkEvents(req, cb) {
        return this.request("DescribeStreamLinkEvents", req, cb);
    }
    async ModifyAsrHotwords(req, cb) {
        return this.request("ModifyAsrHotwords", req, cb);
    }
    async DescribeStreamLinkFlowSRTStatistics(req, cb) {
        return this.request("DescribeStreamLinkFlowSRTStatistics", req, cb);
    }
    async CreateStreamLinkOutputInfo(req, cb) {
        return this.request("CreateStreamLinkOutputInfo", req, cb);
    }
    async DescribeWorkflows(req, cb) {
        return this.request("DescribeWorkflows", req, cb);
    }
    async ModifyWatermarkTemplate(req, cb) {
        return this.request("ModifyWatermarkTemplate", req, cb);
    }
    async CreateVideoSearchTask(req, cb) {
        return this.request("CreateVideoSearchTask", req, cb);
    }
    async DescribeStreamLinkActivateState(req, cb) {
        return this.request("DescribeStreamLinkActivateState", req, cb);
    }
    async CreateImageSpriteTemplate(req, cb) {
        return this.request("CreateImageSpriteTemplate", req, cb);
    }
    async DescribePersonSamples(req, cb) {
        return this.request("DescribePersonSamples", req, cb);
    }
    async ParseNotification(req, cb) {
        return this.request("ParseNotification", req, cb);
    }
    async DeleteAIRecognitionTemplate(req, cb) {
        return this.request("DeleteAIRecognitionTemplate", req, cb);
    }
    async DescribeAnimatedGraphicsTemplates(req, cb) {
        return this.request("DescribeAnimatedGraphicsTemplates", req, cb);
    }
    async DeleteWordSamples(req, cb) {
        return this.request("DeleteWordSamples", req, cb);
    }
    async ManageTask(req, cb) {
        return this.request("ManageTask", req, cb);
    }
    async DescribeVideoDatabaseEntryTaskDetail(req, cb) {
        return this.request("DescribeVideoDatabaseEntryTaskDetail", req, cb);
    }
    async ModifySnapshotByTimeOffsetTemplate(req, cb) {
        return this.request("ModifySnapshotByTimeOffsetTemplate", req, cb);
    }
    async BatchStartStreamLinkFlow(req, cb) {
        return this.request("BatchStartStreamLinkFlow", req, cb);
    }
    async CreateWorkflow(req, cb) {
        return this.request("CreateWorkflow", req, cb);
    }
    async DescribeAdaptiveDynamicStreamingTemplates(req, cb) {
        return this.request("DescribeAdaptiveDynamicStreamingTemplates", req, cb);
    }
    async CreateSchedule(req, cb) {
        return this.request("CreateSchedule", req, cb);
    }
    async ModifyWordSample(req, cb) {
        return this.request("ModifyWordSample", req, cb);
    }
    async DescribeImageSpriteTemplates(req, cb) {
        return this.request("DescribeImageSpriteTemplates", req, cb);
    }
    async DescribeWatermarkTemplates(req, cb) {
        return this.request("DescribeWatermarkTemplates", req, cb);
    }
    async CreateWatermarkTemplate(req, cb) {
        return this.request("CreateWatermarkTemplate", req, cb);
    }
    async ModifyStreamLinkEvent(req, cb) {
        return this.request("ModifyStreamLinkEvent", req, cb);
    }
    async DescribeAIRecognitionTemplates(req, cb) {
        return this.request("DescribeAIRecognitionTemplates", req, cb);
    }
    async WithdrawsWatermark(req, cb) {
        return this.request("WithdrawsWatermark", req, cb);
    }
    async DescribeStreamLinkFlow(req, cb) {
        return this.request("DescribeStreamLinkFlow", req, cb);
    }
    async DeleteSchedule(req, cb) {
        return this.request("DeleteSchedule", req, cb);
    }
    async ModifyAdaptiveDynamicStreamingTemplate(req, cb) {
        return this.request("ModifyAdaptiveDynamicStreamingTemplate", req, cb);
    }
    async DeleteWatermarkTemplate(req, cb) {
        return this.request("DeleteWatermarkTemplate", req, cb);
    }
    async DeletePersonSample(req, cb) {
        return this.request("DeletePersonSample", req, cb);
    }
    async DeleteSnapshotByTimeOffsetTemplate(req, cb) {
        return this.request("DeleteSnapshotByTimeOffsetTemplate", req, cb);
    }
    async EnableSchedule(req, cb) {
        return this.request("EnableSchedule", req, cb);
    }
    async CreateVideoDatabaseEntryTask(req, cb) {
        return this.request("CreateVideoDatabaseEntryTask", req, cb);
    }
    async ProcessLiveStream(req, cb) {
        return this.request("ProcessLiveStream", req, cb);
    }
    async DeleteLiveRecordTemplate(req, cb) {
        return this.request("DeleteLiveRecordTemplate", req, cb);
    }
    async DeleteContentReviewTemplate(req, cb) {
        return this.request("DeleteContentReviewTemplate", req, cb);
    }
    async DeleteSampleSnapshotTemplate(req, cb) {
        return this.request("DeleteSampleSnapshotTemplate", req, cb);
    }
    async DescribeStreamLinkFlowMediaStatistics(req, cb) {
        return this.request("DescribeStreamLinkFlowMediaStatistics", req, cb);
    }
    async CreateAsrHotwords(req, cb) {
        return this.request("CreateAsrHotwords", req, cb);
    }
    async CreateStreamLinkEvent(req, cb) {
        return this.request("CreateStreamLinkEvent", req, cb);
    }
    async CreatePersonSample(req, cb) {
        return this.request("CreatePersonSample", req, cb);
    }
    async DescribeQualityControlTemplates(req, cb) {
        return this.request("DescribeQualityControlTemplates", req, cb);
    }
    async CreateStreamLinkSecurityGroup(req, cb) {
        return this.request("CreateStreamLinkSecurityGroup", req, cb);
    }
    async DisableSchedule(req, cb) {
        return this.request("DisableSchedule", req, cb);
    }
    async CreateStreamLinkInput(req, cb) {
        return this.request("CreateStreamLinkInput", req, cb);
    }
    async DisassociateSecurityGroup(req, cb) {
        return this.request("DisassociateSecurityGroup", req, cb);
    }
    async ModifyContentReviewTemplate(req, cb) {
        return this.request("ModifyContentReviewTemplate", req, cb);
    }
    async CreateAIRecognitionTemplate(req, cb) {
        return this.request("CreateAIRecognitionTemplate", req, cb);
    }
    async BatchStopStreamLinkFlow(req, cb) {
        return this.request("BatchStopStreamLinkFlow", req, cb);
    }
    async ModifyAIRecognitionTemplate(req, cb) {
        return this.request("ModifyAIRecognitionTemplate", req, cb);
    }
    async DeleteStreamLinkEvent(req, cb) {
        return this.request("DeleteStreamLinkEvent", req, cb);
    }
    async DescribeStreamLinkFlowLogs(req, cb) {
        return this.request("DescribeStreamLinkFlowLogs", req, cb);
    }
    async DeleteImageSpriteTemplate(req, cb) {
        return this.request("DeleteImageSpriteTemplate", req, cb);
    }
}
