import 'package:get/get.dart';
import 'package:hive/hive.dart';
import 'package:novel_app/models/agent_message.dart';
import 'package:novel_app/models/agent_session.dart';
import 'package:flutter/foundation.dart' show kIsWeb;

/// Agent历史记录服务，用于管理岱宗AI辅助助手的对话历史
class AgentHistoryService extends GetxService {
  static const String _messagesBoxName = 'agent_messages';
  static const String _sessionsBoxName = 'agent_sessions';
  
  late Box<Map> _messagesBox;
  Box<AgentSession>? _sessionsBox;

  // 当前加载的消息历史
  final RxList<AgentMessage> messages = <AgentMessage>[].obs;

  // 当前选中的会话
  final Rx<AgentSession?> currentSession = Rx<AgentSession?>(null);

  // 所有会话
  final RxList<AgentSession> sessions = <AgentSession>[].obs;

  /// 初始化服务
  Future<AgentHistoryService> init() async {
    try {
      print('[AgentHistoryService] 开始初始化');

      // 打开消息Box
      try {
        if (Hive.isBoxOpen(_messagesBoxName)) {
          _messagesBox = Hive.box<Map>(_messagesBoxName);
          print('[AgentHistoryService] 消息盒子已经打开，直接使用');
        } else {
          _messagesBox = await Hive.openBox<Map>(_messagesBoxName);
          print('[AgentHistoryService] 已打开消息盒子');
        }
      } catch (e) {
        print('[AgentHistoryService] 打开消息盒子失败: $e');
        _messagesBox = await Hive.openBox<Map>(_messagesBoxName, path: '');
      }

      // 打开会话Box
      try {
        if (Hive.isBoxOpen(_sessionsBoxName)) {
          _sessionsBox = Hive.box<AgentSession>(_sessionsBoxName);
          print('[AgentHistoryService] 会话盒子已经打开，直接使用');
        } else {
          _sessionsBox = await Hive.openBox<AgentSession>(_sessionsBoxName);
          print('[AgentHistoryService] 已打开会话盒子');
        }

        // 加载所有会话
        _loadAllSessions();

        print('[AgentHistoryService] 初始化成功，已加载 ${_messagesBox.length} 条消息，${_sessionsBox?.length ?? 0} 个会话');
      } catch (e) {
        print('[AgentHistoryService] 初始化会话盒子失败: $e');
        try {
          if (!Hive.isBoxOpen(_sessionsBoxName)) {
            _sessionsBox = await Hive.openBox<AgentSession>(_sessionsBoxName, path: '');
            print('[AgentHistoryService] 成功创建内存会话盒子');
          } else {
            _sessionsBox = Hive.box<AgentSession>(_sessionsBoxName);
            print('[AgentHistoryService] 成功获取已打开的内存会话盒子');
          }
        } catch (innerE) {
          print('[AgentHistoryService] 创建内存会话盒子也失败: $innerE');
          _sessionsBox = null;
        }
        sessions.clear();
      }

      return this;
    } catch (e) {
      print('[AgentHistoryService] 初始化失败: $e');
      rethrow;
    }
  }

  /// 加载所有会话
  void _loadAllSessions() {
    if (_sessionsBox == null) return;

    try {
      final allSessions = _sessionsBox!.values.toList();
      
      // 按最后更新时间排序
      allSessions.sort((a, b) => b.lastUpdatedAt.compareTo(a.lastUpdatedAt));
      
      sessions.assignAll(allSessions);
      print('[AgentHistoryService] 已加载 ${sessions.length} 个会话');
    } catch (e) {
      print('[AgentHistoryService] 加载会话失败: $e');
      sessions.clear();
    }
  }

  /// 创建新会话
  Future<AgentSession> createSession({
    required String novelTitle,
    required AgentSessionType type,
    int? chapterNumber,
    String? customTitle,
  }) async {
    try {
      if (_sessionsBox == null) {
        throw Exception('会话盒子未初始化，无法创建会话');
      }

      // 将之前的活跃会话设为非活跃
      await _deactivateAllSessions();

      final session = type == AgentSessionType.chat
          ? AgentSession.createChat(
              novelTitle: novelTitle,
              chapterNumber: chapterNumber,
              customTitle: customTitle,
            )
          : AgentSession.createCreative(
              novelTitle: novelTitle,
              chapterNumber: chapterNumber,
              customTitle: customTitle,
            );

      session.setActive(true);
      await _sessionsBox!.put(session.id, session);

      // 重新加载所有会话
      _loadAllSessions();

      // 设置为当前会话
      currentSession.value = session;

      // 清空当前消息列表
      messages.clear();

      print('[AgentHistoryService] 已创建新会话: ${session.title}');
      return session;
    } catch (e) {
      print('[AgentHistoryService] 创建会话失败: $e');
      rethrow;
    }
  }

  /// 将所有会话设为非活跃
  Future<void> _deactivateAllSessions() async {
    if (_sessionsBox == null) return;

    try {
      for (final session in _sessionsBox!.values) {
        if (session.isActive) {
          session.setActive(false);
          await _sessionsBox!.put(session.id, session);
        }
      }
    } catch (e) {
      print('[AgentHistoryService] 取消活跃会话失败: $e');
    }
  }

  /// 加载指定会话的历史记录
  Future<List<AgentMessage>> loadSessionHistory(String sessionId) async {
    try {
      if (_sessionsBox == null) {
        throw Exception('会话盒子未初始化，无法加载会话');
      }

      // 查找会话
      final session = _sessionsBox!.get(sessionId);
      if (session == null) {
        throw Exception('会话不存在');
      }

      // 设置为当前会话
      currentSession.value = session;

      // 清空当前消息列表
      messages.clear();

      // 从Hive加载数据
      final allMessages = _messagesBox.values.toList();

      // 过滤出指定会话的消息
      final sessionMessages = allMessages
          .where((map) => map['sessionId'] == sessionId)
          .toList();

      // 按时间戳排序
      sessionMessages.sort(
          (a, b) => (a['timestamp'] as int).compareTo(b['timestamp'] as int));

      // 转换为AgentMessage对象
      final result = sessionMessages
          .map((map) => AgentMessage.fromMap(Map<String, dynamic>.from(map)))
          .toList();

      messages.assignAll(result);

      // 更新会话的最后更新时间
      session.updateLastUpdatedAt();
      await _sessionsBox!.put(sessionId, session);

      print('[AgentHistoryService] 已加载 ${messages.length} 条会话 "$sessionId" 的消息');
      return messages;
    } catch (e) {
      print('[AgentHistoryService] 加载会话历史失败: $e');
      return [];
    }
  }

  /// 添加消息
  Future<void> addMessage(AgentMessage message) async {
    try {
      final messageMap = message.toMap();
      await _messagesBox.put(message.id, messageMap);
      
      // 添加到当前消息列表
      messages.add(message);

      // 如果有当前会话，更新会话的最后更新时间
      if (currentSession.value != null && _sessionsBox != null) {
        final session = currentSession.value!;
        session.updateLastUpdatedAt();
        await _sessionsBox!.put(session.id, session);
        
        // 重新加载会话列表以更新排序
        _loadAllSessions();
      }

      print('[AgentHistoryService] 已添加消息: ${message.id}');
    } catch (e) {
      print('[AgentHistoryService] 添加消息失败: $e');
    }
  }

  /// 删除会话
  Future<void> deleteSession(String sessionId) async {
    try {
      if (_sessionsBox == null) return;

      // 删除会话相关的所有消息
      final messagesToDelete = _messagesBox.values
          .where((map) => map['sessionId'] == sessionId)
          .toList();

      for (final messageMap in messagesToDelete) {
        await _messagesBox.delete(messageMap['id']);
      }

      // 删除会话
      await _sessionsBox!.delete(sessionId);

      // 如果删除的是当前会话，清空当前状态
      if (currentSession.value?.id == sessionId) {
        currentSession.value = null;
        messages.clear();
      }

      // 重新加载会话列表
      _loadAllSessions();

      print('[AgentHistoryService] 已删除会话: $sessionId');
    } catch (e) {
      print('[AgentHistoryService] 删除会话失败: $e');
    }
  }

  /// 获取指定小说的所有会话
  List<AgentSession> getSessionsForNovel(String novelTitle) {
    return sessions.where((session) => session.novelTitle == novelTitle).toList();
  }

  /// 获取当前活跃会话
  AgentSession? getActiveSession() {
    return sessions.firstWhereOrNull((session) => session.isActive);
  }

  @override
  void onClose() {
    // 关闭时不需要手动关闭Box，Hive会自动管理
    super.onClose();
  }
}
