{"buildFiles": ["D:\\element\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\vs code\\novel_new\\novel_app\\android\\app\\.cxx\\RelWithDebInfo\\4u3d1b1p\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\vs code\\novel_new\\novel_app\\android\\app\\.cxx\\RelWithDebInfo\\4u3d1b1p\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}