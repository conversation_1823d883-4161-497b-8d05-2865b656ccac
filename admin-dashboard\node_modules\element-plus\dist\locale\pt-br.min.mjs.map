{"version": 3, "file": "pt-br.min.mjs", "sources": ["../../../../packages/locale/lang/pt-br.ts"], "sourcesContent": ["export default {\n  name: 'pt-br',\n  el: {\n    breadcrumb: {\n      label: 'Breadcrumb', // to be translated\n    },\n    colorpicker: {\n      confirm: 'Confirmar',\n      clear: '<PERSON><PERSON>',\n    },\n    datepicker: {\n      now: '<PERSON><PERSON>a',\n      today: '<PERSON><PERSON>',\n      cancel: 'Cancelar',\n      clear: '<PERSON><PERSON>',\n      confirm: 'Confirmar',\n      selectDate: 'Selecione a data',\n      selectTime: 'Selecione a hora',\n      startDate: 'Data inicial',\n      startTime: 'Hora inicial',\n      endDate: 'Data final',\n      endTime: 'Hora final',\n      prevYear: 'Ano anterior',\n      nextYear: 'Próximo ano',\n      prevMonth: 'Mês anterior',\n      nextMonth: 'Próximo mês',\n      year: '',\n      month1: 'Janeiro',\n      month2: 'Fevereiro',\n      month3: 'Março',\n      month4: 'Abril',\n      month5: 'Maio',\n      month6: 'Junho',\n      month7: 'Julho',\n      month8: 'Agosto',\n      month9: 'Setem<PERSON>',\n      month10: 'Outubro',\n      month11: 'Novembro',\n      month12: 'Dezembro',\n      // week: 'semana',\n      weeks: {\n        sun: 'Dom',\n        mon: 'Seg',\n        tue: 'Ter',\n        wed: 'Qua',\n        thu: 'Qui',\n        fri: 'Sex',\n        sat: 'Sab',\n      },\n      months: {\n        jan: 'Jan',\n        feb: 'Fev',\n        mar: 'Mar',\n        apr: 'Abr',\n        may: 'Mai',\n        jun: 'Jun',\n        jul: 'Jul',\n        aug: 'Ago',\n        sep: 'Set',\n        oct: 'Out',\n        nov: 'Nov',\n        dec: 'Dez',\n      },\n    },\n    select: {\n      loading: 'Carregando',\n      noMatch: 'Sem resultados',\n      noData: 'Sem dados',\n      placeholder: 'Selecione',\n    },\n    mention: {\n      loading: 'Carregando',\n    },\n    cascader: {\n      noMatch: 'Sem resultados',\n      loading: 'Carregando',\n      placeholder: 'Selecione',\n      noData: 'Sem dados',\n    },\n    pagination: {\n      goto: 'Ir para',\n      pagesize: '/página',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mensagem',\n      confirm: 'Confirmar',\n      cancel: 'Cancelar',\n      error: 'Erro!',\n    },\n    upload: {\n      deleteTip: 'aperte delete para apagar',\n      delete: 'Apagar',\n      preview: 'Pré-visualizar',\n      continue: 'Continuar',\n    },\n    table: {\n      emptyText: 'Sem dados',\n      confirmFilter: 'Confirmar',\n      resetFilter: 'Limpar',\n      clearFilter: 'Todos',\n      sumText: 'Total',\n    },\n    tour: {\n      next: 'Próximo',\n      previous: 'Anterior',\n      finish: 'Finalizar',\n    },\n    tree: {\n      emptyText: 'Sem dados',\n    },\n    transfer: {\n      noMatch: 'Sem resultados',\n      noData: 'Sem dados',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Digite uma palavra-chave',\n      noCheckedFormat: '{total} itens',\n      hasCheckedFormat: '{checked}/{total} selecionados',\n    },\n    image: {\n      error: 'Erro ao carregar imagem',\n    },\n    pageHeader: {\n      title: 'Voltar',\n    },\n    popconfirm: {\n      confirmButtonText: 'Sim',\n      cancelButtonText: 'Não',\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": ";;AAAA,WAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,UAAU,CAAC,kBAAkB,CAAC,UAAU,CAAC,kBAAkB,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,CAAC,eAAe,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,eAAe,CAAC,eAAe,CAAC,gBAAgB,CAAC,gCAAgC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC,UAAU,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC,SAAS,CAAC,qBAAqB,CAAC,UAAU,CAAC,sBAAsB,CAAC,SAAS,CAAC,kCAAkC,CAAC,CAAC,CAAC;;;;"}