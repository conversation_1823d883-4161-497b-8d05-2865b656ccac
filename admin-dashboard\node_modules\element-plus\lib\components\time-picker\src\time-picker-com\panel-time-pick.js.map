{"version": 3, "file": "panel-time-pick.js", "sources": ["../../../../../../../packages/components/time-picker/src/time-picker-com/panel-time-pick.vue"], "sourcesContent": ["<template>\n  <transition :name=\"transitionName\">\n    <div v-if=\"actualVisible || visible\" :class=\"ns.b('panel')\">\n      <div :class=\"[ns.be('panel', 'content'), { 'has-seconds': showSeconds }]\">\n        <time-spinner\n          ref=\"spinner\"\n          :role=\"datetimeRole || 'start'\"\n          :arrow-control=\"arrowControl\"\n          :show-seconds=\"showSeconds\"\n          :am-pm-mode=\"amPmMode\"\n          :spinner-date=\"(parsedValue as any)\"\n          :disabled-hours=\"disabledHours\"\n          :disabled-minutes=\"disabledMinutes\"\n          :disabled-seconds=\"disabledSeconds\"\n          @change=\"handleChange\"\n          @set-option=\"onSetOption\"\n          @select-range=\"setSelectionRange\"\n        />\n      </div>\n      <div :class=\"ns.be('panel', 'footer')\">\n        <button\n          type=\"button\"\n          :class=\"[ns.be('panel', 'btn'), 'cancel']\"\n          @click=\"handleCancel\"\n        >\n          {{ t('el.datepicker.cancel') }}\n        </button>\n        <button\n          type=\"button\"\n          :class=\"[ns.be('panel', 'btn'), 'confirm']\"\n          @click=\"handleConfirm()\"\n        >\n          {{ t('el.datepicker.confirm') }}\n        </button>\n      </div>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref } from 'vue'\nimport dayjs from 'dayjs'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { isUndefined } from '@element-plus/utils'\nimport { PICKER_BASE_INJECTION_KEY } from '../constants'\nimport { panelTimePickerProps } from '../props/panel-time-picker'\nimport { useTimePanel } from '../composables/use-time-panel'\nimport {\n  buildAvailableTimeSlotGetter,\n  useOldValue,\n} from '../composables/use-time-picker'\nimport TimeSpinner from './basic-time-spinner.vue'\n\nimport type { Dayjs } from 'dayjs'\n\nconst props = defineProps(panelTimePickerProps)\nconst emit = defineEmits(['pick', 'select-range', 'set-picker-option'])\n\n// Injections\nconst pickerBase = inject(PICKER_BASE_INJECTION_KEY) as any\nconst {\n  arrowControl,\n  disabledHours,\n  disabledMinutes,\n  disabledSeconds,\n  defaultValue,\n} = pickerBase.props\nconst { getAvailableHours, getAvailableMinutes, getAvailableSeconds } =\n  buildAvailableTimeSlotGetter(disabledHours, disabledMinutes, disabledSeconds)\n\nconst ns = useNamespace('time')\nconst { t, lang } = useLocale()\n// data\nconst selectionRange = ref([0, 2])\nconst oldValue = useOldValue(props)\n// computed\nconst transitionName = computed(() => {\n  return isUndefined(props.actualVisible)\n    ? `${ns.namespace.value}-zoom-in-top`\n    : ''\n})\nconst showSeconds = computed(() => {\n  return props.format.includes('ss')\n})\nconst amPmMode = computed(() => {\n  if (props.format.includes('A')) return 'A'\n  if (props.format.includes('a')) return 'a'\n  return ''\n})\n// method\nconst isValidValue = (_date: Dayjs) => {\n  const parsedDate = dayjs(_date).locale(lang.value)\n  const result = getRangeAvailableTime(parsedDate)\n  return parsedDate.isSame(result)\n}\nconst handleCancel = () => {\n  emit('pick', oldValue.value, false)\n}\nconst handleConfirm = (visible = false, first = false) => {\n  if (first) return\n  emit('pick', props.parsedValue, visible)\n}\nconst handleChange = (_date: Dayjs) => {\n  // visible avoids edge cases, when use scrolls during panel closing animation\n  if (!props.visible) {\n    return\n  }\n  const result = getRangeAvailableTime(_date).millisecond(0)\n  emit('pick', result, true)\n}\n\nconst setSelectionRange = (start: number, end: number) => {\n  emit('select-range', start, end)\n  selectionRange.value = [start, end]\n}\n\nconst changeSelectionRange = (step: number) => {\n  const list = [0, 3].concat(showSeconds.value ? [6] : [])\n  const mapping = ['hours', 'minutes'].concat(\n    showSeconds.value ? ['seconds'] : []\n  )\n  const index = list.indexOf(selectionRange.value[0])\n  const next = (index + step + list.length) % list.length\n  timePickerOptions['start_emitSelectRange'](mapping[next])\n}\n\nconst handleKeydown = (event: KeyboardEvent) => {\n  const code = event.code\n\n  const { left, right, up, down } = EVENT_CODE\n\n  if ([left, right].includes(code)) {\n    const step = code === left ? -1 : 1\n    changeSelectionRange(step)\n    event.preventDefault()\n    return\n  }\n\n  if ([up, down].includes(code)) {\n    const step = code === up ? -1 : 1\n    timePickerOptions['start_scrollDown'](step)\n    event.preventDefault()\n    return\n  }\n}\n\nconst { timePickerOptions, onSetOption, getAvailableTime } = useTimePanel({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n})\n\nconst getRangeAvailableTime = (date: Dayjs) => {\n  return getAvailableTime(date, props.datetimeRole || '', true)\n}\n\nconst parseUserInput = (value: Dayjs) => {\n  if (!value) return null\n  return dayjs(value, props.format).locale(lang.value)\n}\n\nconst formatToString = (value: Dayjs) => {\n  if (!value) return null\n  return value.format(props.format)\n}\n\nconst getDefaultValue = () => {\n  return dayjs(defaultValue).locale(lang.value)\n}\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['handleKeydownInput', handleKeydown])\nemit('set-picker-option', ['getRangeAvailableTime', getRangeAvailableTime])\nemit('set-picker-option', ['getDefaultValue', getDefaultValue])\n</script>\n"], "names": ["inject", "PICKER_BASE_INJECTION_KEY", "buildAvailableTimeSlotGetter", "useNamespace", "useLocale", "ref", "useOldValue", "computed", "isUndefined", "EVENT_CODE", "useTimePanel", "dayjs"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AA4DA,IAAM,MAAA,UAAA,GAAaA,WAAOC,mCAAyB,CAAA,CAAA;AACnD,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,aAAA;AAAA,MACA,eAAA;AAAA,MACA,eAAA;AAAA,MACA,YAAA;AAAA,QACE,UAAW,CAAA,KAAA,CAAA;AACf,IAAM,MAAA,EAAE,mBAAmB,mBAAqB,EAAA,mBAAA,KAC9CC,0CAA6B,CAAA,aAAA,EAAe,iBAAiB,eAAe,CAAA,CAAA;AAE9E,IAAM,MAAA,EAAA,GAAKC,mBAAa,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAM,EAAE,CAAA,EAAG,IAAK,EAAA,GAAIC,iBAAU,EAAA,CAAA;AAE9B,IAAA,MAAM,cAAiB,GAAAC,OAAA,CAAI,CAAC,CAAA,EAAG,CAAC,CAAC,CAAA,CAAA;AACjC,IAAM,MAAA,QAAA,GAAWC,0BAAY,KAAK,CAAA,CAAA;AAElC,IAAM,MAAA,cAAA,GAAiBC,aAAS,MAAM;AACpC,MAAO,OAAAC,iBAAA,CAAY,MAAM,aAAa,CAAA,GAClC,GAAG,EAAG,CAAA,SAAA,CAAU,KAAK,CACrB,YAAA,CAAA,GAAA,EAAA,CAAA;AAAA,KACL,CAAA,CAAA;AACD,IAAM,MAAA,WAAA,GAAcD,aAAS,MAAM;AACjC,MAAO,OAAA,KAAA,CAAM,MAAO,CAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AAAA,KAClC,CAAA,CAAA;AACD,IAAM,MAAA,QAAA,GAAWA,aAAS,MAAM;AAC9B,MAAA,IAAI,KAAM,CAAA,MAAA,CAAO,QAAS,CAAA,GAAG;AAC7B,QAAA,OAAU,GAAA,CAAA;AACV,MAAO,IAAA,KAAA,CAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA;AAAA,QACR,OAAA,GAAA,CAAA;AAED,MAAM,OAAA,EAAA,CAAA;AACJ,KAAA,CAAA,CAAA;AACA,IAAM,MAAA;AACN,MAAO,MAAA,UAAA,6BAAkB,KAAM,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACjC,MAAA,MAAA,GAAA,qBAAA,CAAA,UAAA,CAAA,CAAA;AACA,MAAA,wBAA2B,CAAA,MAAA,CAAA,CAAA;AACzB,KAAK,CAAA;AAA6B,IACpC,MAAA,YAAA,GAAA,MAAA;AACA,MAAA,IAAM,CAAgB,MAAA,EAAA,QAAC,CAAU,KAAA,EAAA,KAAA,CAAA,CAAA;AAC/B,KAAA,CAAA;AACA,IAAK,MAAA,aAAc,GAAA,CAAA,OAAA,GAAA,KAAoB,EAAA,KAAA,GAAA,KAAA,KAAA;AAAA,MACzC,IAAA,KAAA;AACA,QAAM,OAAA;AAEJ,MAAI,IAAA,CAAC,MAAM,EAAS,KAAA,CAAA,WAAA,EAAA,OAAA,CAAA,CAAA;AAClB,KAAA,CAAA;AAAA,IACF,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAA,IAAA,CAAA,KAAe,CAAA,OAAA,EAAA;AACf,QAAK,OAAA;AAAoB,OAC3B;AAEA,MAAM,MAAA,MAAA,GAAA,qBAAoD,CAAA,KAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA;AACxD,MAAK,IAAA,CAAA,MAAA,EAAA,MAAA,EAAgB;AACrB,KAAe,CAAA;AAAmB,IACpC,MAAA,iBAAA,GAAA,CAAA,KAAA,EAAA,GAAA,KAAA;AAEA,MAAM,IAAA,CAAA,cAAA,EAAA,KAAA,EAAwB,GAAiB,CAAA,CAAA;AAC7C,MAAA,cAAc,CAAG,KAAG,GAAA,CAAA,KAAmB,EAAA,GAAA,CAAA,CAAA;AACvC,KAAA,CAAA;AAAqC,IAAA,MACvB,oBAAS,GAAA,CAAA,IAAS,KAAK;AAAA,MACrC,MAAA,IAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,WAAA,CAAA,KAAA,GAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AACA,MAAA,MAAM,UAAa,CAAA,OAAA,EAAA,SAAuB,CAAA,CAAA,MAAA,CAAA,WAAQ,CAAA,KAAA,GAAA,CAAA,SAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AAClD,MAAA,MAAM,KAAQ,GAAA,IAAA,CAAA,OAAe,CAAA,oBAAoB,CAAA,CAAA,CAAA,CAAA,CAAA;AACjD,MAAA,MAAA,IAAA,GAAA,CAAA,KAAyC,GAAA,IAAA,GAAA,IAAA,CAAA,MAAA,IAAU,IAAA,CAAA,MAAI,CAAC;AAAA,MAC1D,iBAAA,CAAA,uBAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAA,aAAmB,GAAA,CAAA,KAAA,KAAA;AAEnB,MAAA,MAAM,IAAE,GAAA,KAAa,CAAA,IAAA,CAAA;AAErB,MAAA,MAAW,EAAA,IAAA,EAAA,KAAO,EAAA,EAAA,EAAA,MAAa,GAAGE,eAAA,CAAA;AAChC,MAAM,IAAA,CAAA,IAAA,EAAA,KAAgB,CAAA,CAAA,QAAA,CAAA,IAAO,CAAK,EAAA;AAClC,QAAA,MAAA,IAAA,GAAA,IAAA,KAAyB,IAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AACzB,QAAA,oBAAqB,CAAA,IAAA,CAAA,CAAA;AACrB,QAAA,KAAA,CAAA,cAAA,EAAA,CAAA;AAAA,QACF,OAAA;AAEA,OAAA;AACE,MAAM,IAAA,CAAA,EAAA,EAAA,IAAA,CAAA,CAAO,QAAS,CAAA,IAAA,CAAK,EAAK;AAChC,QAAkB,MAAA,IAAA,GAAA,IAAA,KAAA,EAAA,GAAA,CAAA,CAAA,GAAA,CAAA,CAAA;AAClB,QAAA,iBAAqB,CAAA,kBAAA,CAAA,CAAA,IAAA,CAAA,CAAA;AACrB,QAAA,KAAA,CAAA,cAAA,EAAA,CAAA;AAAA,QACF,OAAA;AAAA,OACF;AAEA,KAAA,CAAA;AAA0E,IACxE,MAAA,EAAA,iBAAA,EAAA,WAAA,EAAA,gBAAA,EAAA,GAAAC,yBAAA,CAAA;AAAA,MACA,iBAAA;AAAA,MACA,mBAAA;AAAA,MACD,mBAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,qBAAwB,GAAA,CAAA,IAAY,KAAA;AAAwB,MAC9D,OAAA,gBAAA,CAAA,IAAA,EAAA,KAAA,CAAA,YAAA,IAAA,EAAA,EAAA,IAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,cAAe,GAAA,CAAA,KAAA,KAAA;AACnB,MAAA,IAAA,CAAA;AAAmD,QACrD,OAAA,IAAA,CAAA;AAEA,MAAM,OAAAC,yBAAA,CAAA,KAAA,EAAA,KAAmC,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AACvC,KAAI,CAAA;AACJ,IAAO,MAAA,cAAa,GAAA,CAAA,KAAY,KAAA;AAAA,MAClC,IAAA,CAAA,KAAA;AAEA,QAAA;AACE,MAAA,OAAO,KAAM,CAAA,MAAA,CAAA,KAAY,CAAE,MAAA,CAAA,CAAO;AAAU,KAC9C,CAAA;AAEA,IAAA,MAA0B,eAAA,GAAA,MAAiB;AAC3C,MAAA,OAA0BA,yBAAA,CAAA,YAAC,CAAkB,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA;AAC7C,KAAA,CAAA;AACA,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,cAAsB,EAAA,YAAA,CAAA,CAAA,CAAA;AACjD,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAyB,EAAA,cAAA,CAAA,CAAA,CAAA;AACpD,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAmB,EAAA,cAAA,CAAA,CAAe,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}