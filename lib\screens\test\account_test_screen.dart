import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/user_controller.dart';
import '../../services/auth_service.dart';
import '../../services/daily_limit_service.dart';
import '../auth/login_screen.dart';
import '../user/user_settings_screen.dart';

/// 账号系统测试页面
class AccountTestScreen extends StatelessWidget {
  const AccountTestScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final UserController userController = Get.put(UserController());
    final AuthService authService = Get.find<AuthService>();

    return Scaffold(
      appBar: AppBar(
        title: const Text('账号系统测试'),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          mainAxisSize: MainAxisSize.min,
          children: [
            // 当前状态显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '当前状态',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Obx(() => Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('登录状态: ${userController.isLoggedIn.value ? "已登录" : "未登录"}'),
                        if (userController.currentUser.value != null) ...[
                          Text('用户名: ${userController.currentUser.value!.username}'),
                          Text('手机号: ${userController.currentUser.value!.phoneNumber}'),
                          Text('会员状态: ${userController.getMembershipStatusText()}'),
                          Text('数据同步: ${userController.isSyncEnabled.value ? "已开启" : "已关闭"}'),
                        ],
                      ],
                    )),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 功能测试按钮
            const Text(
              '功能测试',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 登录/注册
            ElevatedButton(
              onPressed: () {
                Get.to(() => const LoginScreen());
              },
              child: const Text('登录/注册'),
            ),
            
            const SizedBox(height: 8),
            
            // 用户设置
            Obx(() => ElevatedButton(
              onPressed: userController.isLoggedIn.value
                  ? () => Get.to(() => const UserSettingsScreen())
                  : null,
              child: const Text('用户设置'),
            )),
            
            const SizedBox(height: 8),
            
            // 手动同步
            Obx(() => ElevatedButton(
              onPressed: userController.isLoggedIn.value && !userController.isSyncing.value
                  ? userController.manualSync
                  : null,
              child: userController.isSyncing.value
                  ? const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        ),
                        SizedBox(width: 8),
                        Text('同步中...'),
                      ],
                    )
                  : const Text('手动同步'),
            )),
            
            const SizedBox(height: 8),
            
            // 退出登录
            Obx(() => ElevatedButton(
              onPressed: userController.isLoggedIn.value
                  ? () => authService.logout()
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('退出登录'),
            )),
            
            const SizedBox(height: 24),
            
            // 会员权益展示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '当前权益',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...userController.getMembershipBenefits().map((benefit) => 
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2),
                        child: Row(
                          children: [
                            Icon(
                              Icons.check_circle,
                              color: userController.currentUser.value?.isValidMember == true
                                  ? Colors.green
                                  : Colors.grey,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Expanded(child: Text(benefit, style: const TextStyle(fontSize: 14))),
                          ],
                        ),
                      ),
                    ).toList(),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 功能权限测试
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '功能权限测试',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildPermissionTest('扩展功能', 'extended_features', userController),
                    _buildPermissionTest('高级AI', 'advanced_ai', userController),
                    _buildPermissionTest('多格式导出', 'multiple_formats', userController),
                    _buildPermissionTest('数据同步', 'data_sync', userController),
                    const SizedBox(height: 8),
                    _buildDailyChapterLimitTest(),
                    _buildLimitTest('知识库限制', 'knowledge_documents', 3, userController),
                    _buildLimitTest('每日生成限制', 'novels_per_day', 5, userController),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建权限测试项
  Widget _buildPermissionTest(String name, String feature, UserController controller) {
    final canUse = controller.canUseFeature(feature);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            canUse ? Icons.check_circle : Icons.cancel,
            color: canUse ? Colors.green : Colors.red,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text('$name: ${canUse ? "可用" : "不可用"}', style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  /// 构建限制测试项
  Widget _buildLimitTest(String name, String limitType, int currentCount, UserController controller) {
    final withinLimit = controller.isWithinLimits(limitType, currentCount);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(
            withinLimit ? Icons.check_circle : Icons.warning,
            color: withinLimit ? Colors.green : Colors.orange,
            size: 16,
          ),
          const SizedBox(width: 8),
          Text('$name ($currentCount): ${withinLimit ? "未超限" : "已超限"}', style: const TextStyle(fontSize: 14)),
        ],
      ),
    );
  }

  /// 构建每日章节限制测试
  Widget _buildDailyChapterLimitTest() {
    return GetBuilder<DailyLimitService>(
      builder: (dailyLimitService) {
        final canGenerate = dailyLimitService.canGenerateChapter();
        final limitText = dailyLimitService.getLimitText();

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 2),
          child: Row(
            children: [
              Icon(
                canGenerate ? Icons.check_circle : Icons.warning,
                color: canGenerate ? Colors.green : Colors.orange,
                size: 16,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '每日章节限制: $limitText',
                  style: const TextStyle(fontSize: 14),
                ),
              ),
              TextButton(
                onPressed: () {
                  dailyLimitService.refreshCount();
                },
                child: const Text('刷新', style: TextStyle(fontSize: 12)),
              ),
            ],
          ),
        );
      },
    );
  }
}
