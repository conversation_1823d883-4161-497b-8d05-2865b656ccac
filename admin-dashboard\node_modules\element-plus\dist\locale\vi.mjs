/*! Element Plus v2.10.4 */

var vi = {
  name: "vi",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "OK",
      clear: "X\xF3a"
    },
    datepicker: {
      now: "Hi\u1EC7n t\u1EA1i",
      today: "H\xF4m nay",
      cancel: "H\u1EE7y",
      clear: "X\xF3a",
      confirm: "OK",
      selectDate: "Ch\u1ECDn ng\xE0y",
      selectTime: "Ch\u1ECDn gi\u1EDD",
      startDate: "Ng\xE0y b\u1EAFt \u0111\u1EA7u",
      startTime: "Th\u1EDDi gian b\u1EAFt \u0111\u1EA7u",
      endDate: "Ng\xE0y k\u1EBFt th\xFAc",
      endTime: "Th\u1EDDi gian k\u1EBFt th\xFAc",
      prevYear: "N\u0103m tr\u01B0\u1EDBc",
      nextYear: "N\u0103m t\u1EDBi",
      prevMonth: "Th\xE1ng tr\u01B0\u1EDBc",
      nextMonth: "Th\xE1ng t\u1EDBi",
      year: "N\u0103m",
      month1: "Th\xE1ng 1",
      month2: "Th\xE1ng 2",
      month3: "Th\xE1ng 3",
      month4: "Th\xE1ng 4",
      month5: "Th\xE1ng 5",
      month6: "Th\xE1ng 6",
      month7: "Th\xE1ng 7",
      month8: "Th\xE1ng 8",
      month9: "Th\xE1ng 9",
      month10: "Th\xE1ng 10",
      month11: "Th\xE1ng 11",
      month12: "Th\xE1ng 12",
      weeks: {
        sun: "CN",
        mon: "T2",
        tue: "T3",
        wed: "T4",
        thu: "T5",
        fri: "T6",
        sat: "T7"
      },
      months: {
        jan: "Th.1",
        feb: "Th.2",
        mar: "Th.3",
        apr: "Th.4",
        may: "Th.5",
        jun: "Th.6",
        jul: "Th.7",
        aug: "Th.8",
        sep: "Th.9",
        oct: "Th.10",
        nov: "Th.11",
        dec: "Th.12"
      }
    },
    select: {
      loading: "\u0110ang t\u1EA3i",
      noMatch: "D\u1EEF li\u1EC7u kh\xF4ng ph\xF9 h\u1EE3p",
      noData: "Kh\xF4ng t\xECm th\u1EA5y d\u1EEF li\u1EC7u",
      placeholder: "Ch\u1ECDn"
    },
    mention: {
      loading: "\u0110ang t\u1EA3i"
    },
    cascader: {
      noMatch: "D\u1EEF li\u1EC7u kh\xF4ng ph\xF9 h\u1EE3p",
      loading: "\u0110ang t\u1EA3i",
      placeholder: "Ch\u1ECDn",
      noData: "Kh\xF4ng t\xECm th\u1EA5y d\u1EEF li\u1EC7u"
    },
    pagination: {
      goto: "Nh\u1EA3y t\u1EDBi",
      pagesize: "/trang",
      total: "T\u1ED5ng {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      title: "Th\xF4ng b\xE1o",
      confirm: "OK",
      cancel: "H\u1EE7y",
      error: "D\u1EEF li\u1EC7u kh\xF4ng h\u1EE3p l\u1EC7"
    },
    upload: {
      deleteTip: "Nh\u1EA5n xo\xE1 \u0111\u1EC3 xo\xE1",
      delete: "X\xF3a",
      preview: "Xem tr\u01B0\u1EDBc",
      continue: "Ti\u1EBFp t\u1EE5c"
    },
    table: {
      emptyText: "Kh\xF4ng c\xF3 d\u1EEF li\u1EC7u",
      confirmFilter: "X\xE1c nh\u1EADn",
      resetFilter: "L\xE0m m\u1EDBi",
      clearFilter: "X\xF3a h\u1EBFt",
      sumText: "T\u1ED5ng"
    },
    tour: {
      next: "Ti\u1EBFp",
      previous: "Tr\u01B0\u1EDBc",
      finish: "Ho\xE0n th\xE0nh"
    },
    tree: {
      emptyText: "Kh\xF4ng c\xF3 d\u1EEF li\u1EC7u"
    },
    transfer: {
      noMatch: "D\u1EEF li\u1EC7u kh\xF4ng ph\xF9 h\u1EE3p",
      noData: "Kh\xF4ng t\xECm th\u1EA5y d\u1EEF li\u1EC7u",
      titles: ["Danh s\xE1ch 1", "Danh s\xE1ch 2"],
      filterPlaceholder: "Nh\u1EADp t\u1EEB kh\xF3a",
      noCheckedFormat: "{total} m\u1EE5c",
      hasCheckedFormat: "{checked}/{total} \u0111\xE3 ch\u1ECDn "
    },
    image: {
      error: "L\u1ED6I"
    },
    pageHeader: {
      title: "Quay l\u1EA1i"
    },
    popconfirm: {
      confirmButtonText: "Ok",
      cancelButtonText: "Hu\u1EF7"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { vi as default };
