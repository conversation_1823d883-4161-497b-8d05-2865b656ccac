import { AbstractClient } from "../../../common/abstract_client";
export class Client extends AbstractClient {
    constructor(clientConfig) {
        super("cfs.tencentcloudapi.com", "2019-07-19", clientConfig);
    }
    async DescribeCfsRules(req, cb) {
        return this.request("DescribeCfsRules", req, cb);
    }
    async UpdateCfsFileSystemPGroup(req, cb) {
        return this.request("UpdateCfsFileSystemPGroup", req, cb);
    }
    async SignUpCfsService(req, cb) {
        return this.request("SignUpCfsService", req, cb);
    }
    async DeleteCfsSnapshot(req, cb) {
        return this.request("DeleteCfsSnapshot", req, cb);
    }
    async DescribeCfsPGroups(req, cb) {
        return this.request("DescribeCfsPGroups", req, cb);
    }
    async UpdateFileSystemBandwidthLimit(req, cb) {
        return this.request("UpdateFileSystemBandwidthLimit", req, cb);
    }
    async SetUserQuota(req, cb) {
        return this.request("SetUserQuota", req, cb);
    }
    async DescribeAutoSnapshotPolicies(req, cb) {
        return this.request("DescribeAutoSnapshotPolicies", req, cb);
    }
    async DescribeMountTargets(req, cb) {
        return this.request("DescribeMountTargets", req, cb);
    }
    async DeleteCfsPGroup(req, cb) {
        return this.request("DeleteCfsPGroup", req, cb);
    }
    async DescribeCfsFileSystemClients(req, cb) {
        return this.request("DescribeCfsFileSystemClients", req, cb);
    }
    async DescribeCfsServiceStatus(req, cb) {
        return this.request("DescribeCfsServiceStatus", req, cb);
    }
    async StopMigrationTask(req, cb) {
        return this.request("StopMigrationTask", req, cb);
    }
    async DescribeMigrationTasks(req, cb) {
        return this.request("DescribeMigrationTasks", req, cb);
    }
    async DescribeAvailableZoneInfo(req, cb) {
        return this.request("DescribeAvailableZoneInfo", req, cb);
    }
    async UpdateCfsFileSystemName(req, cb) {
        return this.request("UpdateCfsFileSystemName", req, cb);
    }
    async DeleteMigrationTask(req, cb) {
        return this.request("DeleteMigrationTask", req, cb);
    }
    async ScaleUpFileSystem(req, cb) {
        return this.request("ScaleUpFileSystem", req, cb);
    }
    async UpdateCfsFileSystemSizeLimit(req, cb) {
        return this.request("UpdateCfsFileSystemSizeLimit", req, cb);
    }
    async CreateAutoSnapshotPolicy(req, cb) {
        return this.request("CreateAutoSnapshotPolicy", req, cb);
    }
    async DeleteCfsFileSystem(req, cb) {
        return this.request("DeleteCfsFileSystem", req, cb);
    }
    async CreateCfsFileSystem(req, cb) {
        return this.request("CreateCfsFileSystem", req, cb);
    }
    async UpdateCfsSnapshotAttribute(req, cb) {
        return this.request("UpdateCfsSnapshotAttribute", req, cb);
    }
    async DescribeCfsSnapshotOverview(req, cb) {
        return this.request("DescribeCfsSnapshotOverview", req, cb);
    }
    async DescribeUserQuota(req, cb) {
        return this.request("DescribeUserQuota", req, cb);
    }
    async ModifyFileSystemAutoScaleUpRule(req, cb) {
        return this.request("ModifyFileSystemAutoScaleUpRule", req, cb);
    }
    async UpdateAutoSnapshotPolicy(req, cb) {
        return this.request("UpdateAutoSnapshotPolicy", req, cb);
    }
    async UpdateCfsRule(req, cb) {
        return this.request("UpdateCfsRule", req, cb);
    }
    async DescribeCfsFileSystems(req, cb) {
        return this.request("DescribeCfsFileSystems", req, cb);
    }
    async DescribeCfsSnapshots(req, cb) {
        return this.request("DescribeCfsSnapshots", req, cb);
    }
    async BindAutoSnapshotPolicy(req, cb) {
        return this.request("BindAutoSnapshotPolicy", req, cb);
    }
    async DeleteCfsRule(req, cb) {
        return this.request("DeleteCfsRule", req, cb);
    }
    async DescribeBucketList(req, cb) {
        return this.request("DescribeBucketList", req, cb);
    }
    async CreateMigrationTask(req, cb) {
        return this.request("CreateMigrationTask", req, cb);
    }
    async DeleteMountTarget(req, cb) {
        return this.request("DeleteMountTarget", req, cb);
    }
    async DeleteUserQuota(req, cb) {
        return this.request("DeleteUserQuota", req, cb);
    }
    async CreateCfsPGroup(req, cb) {
        return this.request("CreateCfsPGroup", req, cb);
    }
    async DeleteAutoSnapshotPolicy(req, cb) {
        return this.request("DeleteAutoSnapshotPolicy", req, cb);
    }
    async CreateCfsSnapshot(req, cb) {
        return this.request("CreateCfsSnapshot", req, cb);
    }
    async UnbindAutoSnapshotPolicy(req, cb) {
        return this.request("UnbindAutoSnapshotPolicy", req, cb);
    }
    async DescribeSnapshotOperationLogs(req, cb) {
        return this.request("DescribeSnapshotOperationLogs", req, cb);
    }
    async CreateCfsRule(req, cb) {
        return this.request("CreateCfsRule", req, cb);
    }
    async CreateAccessCert(req, cb) {
        return this.request("CreateAccessCert", req, cb);
    }
    async UpdateCfsPGroup(req, cb) {
        return this.request("UpdateCfsPGroup", req, cb);
    }
}
