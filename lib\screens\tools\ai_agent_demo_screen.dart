import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AIAgentDemoScreen extends StatefulWidget {
  const AIAgentDemoScreen({super.key});

  @override
  State<AIAgentDemoScreen> createState() => _AIAgentDemoScreenState();
}

class _AIAgentDemoScreenState extends State<AIAgentDemoScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _progressAnimation;
  
  final TextEditingController _promptController = TextEditingController();
  bool _isGenerating = false;
  int _currentStep = 0;
  String _currentStatus = '准备开始...';
  
  final List<String> _agentNames = ['编排者', '架构师', '执笔者', '典籍官', '校订者'];
  final List<String> _statusMessages = [
    '编排者正在初始化项目...',
    '架构师正在构建世界观和角色...',
    '执笔者正在撰写精彩章节...',
    '典籍官正在更新知识库...',
    '校订者正在进行质量检查...',
    '生成完成！'
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    _promptController.dispose();
    super.dispose();
  }

  void _startDemo() async {
    if (_promptController.text.trim().isEmpty) {
      Get.snackbar('提示', '请输入创作提示');
      return;
    }

    setState(() {
      _isGenerating = true;
      _currentStep = 0;
      _currentStatus = _statusMessages[0];
    });

    // 模拟智能体工作流程
    for (int i = 0; i < _statusMessages.length; i++) {
      await Future.delayed(const Duration(seconds: 3));
      if (mounted) {
        setState(() {
          _currentStep = i;
          _currentStatus = _statusMessages[i];
        });
        
        // 更新进度动画
        _animationController.animateTo((i + 1) / _statusMessages.length);
      }
    }

    if (mounted) {
      setState(() {
        _isGenerating = false;
      });
      
      // 显示完成对话框
      _showCompletionDialog();
    }
  }

  void _showCompletionDialog() {
    Get.dialog(
      AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('生成完成！'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('AI智能体协作创作演示完成'),
            const SizedBox(height: 12),
            const Text('模拟生成结果：'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('📖 世界观：${_promptController.text.contains('修仙') ? '修仙大陆' : '异能都市'}'),
                  const Text('👥 角色：主角 + 5个配角 + 3个反派'),
                  const Text('📋 大纲：100章完整规划'),
                  const Text('📚 章节：前3章详细内容'),
                  const Text('💾 知识库：四层RAG结构'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              _resetDemo();
            },
            child: const Text('重新演示'),
          ),
        ],
      ),
    );
  }

  void _resetDemo() {
    setState(() {
      _isGenerating = false;
      _currentStep = 0;
      _currentStatus = '准备开始...';
    });
    _animationController.reset();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('AI智能体小说生成演示'),
        backgroundColor: Colors.purple.shade100,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 系统介绍
            Card(
              elevation: 4,
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.auto_awesome, color: Colors.purple.shade600),
                        const SizedBox(width: 8),
                        Text(
                          'AI智能体协作创作系统演示',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.purple.shade600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '这是一个演示版本，展示了多智能体协作创作的完整流程。'
                      '实际版本需要连接后端API和OpenAI服务。',
                      style: TextStyle(fontSize: 14, height: 1.4),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),

            // 输入区域
            if (!_isGenerating) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '创作提示',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: _promptController,
                        maxLines: 4,
                        decoration: const InputDecoration(
                          hintText: '请描述您想要创作的小说...\n\n例如：一部修仙小说，主角的金手指是一个可以调试物理法则的系统',
                          border: OutlineInputBorder(),
                        ),
                      ),
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: ElevatedButton.icon(
                          onPressed: _startDemo,
                          icon: const Icon(Icons.play_arrow),
                          label: const Text('开始演示'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple.shade600,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],

            // 生成过程
            if (_isGenerating) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      const CircularProgressIndicator(),
                      const SizedBox(height: 16),
                      Text(
                        _currentStatus,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      AnimatedBuilder(
                        animation: _progressAnimation,
                        builder: (context, child) {
                          return LinearProgressIndicator(
                            value: _progressAnimation.value,
                            backgroundColor: Colors.grey.shade300,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.purple.shade600,
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 8),
                      AnimatedBuilder(
                        animation: _progressAnimation,
                        builder: (context, child) {
                          return Text(
                            '${(_progressAnimation.value * 100).toInt()}%',
                            style: const TextStyle(fontSize: 12),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // 智能体状态
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '智能体工作状态',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 12),
                      ...List.generate(_agentNames.length, (index) {
                        final isActive = index == _currentStep && _currentStep < _agentNames.length;
                        final isCompleted = index < _currentStep;
                        
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            children: [
                              Icon(
                                isActive
                                    ? Icons.play_circle_filled
                                    : isCompleted
                                        ? Icons.check_circle
                                        : Icons.radio_button_unchecked,
                                color: isActive
                                    ? Colors.blue
                                    : isCompleted
                                        ? Colors.green
                                        : Colors.grey,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Text(_agentNames[index]),
                              const Spacer(),
                              Text(
                                isActive
                                    ? '工作中'
                                    : isCompleted
                                        ? '已完成'
                                        : '等待中',
                                style: TextStyle(
                                  color: isActive
                                      ? Colors.blue
                                      : isCompleted
                                          ? Colors.green
                                          : Colors.grey,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
                ),
              ),
            ],

            const SizedBox(height: 20),

            // 系统特性介绍
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '系统特性',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 12),
                    _buildFeatureItem(
                      Icons.group_work,
                      '多智能体协作',
                      '五大专业智能体分工合作，各司其职',
                    ),
                    _buildFeatureItem(
                      Icons.memory,
                      '分层RAG记忆',
                      '四层知识库结构，智能检索上下文',
                    ),
                    _buildFeatureItem(
                      Icons.auto_fix_high,
                      '"爽文"范式',
                      '内置爽文创作规则，黄金三章设计',
                    ),
                    _buildFeatureItem(
                      Icons.fact_check,
                      '自我批判',
                      '质量自动检查，确保内容一致性',
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: Colors.purple.shade600, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
