{"version": 3, "file": "pagination.mjs", "sources": ["../../../../../../packages/components/pagination/src/pagination.ts"], "sourcesContent": ["import {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  h,\n  provide,\n  ref,\n  watch,\n} from 'vue'\nimport { <PERSON>Lef<PERSON>, ArrowRight } from '@element-plus/icons-vue'\nimport {\n  buildProps,\n  debugWarn,\n  definePropType,\n  iconPropType,\n  isNumber,\n  mutable,\n} from '@element-plus/utils'\nimport {\n  useDeprecated,\n  useGlobalSize,\n  useLocale,\n  useNamespace,\n  useSizeProp,\n} from '@element-plus/hooks'\nimport { CHANGE_EVENT } from '@element-plus/constants'\nimport { elPaginationKey } from './constants'\nimport Prev from './components/prev.vue'\nimport Next from './components/next.vue'\nimport Sizes from './components/sizes.vue'\nimport Jumper from './components/jumper.vue'\nimport Total from './components/total.vue'\nimport Pager from './components/pager.vue'\n\nimport type { ExtractPropTypes, VNode, __ExtractPublicPropTypes } from 'vue'\n/**\n * It it user's responsibility to guarantee that the value of props.total... is number\n * (same as pageSize, defaultPageSize, currentPage, defaultCurrentPage, pageCount)\n * Otherwise we can reasonable infer that the corresponding field is absent\n */\nconst isAbsent = (v: unknown): v is undefined => typeof v !== 'number'\n\ntype LayoutKey =\n  | 'prev'\n  | 'pager'\n  | 'next'\n  | 'jumper'\n  | '->'\n  | 'total'\n  | 'sizes'\n  | 'slot'\n\nexport const paginationProps = buildProps({\n  /**\n   * @description options of item count per page\n   */\n  pageSize: Number,\n  /**\n   * @description default initial value of page size, not setting is the same as setting 10\n   */\n  defaultPageSize: Number,\n  /**\n   * @description total item count\n   */\n  total: Number,\n  /**\n   * @description total page count. Set either `total` or `page-count` and pages will be displayed; if you need `page-sizes`, `total` is required\n   */\n  pageCount: Number,\n  /**\n   * @description number of pagers. Pagination collapses when the total page count exceeds this value\n   */\n  pagerCount: {\n    type: Number,\n    validator: (value: unknown) => {\n      return (\n        isNumber(value) &&\n        Math.trunc(value) === value &&\n        value > 4 &&\n        value < 22 &&\n        value % 2 === 1\n      )\n    },\n    default: 7,\n  },\n  /**\n   * @description current page number\n   */\n  currentPage: Number,\n  /**\n   * @description default initial value of current-page, not setting is the same as setting 1\n   */\n  defaultCurrentPage: Number,\n  /**\n   * @description layout of Pagination, elements separated with a comma\n   */\n  layout: {\n    type: String,\n    default: (\n      ['prev', 'pager', 'next', 'jumper', '->', 'total'] as LayoutKey[]\n    ).join(', '),\n  },\n  /**\n   * @description item count of each page\n   */\n  pageSizes: {\n    type: definePropType<number[]>(Array),\n    default: () => mutable([10, 20, 30, 40, 50, 100] as const),\n  },\n  /**\n   * @description custom class name for the page size Select's dropdown\n   */\n  popperClass: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description text for the prev button\n   */\n  prevText: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description icon for the prev button, higher priority of `prev-text`\n   */\n  prevIcon: {\n    type: iconPropType,\n    default: () => ArrowLeft,\n  },\n  /**\n   * @description text for the next button\n   */\n  nextText: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description icon for the next button, higher priority of `next-text`\n   */\n  nextIcon: {\n    type: iconPropType,\n    default: () => ArrowRight,\n  },\n  /**\n   * @description whether Pagination size is teleported to body\n   */\n  teleported: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description whether to use small pagination\n   */\n  small: Boolean,\n  /**\n   * @description set page size\n   */\n  size: useSizeProp,\n  /**\n   * @description whether the buttons have a background color\n   */\n  background: Boolean,\n  /**\n   * @description whether Pagination is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether to hide when there's only one page\n   */\n  hideOnSinglePage: Boolean,\n  /**\n   * @description which element the size dropdown appends to.\n   */\n  appendSizeTo: String,\n} as const)\nexport type PaginationProps = ExtractPropTypes<typeof paginationProps>\nexport type PaginationPropsPublic = __ExtractPublicPropTypes<\n  typeof paginationProps\n>\n\nexport const paginationEmits = {\n  'update:current-page': (val: number) => isNumber(val),\n  'update:page-size': (val: number) => isNumber(val),\n  'size-change': (val: number) => isNumber(val),\n  change: (currentPage: number, pageSize: number) =>\n    isNumber(currentPage) && isNumber(pageSize),\n  'current-change': (val: number) => isNumber(val),\n  'prev-click': (val: number) => isNumber(val),\n  'next-click': (val: number) => isNumber(val),\n}\nexport type PaginationEmits = typeof paginationEmits\n\nconst componentName = 'ElPagination'\nexport default defineComponent({\n  name: componentName,\n\n  props: paginationProps,\n  emits: paginationEmits,\n\n  setup(props, { emit, slots }) {\n    const { t } = useLocale()\n    const ns = useNamespace('pagination')\n    const vnodeProps = getCurrentInstance()!.vnode.props || {}\n    const _globalSize = useGlobalSize()\n    const _size = computed(() =>\n      props.small ? 'small' : props.size ?? _globalSize.value\n    )\n    useDeprecated(\n      {\n        from: 'small',\n        replacement: 'size',\n        version: '3.0.0',\n        scope: 'el-pagination',\n        ref: 'https://element-plus.org/zh-CN/component/pagination.html',\n      },\n      computed(() => !!props.small)\n    )\n    // we can find @xxx=\"xxx\" props on `vnodeProps` to check if user bind corresponding events\n    const hasCurrentPageListener =\n      'onUpdate:currentPage' in vnodeProps ||\n      'onUpdate:current-page' in vnodeProps ||\n      'onCurrentChange' in vnodeProps\n    const hasPageSizeListener =\n      'onUpdate:pageSize' in vnodeProps ||\n      'onUpdate:page-size' in vnodeProps ||\n      'onSizeChange' in vnodeProps\n    const assertValidUsage = computed(() => {\n      // Users have to set either one, otherwise count of pages cannot be determined\n      if (isAbsent(props.total) && isAbsent(props.pageCount)) return false\n      // <el-pagination ...otherProps :current-page=\"xxx\" /> without corresponding listener is forbidden now\n      // Users have to use two way binding of `currentPage`\n      // If users just want to provide a default value, `defaultCurrentPage` is here for you\n      if (!isAbsent(props.currentPage) && !hasCurrentPageListener) return false\n      // When you want to change sizes, things get more complex, detailed below\n      // Basically the most important value we need is page count\n      // either directly from props.pageCount\n      // or calculated from props.total\n      // we will take props.pageCount precedence over props.total\n      if (props.layout.includes('sizes')) {\n        if (!isAbsent(props.pageCount)) {\n          // if props.pageCount is assign by user, then user have to watch pageSize change\n          // and recalculate pageCount\n          if (!hasPageSizeListener) return false\n        } else if (!isAbsent(props.total)) {\n          // Otherwise, we will see if user have props.pageSize defined\n          // If so, meaning user want to have pageSize controlled himself/herself from component\n          // Thus page size listener is required\n          // users are account for page size change\n          if (!isAbsent(props.pageSize)) {\n            if (!hasPageSizeListener) {\n              return false\n            }\n          } else {\n            // (else block just for explaination)\n            // else page size is controlled by el-pagination internally\n          }\n        }\n      }\n      return true\n    })\n\n    const innerPageSize = ref(\n      isAbsent(props.defaultPageSize) ? 10 : props.defaultPageSize\n    )\n    const innerCurrentPage = ref(\n      isAbsent(props.defaultCurrentPage) ? 1 : props.defaultCurrentPage\n    )\n\n    const pageSizeBridge = computed({\n      get() {\n        return isAbsent(props.pageSize) ? innerPageSize.value : props.pageSize\n      },\n      set(v: number) {\n        if (isAbsent(props.pageSize)) {\n          innerPageSize.value = v\n        }\n        if (hasPageSizeListener) {\n          emit('update:page-size', v)\n          emit('size-change', v)\n        }\n      },\n    })\n\n    const pageCountBridge = computed<number>(() => {\n      let pageCount = 0\n      if (!isAbsent(props.pageCount)) {\n        pageCount = props.pageCount\n      } else if (!isAbsent(props.total)) {\n        pageCount = Math.max(1, Math.ceil(props.total / pageSizeBridge.value))\n      }\n      return pageCount\n    })\n\n    const currentPageBridge = computed<number>({\n      get() {\n        return isAbsent(props.currentPage)\n          ? innerCurrentPage.value\n          : props.currentPage\n      },\n      set(v) {\n        let newCurrentPage = v\n        if (v < 1) {\n          newCurrentPage = 1\n        } else if (v > pageCountBridge.value) {\n          newCurrentPage = pageCountBridge.value\n        }\n        if (isAbsent(props.currentPage)) {\n          innerCurrentPage.value = newCurrentPage\n        }\n        if (hasCurrentPageListener) {\n          emit('update:current-page', newCurrentPage)\n          emit('current-change', newCurrentPage)\n        }\n      },\n    })\n\n    watch(pageCountBridge, (val) => {\n      if (currentPageBridge.value > val) currentPageBridge.value = val\n    })\n\n    watch(\n      [currentPageBridge, pageSizeBridge],\n      (value) => {\n        emit(CHANGE_EVENT, ...value)\n      },\n      { flush: 'post' }\n    )\n\n    function handleCurrentChange(val: number) {\n      currentPageBridge.value = val\n    }\n\n    function handleSizeChange(val: number) {\n      pageSizeBridge.value = val\n      const newPageCount = pageCountBridge.value\n      if (currentPageBridge.value > newPageCount) {\n        currentPageBridge.value = newPageCount\n      }\n    }\n\n    function prev() {\n      if (props.disabled) return\n      currentPageBridge.value -= 1\n      emit('prev-click', currentPageBridge.value)\n    }\n\n    function next() {\n      if (props.disabled) return\n      currentPageBridge.value += 1\n      emit('next-click', currentPageBridge.value)\n    }\n\n    function addClass(element: any, cls: string) {\n      if (element) {\n        if (!element.props) {\n          element.props = {}\n        }\n        element.props.class = [element.props.class, cls].join(' ')\n      }\n    }\n\n    provide(elPaginationKey, {\n      pageCount: pageCountBridge,\n      disabled: computed(() => props.disabled),\n      currentPage: currentPageBridge,\n      changeEvent: handleCurrentChange,\n      handleSizeChange,\n    })\n\n    return () => {\n      if (!assertValidUsage.value) {\n        debugWarn(componentName, t('el.pagination.deprecationWarning'))\n        return null\n      }\n      if (!props.layout) return null\n      if (props.hideOnSinglePage && pageCountBridge.value <= 1) return null\n      const rootChildren: Array<VNode | VNode[] | null> = []\n      const rightWrapperChildren: Array<VNode | VNode[] | null> = []\n      const rightWrapperRoot = h(\n        'div',\n        { class: ns.e('rightwrapper') },\n        rightWrapperChildren\n      )\n      const TEMPLATE_MAP: Record<\n        Exclude<LayoutKey, '->'>,\n        VNode | VNode[] | null\n      > = {\n        prev: h(Prev, {\n          disabled: props.disabled,\n          currentPage: currentPageBridge.value,\n          prevText: props.prevText,\n          prevIcon: props.prevIcon,\n          onClick: prev,\n        }),\n        jumper: h(Jumper, {\n          size: _size.value,\n        }),\n        pager: h(Pager, {\n          currentPage: currentPageBridge.value,\n          pageCount: pageCountBridge.value,\n          pagerCount: props.pagerCount,\n          onChange: handleCurrentChange,\n          disabled: props.disabled,\n        }),\n        next: h(Next, {\n          disabled: props.disabled,\n          currentPage: currentPageBridge.value,\n          pageCount: pageCountBridge.value,\n          nextText: props.nextText,\n          nextIcon: props.nextIcon,\n          onClick: next,\n        }),\n        sizes: h(Sizes, {\n          pageSize: pageSizeBridge.value,\n          pageSizes: props.pageSizes,\n          popperClass: props.popperClass,\n          disabled: props.disabled,\n          teleported: props.teleported,\n          size: _size.value,\n          appendSizeTo: props.appendSizeTo,\n        }),\n        slot: slots?.default?.() ?? null,\n        total: h(Total, { total: isAbsent(props.total) ? 0 : props.total }),\n      }\n\n      const components = props.layout\n        .split(',')\n        .map((item: string) => item.trim()) as LayoutKey[]\n\n      let haveRightWrapper = false\n\n      components.forEach((c) => {\n        if (c === '->') {\n          haveRightWrapper = true\n          return\n        }\n        if (!haveRightWrapper) {\n          rootChildren.push(TEMPLATE_MAP[c])\n        } else {\n          rightWrapperChildren.push(TEMPLATE_MAP[c])\n        }\n      })\n\n      addClass(rootChildren[0], ns.is('first'))\n      addClass(rootChildren[rootChildren.length - 1], ns.is('last'))\n\n      if (haveRightWrapper && rightWrapperChildren.length > 0) {\n        addClass(rightWrapperChildren[0], ns.is('first'))\n        addClass(\n          rightWrapperChildren[rightWrapperChildren.length - 1],\n          ns.is('last')\n        )\n        rootChildren.push(rightWrapperRoot)\n      }\n      return h(\n        'div',\n        {\n          class: [\n            ns.b(),\n            ns.is('background', props.background),\n            ns.m(_size.value),\n          ],\n        },\n        rootChildren\n      )\n    }\n  },\n})\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAiCA,MAAM,QAAQ,GAAG,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,QAAQ,CAAC;AAClC,MAAC,eAAe,GAAG,UAAU,CAAC;AAC1C,EAAE,QAAQ,EAAE,MAAM;AAClB,EAAE,eAAe,EAAE,MAAM;AACzB,EAAE,KAAK,EAAE,MAAM;AACf,EAAE,SAAS,EAAE,MAAM;AACnB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,SAAS,EAAE,CAAC,KAAK,KAAK;AAC1B,MAAM,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,EAAE,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC;AAC1G,KAAK;AACL,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,WAAW,EAAE,MAAM;AACrB,EAAE,kBAAkB,EAAE,MAAM;AAC5B,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;AAC1E,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,KAAK,CAAC;AAC/B,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC;AACrD,GAAG;AACH,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,OAAO,EAAE,MAAM,SAAS;AAC5B,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,YAAY;AACtB,IAAI,OAAO,EAAE,MAAM,UAAU;AAC7B,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,UAAU,EAAE,OAAO;AACrB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,gBAAgB,EAAE,OAAO;AAC3B,EAAE,YAAY,EAAE,MAAM;AACtB,CAAC,EAAE;AACS,MAAC,eAAe,GAAG;AAC/B,EAAE,qBAAqB,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AAC/C,EAAE,kBAAkB,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AAC5C,EAAE,aAAa,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AACvC,EAAE,MAAM,EAAE,CAAC,WAAW,EAAE,QAAQ,KAAK,QAAQ,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC;AAChF,EAAE,gBAAgB,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AAC1C,EAAE,YAAY,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AACtC,EAAE,YAAY,EAAE,CAAC,GAAG,KAAK,QAAQ,CAAC,GAAG,CAAC;AACtC,EAAE;AACF,MAAM,aAAa,GAAG,cAAc,CAAC;AACrC,iBAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,KAAK,EAAE,eAAe;AACxB,EAAE,KAAK,EAAE,eAAe;AACxB,EAAE,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;AAChC,IAAI,MAAM,EAAE,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC;AAC9B,IAAI,MAAM,EAAE,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;AAC1C,IAAI,MAAM,UAAU,GAAG,kBAAkB,EAAE,CAAC,KAAK,CAAC,KAAK,IAAI,EAAE,CAAC;AAC9D,IAAI,MAAM,WAAW,GAAG,aAAa,EAAE,CAAC;AACxC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM;AACjC,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,OAAO,GAAG,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,KAAK,IAAI,GAAG,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC;AACxF,KAAK,CAAC,CAAC;AACP,IAAI,aAAa,CAAC;AAClB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,WAAW,EAAE,MAAM;AACzB,MAAM,OAAO,EAAE,OAAO;AACtB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,GAAG,EAAE,0DAA0D;AACrE,KAAK,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACtC,IAAI,MAAM,sBAAsB,GAAG,sBAAsB,IAAI,UAAU,IAAI,uBAAuB,IAAI,UAAU,IAAI,iBAAiB,IAAI,UAAU,CAAC;AACpJ,IAAI,MAAM,mBAAmB,GAAG,mBAAmB,IAAI,UAAU,IAAI,oBAAoB,IAAI,UAAU,IAAI,cAAc,IAAI,UAAU,CAAC;AACxI,IAAI,MAAM,gBAAgB,GAAG,QAAQ,CAAC,MAAM;AAC5C,MAAM,IAAI,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC;AAC5D,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,sBAAsB;AACjE,QAAQ,OAAO,KAAK,CAAC;AACrB,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC1C,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACxC,UAAU,IAAI,CAAC,mBAAmB;AAClC,YAAY,OAAO,KAAK,CAAC;AACzB,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AAC3C,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AACzC,YAAY,IAAI,CAAC,mBAAmB,EAAE;AACtC,cAAc,OAAO,KAAK,CAAC;AAC3B,aAAa;AACb,WACW;AACX,SAAS;AACT,OAAO;AACP,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,aAAa,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,GAAG,EAAE,GAAG,KAAK,CAAC,eAAe,CAAC,CAAC;AAC5F,IAAI,MAAM,gBAAgB,GAAG,GAAG,CAAC,QAAQ,CAAC,KAAK,CAAC,kBAAkB,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC;AACpG,IAAI,MAAM,cAAc,GAAG,QAAQ,CAAC;AACpC,MAAM,GAAG,GAAG;AACZ,QAAQ,OAAO,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAC;AAC/E,OAAO;AACP,MAAM,GAAG,CAAC,CAAC,EAAE;AACb,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;AACtC,UAAU,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC;AAClC,SAAS;AACT,QAAQ,IAAI,mBAAmB,EAAE;AACjC,UAAU,IAAI,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC;AACtC,UAAU,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC;AACjC,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AAC3C,MAAM,IAAI,SAAS,GAAG,CAAC,CAAC;AACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE;AACtC,QAAQ,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AACpC,OAAO,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACzC,QAAQ,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/E,OAAO;AACP,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,iBAAiB,GAAG,QAAQ,CAAC;AACvC,MAAM,GAAG,GAAG;AACZ,QAAQ,OAAO,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC,WAAW,CAAC;AACxF,OAAO;AACP,MAAM,GAAG,CAAC,CAAC,EAAE;AACb,QAAQ,IAAI,cAAc,GAAG,CAAC,CAAC;AAC/B,QAAQ,IAAI,CAAC,GAAG,CAAC,EAAE;AACnB,UAAU,cAAc,GAAG,CAAC,CAAC;AAC7B,SAAS,MAAM,IAAI,CAAC,GAAG,eAAe,CAAC,KAAK,EAAE;AAC9C,UAAU,cAAc,GAAG,eAAe,CAAC,KAAK,CAAC;AACjD,SAAS;AACT,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;AACzC,UAAU,gBAAgB,CAAC,KAAK,GAAG,cAAc,CAAC;AAClD,SAAS;AACT,QAAQ,IAAI,sBAAsB,EAAE;AACpC,UAAU,IAAI,CAAC,qBAAqB,EAAE,cAAc,CAAC,CAAC;AACtD,UAAU,IAAI,CAAC,gBAAgB,EAAE,cAAc,CAAC,CAAC;AACjD,SAAS;AACT,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC,GAAG,KAAK;AACpC,MAAM,IAAI,iBAAiB,CAAC,KAAK,GAAG,GAAG;AACvC,QAAQ,iBAAiB,CAAC,KAAK,GAAG,GAAG,CAAC;AACtC,KAAK,CAAC,CAAC;AACP,IAAI,KAAK,CAAC,CAAC,iBAAiB,EAAE,cAAc,CAAC,EAAE,CAAC,KAAK,KAAK;AAC1D,MAAM,IAAI,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC,CAAC;AACnC,KAAK,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AAC1B,IAAI,SAAS,mBAAmB,CAAC,GAAG,EAAE;AACtC,MAAM,iBAAiB,CAAC,KAAK,GAAG,GAAG,CAAC;AACpC,KAAK;AACL,IAAI,SAAS,gBAAgB,CAAC,GAAG,EAAE;AACnC,MAAM,cAAc,CAAC,KAAK,GAAG,GAAG,CAAC;AACjC,MAAM,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,CAAC;AACjD,MAAM,IAAI,iBAAiB,CAAC,KAAK,GAAG,YAAY,EAAE;AAClD,QAAQ,iBAAiB,CAAC,KAAK,GAAG,YAAY,CAAC;AAC/C,OAAO;AACP,KAAK;AACL,IAAI,SAAS,IAAI,GAAG;AACpB,MAAM,IAAI,KAAK,CAAC,QAAQ;AACxB,QAAQ,OAAO;AACf,MAAM,iBAAiB,CAAC,KAAK,IAAI,CAAC,CAAC;AACnC,MAAM,IAAI,CAAC,YAAY,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,SAAS,IAAI,GAAG;AACpB,MAAM,IAAI,KAAK,CAAC,QAAQ;AACxB,QAAQ,OAAO;AACf,MAAM,iBAAiB,CAAC,KAAK,IAAI,CAAC,CAAC;AACnC,MAAM,IAAI,CAAC,YAAY,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,SAAS,QAAQ,CAAC,OAAO,EAAE,GAAG,EAAE;AACpC,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;AAC5B,UAAU,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;AAC7B,SAAS;AACT,QAAQ,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACnE,OAAO;AACP,KAAK;AACL,IAAI,OAAO,CAAC,eAAe,EAAE;AAC7B,MAAM,SAAS,EAAE,eAAe;AAChC,MAAM,QAAQ,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,QAAQ,CAAC;AAC9C,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,WAAW,EAAE,mBAAmB;AACtC,MAAM,gBAAgB;AACtB,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,MAAM;AACjB,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;AACnC,QAAQ,SAAS,CAAC,aAAa,EAAE,CAAC,CAAC,kCAAkC,CAAC,CAAC,CAAC;AACxE,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;AACvB,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,IAAI,KAAK,CAAC,gBAAgB,IAAI,eAAe,CAAC,KAAK,IAAI,CAAC;AAC9D,QAAQ,OAAO,IAAI,CAAC;AACpB,MAAM,MAAM,YAAY,GAAG,EAAE,CAAC;AAC9B,MAAM,MAAM,oBAAoB,GAAG,EAAE,CAAC;AACtC,MAAM,MAAM,gBAAgB,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,EAAE,oBAAoB,CAAC,CAAC;AAC/F,MAAM,MAAM,YAAY,GAAG;AAC3B,QAAQ,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;AACtB,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,WAAW,EAAE,iBAAiB,CAAC,KAAK;AAC9C,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,OAAO,EAAE,IAAI;AACvB,SAAS,CAAC;AACV,QAAQ,MAAM,EAAE,CAAC,CAAC,MAAM,EAAE;AAC1B,UAAU,IAAI,EAAE,KAAK,CAAC,KAAK;AAC3B,SAAS,CAAC;AACV,QAAQ,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;AACxB,UAAU,WAAW,EAAE,iBAAiB,CAAC,KAAK;AAC9C,UAAU,SAAS,EAAE,eAAe,CAAC,KAAK;AAC1C,UAAU,UAAU,EAAE,KAAK,CAAC,UAAU;AACtC,UAAU,QAAQ,EAAE,mBAAmB;AACvC,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,SAAS,CAAC;AACV,QAAQ,IAAI,EAAE,CAAC,CAAC,IAAI,EAAE;AACtB,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,WAAW,EAAE,iBAAiB,CAAC,KAAK;AAC9C,UAAU,SAAS,EAAE,eAAe,CAAC,KAAK;AAC1C,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,OAAO,EAAE,IAAI;AACvB,SAAS,CAAC;AACV,QAAQ,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE;AACxB,UAAU,QAAQ,EAAE,cAAc,CAAC,KAAK;AACxC,UAAU,SAAS,EAAE,KAAK,CAAC,SAAS;AACpC,UAAU,WAAW,EAAE,KAAK,CAAC,WAAW;AACxC,UAAU,QAAQ,EAAE,KAAK,CAAC,QAAQ;AAClC,UAAU,UAAU,EAAE,KAAK,CAAC,UAAU;AACtC,UAAU,IAAI,EAAE,KAAK,CAAC,KAAK;AAC3B,UAAU,YAAY,EAAE,KAAK,CAAC,YAAY;AAC1C,SAAS,CAAC;AACV,QAAQ,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK,CAAC,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI;AACxH,QAAQ,KAAK,EAAE,CAAC,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AAC3E,OAAO,CAAC;AACR,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;AAC5E,MAAM,IAAI,gBAAgB,GAAG,KAAK,CAAC;AACnC,MAAM,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;AAChC,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;AACxB,UAAU,gBAAgB,GAAG,IAAI,CAAC;AAClC,UAAU,OAAO;AACjB,SAAS;AACT,QAAQ,IAAI,CAAC,gBAAgB,EAAE;AAC/B,UAAU,YAAY,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7C,SAAS,MAAM;AACf,UAAU,oBAAoB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,SAAS;AACT,OAAO,CAAC,CAAC;AACT,MAAM,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AAChD,MAAM,QAAQ,CAAC,YAAY,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACrE,MAAM,IAAI,gBAAgB,IAAI,oBAAoB,CAAC,MAAM,GAAG,CAAC,EAAE;AAC/D,QAAQ,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AAC1D,QAAQ,QAAQ,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;AACvF,QAAQ,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;AAC5C,OAAO;AACP,MAAM,OAAO,CAAC,CAAC,KAAK,EAAE;AACtB,QAAQ,KAAK,EAAE;AACf,UAAU,EAAE,CAAC,CAAC,EAAE;AAChB,UAAU,EAAE,CAAC,EAAE,CAAC,YAAY,EAAE,KAAK,CAAC,UAAU,CAAC;AAC/C,UAAU,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC;AAC3B,SAAS;AACT,OAAO,EAAE,YAAY,CAAC,CAAC;AACvB,KAAK,CAAC;AACN,GAAG;AACH,CAAC,CAAC;;;;"}