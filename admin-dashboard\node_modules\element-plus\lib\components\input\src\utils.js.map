{"version": 3, "file": "utils.js", "sources": ["../../../../../../packages/components/input/src/utils.ts"], "sourcesContent": ["import { isFirefox, isNumber } from '@element-plus/utils'\n\nlet hiddenTextarea: HTMLTextAreaElement | undefined = undefined\n\nconst HIDDEN_STYLE = {\n  height: '0',\n  visibility: 'hidden',\n  overflow: isFirefox() ? '' : 'hidden',\n  position: 'absolute',\n  'z-index': '-1000',\n  top: '0',\n  right: '0',\n}\n\nconst CONTEXT_STYLE = [\n  'letter-spacing',\n  'line-height',\n  'padding-top',\n  'padding-bottom',\n  'font-family',\n  'font-weight',\n  'font-size',\n  'text-rendering',\n  'text-transform',\n  'width',\n  'text-indent',\n  'padding-left',\n  'padding-right',\n  'border-width',\n  'box-sizing',\n]\n\ntype NodeStyle = {\n  contextStyle: string[][]\n  boxSizing: string\n  paddingSize: number\n  borderSize: number\n}\n\ntype TextAreaHeight = {\n  height: string\n  minHeight?: string\n}\n\nfunction calculateNodeStyling(targetElement: Element): NodeStyle {\n  const style = window.getComputedStyle(targetElement)\n\n  const boxSizing = style.getPropertyValue('box-sizing')\n\n  const paddingSize =\n    Number.parseFloat(style.getPropertyValue('padding-bottom')) +\n    Number.parseFloat(style.getPropertyValue('padding-top'))\n\n  const borderSize =\n    Number.parseFloat(style.getPropertyValue('border-bottom-width')) +\n    Number.parseFloat(style.getPropertyValue('border-top-width'))\n\n  const contextStyle = CONTEXT_STYLE.map((name) => [\n    name,\n    style.getPropertyValue(name),\n  ])\n\n  return { contextStyle, paddingSize, borderSize, boxSizing }\n}\n\nexport function calcTextareaHeight(\n  targetElement: HTMLTextAreaElement,\n  minRows = 1,\n  maxRows?: number\n): TextAreaHeight {\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea')\n    document.body.appendChild(hiddenTextarea)\n  }\n\n  const { paddingSize, borderSize, boxSizing, contextStyle } =\n    calculateNodeStyling(targetElement)\n\n  contextStyle.forEach(([key, value]) =>\n    hiddenTextarea?.style.setProperty(key, value)\n  )\n\n  Object.entries(HIDDEN_STYLE).forEach(([key, value]) =>\n    hiddenTextarea?.style.setProperty(key, value, 'important')\n  )\n\n  hiddenTextarea.value = targetElement.value || targetElement.placeholder || ''\n\n  let height = hiddenTextarea.scrollHeight\n  const result = {} as TextAreaHeight\n\n  if (boxSizing === 'border-box') {\n    height = height + borderSize\n  } else if (boxSizing === 'content-box') {\n    height = height - paddingSize\n  }\n\n  hiddenTextarea.value = ''\n  const singleRowHeight = hiddenTextarea.scrollHeight - paddingSize\n\n  if (isNumber(minRows)) {\n    let minHeight = singleRowHeight * minRows\n    if (boxSizing === 'border-box') {\n      minHeight = minHeight + paddingSize + borderSize\n    }\n    height = Math.max(minHeight, height)\n    result.minHeight = `${minHeight}px`\n  }\n  if (isNumber(maxRows)) {\n    let maxHeight = singleRowHeight * maxRows\n    if (boxSizing === 'border-box') {\n      maxHeight = maxHeight + paddingSize + borderSize\n    }\n    height = Math.min(maxHeight, height)\n  }\n  result.height = `${height}px`\n  hiddenTextarea.parentNode?.removeChild(hiddenTextarea)\n  hiddenTextarea = undefined\n\n  return result\n}\n"], "names": ["isFirefox", "isNumber"], "mappings": ";;;;;;;AACA,IAAI,cAAc,GAAG,KAAK,CAAC,CAAC;AAC5B,MAAM,YAAY,GAAG;AACrB,EAAE,MAAM,EAAE,GAAG;AACb,EAAE,UAAU,EAAE,QAAQ;AACtB,EAAE,QAAQ,EAAEA,iBAAS,EAAE,GAAG,EAAE,GAAG,QAAQ;AACvC,EAAE,QAAQ,EAAE,UAAU;AACtB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,GAAG,EAAE,GAAG;AACV,EAAE,KAAK,EAAE,GAAG;AACZ,CAAC,CAAC;AACF,MAAM,aAAa,GAAG;AACtB,EAAE,gBAAgB;AAClB,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,gBAAgB;AAClB,EAAE,aAAa;AACf,EAAE,aAAa;AACf,EAAE,WAAW;AACb,EAAE,gBAAgB;AAClB,EAAE,gBAAgB;AAClB,EAAE,OAAO;AACT,EAAE,aAAa;AACf,EAAE,cAAc;AAChB,EAAE,eAAe;AACjB,EAAE,cAAc;AAChB,EAAE,YAAY;AACd,CAAC,CAAC;AACF,SAAS,oBAAoB,CAAC,aAAa,EAAE;AAC7C,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC;AACvD,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAC;AACzD,EAAE,MAAM,WAAW,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,CAAC;AAC7I,EAAE,MAAM,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,qBAAqB,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC,CAAC;AACtJ,EAAE,MAAM,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK;AACnD,IAAI,IAAI;AACR,IAAI,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,CAAC;AAC9D,CAAC;AACM,SAAS,kBAAkB,CAAC,aAAa,EAAE,OAAO,GAAG,CAAC,EAAE,OAAO,EAAE;AACxE,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,IAAI,CAAC,cAAc,EAAE;AACvB,IAAI,cAAc,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;AACxD,IAAI,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AAC9C,GAAG;AACH,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,GAAG,oBAAoB,CAAC,aAAa,CAAC,CAAC;AACnG,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;AACzH,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,KAAK,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;AACtJ,EAAE,cAAc,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,IAAI,aAAa,CAAC,WAAW,IAAI,EAAE,CAAC;AAChF,EAAE,IAAI,MAAM,GAAG,cAAc,CAAC,YAAY,CAAC;AAC3C,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,IAAI,SAAS,KAAK,YAAY,EAAE;AAClC,IAAI,MAAM,GAAG,MAAM,GAAG,UAAU,CAAC;AACjC,GAAG,MAAM,IAAI,SAAS,KAAK,aAAa,EAAE;AAC1C,IAAI,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC;AAClC,GAAG;AACH,EAAE,cAAc,CAAC,KAAK,GAAG,EAAE,CAAC;AAC5B,EAAE,MAAM,eAAe,GAAG,cAAc,CAAC,YAAY,GAAG,WAAW,CAAC;AACpE,EAAE,IAAIC,cAAQ,CAAC,OAAO,CAAC,EAAE;AACzB,IAAI,IAAI,SAAS,GAAG,eAAe,GAAG,OAAO,CAAC;AAC9C,IAAI,IAAI,SAAS,KAAK,YAAY,EAAE;AACpC,MAAM,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,UAAU,CAAC;AACvD,KAAK;AACL,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACzC,IAAI,MAAM,CAAC,SAAS,GAAG,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,IAAIA,cAAQ,CAAC,OAAO,CAAC,EAAE;AACzB,IAAI,IAAI,SAAS,GAAG,eAAe,GAAG,OAAO,CAAC;AAC9C,IAAI,IAAI,SAAS,KAAK,YAAY,EAAE;AACpC,MAAM,SAAS,GAAG,SAAS,GAAG,WAAW,GAAG,UAAU,CAAC;AACvD,KAAK;AACL,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACzC,GAAG;AACH,EAAE,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC;AAChC,EAAE,CAAC,EAAE,GAAG,cAAc,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;AACrF,EAAE,cAAc,GAAG,KAAK,CAAC,CAAC;AAC1B,EAAE,OAAO,MAAM,CAAC;AAChB;;;;"}