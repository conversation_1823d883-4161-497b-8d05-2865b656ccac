{"version": 3, "file": "shared.js", "sources": ["../../../../../../../packages/components/time-picker/src/props/shared.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type {\n  GetDisabledHours,\n  GetDisabledMinutes,\n  GetDisabledSeconds,\n} from '../common/props'\n\nexport const disabledTimeListsProps = buildProps({\n  /**\n   * @description To specify the array of hours that cannot be selected\n   */\n  disabledHours: {\n    type: definePropType<GetDisabledHours>(Function),\n  },\n  /**\n   * @description To specify the array of minutes that cannot be selected\n   */\n  disabledMinutes: {\n    type: definePropType<GetDisabledMinutes>(Function),\n  },\n  /**\n   * @description To specify the array of seconds that cannot be selected\n   */\n  disabledSeconds: {\n    type: definePropType<GetDisabledSeconds>(Function),\n  },\n} as const)\n\nexport type DisabledTimeListsProps = ExtractPropTypes<\n  typeof disabledTimeListsProps\n>\nexport type DisabledTimeListsPropsPublic = __ExtractPublicPropTypes<\n  typeof disabledTimeListsProps\n>\n\nexport const timePanelSharedProps = buildProps({\n  visible: Boolean,\n  actualVisible: {\n    type: Boolean,\n    default: undefined,\n  },\n  format: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type TimePanelSharedProps = ExtractPropTypes<typeof timePanelSharedProps>\nexport type TimePanelSharedPropsPublic = __ExtractPublicPropTypes<\n  typeof timePanelSharedProps\n>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,sBAAsB,GAAGA,kBAAU,CAAC;AACjD,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEC,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,CAAC,EAAE;AACS,MAAC,oBAAoB,GAAGD,kBAAU,CAAC;AAC/C,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,CAAC;;;;;"}