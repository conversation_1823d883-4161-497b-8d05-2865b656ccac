var _typeof=function(obj){"@swc/helpers - typeof";return obj&& typeof Symbol!=="undefined"&&obj.constructor===Symbol?"symbol":typeof obj};var f=Object.defineProperty;var Y=Object.getOwnPropertyDescriptor;var L=Object.getOwnPropertyNames;var W=Object.prototype.hasOwnProperty;var g=function(e,n){for(var i in n)f(e,i,{get:n[i],enumerable:!0})},k=function(e,n,i,r){var _iteratorNormalCompletion=true,_didIteratorError=false,_iteratorError=undefined;if(n&& typeof n=="object"|| typeof n=="function")try{var _loop=function(_iterator,_step){var t=_step.value;!W.call(e,t)&&t!==i&&f(e,t,{get:function(){return n[t]},enumerable:!(r=Y(n,t))||r.enumerable})};for(var _iterator=L(n)[Symbol.iterator](),_step;!(_iteratorNormalCompletion=(_step=_iterator.next()).done);_iteratorNormalCompletion=true)_loop(_iterator,_step)}catch(err){_didIteratorError=true;_iteratorError=err}finally{try{if(!_iteratorNormalCompletion&&_iterator.return!=null){_iterator.return()}}finally{if(_didIteratorError){throw _iteratorError}}}return e};var H=function(e){return k(f({},"__esModule",{value:!0}),e)};var G={};g(G,{default:function(){return S}});module.exports=H(G);var N=!1,o,s,p,u,d,D,l,m,w,x,M,E,_,F,A;function a(){if(!N){N=!0;var e=navigator.userAgent,n=/(?:MSIE.(\d+\.\d+))|(?:(?:Firefox|GranParadiso|Iceweasel).(\d+\.\d+))|(?:Opera(?:.+Version.|.)(\d+\.\d+))|(?:AppleWebKit.(\d+(?:\.\d+)?))|(?:Trident\/\d+\.\d+.*rv:(\d+\.\d+))/.exec(e),i=/(Mac OS X)|(Windows)|(Linux)/.exec(e);if(E=/\b(iPhone|iP[ao]d)/.exec(e),_=/\b(iP[ao]d)/.exec(e),x=/Android/i.exec(e),F=/FBAN\/\w+;/i.exec(e),A=/Mobile/i.exec(e),M=!!/Win64/.exec(e),n){o=n[1]?parseFloat(n[1]):n[5]?parseFloat(n[5]):NaN,o&&document&&document.documentMode&&(o=document.documentMode);var r=/(?:Trident\/(\d+.\d+))/.exec(e);D=r?parseFloat(r[1])+4:o,s=n[2]?parseFloat(n[2]):NaN,p=n[3]?parseFloat(n[3]):NaN,u=n[4]?parseFloat(n[4]):NaN,u?(n=/(?:Chrome\/(\d+\.\d+))/.exec(e),d=n&&n[1]?parseFloat(n[1]):NaN):d=NaN}else o=s=p=d=u=NaN;if(i){if(i[1]){var t=/(?:Mac OS X (\d+(?:[._]\d+)?))/.exec(e);l=t?parseFloat(t[1].replace("_",".")):!0}else l=!1;m=!!i[2],w=!!i[3]}else l=m=w=!1}}var h={ie:function ie(){return a()||o},ieCompatibilityMode:function ieCompatibilityMode(){return a()||D>o},ie64:function ie64(){return h.ie()&&M},firefox:function firefox(){return a()||s},opera:function opera(){return a()||p},webkit:function webkit(){return a()||u},safari:function safari(){return h.webkit()},chrome:function chrome(){return a()||d},windows:function windows(){return a()||m},osx:function osx(){return a()||l},linux:function linux(){return a()||w},iphone:function iphone(){return a()||E},mobile:function mobile(){return a()||E||_||x||A},nativeApp:function nativeApp(){return a()||F},android:function android(){return a()||x},ipad:function ipad(){return a()||_}},X=h;var c=!!((typeof window==="undefined"?"undefined":_typeof(window))<"u"&&window.document&&window.document.createElement),y={canUseDOM:c,canUseWorkers:(typeof Worker==="undefined"?"undefined":_typeof(Worker))<"u",canUseEventListeners:c&&!!(window.addEventListener||window.attachEvent),canUseViewport:c&&!!window.screen,isInWorker:!c},v=y;var b;v.canUseDOM&&(b=document.implementation&&document.implementation.hasFeature&&document.implementation.hasFeature("","")!== !0);function C(e,n){if(!v.canUseDOM||n&&!("addEventListener"in document))return!1;var i="on"+e,r=i in document;if(!r){var t=document.createElement("div");t.setAttribute(i,"return;"),r=typeof t[i]=="function"}return!r&&b&&e==="wheel"&&(r=document.implementation.hasFeature("Events.wheel","3.0")),r}var O=C;var I=10,P=40,T=800;function U(e){var n=0,i=0,r=0,t=0;return"detail"in e&&(i=e.detail),"wheelDelta"in e&&(i=-e.wheelDelta/120),"wheelDeltaY"in e&&(i=-e.wheelDeltaY/120),"wheelDeltaX"in e&&(n=-e.wheelDeltaX/120),"axis"in e&&e.axis===e.HORIZONTAL_AXIS&&(n=i,i=0),r=n*I,t=i*I,"deltaY"in e&&(t=e.deltaY),"deltaX"in e&&(r=e.deltaX),(r||t)&&e.deltaMode&&(e.deltaMode==1?(r*=P,t*=P):(r*=T,t*=T)),r&&!n&&(n=r<1?-1:1),t&&!i&&(i=t<1?-1:1),{spinX:n,spinY:i,pixelX:r,pixelY:t}}U.getEventType=function(){return X.firefox()?"DOMMouseScroll":O("wheel")?"wheel":"mousewheel"};var S=U;0&&(module.exports={}); /**
 * Checks if an event is supported in the current execution environment.
 *
 * NOTE: This will not work correctly for non-generic events such as `change`,
 * `reset`, `load`, `error`, and `select`.
 *
 * Borrows from Modernizr.
 *
 * @param {string} eventNameSuffix Event name, e.g. "click".
 * @param {?boolean} capture Check if the capture phase is supported.
 * @return {boolean} True if the event is supported.
 * @internal
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */ //# sourceMappingURL=index.js.map