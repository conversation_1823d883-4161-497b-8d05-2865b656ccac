declare const _default: import("vue").DefineComponent<{
    isAutoWidth: BooleanConstructor;
    updateAll: BooleanConstructor;
}, () => JSX.Element | null, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    isAutoWidth: BooleanConstructor;
    updateAll: BooleanConstructor;
}>>, {
    isAutoWidth: boolean;
    updateAll: boolean;
}>;
export default _default;
