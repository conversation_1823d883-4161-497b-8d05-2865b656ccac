{"version": 3, "file": "mask.js", "sources": ["../../../../../../packages/components/tour/src/mask.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes, __ExtractPublicPropTypes } from 'vue'\nimport type { PosInfo } from './types'\n\nexport const maskProps = buildProps({\n  /**\n   * @description mask's zIndex\n   */\n  zIndex: {\n    type: Number,\n    default: 1001,\n  },\n  /**\n   * @description whether to show the mask\n   */\n  visible: Boolean,\n  /**\n   * @description mask's fill\n   */\n  fill: {\n    type: String,\n    default: 'rgba(0,0,0,0.5)',\n  },\n  /***\n   * @description mask's transparent space position\n   */\n  pos: {\n    type: definePropType<PosInfo | null>(Object),\n  },\n  /**\n   * @description whether the target element can be clickable, when using mask\n   */\n  targetAreaClickable: {\n    type: Boolean,\n    default: true,\n  },\n})\n\nexport type MaskProps = ExtractPropTypes<typeof maskProps>\nexport type MaskPropsPublic = __ExtractPublicPropTypes<typeof maskProps>\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;AACY,MAAC,SAAS,GAAGA,kBAAU,CAAC;AACpC,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,OAAO,EAAE,OAAO;AAClB,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,iBAAiB;AAC9B,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,mBAAmB,EAAE;AACvB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC;;;;"}