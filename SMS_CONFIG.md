# 📱 手机号验证码功能配置指南

## 🔍 当前状态

### ✅ 已实现功能
- **智能验证码系统**：开发环境使用固定验证码，生产环境使用随机验证码
- **频率限制**：1分钟内只能发送一次验证码
- **有效期控制**：验证码5分钟内有效
- **尝试次数限制**：最多尝试3次，超过后需重新获取
- **手机号格式验证**：严格验证中国大陆手机号格式

### 🚀 开发环境（当前）
- **验证码**：固定为随机6位数字
- **发送方式**：控制台日志输出
- **响应消息**：显示验证码内容（便于测试）

## 🏭 启用生产环境真实短信服务

### 方案1：腾讯云短信服务（推荐）

#### 1. 开通腾讯云短信服务
1. 登录 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 搜索并进入"短信"服务
3. 开通短信服务
4. 创建应用，获取 `SDKAppID`

#### 2. 申请短信签名和模板
1. **申请短信签名**：
   - 签名内容：`岱宗文脉`
   - 签名类型：App
   - 提交相关资质证明

2. **申请短信模板**：
   - 模板内容：`您的验证码是{1}，{2}分钟内有效，请勿泄露给他人。`
   - 模板类型：验证码
   - 获取模板ID

#### 3. 获取API密钥
1. 进入 [API密钥管理](https://console.cloud.tencent.com/cam/capi)
2. 创建密钥，获取 `SecretId` 和 `SecretKey`

#### 4. 配置环境变量
在CloudBase环境变量中设置：

```bash
# 腾讯云短信服务配置
SMS_SECRET_ID=your-secret-id
SMS_SECRET_KEY=your-secret-key
SMS_APP_ID=your-sdk-app-id
SMS_SIGN_NAME=岱宗文脉
SMS_TEMPLATE_ID=your-template-id
NODE_ENV=production
```

### 方案2：阿里云短信服务

#### 1. 开通阿里云短信服务
1. 登录阿里云控制台
2. 开通短信服务
3. 申请签名和模板

#### 2. 修改代码
需要将 `sendSMSCode` 函数中的腾讯云SDK替换为阿里云SDK。

### 方案3：其他短信服务商
- 容联云通讯
- 网易云信
- 华为云短信

## 🔧 部署步骤

### 1. 安装依赖
```bash
cd cloudbase/cloudbase-deploy/novel-app-api
npm install tencentcloud-sdk-nodejs
```

### 2. 配置环境变量
在CloudBase控制台的环境变量中添加短信服务配置。

### 3. 重新部署云函数
```bash
cd cloudbase/cloudbase-deploy
tcb fn deploy novel-app-api --envId your-env-id
```

## 📋 测试验证

### 开发环境测试
```javascript
// 发送验证码
POST /api/auth/send-code
{
  "phoneNumber": "13800138000"
}

// 响应（开发环境）
{
  "success": true,
  "message": "验证码发送成功（开发环境：123456）",
  "debugCode": "123456"
}
```

### 生产环境测试
```javascript
// 发送验证码
POST /api/auth/send-code
{
  "phoneNumber": "13800138000"
}

// 响应（生产环境）
{
  "success": true,
  "message": "验证码发送成功，请查收短信"
}
```

## 🛡️ 安全特性

1. **频率限制**：防止短信轰炸
2. **有效期控制**：防止验证码被长期使用
3. **尝试次数限制**：防止暴力破解
4. **手机号验证**：确保手机号格式正确
5. **环境隔离**：开发和生产环境分离

## 💰 费用说明

### 腾讯云短信费用
- **国内短信**：约0.045元/条
- **免费额度**：新用户通常有免费试用额度
- **按量计费**：根据实际发送量计费

### 成本优化建议
1. 合理设置发送频率限制
2. 监控异常发送行为
3. 定期清理过期验证码
4. 考虑使用语音验证码作为备选方案

## 🔄 切换到生产环境

要启用真实短信服务，您需要：

1. ✅ **完成腾讯云短信服务申请**（签名+模板审核通过）
2. ✅ **配置CloudBase环境变量**
3. ✅ **重新部署云函数**
4. ✅ **测试验证功能**

当前代码已经支持自动切换，只需要配置环境变量即可！
