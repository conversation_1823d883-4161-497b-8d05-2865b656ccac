{"version": 3, "file": "useSize.js", "sources": ["../../../../../../../packages/components/splitter/src/hooks/useSize.ts"], "sourcesContent": ["import { computed, ref, watch } from 'vue'\nimport { isString } from '@element-plus/utils'\n\nimport type { ComputedRef, Ref } from 'vue'\nimport type { PanelItemState } from '../type'\n\nexport function getPct(str: string) {\n  return Number(str.slice(0, -1)) / 100\n}\n\nexport function getPx(str: string) {\n  return Number(str.slice(0, -2))\n}\n\nexport function isPct(\n  itemSize: string | number | undefined\n): itemSize is string {\n  return isString(itemSize) && itemSize.endsWith('%')\n}\n\nexport function isPx(\n  itemSize: string | number | undefined\n): itemSize is string {\n  return isString(itemSize) && itemSize.endsWith('px')\n}\n\nexport function useSize(\n  panels: Ref<PanelItemState[]>,\n  containerSize: ComputedRef<number>\n) {\n  const propSizes = computed(() => panels.value.map((i) => i.size))\n\n  const panelCounts = computed(() => panels.value.length)\n\n  const percentSizes = ref<number[]>([])\n\n  watch([propSizes, panelCounts, containerSize], () => {\n    let ptgList: (number | undefined)[] = []\n    let emptyCount = 0\n\n    // Convert the passed props size to a percentage\n    for (let i = 0; i < panelCounts.value; i += 1) {\n      const itemSize = panels.value[i]?.size\n\n      if (isPct(itemSize)) {\n        ptgList[i] = getPct(itemSize)\n      } else if (isPx(itemSize)) {\n        ptgList[i] = getPx(itemSize) / containerSize.value\n      } else if (itemSize || itemSize === 0) {\n        const num = Number(itemSize)\n\n        if (!Number.isNaN(num)) {\n          ptgList[i] = num / containerSize.value\n        }\n      } else {\n        emptyCount += 1\n        ptgList[i] = undefined\n      }\n    }\n\n    const totalPtg = ptgList.reduce<number>((acc, ptg) => acc + (ptg || 0), 0)\n\n    if (totalPtg > 1 || !emptyCount) {\n      // If it is greater than 1, the scaling ratio\n      const scale = 1 / totalPtg\n      ptgList = ptgList.map((ptg) => (ptg === undefined ? 0 : ptg * scale))\n    } else {\n      // If it is less than 1, the filling ratio\n      const avgRest = (1 - totalPtg) / emptyCount\n      ptgList = ptgList.map((ptg) => (ptg === undefined ? avgRest : ptg))\n    }\n\n    percentSizes.value = ptgList as number[]\n  })\n\n  const ptg2px = (ptg: number) => ptg * containerSize.value\n  const pxSizes = computed(() => percentSizes.value.map(ptg2px))\n\n  return { percentSizes, pxSizes }\n}\n"], "names": ["isString", "computed", "ref", "watch"], "mappings": ";;;;;;;AAEO,SAAS,MAAM,CAAC,GAAG,EAAE;AAC5B,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACxC,CAAC;AACM,SAAS,KAAK,CAAC,GAAG,EAAE;AAC3B,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAClC,CAAC;AACM,SAAS,KAAK,CAAC,QAAQ,EAAE;AAChC,EAAE,OAAOA,eAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AACtD,CAAC;AACM,SAAS,IAAI,CAAC,QAAQ,EAAE;AAC/B,EAAE,OAAOA,eAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AACvD,CAAC;AACM,SAAS,OAAO,CAAC,MAAM,EAAE,aAAa,EAAE;AAC/C,EAAE,MAAM,SAAS,GAAGC,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AACpE,EAAE,MAAM,WAAW,GAAGA,YAAQ,CAAC,MAAM,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;AAC1D,EAAE,MAAM,YAAY,GAAGC,OAAG,CAAC,EAAE,CAAC,CAAC;AAC/B,EAAEC,SAAK,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE,aAAa,CAAC,EAAE,MAAM;AACvD,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,IAAI,OAAO,GAAG,EAAE,CAAC;AACrB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;AACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE;AACnD,MAAM,MAAM,QAAQ,GAAG,CAAC,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;AACzE,MAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE;AAC3B,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACtC,OAAO,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE;AACjC,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC,KAAK,CAAC;AAC3D,OAAO,MAAM,IAAI,QAAQ,IAAI,QAAQ,KAAK,CAAC,EAAE;AAC7C,QAAQ,MAAM,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAChC,UAAU,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC;AACjD,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,UAAU,IAAI,CAAC,CAAC;AACxB,QAAQ,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC;AAC5B,OAAO;AACP,KAAK;AACL,IAAI,MAAM,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,KAAK,GAAG,IAAI,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACvE,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE;AACrC,MAAM,MAAM,KAAK,GAAG,CAAC,GAAG,QAAQ,CAAC;AACjC,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC;AACvE,KAAK,MAAM;AACX,MAAM,MAAM,OAAO,GAAG,CAAC,CAAC,GAAG,QAAQ,IAAI,UAAU,CAAC;AAClD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,KAAK,CAAC,GAAG,OAAO,GAAG,GAAG,CAAC,CAAC;AACrE,KAAK;AACL,IAAI,YAAY,CAAC,KAAK,GAAG,OAAO,CAAC;AACjC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC;AACpD,EAAE,MAAM,OAAO,GAAGF,YAAQ,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;AACjE,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,CAAC;AACnC;;;;;;;;"}