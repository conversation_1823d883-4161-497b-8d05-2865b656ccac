{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/hooks/use-popper/index.ts"], "sourcesContent": ["import { computed, onBeforeUnmount, ref, shallowRef, unref, watch } from 'vue'\nimport { createPopper } from '@popperjs/core'\nimport { fromPairs } from 'lodash-unified'\n\nimport type { Ref } from 'vue'\nimport type {\n  Instance,\n  Modifier,\n  Options,\n  State,\n  VirtualElement,\n} from '@popperjs/core'\n\ntype ElementType = HTMLElement | undefined\ntype ReferenceElement = ElementType | VirtualElement\nexport type PartialOptions = Partial<Options>\n\nexport const usePopper = (\n  referenceElementRef: Ref<ReferenceElement>,\n  popperElementRef: Ref<ElementType>,\n  opts: Ref<PartialOptions> | PartialOptions = {} as PartialOptions\n) => {\n  const stateUpdater = {\n    name: 'updateState',\n    enabled: true,\n    phase: 'write',\n    fn: ({ state }) => {\n      const derivedState = deriveState(state)\n\n      Object.assign(states.value, derivedState)\n    },\n    requires: ['computeStyles'],\n  } as Modifier<'updateState', any>\n\n  const options = computed<Options>(() => {\n    const { onFirstUpdate, placement, strategy, modifiers } = unref(opts)\n\n    return {\n      onFirstUpdate,\n      placement: placement || 'bottom',\n      strategy: strategy || 'absolute',\n      modifiers: [\n        ...(modifiers || []),\n        stateUpdater,\n        { name: 'applyStyles', enabled: false },\n      ],\n    }\n  })\n\n  const instanceRef = shallowRef<Instance | undefined>()\n  const states = ref<Pick<State, 'styles' | 'attributes'>>({\n    styles: {\n      popper: {\n        position: unref(options).strategy,\n        left: '0',\n        top: '0',\n      },\n      arrow: {\n        position: 'absolute',\n      },\n    },\n    attributes: {},\n  })\n\n  const destroy = () => {\n    if (!instanceRef.value) return\n\n    instanceRef.value.destroy()\n    instanceRef.value = undefined\n  }\n\n  watch(\n    options,\n    (newOptions) => {\n      const instance = unref(instanceRef)\n      if (instance) {\n        instance.setOptions(newOptions)\n      }\n    },\n    {\n      deep: true,\n    }\n  )\n\n  watch(\n    [referenceElementRef, popperElementRef],\n    ([referenceElement, popperElement]) => {\n      destroy()\n      if (!referenceElement || !popperElement) return\n\n      instanceRef.value = createPopper(\n        referenceElement,\n        popperElement,\n        unref(options)\n      )\n    }\n  )\n\n  onBeforeUnmount(() => {\n    destroy()\n  })\n\n  return {\n    state: computed(() => ({ ...(unref(instanceRef)?.state || {}) })),\n    styles: computed(() => unref(states).styles),\n    attributes: computed(() => unref(states).attributes),\n    update: () => unref(instanceRef)?.update(),\n    forceUpdate: () => unref(instanceRef)?.forceUpdate(),\n    // Preventing end users from modifying the instance.\n    instanceRef: computed(() => unref(instanceRef)),\n  }\n}\n\nfunction deriveState(state: State) {\n  const elements = Object.keys(state.elements) as unknown as Array<\n    keyof State['elements']\n  >\n\n  const styles = fromPairs(\n    elements.map(\n      (element) =>\n        [element, state.styles[element] || {}] as [\n          string,\n          State['styles'][keyof State['styles']]\n        ]\n    )\n  )\n\n  const attributes = fromPairs(\n    elements.map(\n      (element) =>\n        [element, state.attributes[element]] as [\n          string,\n          State['attributes'][keyof State['attributes']]\n        ]\n    )\n  )\n\n  return {\n    styles,\n    attributes,\n  }\n}\n\nexport type UsePopperReturn = ReturnType<typeof usePopper>\n"], "names": [], "mappings": ";;;;AAGY,MAAC,SAAS,GAAG,CAAC,mBAAmB,EAAE,gBAAgB,EAAE,IAAI,GAAG,EAAE,KAAK;AAC/E,EAAE,MAAM,YAAY,GAAG;AACvB,IAAI,IAAI,EAAE,aAAa;AACvB,IAAI,OAAO,EAAE,IAAI;AACjB,IAAI,KAAK,EAAE,OAAO;AAClB,IAAI,EAAE,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK;AACvB,MAAM,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAC9C,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAChD,KAAK;AACL,IAAI,QAAQ,EAAE,CAAC,eAAe,CAAC;AAC/B,GAAG,CAAC;AACJ,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM;AACjC,IAAI,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;AAC1E,IAAI,OAAO;AACX,MAAM,aAAa;AACnB,MAAM,SAAS,EAAE,SAAS,IAAI,QAAQ;AACtC,MAAM,QAAQ,EAAE,QAAQ,IAAI,UAAU;AACtC,MAAM,SAAS,EAAE;AACjB,QAAQ,GAAG,SAAS,IAAI,EAAE;AAC1B,QAAQ,YAAY;AACpB,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE;AAC/C,OAAO;AACP,KAAK,CAAC;AACN,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,WAAW,GAAG,UAAU,EAAE,CAAC;AACnC,EAAE,MAAM,MAAM,GAAG,GAAG,CAAC;AACrB,IAAI,MAAM,EAAE;AACZ,MAAM,MAAM,EAAE;AACd,QAAQ,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,QAAQ;AACzC,QAAQ,IAAI,EAAE,GAAG;AACjB,QAAQ,GAAG,EAAE,GAAG;AAChB,OAAO;AACP,MAAM,KAAK,EAAE;AACb,QAAQ,QAAQ,EAAE,UAAU;AAC5B,OAAO;AACP,KAAK;AACL,IAAI,UAAU,EAAE,EAAE;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAG,MAAM;AACxB,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK;AAC1B,MAAM,OAAO;AACb,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;AAChC,IAAI,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;AAC/B,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,UAAU,KAAK;AACjC,IAAI,MAAM,QAAQ,GAAG,KAAK,CAAC,WAAW,CAAC,CAAC;AACxC,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;AACtC,KAAK;AACL,GAAG,EAAE;AACL,IAAI,IAAI,EAAE,IAAI;AACd,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,CAAC,mBAAmB,EAAE,gBAAgB,CAAC,EAAE,CAAC,CAAC,gBAAgB,EAAE,aAAa,CAAC,KAAK;AACxF,IAAI,OAAO,EAAE,CAAC;AACd,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,aAAa;AAC3C,MAAM,OAAO;AACb,IAAI,WAAW,CAAC,KAAK,GAAG,YAAY,CAAC,gBAAgB,EAAE,aAAa,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;AACtF,GAAG,CAAC,CAAC;AACL,EAAE,eAAe,CAAC,MAAM;AACxB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,KAAK,EAAE,QAAQ,CAAC,MAAM;AAC1B,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,KAAK,EAAE,EAAE,CAAC;AAClF,KAAK,CAAC;AACN,IAAI,MAAM,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;AAChD,IAAI,UAAU,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC;AACxD,IAAI,MAAM,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,MAAM,EAAE,CAAC;AACtE,KAAK;AACL,IAAI,WAAW,EAAE,MAAM;AACvB,MAAM,IAAI,EAAE,CAAC;AACb,MAAM,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,EAAE,CAAC;AAC3E,KAAK;AACL,IAAI,WAAW,EAAE,QAAQ,CAAC,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC;AACnD,GAAG,CAAC;AACJ,EAAE;AACF,SAAS,WAAW,CAAC,KAAK,EAAE;AAC5B,EAAE,MAAM,QAAQ,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/C,EAAE,MAAM,MAAM,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;AAC9F,EAAE,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAO,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAChG,EAAE,OAAO;AACT,IAAI,MAAM;AACV,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}